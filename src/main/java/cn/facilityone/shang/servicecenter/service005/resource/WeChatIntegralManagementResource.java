package cn.facilityone.shang.servicecenter.service005.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.servicecenter.service005.dto.UserPointUpdateDTO;
import cn.facilityone.shang.servicecenter.service005.service.WeChatIntegralManagementService;
import cn.facilityone.shang.wechat.h5.entity.UserProject;
import cn.facilityone.shang.wechat.h5.service.UserProjectService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * 积分管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/6/6 下午3:30
 */
@Path("/service005")
public class WeChatIntegralManagementResource {

    private static final String TEMPLATE_PATH = "/business/service/service005-wechatpoint.ftl";

    @Autowired
    private WeChatIntegralManagementService weChatIntegralManagementService;
    @Autowired
    private UserProjectService userProjectService;

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> init() {
        return new HashMap<>(0);
    }

    /**
     * 获取需求表格数据
     *
     * @param request
     * @return
     */
    @POST
    @Path("user/point/table")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findWorkOrder(DataTableRequest request) {
        return  weChatIntegralManagementService.findUserPoint(request);
    }


    @GET
    @Path("/user-project/{openId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result findWorkOrder(@PathParam("openId")String openId) {
        UserProject userProject=userProjectService.findUserProject(openId, ProjectContext.getCurrentProject());
        return  new Result(userProject);
    }

    @PUT
    @Path("update/point")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result findWorkOrder(UserPointUpdateDTO userPointUpdateDTO){
        weChatIntegralManagementService.updateUserPoint(userPointUpdateDTO);
        return new Result();
    }


    @POST
    @Path("/user/point/history/{openId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findWorkOrder(@PathParam("openId")String openId,DataTableRequest request) {
        return  weChatIntegralManagementService.findUserPointHistory(request,openId);
    }


}
