package cn.facilityone.shang.servicecenter.service005.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.service.CommonService;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.servicecenter.common.alias.UserPointHistoryView;
import cn.facilityone.shang.servicecenter.common.alias.UserProjectView;
import cn.facilityone.shang.servicecenter.common.mapper.UserPointMapper;
import cn.facilityone.shang.servicecenter.common.mapper.UserProjectMapper;
import cn.facilityone.shang.servicecenter.service005.dto.UserPointUpdateDTO;
import cn.facilityone.shang.wechat.h5.entity.Point;
import cn.facilityone.shang.wechat.h5.entity.UserProject;
import cn.facilityone.shang.wechat.h5.repository.UserProjectRepository;
import cn.facilityone.shang.wechat.h5.service.PointService;
import cn.facilityone.shang.wechat.h5.service.UserProjectService;
import cn.facilityone.xia.core.exception.BusinessException;
import cn.facilityone.xia.persistence.project.ProjectContext;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.POST;
import java.util.HashMap;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/6/6 下午4:13
 */
@Service
public class WeChatIntegralManagementServiceImpl implements WeChatIntegralManagementService {

    @Autowired
    private UserProjectMapper userProjectMapper;

    @Autowired
    private UserProjectService userProjectService;

    @Autowired
    private PointService pointService;

    @Autowired
    private CommonUserService commonUserService;

    @Autowired
    private UserProjectRepository userProjectRepository;

    @Autowired
    private UserPointMapper userPointMapper;

    @Override
    public DataTableResponse findUserPoint(DataTableRequest request) {
        PageHelper.startPage(request.getPageNumber(), request.getPageSize());
        List<DataTableColumn> columns = request.getColumns();
        HashMap<String, String> searchMap = new HashMap<>(16);
        for (DataTableColumn tableColumn : columns) {
            if (StringUtils.isEmpty(tableColumn.getSearchText())) {
                continue;
            }
            searchMap.put(tableColumn.getName(), tableColumn.getSearchText());
        }
        List<UserProjectView> userProjectViews = userProjectMapper.findUserProject(searchMap, ProjectContext.getCurrentProject());
        PageInfo pageInfo = new PageInfo<>(userProjectViews);
        return new DataTableResponse(userProjectViews, (int) pageInfo.getTotal(), pageInfo.getPageNum(), request.getDraw());
    }

    @Override
    public void updateUserPoint(UserPointUpdateDTO userPointUpdateDTO) {
        UserProject userProject=userProjectService.findUserProject(userPointUpdateDTO.getOpenId(), ProjectContext.getCurrentProject());
        if(userProject==null){
            throw  new BusinessException("数据异常");
        }
        int oldPoint=userProject.getPoint();
        int newPoint=userPointUpdateDTO.getPoint();
        if(newPoint<0){
            newPoint=0;
        }
        Long projectId=ProjectContext.getCurrentProject();

        Point.RewordType rewordType=Point.RewordType.PLUS;
        if(oldPoint>newPoint){
            rewordType=Point.RewordType.MINUS;
        }
        int variety=Math.abs(oldPoint-newPoint);
        Employee employee=commonUserService.findLoginEmployee();
        String name="系统";
        if (employee!=null){
            name=employee.getName();
        }
        userProject.setPoint(newPoint);
        userProjectRepository.save(userProject);
        pointService.savePointV2(userPointUpdateDTO.getOpenId(),rewordType,userPointUpdateDTO.getDescription(),name,projectId,variety,newPoint);

    }

    @Override
    public DataTableResponse findUserPointHistory(DataTableRequest request, String openId) {
        PageHelper.startPage(request.getPageNumber(), request.getPageSize());
        List<DataTableColumn> columns = request.getColumns();
        HashMap<String, String> searchMap = new HashMap<>(16);
        for (DataTableColumn tableColumn : columns) {
            if (StringUtils.isEmpty(tableColumn.getSearchText())) {
                continue;
            }
            searchMap.put(tableColumn.getName(), tableColumn.getSearchText());
        }
        List<UserPointHistoryView> userPointHistoryList = userPointMapper.findUserPointHistory(searchMap,ProjectContext.getCurrentProject(),openId);
        PageInfo pageInfo = new PageInfo<>(userPointHistoryList);
        return new DataTableResponse(userPointHistoryList, (int) pageInfo.getTotal(), pageInfo.getPageNum(), request.getDraw());
    }
}
