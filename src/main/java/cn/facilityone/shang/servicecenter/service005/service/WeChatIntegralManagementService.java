package cn.facilityone.shang.servicecenter.service005.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.servicecenter.service005.dto.UserPointUpdateDTO;

/**
 * service
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/6/6 下午4:11
 */
public interface WeChatIntegralManagementService {

    DataTableResponse findUserPoint(DataTableRequest request);

    void  updateUserPoint(UserPointUpdateDTO userPointUpdateDTO);

    DataTableResponse findUserPointHistory(DataTableRequest request,String openId);


}
