package cn.facilityone.shang.servicecenter.service003.resource;

import cn.facilityone.shang.common.component.ztree.ZTreeNode;
import cn.facilityone.shang.entity.servercenter.RequirementType;
import cn.facilityone.shang.servicecenter.service003.dto.RequirementCheckDto;
import cn.facilityone.shang.servicecenter.service003.service.RequirementTypeService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 需求类型
 * Created by renfr.wang on 2015/6/1.
 *
 * @since 1.0
 * <AUTHOR>
 *
 */
@Path("/service003")
public class RequirementTypeResource {

    private static final String TEMPLATE_SERVICE_TYPE_PATH = "/business/service/service003-type.ftl";

    @Autowired
    RequirementTypeService requirementTypeService;


    /**
     * 初始化
     *
     * @return
     */
    @GET
    @Template(name = TEMPLATE_SERVICE_TYPE_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>();
    }


    /**
     * 获得需求类型的树状图数据
     *
     * @return list<ZTreeNode>
     */
    @GET
    @Path("tree")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getTreeData() {
        List<ZTreeNode> roleTree = requirementTypeService.findRequirementTypeTree();
        return Result.data(roleTree);
    }

    /**
     * 获得需求类型详细数据
     */
    @GET
    @Path("nodes/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getTreeData(@PathParam("Id") Long id) {
        return new Result(requirementTypeService.findRequirementType(id));
    }

    /**
     * 是否需要创建工单
     */
    @GET
    @Path("requirementType/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getGenWo(@PathParam("Id") Long id) {
        return new Result(requirementTypeService.getGenWo(id));
    }

    /**
     * 新增树信息
     */
    @POST
    @Path("nodes")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result addRequirementType(RequirementType requirementType) {
        Result result = new Result(requirementTypeService.createRequirementType(requirementType));
        result.setMessage(XiaMesssageResource.getMessage("server.result.success.add", null, ""));
        return result;
    }

    /**
     * 修改树信息
     */
    @PUT
    @Path("nodes")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result updateRequirementType(RequirementType requirementType) {
        Result result = new Result(requirementTypeService.updateRequirementType(requirementType));
        result.setMessage(XiaMesssageResource.getMessage("MS000017", null, ""));
        return result;
    }

    /**
     * 删除树信息
     *
     * @return
     */
    @DELETE
    @Path("nodes/{requirementTypeId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result delEquipmentSystem(@PathParam("requirementTypeId") Long requirementTypeId) {
        requirementTypeService.deleteOneAndChildren(requirementTypeId);
        return new Result(XiaMesssageResource.getMessage("server.result.success.delete", null, ""));
    }
}

