package cn.facilityone.shang.servicecenter.service003.service;

import cn.facilityone.shang.common.component.ztree.ZTreeNode;
import cn.facilityone.shang.entity.servercenter.RequirementType;
import cn.facilityone.shang.servicecenter.service003.dto.RequirementCheckDto;

import java.util.List;

/**
 * 需求类型
 *
 * @version 1.0
 *          Created by renfr.wang on 2015/6/1.
 */
public interface RequirementTypeService {
    /**
     * 获取需求类型树
     *
     * @return
     */
    List<ZTreeNode> findRequirementTypeTree();

    /**
     * 详细数据
     *
     * @param id
     * @return
     */
    RequirementType findRequirementType(Long id);

    /**
     * 添加需求信息
     *
     * @param dto
     * @return
     */
    RequirementType createRequirementType(RequirementType dto);

    /**
     * 修改需求信息
     *
     * @param requirementType
     * @return
     */
    RequirementType updateRequirementType(RequirementType requirementType);

    /**
     * 删除需求类型
     *
     * @param id
     * @return
     */
    void deleteOneAndChildren(Long id);

    /**
     * 是否需要创建工单
     *
     * @param id
     * @return
     */
    RequirementType getGenWo(Long id);

    /**
     * 获取项目中所有需求类型
     * @param projectId
     * @return
     */
    List<RequirementType> findAllRequirementTypeByProjectId(Long projectId);

    RequirementType findRequirementTypeHardly(Long id, Long proId);
}
