package cn.facilityone.shang.servicecenter.service003.service;

import cn.facilityone.shang.common.component.ztree.ZTreeNode;
import cn.facilityone.shang.common.staticmetamodel.CommonFields_;
import cn.facilityone.shang.entity.servercenter.RequirementType;
import cn.facilityone.shang.entity.workorder.ServiceType;
import cn.facilityone.shang.servicecenter.common.repository.RequirementTypeRepository;
import cn.facilityone.shang.servicecenter.service003.dto.RequirementCheckDto;
import cn.facilityone.shang.workorder.common.repository.ServiceTypeRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;

import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 需求类型
 *
 * @version 1.0
 *          Created by renfr.wang on 2015/6/1.
 */
@Service
public class RequirementTypeServiceImpl implements RequirementTypeService {

    @Autowired
    private RequirementTypeRepository requirementTypeRepository;
    @Autowired
    private ServiceTypeRepository serviceTypeRepository;

    /**
     * 需求树
     *
     * @return
     */
    @Override
    public List<ZTreeNode> findRequirementTypeTree() {
        //Sort sort = new Sort(new Sort.Order(Sort.Direction.DESC, "sort"));
        List<RequirementType> requirementTypes = requirementTypeRepository.findAllOderSortDesc();
        Collections.sort(requirementTypes, new Comparator<RequirementType>() {
            public int compare(RequirementType re1, RequirementType re2) {
                return re1.getSort() - re2.getSort();
            }
        });
        List<ZTreeNode> RequirementTypeTree = new ArrayList<ZTreeNode>();
        for (RequirementType requirementType : requirementTypes) {
            
            //过滤掉微信固定的三个类型
            if(requirementType.getWcType()!=null){
                continue;
            }
            
            ZTreeNode treeRequirementTypes;
            if (requirementType.getParentReqType() == null) {
                treeRequirementTypes = new ZTreeNode(requirementType.getId(), requirementType.getName(),
                        requirementType.getId());
            } else {
                treeRequirementTypes = new ZTreeNode(requirementType.getId(), requirementType.getName(),
                        requirementType.getParentReqType().getId());
            }
            Map properties = new HashMap();
            properties.put("fullName", requirementType.getFullName());
            treeRequirementTypes.setProperties(properties);
            RequirementTypeTree.add(treeRequirementTypes);
        }
        return RequirementTypeTree;
    }

    /**
     * 查询需求类型及需求类型的上级
     *
     * @param id
     * @return
     */
    @Override
    @XiaTransactional(readOnly = true)
    public RequirementType findRequirementType(Long id) {
        return requirementTypeRepository.findRTFetchParent(id);
    }

    /**
     * 查询需求类型及需求类型的上级
     *
     * @param id
     * @return
     */
    @Override
    @XiaTransactional(readOnly = true)
    public RequirementType findRequirementTypeHardly(Long id,Long proId) {
        return requirementTypeRepository.findRTFetchParentHardly(id,proId);
    }

    /**
     * 添加需求类型
     *
     * @param requirementType
     */
    @Override
    @XiaTransactional(readOnly = false)
    public RequirementType createRequirementType(RequirementType requirementType) {
        if (requirementType == null) {
            return null;
        }
        if (requirementType.getSort() == 0) {
            Integer sort = null;
            //无父节点
            if (requirementType.getParentReqType() == null || (requirementType.getParentReqType() != null && requirementType.getParentReqType().getId() == null)) {
                sort = requirementTypeRepository.findMaxSortNumber();
            }

            // 有父节点
            if (requirementType.getParentReqType() != null && requirementType.getParentReqType().getId() != null) {
                sort = requirementTypeRepository.findMaxSortNumber(requirementType.getParentReqType().getId());
            }

            // 无最大排序值
            if (sort == null) {
                sort = Integer.valueOf(0);
            }
            requirementType.setSort(sort.intValue() + 100);
        }
        if (requirementType.getGenWo() && requirementType.getServiceType() != null && requirementType.getServiceType().getId() != null) {
            requirementType.setServiceType(serviceTypeRepository.findOne(requirementType.getServiceType().getId()));
        }else if(requirementType.getGenWo()){
            requirementType.setServiceType(null);
            requirementType.setGenWo(true);
        }else {
            requirementType.setServiceType(null);
            requirementType.setGenWo(false);
        }

        // set Full Name
        if (requirementType.getParentReqType() != null && requirementType.getParentReqType().getId() != null) {
            requirementType.setParentReqType(requirementTypeRepository.findOne(requirementType.getParentReqType().getId()));
            requirementType.setFullName(requirementType.getParentReqType().getFullName() + " / " + requirementType.getName());
        } else {
            requirementType.setParentReqType(null);
            requirementType.setFullName(requirementType.getName());
        }

        return requirementTypeRepository.save(requirementType);
    }

    /**
     * 修改需求类型
     *
     * @param requirementType
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public RequirementType updateRequirementType(RequirementType requirementType) {
        if (requirementType == null || requirementType.getId() == null) {
            return null;
        }
        RequirementType updateRequirementType = requirementTypeRepository.findOne(requirementType.getId());
        Long projectId = updateRequirementType.getProject();
        BeanUtils.copyProperties(requirementType, updateRequirementType, CommonFields_.CREATED_BY, CommonFields_.CREATED_DATE);
        updateRequirementType.setProject(projectId);
        if (updateRequirementType.getGenWo() && requirementType.getServiceType() != null && requirementType.getServiceType().getId() != null) {
            updateRequirementType.setServiceType(serviceTypeRepository.findOne(requirementType.getServiceType().getId()));
        }else if(updateRequirementType.getGenWo() ){
            updateRequirementType.setServiceType(null);
            updateRequirementType.setGenWo(true);
        }else {
            updateRequirementType.setServiceType(null);
            updateRequirementType.setGenWo(false);
        }

        if (updateRequirementType.getParentReqType() != null && updateRequirementType.getParentReqType().getId() != null) {
            RequirementType parent = requirementTypeRepository.findOne(updateRequirementType.getParentReqType().getId());

            updateRequirementType.setParentReqType(parent);
            updateRequirementType.setFullName(parent.getFullName() + " / " + updateRequirementType.getName());

        } else {
            updateRequirementType.setParentReqType(null);
            updateRequirementType.setFullName(updateRequirementType.getName());
        }
        List<RequirementType> RequirementTypes = updateChildrenName(updateRequirementType);
        RequirementTypes.add(updateRequirementType);
        requirementTypeRepository.saveInBatch(RequirementTypes);

        return requirementTypeRepository.save(updateRequirementType);
    }
    //保存子类型的fullName
    private List<RequirementType> updateChildrenName(RequirementType org) {
        List<RequirementType> orgs = new ArrayList<RequirementType>();
        if (org != null) {
            List<RequirementType> childs = requirementTypeRepository.findByParentId(org.getId());
            if (childs.size() > 0) {
                for (RequirementType o : childs) {
                    o.setFullName(org.getFullName() + " / " + o.getName());
                    orgs.addAll(updateChildrenName(o));
                }
                orgs.addAll(childs);
            }
        }
        return orgs;
    }
    /**
     * 删除需求类型
     *
     * @param id
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void deleteOneAndChildren(Long id) {
        if (id == null) {
            return;
        }
        List<Long> children = new ArrayList<Long>();
        //查找所有子节点
        findAllChildren(children, id);
        children.add(id);
        // 删除所有子节点及当前节点
        if(!CollectionUtils.isEmpty(children)){
            for(Long rtid:children){
                requirementTypeRepository.delete(rtid);
            }
        }
    }


    private void findAllChildren(List<Long> children, Long id) {
        //每层的子节点
        List<RequirementType> requirementTypeList = requirementTypeRepository.findAllChildren(id);
        if (requirementTypeList != null && requirementTypeList.size() > 0) {
            for (RequirementType type : requirementTypeList) {
                children.add(type.getId());
                findAllChildren(children, type.getId());
            }
        }
    }

    /**
     * 是否需要创建工单
     *
     * @param id
     * @return
     */
    @Override
    public RequirementType getGenWo(Long id) {
        RequirementType requirementType = requirementTypeRepository.findOne(id);
        return requirementType;
    }

    @Override
    public List<RequirementType> findAllRequirementTypeByProjectId(Long projectId) {
        ProjectContext.setCurrentProject(projectId);
        List<RequirementType> requirementTypes = requirementTypeRepository.findAll(new Sort(new Sort.Order(Sort.Direction.ASC, "sort"),
                new Sort.Order(Sort.Direction.ASC, "id")));
        return requirementTypes;
    }
}
