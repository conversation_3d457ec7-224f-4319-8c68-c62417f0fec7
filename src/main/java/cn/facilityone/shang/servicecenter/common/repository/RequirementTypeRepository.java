package cn.facilityone.shang.servicecenter.common.repository;

import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.servercenter.RequirementType;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.hibernate.type.ListType;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 *          Created by renfr.wang on 2015/5/14.
 */
@Repository
public interface RequirementTypeRepository extends XiaRepository<RequirementType, Long> {

    /**
     * 获取需求状态
     *
     * @return
     */
    @Query("SELECT rt FROM #{#entityName} rt LEFT JOIN FETCH rt.parentReqType LEFT JOIN FETCH rt.serviceType  WHERE rt.id = ?1")
    RequirementType findRTFetchParent(Long id);

    @Query("SELECT rt FROM #{#entityName} rt LEFT JOIN FETCH rt.parentReqType WHERE rt.id = ?1 AND rt.project = ?2")
    RequirementType findRTFetchParentHardly(Long id, Long proId);

    /**
     * 获取需求状态
     *
     * @return
     */
    @Query("select rt from  #{#entityName} rt where rt.name=?1  ")
    List<RequirementType> findByName(String wo);

    /**
     * 获取所有子需求
     */
    @Query("select rt from  #{#entityName} rt where rt.parentReqType.id=?1")
    List<RequirementType> findAllChildren(Long  id);

//    /**
//     *获取总量
//     */
//    @Query(value="select count(1) from requirement_type where parent_reqtype_id is null and deleted=0 ",nativeQuery = true)
//    int findLevelTopCount();

//    /**
//     *获取最大排序
//     */
//    @Query(value="select sort from requirement_type where parent_reqtype_id is null and deleted=0 order by sort desc limit 0,1",nativeQuery = true)
//    int findMaxSortNumber();

    /**
     * 获取最大排序
     */
    //TODO 最大值 当没有值时返回null
    //@Query(value = "select sort from eq_sys where parent_sys_id is null and deleted=0 order by sort desc limit 0,1", nativeQuery = true)
    @Query("SELECT MAX(sort) FROM #{#entityName} req WHERE req.parentReqType.id = ?1")
    Integer findMaxSortNumber(Long parentId);

    /**
     * 获取第一层的最大排序
     */
    @Query("SELECT MAX(sort) FROM #{#entityName} req WHERE req.parentReqType.id IS NULL")
    Integer findMaxSortNumber();


    @Query("select r from #{#entityName} r where r.parentReqType.id = ?1")
    List<RequirementType> findByParentId(Long id);
    
    /**
     * serviceType 懒加载
     */
    @Query("select rt from  #{#entityName} rt LEFT JOIN FETCH rt.serviceType WHERE rt.deleted=0  ORDER BY rt.sort DESC")
    List<RequirementType> findAllOderSortDesc();
    
    @Query("select r from #{#entityName} r where r.parentReqType is null")
    List<RequirementType> findByParentIsNull();

    @Query("select r from #{#entityName} r LEFT JOIN FETCH r.serviceType where r.deleted=0 and r.project=?1")
    List<RequirementType> findByProjectHardly(Long currentProjectId);
 
    @Query("select rt from  #{#entityName} rt where rt.wcType = ?1 and rt.project = ?2 ")
    RequirementType findByWcTypeAndProjectHardly(Requirement.RequestType type,Long projectId);

    RequirementType findByWcType(Requirement.RequestType type);

    /**
     * 找到最初项目的所有微信需求类型
     * @return
     */
    @Query(value="select rt.* from requirement_type rt where rt.proj_id=(select min(proj_id) from sys_project) and rt.wc_type is not null",nativeQuery=true)
    List<RequirementType> findAllWctypeOnOneProjectHardly();
    
    /**
     * 更新基础数据专用
     * @param lastModifiedDate
     * @return
     */
    @Query("select sa from #{#entityName} sa where sa.modifiedDate >= ?1 and sa.project=?2 ")
    List<RequirementType> findByModifiedDateAfterHardly(Date lastModifiedDate,Long currentProjectId);
    
    /**
     * 更新基础数据专用
     * @param lastModifiedDate
     * @return
     */
    @Query("select count(1) from #{#entityName} o where o.modifiedDate >= ?1  and o.project=?2")
    Long findCountByModifiedDateAfterHardly(Date lastModifiedDate,Long currentProjectId);

    @Query(value="select * from requirement_type where proj_id=?1 and deleted=0 and parent_reqtype_id is null and (wc_type is null or wc_type =0) order by sort",nativeQuery=true)
    List<RequirementType> findParentAllByProjId(Long projId);

    @Query(value="select * from requirement_type where proj_id=?1 and deleted=0  and reqtype_id in (?2) order by sort",nativeQuery=true)
    List<RequirementType> findParentAllByProjIdAndType(Long projId, Long[] reqType);

    @Query(value="select * from requirement_type where proj_id=?1 and deleted=0  order by sort",nativeQuery=true)
    List<RequirementType> findAllByProjId(Long projId);

    /**
     * 需求类型名称唯一性验证专用(findParentRequirementTypeByName / findRequirementTypeByNameAndParentId)
     *
     * @param name
     * @return
     */
    @Query("select rt from #{#entityName} rt where rt.name=?1 and rt.parentReqType is null")
    List<RequirementType> findParentRequirementTypeByName(String name);
    @Query("select rt from #{#entityName} rt where rt.name=?1 and rt.parentReqType is null and rt.id != ?2")
    List<RequirementType> findOthersParentRequirementTypeByName(String name, Long id);
    @Query("select rt from #{#entityName} rt where rt.name = ?1 and rt.parentReqType.id =?2")
    List<RequirementType> findRequirementTypeByNameAndParentId(String name, Long pid);
    @Query("select rt from #{#entityName} rt where rt.name = ?1 and rt.parentReqType.id =?2 and rt.id != ?3")
    List<RequirementType> findOthersRequirementTypeByNameAndParentId(String name, Long pid, Long id);
}
