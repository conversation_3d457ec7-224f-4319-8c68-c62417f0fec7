package cn.facilityone.shang.servicecenter.common.repository;

import cn.facilityone.shang.asset.asset002.dto.WoLaborerDTO;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderLaborer;
import cn.facilityone.shang.entity.workorder.WorkOrderLaborer.LaborerStatus;
import cn.facilityone.shang.mobile.v1.organize.model.EmployeeAndWoCountModel;
import cn.facilityone.shang.workorder.wo002.dto.WorkOrderActivityExportDto;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @version 1.0
 *          Created by renfr.wang on 2015/5/19.
 */
@Repository
public interface WorkOrderLaborerRepository extends XiaRepository<WorkOrderLaborer, Long> {
    /**
     * 获取执行人
     *
     * @return
     */
    @Query("select wl from  #{#entityName} wl where wl.workOrder=?1")
    List<WorkOrderLaborer> findWlByWo(WorkOrder wo);

    @Query("select wl from  #{#entityName} wl left join fetch wl.laborer where wl.workOrder.id=?1")
    List<WorkOrderLaborer> findByWorkOrderId(Long workOrderId);

    @Modifying
    @Query("delete from  #{#entityName} wl where wl.workOrder.id=?1 and wl.laborer.id=?2")
    int deleteByWorkOrderIdAndLabourId(Long workOrderId, Long labourId);

    @Query("select new cn.facilityone.shang.asset.asset002.dto.WoLaborerDTO(wl.workOrder.id as id,wl.laborer.name as name) from  #{#entityName} wl ")
    List<WoLaborerDTO> findAllWoIdAndLaborerName();

    @Modifying
    @Query("delete from #{#entityName} wl where wl.id in (?1)")
    void deleteAllByIds(Long[] labourIds);

    @Query("select wl from  #{#entityName} wl where wl.workOrder.id =?1 and wl.laborer.id = ?2")
    List<WorkOrderLaborer> findByWorkOrderIdAndEmployeeId(Long workOrderId, Long employeeId);

    @Query("select wl from  #{#entityName} wl left join fetch wl.workOrder w left join fetch wl.laborer l where l.id = ?1 and w.deleted=0 and w.status not in (4,5,6,7)")
    List<WorkOrderLaborer> findByEmployeeId(Long employeeId);

    @Modifying
    @Query("delete from #{#entityName} wl where wl.laborer.id in (?1)")
    void deleteByEmployeeIds(Long[] employeeIds);

    @Query("select wl from  #{#entityName} wl left join fetch wl.laborer where wl.workOrder.id = ?1")
    List<WorkOrderLaborer> findWithEmployeeByWorkOrderID(Long id);

    @Query("select wl from  #{#entityName} wl left join fetch wl.laborer where wl.workOrder.id = ?1 and wl.status in(?2)")
    Set<WorkOrderLaborer> findByWorkOrderIdAndStatus(Long workOrderId, List<WorkOrderLaborer.LaborerStatus> statuses);

    @Query("select wl from  #{#entityName} wl left join fetch wl.workOrder where wl.workOrder.id in (?1) and wl.laborer.id = ?2 and wl.status in(?3)")
    List<WorkOrderLaborer> findByWorkOrderIdAndEmployeeIdAndLaborerStatus(List<Long> workOrderId, Long labourId, List<WorkOrderLaborer.LaborerStatus> statuses);

    @Query("select new cn.facilityone.shang.mobile.v1.organize.model.EmployeeAndWoCountModel(em.id as emId,wo.id as woId) from  #{#entityName} wl left join wl.laborer em left join wl.workOrder wo where em.id in (?1) and wo.status in (?2) ")
    List<EmployeeAndWoCountModel> findEmployeeAndWoCountByEmployeeIdsAndStatus(Long[] ids, WorkOrder.WorkOrderStatus[] status);

    @Modifying
    @Query("delete from #{#entityName} wl where wl.laborer.id in (?1) and wl.workOrder.id = ?2")
    void deleteByEmployeeIdsAndWorkOrderId(Long[] employeeIds, Long id);

    @Modifying
    @Query("delete from #{#entityName} wl where  wl.workOrder.id = ?1")
    void deleteByWorkOrderId(Long workOrderId);

    @Query("select wl.laborer from  #{#entityName} wl where wl.workOrder.id = ?1 and wl.status in(?2) order by wl.id desc ")
    List<Employee> findEmployeeByWorkOrderIdAndStatusWithSort(Long workorderId, List<WorkOrderLaborer.LaborerStatus> statuses);

    @Query("select wl.laborer from  #{#entityName} wl where wl.workOrder.id = ?1 and wl.status in(?2)")
    Set<Employee> findEmployeeByWorkOrderIdAndStatus(Long woId, List<WorkOrderLaborer.LaborerStatus> statuses);

    @Query("select wl from  #{#entityName} wl left join fetch wl.workOrder where wl.laborer.id in(?1)")
    List<WorkOrderLaborer> findByEmployeeIds(Set<Long> longs);

    @Query(value = "SELECT count(1) FROM (SELECT DISTINCT wl.wo_id FROM wo_laborer wl WHERE wl.wo_laborer_id IN (?1)) AS A ", nativeQuery = true)
    Integer countWorkOrderNumberByIds(Set<Long> laborerIds);
    
    @Query("select new cn.facilityone.shang.workorder.wo002.dto.WorkOrderActivityExportDto(r.id,r.createdDate,r.workOrder.id,r.laborer.name,r.responsible) from #{#entityName} r  where r.status in (?1) and r.workOrder.id in (?2)")
    List<WorkOrderActivityExportDto> findByStatusAndWorkOrderIds(List<LaborerStatus> status,List<Long> workOrderIds);

    @Query("select wl from  #{#entityName} wl left join fetch wl.laborer where wl.workOrder.id=?1 and wl.status in (0,1)")
    List<WorkOrderLaborer> findCurrentLaborersByWorkOrderId(Long workOrderId);

    @Query("select wl.laborer from #{#entityName} wl where wl.workOrder.id = ?1 and wl.status = ?2 and wl.actualArrivalDateTime > ?3")
    List<Employee> findLastFinishLaborers(Long workOrderId, WorkOrderLaborer.LaborerStatus status, Date date);

    @Query("select new cn.facilityone.shang.workorder.wo002.dto.WorkOrderActivityExportDto(r.id,r.createdDate,r.workOrder.id,r.laborer.name,r.responsible) from #{#entityName} r  where r.status in (?1) and r.workOrder.id = (?2)")
    List<WorkOrderActivityExportDto> findByStatusAndwoId(List<LaborerStatus> status,Long woId);

    /**
     *执行人处理中工单数
     * @param ids
     * @return
     */
    @Query("select new cn.facilityone.shang.mobile.v1.organize.model.EmployeeAndWoCountModel(em.id as emId,wo.id as woId) from  #{#entityName} wl left join wl.laborer em left join wl.workOrder wo where em.id in (?1) and wl.status in (0,1) ")
    List<EmployeeAndWoCountModel> findEmployeeAndWoCountByEmployeeIdsAndLaborerStatus(Long[] ids);
}
