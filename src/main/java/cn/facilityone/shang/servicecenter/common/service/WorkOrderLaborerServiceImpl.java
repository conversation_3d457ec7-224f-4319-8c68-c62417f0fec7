package cn.facilityone.shang.servicecenter.common.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.entity.sign.Sign;
import cn.facilityone.shang.entity.workorder.*;
import cn.facilityone.shang.organize.org001.service.WorkTeamService;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.servicecenter.common.dto.WorkOrderScheduleDTO;
import cn.facilityone.shang.servicecenter.common.repository.WorkOrderLaborerRepository;
import cn.facilityone.shang.sign.common.repository.SignEmpRepository;
import cn.facilityone.shang.workorder.common.alias.WorkOrderLaborerView;
import cn.facilityone.shang.workorder.common.dto.WorkOrderScheduleRequestDTO;
import cn.facilityone.shang.workorder.common.mapper.WorkOrderLaborerViewMapper;
import cn.facilityone.shang.workorder.common.repository.SchedulingRepository;
import cn.facilityone.shang.workorder.common.service.WorkOrderTaskService;
import cn.facilityone.shang.workorder.wo003.repository.WorkOrderProcessRepository;
import cn.facilityone.xia.core.exception.BusinessException;
import cn.facilityone.xia.core.exception.ValidationException;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;

/**
 * @Author: wayne.fu
 * @Date: 6/1/2015
 */
@Service
public class WorkOrderLaborerServiceImpl implements WorkOrderLaborerService {

    private static final Logger log = LoggerFactory.getLogger(WorkOrderLaborerServiceImpl.class);
    private static final WorkOrderLaborer.LaborerStatus[] STATUSES = {WorkOrderLaborer.LaborerStatus.WAITING, WorkOrderLaborer.LaborerStatus.RECEIVED, WorkOrderLaborer.LaborerStatus.FINISH};
    private static final WorkOrderLaborer.LaborerStatus[] ASSIGNED_STATUS = {WorkOrderLaborer.LaborerStatus.WAITING, WorkOrderLaborer.LaborerStatus.RECEIVED};
    private static final ScheduleClass EMPTY_SCHEDULING_CLASS = new ScheduleClass();
    private static final String SCHEDULE_CLASS_SEPARATOR = " / ";
    private static final String FIELD_SEPARATOR = ".";
    private static final String FIELD_SEPARATOR_REG = "\\.";

    @Autowired
    private WorkOrderLaborerRepository workOrderLaborerRepository;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private WorkOrderTaskService workOrderTaskService;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private SchedulingRepository schedulingRepository;
    @Autowired
    private WorkOrderProcessRepository workOrderProcessRepository;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private WorkTeamService workTeamService;
    @Autowired
    private SignEmpRepository signEmpRepository;
    @Autowired
    private WorkOrderLaborerViewMapper workOrderLaborerViewMapper;


    /**
     * get work order laborer in Waiting and received
     *
     * @param workOrderId
     * @return
     */
    @Override
    @XiaTransactional(readOnly = true)
    public List<WorkOrderLaborer> findByWorkOrderIDAndStatue(Long workOrderId) {
        if (workOrderId == null) {
            return Collections.emptyList();
        }

        List<WorkOrderLaborer.LaborerStatus> allowedStatus = Arrays.asList(STATUSES);

        List<WorkOrderLaborer> results = workOrderLaborerRepository.findByWorkOrderId(workOrderId);
        List<WorkOrderLaborer> filteredResult = new ArrayList<>();
        for (WorkOrderLaborer laborer : results) {
            //filter status not in allowed list
            if (!allowedStatus.contains(laborer.getStatus())) {
                continue;
            }
            if (laborer.getLaborer() != null) {
                laborer.getLaborer().getId();
            }
            if (laborer.getLaborer() != null) {
                Employee employee = laborer.getLaborer();
                if (employee.getPosition() != null) {
                    employee.getPosition().getId();
                }
            }
            filteredResult.add(laborer);
        }
        return filteredResult;
    }

    /**
     * delete work order laborer by work order id and laborer id
     *
     * @param workOrderId
     * @param labourId
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void delete(Long workOrderId, Long labourId) {
        if (workOrderId == null || labourId == null) {
            return;
        }
        workOrderLaborerRepository.deleteByWorkOrderIdAndLabourId(workOrderId, labourId);
    }

    /**
     * accept the work order
     *
     * @param workOrderId
     * @param laborerId
     * @return
     */
    @Override
    public WorkOrder acceptWorkOrder(Long workOrderId, Long laborerId) {
        if (workOrderId == null || laborerId == null) {
            log.error("work order id or laborer id in acceptWorkOrder is empty");
            return null;
        }

        WorkOrder workOrder = workOrderRepository.findOne(workOrderId);
        Employee employee = employeeRepository.findOne(laborerId);

        if (employee == null) {
            throw new BusinessException(XiaMesssageResource.getMessage("server.wo.noEm"));
        }
        if (workOrder == null) {
            log.error("work order id:{} does not exist now.");
            return workOrder;
        }

        workOrderTaskService.acceptWorkOrder(workOrder, employee);
        return workOrder;
    }

    /**
     * find can be scheduling employee
     *
     * @param workOrderId
     * @return
     */
    @Override
    public List<WorkOrderScheduleDTO> findScheduleDTOByWorkOrderId(Long workOrderId) {
        if (workOrderId == null) {
            return Collections.emptyList();
        }
        //get work team
        WorkOrder workOrder = workOrderRepository.findOneWithWorkTeam(workOrderId);
        WorkTeam workTeam = workOrder.getWorkTeam();

        if (workTeam == null) {
            //find work team in work order process
            WorkOrderProcess workOrderProcess = workOrder.getWorkOrderProcess();
            if (workOrderProcess == null) {
                return Collections.emptyList();
            }

            //get all work team in process
            List<WorkTeam> workTeamsInProcess = workOrderProcessRepository.findWorkTeamByProcess(workOrder.getWorkOrderProcess().getId());
            if (CollectionUtils.isEmpty(workTeamsInProcess)) {
                return Collections.emptyList();
            }

            //get current login user
            Long loginUser = commonUserService.findCurrentEmployeeId();

            //get work team contain current user
            for (WorkTeam team : workTeamsInProcess) {
                boolean isIncluded = workTeamService.isEmployeeIncludedIn(team.getId(), loginUser);
                if (isIncluded) {
                    workTeam = team;
                    break;
                }
            }

            if (workTeam == null) {
                return Collections.emptyList();
            }
        }

        Long proId = ProjectContext.getCurrentProject();
        Date date = DateUtil.formatDate(DateUtil.parseDate(new Date(),DateUtil.FORMAT_PATTERN_DATE)) ;
        List<WorkOrderLaborerView> workOrderLaborerViews = workOrderLaborerViewMapper.findByprojectIdAndWorkTeamId(workTeam.getId(),proId,date);

        if (CollectionUtils.isEmpty(workOrderLaborerViews)) {
            return Collections.emptyList();
        }
        List<WorkOrderScheduleDTO> dtos = new ArrayList<>();
        for (WorkOrderLaborerView view :workOrderLaborerViews) {
            WorkOrderScheduleDTO dto = new WorkOrderScheduleDTO();
            dto.setEmployeeId(view.getId());
            dto.setLaborerName(view.getName());
            dto.setAssignedWorkOrder(view.getAssignedWorkOrder());
            dto.setScheduleClass(view.getSche_cls());
            String state = "NONE";
            if(view.getState()!=null){
                state =Sign.SignState.values()[view.getState()].name();
            }
            dto.setState(state);
            dto.setLaborerWorkTeam(workTeam);
            dtos.add(dto);
        }

// 2017.12.21 修改为 上面的workOrderLaborerViewMapper  mybatis实现
//        //find all employee with scheduleClass
//        List<Employee> employees = employeeRepository.findByWorkTeamId(workTeam.getId());
//
//        //no employee in work team
//        if (CollectionUtils.isEmpty(employees)) {
//            return Collections.emptyList();
//        }
//
//        //build dto
//        List<WorkOrderScheduleDTO> dtos = new ArrayList<>();
//        for (Employee employee : employees) {
//            //remove dimission employee
//            if (!employee.isActivated()) {
//                continue;
//            }
//            //get scheduling information
//            List<Scheduling> schedulings = schedulingRepository.findEntireOneByEmployeeIdAndDate(employee.getId(), new Date());
//            //get work order laborer information
//            List<WorkOrderLaborer> laborerList = workOrderLaborerRepository.findByEmployeeId(employee.getId());
//            // sign records
//            List<SignEmp> signEmpList = signEmpRepository.findByEmpAndProjectId(employee, ProjectContext.getCurrentProject());
//            String state = "NONE";
//            if (!CollectionUtils.isEmpty(signEmpList)) {
//                List<Sign> signList = signEmpList.get(0).getSigns();
//                state = Sign.SignState.OUT.toString();
//                if (!CollectionUtils.isEmpty(signList)) {
//                    state = signList.get(0).getState().toString();
//                }
//            }
//            WorkOrderScheduleDTO dto = buildDTO(schedulings, employee, workTeam, laborerList, state);
//            if (dto != null) {
//                dtos.add(dto);
//            }
//        }
        return dtos;
    }

    /**
     * add laborer for the workorder
     *
     * @param workOrder
     * @param employees
     * @param dto
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder addLaborerForWorkOrder(WorkOrder workOrder, List<Employee> employees, WorkOrderScheduleRequestDTO dto) {
        if (workOrder == null || CollectionUtils.isEmpty(employees)) {
            log.debug("workorder or employees in addLaborerForWorkOrder is null or empty");
            return null;
        }

        //save work work order laborer
        Long responsibleId = null;
        if (dto != null) {
            responsibleId = dto.getResponsible();
        }

        List<WorkOrderLaborer> laborerList = new ArrayList<>();
        for (Employee employee : employees) {
            WorkOrderLaborer workOrderLaborer = new WorkOrderLaborer();
            workOrderLaborer.setAssignDateTime(new Date());
            workOrderLaborer.setStatus(WorkOrderLaborer.LaborerStatus.WAITING);
            workOrderLaborer.setWorkOrder(workOrder);
            workOrderLaborer.setLaborer(employee);

            if (responsibleId == null) {
                workOrderLaborer.setResponsible(true);
                responsibleId = -1L;
            } else if (employee.getId().equals(responsibleId)) {
                workOrderLaborer.setResponsible(true);
            }

            laborerList.add(workOrderLaborer);
        }

        //update work order data
        laborerList = workOrderLaborerRepository.save(laborerList);
        workOrder = workOrderRepository.findOneWithWorkOrderLaborers(workOrder.getId());
        if (workOrder.getWorkOrderLaborers() != null) {
            workOrder.getWorkOrderLaborers().addAll(laborerList);
        }
        return workOrder;
    }

    /**
     * reject the work order
     *
     * @param workOrderId
     * @param laborerId
     * @param reason
     * @return
     */
    @Override
    public WorkOrder rejectWorkOrder(Long workOrderId, Long laborerId, String reason) {
        if (workOrderId == null || laborerId == null) {
            log.error("work order id or laborer id in acceptWorkOrder is empty");
            return null;
        }

        WorkOrder workOrder = workOrderRepository.findOne(workOrderId);
        Employee employee = employeeRepository.findOneHardly(laborerId);

        if (employee == null) {
            throw new BusinessException("Employee does not exist now.");
        }
        if (workOrder == null) {
            log.error("work order id:{} does not exist now.");
            return null;
        }

        workOrderTaskService.rejectWorkOrder(workOrder, employee,reason);
        return workOrder;
    }

    /**
     * check the laborer existed in work order and status is not in REJECT
     *
     * @param labourId
     * @param workOrderId
     * @return
     */
    @Override
    public String isLaborExisted(Long labourId, Long workOrderId) {
        WorkOrderLaborer.LaborerStatus[] statuses = {WorkOrderLaborer.LaborerStatus.RECEIVED, WorkOrderLaborer.LaborerStatus.WAITING};
        List<WorkOrderLaborer> laborerList = workOrderLaborerRepository.findByWorkOrderIdAndEmployeeIdAndLaborerStatus(Arrays.asList(workOrderId), labourId, Arrays.asList(statuses));
        return String.valueOf(CollectionUtils.isNotEmpty(laborerList));
    }

    /**
     * get id from employee list, make sure list is not empty
     *
     * @param employees
     * @return
     */
    private Long[] getEmployeeIds(List<Employee> employees) {
        Long[] ids = new Long[employees.size()];
        int i = 0;
        for (Employee employee : employees) {
            ids[i++] = employee.getId();
        }
        return ids;
    }

    /**
     * build DTO from the entity
     *
     * @param scheduling
     * @param employee
     * @param workTeam
     * @return
     */
    private WorkOrderScheduleDTO buildDTO(Collection<Scheduling> scheduling, Employee employee, WorkTeam workTeam, List<WorkOrderLaborer> laborerList, String state) {
        if (employee == null || workTeam == null) {
            return null;
        }

        WorkOrderScheduleDTO dto = new WorkOrderScheduleDTO();
        dto.setEmployeeId(employee.getId());
        dto.setLaborerName(employee.getName());
        dto.setLaborerWorkTeam(workTeam);
        dto.setScheduleClass(getSchedulingClassName(scheduling));
        dto.setAssignedWorkOrder(countAssignedWorkOrder(laborerList));
        dto.setState(state);
        return dto;
    }

    private String getSchedulingClassName(Collection<Scheduling> schedulings) {
        if (CollectionUtils.isEmpty(schedulings)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (Scheduling scheduling : schedulings) {
            if (scheduling.getCls() != null) {
                sb.append(scheduling.getCls().getName());
                sb.append(SCHEDULE_CLASS_SEPARATOR);
            }
        }
        sb.setLength(sb.length() - SCHEDULE_CLASS_SEPARATOR.length());
        return sb.toString();
    }

    /**
     * count task number assigned to the work order laborer
     *
     * @param laborerList
     * @return
     */
    private int countAssignedWorkOrder(List<WorkOrderLaborer> laborerList) {
        if (CollectionUtils.isEmpty(laborerList)) {
            return 0;
        }
        int result = 0;
        List status = Arrays.asList(ASSIGNED_STATUS);
        for (WorkOrderLaborer laborer : laborerList) {
            if (status.contains(laborer.getStatus())) {
                result++;
            }
        }
        return result;
    }

    /**
     * count task number assigned to the work order laborer
     *
     * @param employee
     * @return
     */
    @Override
    public int countAssignedWorkOrder(Employee employee) {
        if (employee == null) {
            return 0;
        }

        List<WorkOrderLaborer> laborerList = workOrderLaborerRepository.findByEmployeeId(employee.getId());

        if (CollectionUtils.isEmpty(laborerList)) {
            return 0;
        }
        int result = 0;
        List status = Arrays.asList(ASSIGNED_STATUS);
        for (WorkOrderLaborer laborer : laborerList) {
            if (status.contains(laborer.getStatus())) {
                result++;
            }
        }
        return result;
    }

    /**
     * find schedule request dto
     *
     * @param workOrderId
     * @return
     */
    @Override
    @XiaTransactional(readOnly = true)
    public WorkOrderScheduleRequestDTO getScheduleRequestDTOByWorkOrderId(Long workOrderId) {
        if (workOrderId == null) {
            return null;
        }
        WorkOrder workOrder = workOrderRepository.findOne(workOrderId);
        List<WorkOrderLaborer> laborerList = workOrderLaborerRepository.findWithEmployeeByWorkOrderID(workOrderId);

        WorkOrderScheduleRequestDTO scheduleDTO = new WorkOrderScheduleRequestDTO();
        if (workOrder.getEstimatedWorkingTime() != null) {
            scheduleDTO.setEstimatedWorkingTime(workOrder.getEstimatedWorkingTime().getValue());
            scheduleDTO.setUnit(workOrder.getEstimatedWorkingTime().getUnit());
        }
        scheduleDTO.setEstimatedFinishDateTime(workOrder.getEstimatedCompletionDateTime());
        scheduleDTO.setEstimatedArriveDateTime(workOrder.getEstimatedArrivalDateTime());
        scheduleDTO.setSendWorkContent(workOrder.getSendWorkContent());

        if (CollectionUtils.isEmpty(laborerList)) {
            return scheduleDTO;
        }

        for (WorkOrderLaborer laborer : laborerList) {
            if (!WorkOrderLaborer.LaborerStatus.WAITING.equals(laborer.getStatus())) {
                continue;
            }
            Employee employee = laborer.getLaborer();
            if (employee != null) {
                scheduleDTO.getLaborerIds().add(employee.getId());
                scheduleDTO.getLaborerName().put(employee.getId(), employee.getName());
            }

            if (laborer.getResponsible() == null ? false : laborer.getResponsible()) {
                scheduleDTO.setResponsible(employee.getId());
            }
        }
        return scheduleDTO;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void deleteByWorkOrderId(Long workOrderId) {
        if (workOrderId == null) {
            return;
        }
        workOrderLaborerRepository.deleteByWorkOrderId(workOrderId);
    }

    @Override
    public List<WorkOrderScheduleDTO> findScheduleDTOByWorkOrderId(Long workOrderId, DataTableRequest request) {
        if (workOrderId == null || request == null) {
            return Collections.emptyList();
        }

        List<WorkOrderScheduleDTO> results = this.findScheduleDTOByWorkOrderId(workOrderId);

        if (CollectionUtils.isNotEmpty(results)) {
            Iterator<WorkOrderScheduleDTO> dtoIterator = results.iterator();
            while (dtoIterator.hasNext()) {
                WorkOrderScheduleDTO dto = dtoIterator.next();
                if (!filterByRequest(dto, request)) {
                    dtoIterator.remove();
                }
            }
        }

        return results;
    }

    /**
     * find all laborer
     * @param workOrderId
     * @return
     */
    @Override
    public List<WorkOrderLaborer> findByWorkOrderID(Long workOrderId) {
        return workOrderLaborerRepository.findByWorkOrderId(workOrderId);
    }

    @Override
    public String findLaborerForPrintByWoId(Long workOrderId) {
        String laborers = "";
        List<WorkOrderLaborer.LaborerStatus> statuses = new ArrayList<>();
        //only finish status work order laborer.
        statuses.add(WorkOrderLaborer.LaborerStatus.FINISH);
        Set<Employee> laborerSet = workOrderLaborerRepository.findEmployeeByWorkOrderIdAndStatus(workOrderId, statuses);
        if (CollectionUtils.isNotEmpty(laborerSet)) {
            StringBuilder sb = new StringBuilder();
            for (Employee employee : laborerSet) {
                sb.append(employee.getName());
                sb.append("，");
            }
            sb.setLength(sb.length() - 1);
            laborers = sb.toString();
        }
        return laborers;
    }

    @Override
    public String findAcceptLaborerForPrintByWoId(Long workOrderId) {
        String laborers = "";
        List<WorkOrderLaborer.LaborerStatus> statuses = new ArrayList<>();
        //only accept status work order laborer.
        statuses.add(WorkOrderLaborer.LaborerStatus.RECEIVED);
        List<Employee> laborerSet = workOrderLaborerRepository.findEmployeeByWorkOrderIdAndStatusWithSort(workOrderId, statuses);
        if (CollectionUtils.isNotEmpty(laborerSet)) {
            laborers = laborerSet.get(0).getName();
        }
        return laborers;
    }

    /**
     * filter by column search text
     *
     * TODO: Performance Tuning
     *
     * @param dto
     * @param request
     * @return
     */
    private boolean filterByRequest(WorkOrderScheduleDTO dto, DataTableRequest request) {
        if (CollectionUtils.isEmpty(request.getColumns())) {
            return false;
        }

        List<DataTableColumn> columnList = request.getColumns();

        Class<? extends WorkOrderScheduleDTO> clazz = dto.getClass();

        for (DataTableColumn column : columnList) {
            String columnName = column.getName();

            if (StringUtils.isBlank(columnName)) {
                continue;
            }
            try {
                Object fieldValue;
                String fieldName = columnName;
                if (columnName.contains(FIELD_SEPARATOR)) {
                    String[] columnNames = columnName.split(FIELD_SEPARATOR_REG);
                    //only support x.y
                    if (columnNames.length > 2 && columnNames.length < 1) {
                        continue;
                    }

                    //get x
                    Field field = clazz.getDeclaredField(columnNames[0]);
                    field.setAccessible(true);
                    fieldValue = field.get(dto);

                    //get y
                    Class fieldClazz = field.getType();
                    Field fieldClazzField = fieldClazz.getDeclaredField(columnNames[1]);
                    fieldClazzField.setAccessible(true);
                    fieldValue = fieldClazzField.get(fieldValue);
                } else {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    fieldValue = field.get(dto);
                }

                if (StringUtils.isNotBlank(column.getSearchText())) {
                    String value = String.valueOf(fieldValue);
                    if (!StringUtils.containsIgnoreCase(value, column.getSearchText())) {
                        return false;
                    }
                }

            } catch (NoSuchFieldException e) {
                log.error("cannot find field for columnName :{}", column.getName(), e);
            } catch (IllegalAccessException e) {
                log.error("cannot access field for columnName :{}", column.getName(), e);
            }
        }

        return true;
    }
}
