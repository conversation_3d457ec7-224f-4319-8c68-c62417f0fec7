package cn.facilityone.shang.servicecenter.common.dto;

import cn.facilityone.shang.entity.organize.WorkTeam;

/**
 * do not change getter and setter method
 *
 * <br />
 *
 * or, change logic in {@link cn.facilityone.shang.servicecenter.common.service.WorkOrderLaborerServiceImpl#filterByRequest}
 *
 * @Author: wayne.fu
 * @Date: 6/16/2015
 */
public class WorkOrderScheduleDTO {
    private String laborerName;
    private Long employeeId;
    private WorkTeam laborerWorkTeam;
    private String scheduleClass;
    private int assignedWorkOrder;
    private String state ;

    public int getAssignedWorkOrder() {
        return assignedWorkOrder;
    }

    public void setAssignedWorkOrder(int assignedWorkOrder) {
        this.assignedWorkOrder = assignedWorkOrder;
    }

    public String getLaborerName() {
        return laborerName;
    }

    public void setLaborerName(String laborerName) {
        this.laborerName = laborerName;
    }

    public WorkTeam getLaborerWorkTeam() {
        return laborerWorkTeam;
    }

    public void setLaborerWorkTeam(WorkTeam laborerWorkTeam) {
        this.laborerWorkTeam = laborerWorkTeam;
    }

    public String getScheduleClass() {
        return scheduleClass;
    }

    public void setScheduleClass(String scheduleClass) {
        this.scheduleClass = scheduleClass;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
