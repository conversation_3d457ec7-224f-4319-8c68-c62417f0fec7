package cn.facilityone.shang.servicecenter.common.repository;

import cn.facilityone.shang.entity.servercenter.RequirementNoticeSwitch;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * Author：panda.yang
 * CreateTime：2016/9/29 17:47
 */
public interface RequirementNoticeSwitchRepositroy extends XiaRepository<RequirementNoticeSwitch, Long> {

    @Query(value = "select re.* from requirement_notice_switch re where re.status=?1 and re.proj_id=?2 and deleted=0 ", nativeQuery = true)
    RequirementNoticeSwitch getRequirementNoticeSwitchByStatus(int index, Long id);

}
