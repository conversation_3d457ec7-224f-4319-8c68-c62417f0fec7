package cn.facilityone.shang.servicecenter.common.alias;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

public class ReqStatusCountView {

    private int status;
    private int num;
    
    public static Map<Integer,Integer> build(List<ReqStatusCountView> views){
        Map<Integer,Integer> res = new HashMap<>();
        if(CollectionUtils.isNotEmpty(views)){
            for(ReqStatusCountView view:views){
                res.put(view.getStatus(), view.getNum());
            }
        }
        return res;
    }
    
    public int getStatus() {
        return status;
    }
    public void setStatus(int status) {
        this.status = status;
    }
    public int getNum() {
        return num;
    }
    public void setNum(int num) {
        this.num = num;
    }
}
