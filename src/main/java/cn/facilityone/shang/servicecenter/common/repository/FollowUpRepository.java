package cn.facilityone.shang.servicecenter.common.repository;

import cn.facilityone.shang.entity.servercenter.FollowUp;
import cn.facilityone.shang.entity.servercenter.SatisfactionDegree;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @version 1.0
 *          Created by renfr.wang on 2015/5/22.
 */
@Repository
public interface FollowUpRepository extends XiaRepository<FollowUp, Long> {
    /**
     *满意度等级
     */
    @Query("select fl.degree  from #{#entityName} fl where fl.id=?1 ")
    SatisfactionDegree findSatisfactionDegreeById(Long id);
    /**
     * 获取满意度
     */
    @Query("select sa  from SatisfactionDegree sa where sa.degree=?1 ")
    List<SatisfactionDegree> findSatisfactionDegreeByDegree(String degree);
}
