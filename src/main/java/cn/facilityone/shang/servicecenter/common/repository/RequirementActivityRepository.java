package cn.facilityone.shang.servicecenter.common.repository;

import cn.facilityone.shang.entity.servercenter.RequirementActivity;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 5/29/2015
 */
public interface RequirementActivityRepository extends XiaRepository<RequirementActivity,Long>{

    @Query("select ra  from #{#entityName} ra where ra.requirement.id=?1 order by ra.createdDate asc ")
    List<RequirementActivity> findByRequirementId(Long requirementId);
}
