package cn.facilityone.shang.servicecenter.common.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderLaborer;
import cn.facilityone.shang.servicecenter.common.dto.WorkOrderScheduleDTO;
import cn.facilityone.shang.workorder.common.dto.WorkOrderScheduleRequestDTO;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 6/1/2015
 */
public interface WorkOrderLaborerService {

    List<WorkOrderLaborer> findByWorkOrderIDAndStatue(Long workOrderId);

    void delete(Long workOrderId, Long labourId);

    WorkOrder acceptWorkOrder(Long workOrderId, Long laborerId);

    List<WorkOrderScheduleDTO> findScheduleDTOByWorkOrderId(Long workOrderId);

    WorkOrder addLaborerForWorkOrder(WorkOrder workOrder, List<Employee> employees, WorkOrderScheduleRequestDTO dto);

    WorkOrder rejectWorkOrder(Long workOrderId, Long laborerId, String reason);

    String isLaborExisted(Long labourId, Long workOrderId);

    int countAssignedWorkOrder(Employee employee);

    WorkOrderScheduleRequestDTO getScheduleRequestDTOByWorkOrderId(Long workOrderId);

    void deleteByWorkOrderId(Long workOrderId);

    List<WorkOrderScheduleDTO> findScheduleDTOByWorkOrderId(Long workOrderId, DataTableRequest request);

    List<WorkOrderLaborer> findByWorkOrderID(Long workOrderId);

    String findLaborerForPrintByWoId(Long workOrderId);

    String findAcceptLaborerForPrintByWoId(Long workOrderId);
}
