package cn.facilityone.shang.servicecenter.common.mapper;

import java.util.List;
import cn.facilityone.shang.servicecenter.common.alias.ReqStatusCountView;
import cn.facilityone.shang.servicecenter.common.alias.RequirementView;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import cn.facilityone.xia.persistence.mybatis.XiaMapper;

@Mapper
public interface RequirementMapper extends XiaMapper<RequirementView>{

    /**
     * 按状态分组，查询需求量
     * @return
     */
    List<ReqStatusCountView> countGoupByStatus (@Param("projId")long currentProjectId);
}