package cn.facilityone.shang.servicecenter.common.dao;

import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.workorder.WorkOrderLaborer;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.*;

/**
 * @Author: wayne.fu
 * @Date: 6/3/2015
 */
@Service
public class WorkOrderLaborerDaoImpl implements WorkOrderLaborerDao {

    @PersistenceContext
    private EntityManager em;

    @Override
    @XiaTransactional(readOnly = true)
    public Map<Long, List<WorkOrderLaborer>> findLabourInfoByWorkOrderIds(List<Long> workOrderIds) {

        if(workOrderIds==null||workOrderIds.isEmpty()){
            return Collections.emptyMap();
        }

        StringBuilder sql = new StringBuilder("SELECT wl.*,em.*");
        sql.append(" FROM wo_laborer wl LEFT OUTER JOIN em AS em ON wl.laborer_id = em.em_id");
        sql.append(" WHERE wl.wo_id IN (");
        for (int i = 0, j = workOrderIds.size(); i < j; i++) {
            if (i == j - 1) {
                sql.append("?");
            } else {
                sql.append("?,");
            }
        }
        sql.append(")");

        Query query = em.createNativeQuery(sql.toString(), "WorkOrderResult");
        for (int i =0,j= workOrderIds.size();i<j;i++){
            query.setParameter(i+1,workOrderIds.get(i));
        }

        List<?> result = query.getResultList();

        if(result.isEmpty()){
            return Collections.emptyMap();
        }
        
        Map<Long,List<WorkOrderLaborer>> workOrderLabourMap = new HashMap<>();
        for (Object obj : result) {
            Object[] objResults = (Object[]) obj;
            WorkOrderLaborer workOrderLaborer = (WorkOrderLaborer) objResults[0];
            Employee employee = (Employee) objResults[1];
            
            workOrderLaborer.setLaborer(employee);
            Long workOrderId = workOrderLaborer.getWorkOrder().getId();
            
            if(workOrderLabourMap.get(workOrderId)==null){
                List<WorkOrderLaborer> workOrderLaborers = new ArrayList<>();
                workOrderLaborers.add(workOrderLaborer);
                workOrderLabourMap.put(workOrderId,workOrderLaborers);
            }else{
                workOrderLabourMap.get(workOrderId).add(workOrderLaborer);
            }
        }

        return workOrderLabourMap;
    }
}
