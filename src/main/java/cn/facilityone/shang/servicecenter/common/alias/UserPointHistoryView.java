package cn.facilityone.shang.servicecenter.common.alias;

import java.util.Date;

/**
 * Class description goes here.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/6/6 下午7:45
 */
public class UserPointHistoryView {
    private Date time;

    private int point;

    private int nowPoint;

    private String description;

    private String createdName;


    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public int getPoint() {
        return point;
    }

    public void setPoint(int point) {
        this.point = point;
    }

    public int getNowPoint() {
        return nowPoint;
    }

    public void setNowPoint(int nowPoint) {
        this.nowPoint = nowPoint;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }
}
