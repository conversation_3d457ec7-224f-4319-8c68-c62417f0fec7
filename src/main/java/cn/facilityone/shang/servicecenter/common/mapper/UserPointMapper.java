package cn.facilityone.shang.servicecenter.common.mapper;

import cn.facilityone.shang.servicecenter.common.alias.RequirementView;
import cn.facilityone.shang.servicecenter.common.alias.UserPointHistoryView;
import cn.facilityone.xia.persistence.mybatis.XiaMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/6/6 下午7:24
 */
public interface UserPointMapper extends XiaMapper<RequirementView> {

    List<UserPointHistoryView> findUserPointHistory(@Param("searchMap") Map<String, String> searchMap, @Param("projectId") Long projectId, @Param("openId") String openId);

}
