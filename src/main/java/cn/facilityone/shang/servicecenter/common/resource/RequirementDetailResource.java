package cn.facilityone.shang.servicecenter.common.resource;

import cn.facilityone.shang.common.configuration.properties.SystemProperties;
import cn.facilityone.shang.epayment.epayment000.common.PayMenuConstants;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wayne.fu
 * @Date: 8/7/2015
 */

@Path("/service000")
public class RequirementDetailResource {
    private static final String TEMPLATE_SERVICE_PATH = "/business/service/requirement-detail.ftl";

    @Autowired
    private SystemProperties systemProperties;

    /**
     * 初始化
     *
     * @return
     */
    @GET
    @Produces(MediaType.TEXT_HTML)
    @Template(name = TEMPLATE_SERVICE_PATH)
    public Map<String, Object> init() {
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("i18nLocale", XiaMesssageResource.getLocaleString());
        boolean isShowPayorder = systemProperties.isIsNotShowMenu(PayMenuConstants.MENU_ID_PAY);
        map.put("isShowPayorder", isShowPayorder?"1":"0");
        return map;
    }
}
