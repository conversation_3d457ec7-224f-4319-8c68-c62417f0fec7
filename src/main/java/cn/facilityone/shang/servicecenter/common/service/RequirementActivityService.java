package cn.facilityone.shang.servicecenter.common.service;

import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.servercenter.RequirementActivity;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 5/29/2015
 */
public interface RequirementActivityService {

    List<RequirementActivity> getRequirementActivityByRequirementId(Long requirementId);

    void addRequirementActivity(Requirement requirement, RequirementActivity.ActivityType activityType,String desc);

    void addRequirementActivity_wechat(Requirement requirement, RequirementActivity.ActivityType activityType,String desc);
}
