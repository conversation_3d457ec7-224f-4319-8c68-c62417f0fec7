package cn.facilityone.shang.servicecenter.common.alias;

import java.util.Date;

/**
 * Class description goes here.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/6/6 下午4:19
 */
public class UserProjectView {
    private String openId;
    private String nickName;
    private Date createTime;
    private Integer point;

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getPoint() {
        return point;
    }

    public void setPoint(Integer point) {
        this.point = point;
    }
}
