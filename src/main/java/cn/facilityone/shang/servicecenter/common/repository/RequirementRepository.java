package cn.facilityone.shang.servicecenter.common.repository;

import cn.facilityone.shang.entity.organize.Customer;
import cn.facilityone.shang.entity.servercenter.Evaluation;
import cn.facilityone.shang.entity.servercenter.FollowUp;
import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.servercenter.RequirementType;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 *          Created by renfr.wang on 2015/5/14.
 */
@Repository
public interface RequirementRepository extends XiaRepository<Requirement, Long> {

    /**
     * 需求类型
     *
     * @param id
     * @return
     */
    @Deprecated
    @Query("select re.requirementType  from #{#entityName} re where re.id=?1 ")
    RequirementType findRequirementById(Long id);

    @Query("select re.requirementType  from #{#entityName} re where re.id in (?1) ")
    List<RequirementType> findRequirementTypesById(Long[] ids);

    /**
     * 客户
     *
     * @param id
     * @return
     */
    @Deprecated
    @Query("select re.customer  from #{#entityName} re where re.id=?1 ")
    Customer findCustomerById(Long id);

    /**
     * 根据状态查询需求数据
     *
     * @param status
     * @return
     */
    @Query("select re  from #{#entityName} re where re.status=?1 ")
    List<Requirement> findRequirementByStatus(Requirement.RequirementStatus status);

    /**
     * 根据创建时间查询需求
     *  SELECT  *  FROM  requirement  WHERE To_Days(NOW())-To_Days(created_date)<8
     */
    @Query("select re  from #{#entityName} re where (To_Days(NOW())-To_Days(created_date))<?1 ")
    List<Requirement> findRequirementByCreateTime(Long day);
    /**
     * 根据完成时间查询需求
     */
    @Query("select re  from #{#entityName} re where (To_Days(NOW())-To_Days(completedDateTime))<?1 ")
    List<Requirement> findRequirementByFinishTime(Long day);

    /**
     *满意度
     */
    @Query("select re.followUp  from #{#entityName} re where re.id=?1 ")
    FollowUp findFollowUpById(Long id);

    /**
     * 新满意度
     */
    @Query("select re.evaluation  from #{#entityName} re where re.id=?1 ")
    Evaluation findEvaluateById(Long id);

    /**
     *查询需求编号
     */
    @Query(value = "select re.request_code from requirement re where re.proj_id=?1 ORDER BY re.created_date DESC limit 1 ",nativeQuery = true)
    String findNewId(Long currentProjectId);

    @Query(value = "select max(right(re.request_code,8)) from requirement re where re.proj_id=?1 ",nativeQuery = true)
    String findMaxCode(Long currentProjectId);

    /**
     * 需求编号查询需求
     */
    @Query("select re  from #{#entityName} re where re.code=?1 ")
    Requirement findRequirementByCode(String code);
    /**
     * Find requirement by work order id
     * @param workOrderId
     * @return
     */
    @Query(value = "select r.* from requirement r JOIN wo wo on r.req_id =  wo.req_id where wo.wo_id = ?1 ",nativeQuery = true)
    Requirement findByWorkOrderId(Long workOrderId);

    @Query("select re.workOrder  from #{#entityName} re where re.id=?1")
    List<WorkOrder> findWorkOrderById(Long id);

    /**
     * solr专用
     * @return
     */
    @Query(value="SELECT * FROM requirement WHERE deleted=0  AND created_date <= date_sub(now(),interval 48 hour) AND created_date > date_sub(now(),interval 49 hour)",nativeQuery=true)
    List<Requirement> findOutTimeRequirementForSolr();

    @Query("select req from #{#entityName} req left join fetch req.requirementType  left join fetch req.customer where req.id in (?1)")
    List<Requirement> findByIdIn(Long[] ids);

    @Query("SELECT COUNT(1) FROM #{#entityName} req WHERE req.createdDate >= ?1 AND req.createdDate < ?2 AND req.requirementType.name LIKE '%投诉%'")
    int findAllComplain(Date today, Date tomorrow);

    @Query("SELECT COUNT(1) FROM #{#entityName} req WHERE req.createdDate >= ?1 AND req.createdDate < ?2 AND req.requirementType.name LIKE '%投诉%' AND req.status IN (0,1)")
    int findUnComplain(Date today, Date tomorrow);

    @Query("SELECT COUNT(1) FROM #{#entityName} req WHERE req.requirementType.id = ?1 AND req.createdDate >= ?2 AND req.createdDate < ?3 ")
    int findByTypeId(Long typeId,Date today, Date tomorrow);

    @Query("select req from #{#entityName} req where req.id in (?1)")
    List<Requirement> findByIdInDirectlyHardly(Long[] ids);

    @Query("select req from #{#entityName} req LEFT JOIN FETCH req.requirementType LEFT JOIN FETCH req.workOrder LEFT JOIN FETCH req.employee LEFT JOIN FETCH req.building LEFT JOIN FETCH req.floor LEFT JOIN FETCH req.room where req.id = ?1")
    Requirement findOneLoadReqType(Long id);

    @Query("select req from #{#entityName} req LEFT JOIN FETCH req.requirementType LEFT JOIN FETCH req.employee LEFT JOIN FETCH req.customer LEFT JOIN FETCH req.building LEFT JOIN FETCH req.floor LEFT JOIN FETCH req.room  where req.id = ?1")
    Requirement findOneWithAllInfo(Long id);

    @Query("select req from #{#entityName} req LEFT JOIN FETCH req.customer left join fetch req.employee where req.id = ?1")
    Requirement findWithCustomerAndEmployee(Long id);

    @Query("select req from #{#entityName} req where req.project in (?1)")
    List<Requirement> findRequirementByProjectids(Long[] proIds);

    @Query(value="select req from #{#entityName} req where req.project in (?1)",
            countQuery="select count(1) from Requirement req where req.project in (?1)")
    Page<Requirement> findRequirementByProjectidsPage(Long[] proIds, Pageable pageable);

    @Query("select req from #{#entityName} req where req.project = ?1")
    Requirement getRequirementByProjectId(Long proId);

    @Query("select req from #{#entityName} req where req.project = ?1 and req.wechatRequesterId = ?2 ORDER BY req.id DESC")
    List<Requirement> findByOpenId(Long projectId, String openId, Pageable pageable);

    @Query("select req from #{#entityName} req where req.project = ?1 and req.wechatRequesterId = ?2 ORDER BY req.id DESC")
    Page<Requirement> findPageByOpenId(Long projectId, String openId, Pageable pageable);

    @Query(value = "select req.* from requirement req left join sys_project project on req.proj_id=project.proj_id " +
            " where project.deleted=0 and req.deleted=0 and req.wechat_requester_id  =?1 ORDER BY req.req_id DESC limit ?2,?3", nativeQuery = true)
    List<Requirement> findPageByOpenIdHardly(String openId, int start, int offetsize);

    @Query("select req from #{#entityName} req where req.createdDate >= ?1 and req.createdDate <= ?2")
    List<Requirement> findRqInDateRange(Date start,Date end);

    @Query("select distinct req from #{#entityName} req left join fetch req.workOrder left join fetch req.requirementType where req.createdDate >= ?1 and req.createdDate <= ?2 and req.status != ?3 and req.status != ?4")
    List<Requirement> findRqInDateRanges(Date start,Date end, Requirement.RequirementStatus status1, Requirement.RequirementStatus status2);

    @Query("select distinct req from #{#entityName} req left join fetch req.workOrder left join fetch req.requirementType rt where rt.deleted=0 and req.createdDate >= ?1 and req.createdDate <= ?2 and req.status != ?3 and req.status != ?4 and req.project = ?5 and req.source = ?6")
    List<Requirement> findRqInDateRanges(Date start,Date end, Requirement.RequirementStatus status1, Requirement.RequirementStatus status2, Long proId, Requirement.RequirementSource source);

    @Query("select distinct req from #{#entityName} req left join fetch req.requirementType rt where rt.deleted=0 and req.createdDate >= ?1 and req.createdDate <= ?2 and req.status != ?3 and req.status != ?4 and req.status != ?5 and req.project = ?6")
    List<Requirement> findRqCompleteInDateRanges(Date start,Date end, Requirement.RequirementStatus status1, Requirement.RequirementStatus status2,Requirement.RequirementStatus status3, Long proId);

    @Query("select distinct req from #{#entityName} req left join fetch req.workOrder left join fetch req.requirementType rt where rt.deleted=0 and req.createdDate >= ?1 and req.createdDate <= ?2 and req.status != ?3 and req.status != ?4 and req.project = ?5 and req.source = ?6")
    List<Requirement> findRqInDateRangesHardly(Date firstDay, Date lastDay, Requirement.RequirementStatus create, Requirement.RequirementStatus cancel, Long proId, Requirement.RequirementSource wechat);

    @Query("select distinct req from #{#entityName} req left join fetch req.requirementType rt where rt.deleted=0 and req.createdDate >= ?1 and req.createdDate <= ?2 and req.status != ?3 and req.status != ?4 and req.status != ?5 and req.project = ?6")
    List<Requirement> findRqCompleteInDateRangesHardly(Date start,Date end, Requirement.RequirementStatus status1, Requirement.RequirementStatus status2,Requirement.RequirementStatus status3, Long proId);
}
