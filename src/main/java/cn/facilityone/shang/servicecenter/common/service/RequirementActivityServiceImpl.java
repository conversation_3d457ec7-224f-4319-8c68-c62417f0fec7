package cn.facilityone.shang.servicecenter.common.service;

import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.servercenter.RequirementActivity;
import cn.facilityone.shang.servicecenter.common.repository.RequirementActivityRepository;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.SecurityContextImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 5/29/2015
 */
@Service
public class RequirementActivityServiceImpl implements RequirementActivityService {

    @Autowired
    private RequirementActivityRepository requirementActivityRepository;

    @Override
    public List<RequirementActivity> getRequirementActivityByRequirementId(Long requirementId) {
        if (requirementId == null) {
            return Collections.emptyList();
        }
        return requirementActivityRepository.findByRequirementId(requirementId);
    }

    /**
     * add requirement activity
     *
     * @param requirement
     * @param activityType
     */
    @Override
    public void addRequirementActivity(Requirement requirement, RequirementActivity.ActivityType activityType,String desc) {

        if (requirement == null || activityType == null) {
            return;
        }
        if(desc==null){
            desc = "";
        }

        RequirementActivity requirementActivity = new RequirementActivity();
        requirementActivity.setActivityType(activityType);
        requirementActivity.setDescription(desc);
        requirementActivity.setRequirement(requirement);
        requirementActivityRepository.save(requirementActivity);
    }

    public void addRequirementActivity_wechat(Requirement requirement, RequirementActivity.ActivityType activityType,String desc) {

        if (requirement == null || activityType == null) {
            return;
        }
        if(desc==null){
            desc = "";
        }

        RequirementActivity requirementActivity = new RequirementActivity();
        requirementActivity.setActivityType(activityType);
        requirementActivity.setDescription(desc);
        requirementActivity.setRequirement(requirement);
        SecurityContextImpl context = new SecurityContextImpl();
        context.setRealName(requirement.getRequestName());
        SecurityContextHolder.setContext(context);
        requirementActivityRepository.save(requirementActivity);
    }



}
