package cn.facilityone.shang.servicecenter.service002.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.servicecenter.service002.dto.RequirementColumnsDTO;
import cn.facilityone.shang.servicecenter.service002.dto.WorkOrderSearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 需求查询接口
 *
 * @version 1.0
 *          Created by renfr.wang on 2015/5/29.
 */
public interface RequirementService {

    Page<Requirement> findColumnWorkOrders(DataTableRequest request, Pageable pageable,String status);

    List<RequirementColumnsDTO> findRequirements(DataTableRequest request, Pageable pageable, boolean export);

    int getRequirementCount(DataTableRequest request);

    @Deprecated
    List<WorkOrderSearchDTO>  findColumnRequirement(DataTableRequest request, Pageable pageable,String status);


    Requirement changeStatusTo(Requirement requirement, Requirement.RequirementStatus status);

    Map<String, Long> getStatusNums();

    Map<String, Long> getSourceNums();
}
