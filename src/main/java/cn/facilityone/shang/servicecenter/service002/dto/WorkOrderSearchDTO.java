package cn.facilityone.shang.servicecenter.service002.dto;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;

import java.util.Date;

/**
 * @version 1.0
 * Created by renfr.wang on 2015/5/29.
 */
public class WorkOrderSearchDTO extends DataTableRequest {

    private String reCode;
    private String requestName;
    private String linkCode;
    private String requirementType;
    private String content;
    private String status;
    private Date time;
    private String degree;
    private String woCode;

    public String getReCode() {
        return reCode;
    }

    public String getRequestName() {
        return requestName;
    }

    public void setRequestName(String requestName) {
        this.requestName = requestName;
    }

    public String getLinkCode() {
        return linkCode;
    }

    public void setLinkCode(String linkCode) {
        this.linkCode = linkCode;
    }

    public String getRequirementType() {
        return requirementType;
    }

    public void setRequirementType(String requirementType) {
        this.requirementType = requirementType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWoCode() {
        return woCode;
    }

    public void setWoCode(String woCode) {
        this.woCode = woCode;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public void setReCode(String reCode) {
        this.reCode = reCode;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }
}
