package cn.facilityone.shang.servicecenter.service002.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableConditions;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.asset.Contract;
import cn.facilityone.shang.entity.servercenter.FollowUp;
import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.servercenter.Requirement.RequirementSource;
import cn.facilityone.shang.entity.servercenter.Requirement.RequirementStatus;
import cn.facilityone.shang.entity.servercenter.RequirementType;
import cn.facilityone.shang.entity.servercenter.SatisfactionDegree;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.servicecenter.common.repository.FollowUpRepository;
import cn.facilityone.shang.servicecenter.common.repository.RequirementRepository;
import cn.facilityone.shang.servicecenter.common.staticmetamodel.Requirement_;
import cn.facilityone.shang.servicecenter.service002.dto.RequirementColumnsDTO;
import cn.facilityone.shang.servicecenter.service002.dto.WorkOrderSearchDTO;
import cn.facilityone.shang.workorder.common.staticmetamodel.WorkOrder_;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.datajpa.query.QueryUtils;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 需求查询实现
 *
 * @version 1.0
 *          Created by renfr.wang on 2015/5/29.
 */
@Service
public class RequirementServiceImpl implements RequirementService {

    private static final Logger log = LoggerFactory.getLogger(RequirementServiceImpl.class);
    
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private RequirementRepository requirementRepository;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private FollowUpRepository followUpRepository;
    @Autowired
    private EntityManagerFactory entityManagerFactory;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    @XiaTransactional(readOnly = true)
    public Page<Requirement> findColumnWorkOrders(final DataTableRequest request, Pageable page, String status) {
        if (null == page) {
            page =
                    new DataTableRequest(page.getPageNumber(), page.getOffset(), page.getPageSize(),
                            dataTableService.buildColumnSort(request.getColumns()),
                            request.getDraw());
        }
        Page<Requirement> result = requirementRepository.findAll(new XiaSpecification<Requirement>() {
            @Override
            public Predicate toPredicate(Root<Requirement> root, CriteriaQuery<?> criteriaQuery,
                                         CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                List<DataTableColumn> columnList = request.getColumns();
                if (columnList != null && columnList.size() > 0) {
                    List<Predicate> lps = dataTableService.buildColumnSearch(columnList, root, criteriaBuilder);
                    predicatesList.addAll(lps);
                }
                criteriaQuery.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                return criteriaQuery.getRestriction();
            }

            @Override
            public Root<Requirement> toRoot(Root<Requirement> root, CriteriaQuery<?> query,
                                            CriteriaBuilder cb) {
                root.fetch(Requirement_.CUSTOMER, JoinType.LEFT);
                root.fetch(Requirement_.REQUIREMENT_TYPE, JoinType.LEFT);
                root.fetch(Requirement_.FOLLOW_UP, JoinType.LEFT);
                // root.fetch(Requirement_.FOLLOW_UP, JoinType.LEFT).fetch(FollowUp_.SATUSFACTION_DEGREE_ID,JoinType.LEFT);
                return root;
            }
        }, page);
        return result;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<RequirementColumnsDTO> findRequirements(DataTableRequest request, Pageable pageable, boolean export) {
        EntityManager em = entityManagerFactory.createEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();

        //Entity User retrieve
        CriteriaQuery<Requirement> query = cb.createQuery(Requirement.class);
        Root<Requirement> root = query.from(Requirement.class);
        From from = root;
        List<Predicate> predicates = new ArrayList<>();
        List<Order> orders = new ArrayList<>();
        Fetch followUp = root.fetch(Requirement_.FOLLOW_UP, JoinType.LEFT);
        From degree = (From) followUp.fetch("degree", JoinType.LEFT);
        From req = (From) root.fetch(Requirement_.REQUIREMENT_TYPE, JoinType.LEFT);
        From eval = (From) root.fetch(Requirement_.EVALUATION, JoinType.LEFT);
        Iterator iterator = request.getSort().iterator();
        while (iterator.hasNext()) {
            Sort.Order order = (Sort.Order) iterator.next();
            if ("requirementType.fullName".equals(order.getProperty())) {
                if (Sort.Direction.ASC.equals(order.getDirection())) {
                    orders.add(cb.asc(req.get("id")));
                } else if (Sort.Direction.DESC.equals(order.getDirection())) {
                    orders.add(cb.desc(req.get("id")));
                }
                iterator.remove();
            } else if ("followUp.degree.degree".equals(order.getProperty())) {
                if (Sort.Direction.ASC.equals(order.getDirection())) {
                    orders.add(cb.asc(degree.get("degree")));
                } else if (Sort.Direction.DESC.equals(order.getDirection())) {
                    orders.add(cb.desc(degree.get("degree")));
                }
                iterator.remove();
            }
        }

        for (DataTableColumn column : request.getColumns()) {
            if ("followUp.degree.degree".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(degree.get("degree"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("requirementType.fullName".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(req.get("fullName"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("workOrderCode".equals(column.getName())) {
                From wo = (From) root.fetch("workOrder", JoinType.LEFT);
//                wo.fetch(WorkOrder_.INVENTORY_ACTIVITY, JoinType.LEFT);
                wo.fetch(WorkOrder_.ESTIMATED_WORKING_TIME, JoinType.LEFT);
                wo.fetch(WorkOrder_.ACTUAL_WORKING_TIME, JoinType.LEFT);
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(wo.get("code"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("code".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(from.get("code"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("requestName".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(from.get("requestName"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("contactPhone".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(from.get("contactPhone"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("description".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(from.get("description"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("status".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.equal(root.<Integer>get("status"), Enum.valueOf(Requirement.RequirementStatus.class, column.getSearchText())));
                }
            }
            if ("evaluation.quality".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.and(cb.lessThanOrEqualTo(eval.<Integer>get("quality"), Long.valueOf(column.getSearchText()))));
                }
            }
            if ("evaluation.speed".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.and(cb.lessThanOrEqualTo(eval.<Integer>get("speed"), Long.valueOf(column.getSearchText()))));
                }
            }
            if ("evaluation.attitude".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.and(cb.lessThanOrEqualTo(eval.<Integer>get("attitude"), Long.valueOf(column.getSearchText()))));
                }
            }
            if ("evaluation.description".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(eval.get("description"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("createdDate".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    String[] strCols = column.getSearchText().split(SystemConst.STR_TILDE);
                    if (!StringUtils.isEmpty(strCols[0])) {
                        Date dateFrom = null;
                        try {
                            dateFrom = DateUtils.parseDateStrictly(strCols[0].trim(), SystemConst.DATE_YYYY_MM_DD_HYPHEN);
                        } catch (ParseException e) {
                            log.error("createdDate-0 {} parse error : {}",strCols[0].trim(),e.getMessage());
                        }
                        if (dateFrom != null) {
                            predicates.add(cb.greaterThanOrEqualTo(from.get("createdDate"), dateFrom));
                        }
                    }

                    if (strCols.length == 2 && !StringUtils.isEmpty(strCols[1])) {
                        Date dateTo = null;
                        try {
                            dateTo = DateUtils.parseDateStrictly(strCols[1].trim(), SystemConst.DATE_YYYY_MM_DD_HYPHEN);
                            dateTo = DateUtils.addDays(dateTo, 1);
                        } catch (ParseException e) {
                            log.error("createdDate-1 {} parse error : {}",strCols[1].trim(),e.getMessage());
                        }
                        if (dateTo != null) {
                            predicates.add(cb.lessThan(from.get("createdDate"), dateTo));
                        }
                    }
                }
            }
        }

        //搜索按钮
        List<Predicate> conPreL = buildConditionsSearch(request.getConditions(), root, cb);
        if (!CollectionUtils.isEmpty(conPreL)) {
            predicates.addAll(conPreL);
        }

        predicates.add(cb.equal(root.get("deleted"), Boolean.FALSE));

        //多项目支持
        predicates.add(cb.equal(root.get("project"), ProjectContext.getCurrentProject()));

        query.where(predicates.toArray(new Predicate[0]));
        query.distinct(true);
        query.select(root);// select entity
        orders.addAll(QueryUtils.toOrders(request.getSort(), root, cb));
        query.orderBy(orders);
        TypedQuery<Requirement> typedQuery = em.createQuery(query);
        if (!export) {
            typedQuery.setFirstResult(pageable.getOffset());// pageable
            typedQuery.setMaxResults(pageable.getPageSize());// pageable
        }
        List<Requirement> requirements = typedQuery.getResultList();
        List<RequirementColumnsDTO> dtos = new ArrayList<>();
        for (Requirement requirement : requirements) {
            RequirementColumnsDTO dto = new RequirementColumnsDTO();
            BeanUtils.copyProperties(requirement, dto);
            if (export) {
                dto.setRequirementStatus(requirement.getStatus().toString());
            }
            if (requirement.getWorkOrder() != null) {
                StringBuilder sb = new StringBuilder();
                for (WorkOrder workOrder : requirement.getWorkOrder()) {
                    sb.append(workOrder.getCode() + SystemConst.STR_CONNECT);
                }
                // remove last ” / “
                String code = sb.toString();
                if (!StringUtils.isEmpty(code)) {
                    code = code.substring(0, code.length() - 3);
                }
                dto.setWorkOrderCode(code);
            }
            dto.setWorkOrder(null);
            dtos.add(dto);
        }
        return dtos;
    }

    public int getRequirementCount(DataTableRequest request) {
        EntityManager em = entityManagerFactory.createEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();

        //Entity User retrieve
        CriteriaQuery<Long> query = cb.createQuery(Long.class);
        Root<Requirement> root = query.from(Requirement.class);
        From from = root;
        List<Predicate> predicates = new ArrayList<>();
        for (DataTableColumn column : request.getColumns()) {
            if ("followUp.degree.degree".equals(column.getName())) {
                From followUp = (From) root.join(Requirement_.FOLLOW_UP, JoinType.LEFT);
                From degree = (From) followUp.join("degree", JoinType.LEFT);
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(degree.get("degree"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("requirementType.fullName".equals(column.getName())) {
                From req = (From) root.join(Requirement_.REQUIREMENT_TYPE, JoinType.LEFT);
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(req.get("fullName"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("workOrderCode".equals(column.getName())) {
                From wo = (From) root.join("workOrder", JoinType.LEFT);
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(wo.get("code"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("code".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(from.get("code"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("requestName".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(from.get("requestName"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("contactPhone".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(from.get("contactPhone"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("description".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(from.get("description"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }
            if ("status".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.equal(root.<Integer>get("status"), Enum.valueOf(Requirement.RequirementStatus.class, column.getSearchText())));
                }
            }
            if ("evaluation.quality".equals(column.getName())) {
                From eval = (From) root.join(Requirement_.EVALUATION, JoinType.LEFT);
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.and(cb.lessThanOrEqualTo(eval.<Integer>get("quality"), Long.valueOf(column.getSearchText()))));
                }
            }
            if ("evaluation.speed".equals(column.getName())) {
                From eval = (From) root.join(Requirement_.EVALUATION, JoinType.LEFT);
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.and(cb.lessThanOrEqualTo(eval.<Integer>get("speed"), Long.valueOf(column.getSearchText()))));
                }
            }
            if ("evaluation.attitude".equals(column.getName())) {
                From eval = (From) root.join(Requirement_.EVALUATION, JoinType.LEFT);
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.and(cb.lessThanOrEqualTo(eval.<Integer>get("attitude"), Long.valueOf(column.getSearchText()))));
                }
            }
            if ("evaluation.description".equals(column.getName())) {
                From eval = (From) root.join(Requirement_.EVALUATION, JoinType.LEFT);
                if (!StringUtils.isEmpty(column.getSearchText())) {
                    predicates.add(cb.like(eval.get("description"), SystemConst.STR_PERCENT + column.getSearchText() + SystemConst.STR_PERCENT));
                }
            }

            if ("createdDate".equals(column.getName())) {
                if (!StringUtils.isEmpty(column.getSearchText())) {

                    Date[] dates = buildDateTimeTOSearch(column.getSearchText());
                    if (dates != null && dates[0] != null && dates[1] != null) {
                        predicates.add(cb.between(root.<Date>get("createdDate"), dates[0], dates[1]));
                    }
                }
            }
        }

        //搜索按钮
        List<Predicate> conPreL = buildConditionsSearch(request.getConditions(), root, cb);
        if (!CollectionUtils.isEmpty(conPreL)) {
            predicates.addAll(conPreL);
        }

        predicates.add(cb.equal(root.get("deleted"), Boolean.FALSE));
        predicates.add(cb.equal(root.get("project"), ProjectContext.getCurrentProject()));

        query.where(predicates.toArray(new Predicate[0]));
        query.groupBy(from.get("id"));
        query.select(cb.count(root));// select entity
        TypedQuery<Long> typedQuery = em.createQuery(query);
        return typedQuery.getResultList().size();
    }

    private List<Predicate> buildConditionsSearch(List<DataTableConditions> conditions, Root root, CriteriaBuilder cb) {
        List<Predicate> pL = new ArrayList<Predicate>();
        if (!CollectionUtils.isEmpty(conditions)) {
            for (DataTableConditions con : conditions) {
                if (con.getField().equals("status")) {
                    buildEnumSearchCondition(con.getValues(), RequirementStatus.class, "status", root, cb, pL);
                } else if (con.getField().equals("source")) {
                    buildEnumSearchCondition(con.getValues(), RequirementSource.class, "source", root, cb, pL);
                } else if (con.getField().equals("isLinkWo")) {
                    From wo = (From) root.join("workOrder", JoinType.LEFT);
                    String searchText = con.getValues().get(0);
                    if (searchText.equals("1")) {
                        pL.add(cb.isNotNull(wo.get("id")));
                    } else {
                        pL.add(cb.isNull(wo.get("id")));
                    }
                } else if (con.getField().equals("createdDate")) {
                    String searchText = con.getValues().get(0);
                    buildDateSearchCondition(searchText, "createdDate", root, cb, pL);
                } else if (con.getField().equals("completedDateTime")) {
                    String searchText = con.getValues().get(0);
                    buildDateSearchCondition(searchText, "completedDateTime", root, cb, pL);
                }
            }
        }
        return pL;
    }

    private void buildEnumSearchCondition(List<String> datas, Class<? extends Enum> enumClass, String field, Root root, CriteriaBuilder cb, List<Predicate> pL) {
        if (!CollectionUtils.isEmpty(datas)) {
            List<Object> values = new ArrayList<>();
            for (String data : datas) {
                values.add(Enum.valueOf(enumClass, data.toUpperCase()));
            }
            pL.add(root.get(field).in(values));
        }
    }

    private void buildDateSearchCondition(String searchText, String field, Root root, CriteriaBuilder cb, List<Predicate> pL) {
        try {
            if (!StringUtils.isEmpty(StringUtils.trim(searchText))) {
                Calendar startDate = Calendar.getInstance();
                Calendar endDate = Calendar.getInstance();
                SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.FORMAT_PATTERN_DATE);
                if (searchText.indexOf(DateUtil.DOUBLE_SEPARATE) >= 0) {
                    String[] dates = searchText.split(DateUtil.DOUBLE_SEPARATE);
                    if (!StringUtils.isEmpty(StringUtils.trim(dates[0])) && !StringUtils.isEmpty(StringUtils.trim(dates[1]))) {
                        startDate.setTime(sdf.parse(StringUtils.trim(dates[0])));
                        endDate.setTime(sdf.parse(StringUtils.trim(dates[1])));
                        pL.add(cb.between(root.<Date>get(field), DateUtil.buildDateOnFirstSecond(startDate.getTime()), DateUtil.buildDateOnLastSecond(endDate.getTime())));
                    }
                }
            }
        } catch (ParseException e) {
            log.error("buildDateSearchCondition parse date error : {}",e.getMessage());
        }
    }

    @Override
    @Deprecated
    public List<WorkOrderSearchDTO> findColumnRequirement(final DataTableRequest request, Pageable page, String status) {
        List<WorkOrderSearchDTO> workOrderSearchDTOs = new ArrayList<>();
        List<Requirement> requirements = requirementRepository.findAll();
        for (Requirement re : requirements) {
            WorkOrderSearchDTO woDto = new WorkOrderSearchDTO();
            woDto.setReCode(re.getCode());
            woDto.setContent(re.getDescription());
            FollowUp followUp = requirementRepository.findFollowUpById(re.getId());
            if (followUp != null) {
                SatisfactionDegree degree = followUpRepository.findSatisfactionDegreeById(followUp.getId());
                if (degree != null) {
                    woDto.setDegree(degree.getDegree());
                }
            }
            woDto.setStatus(re.getStatus().toString());
            woDto.setLinkCode(re.getContactPhone());
            woDto.setRequestName(re.getRequestName());
            RequirementType requirementType = requirementRepository.findRequirementById(re.getId());
            if (requirementType != null) {
                woDto.setRequirementType(requirementType.getName());
            }
            woDto.setTime(re.getCreatedDate());
            List<WorkOrder> workOrders = workOrderRepository.findWoByRequirementId(re.getId());
            String woCode = "";
            if (workOrders != null) {
                for (WorkOrder workOrder : workOrders) {
                    woCode += workOrder.getCode() + "/";
                }
            }
            woDto.setWoCode(woCode);
            workOrderSearchDTOs.add(woDto);
        }
        return workOrderSearchDTOs;
    }

    /**
     * change requirement status
     *
     * @param requirement
     * @param status
     */
    @Override
    public Requirement changeStatusTo(Requirement requirement, Requirement.RequirementStatus status) {
        if (requirement == null || requirement.getId() == null || status == null) {
            return null;
        }

        Requirement requirementInDB = requirementRepository.findOne(requirement.getId());
        requirementInDB.setStatus(status);
        requirementInDB.setCompletedDateTime(new Date());
        return requirementRepository.save(requirementInDB);
    }

    /**
     * 获取需求状态数量
     * @return
     */
    @Override
    public Map<String, Long> getStatusNums() {
        Long proId = ProjectContext.getCurrentProject();
        StringBuilder sb = new StringBuilder(" SELECT status, num FROM ( ");
        sb.append(" SELECT 'CREATE' AS status, COUNT(req_id) AS num FROM requirement WHERE status = 0 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" UNION ALL ");
        sb.append(" SELECT 'PROCESS' AS status, COUNT(req_id) AS num FROM requirement WHERE status = 1 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" UNION ALL ");
        sb.append(" SELECT 'FINISH' AS status, COUNT(req_id) AS num FROM requirement WHERE status = 2 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" UNION ALL ");
        sb.append(" SELECT 'FOLLOWUP' AS status, COUNT(req_id) AS num FROM requirement WHERE status = 3 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" UNION ALL ");
        sb.append(" SELECT 'CANCEL' AS status, COUNT(req_id) AS num FROM requirement WHERE status = 4 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" ) a");
        Map<String, Long> statusMap = (Map<String, Long>) jdbcTemplate.query(sb.toString(), new ResultSetExtractor() {
            public Map<String, Long> extractData(ResultSet rs) throws SQLException, DataAccessException {
                Map<String, Long> statusMap = new HashMap<String, Long>();
                statusMap.put(Requirement.RequirementStatus.CREATE.toString(), 0L);
                statusMap.put(Requirement.RequirementStatus.PROCESS.toString(), 0L);
                statusMap.put(Requirement.RequirementStatus.FINISH.toString(), 0L);
                statusMap.put(Requirement.RequirementStatus.FOLLOWUP.toString(), 0L);
                statusMap.put(Requirement.RequirementStatus.CANCEL.toString(), 0L);
                Long total = 0L;
                while (rs.next()) {
                    total += rs.getLong("num");
                    statusMap.put(rs.getString("status"), rs.getLong("num"));
                }
                statusMap.put("status_all", total);
                return statusMap;
            }
        });
        return statusMap;
    }

    @Override
    public Map<String, Long> getSourceNums() {
        Long proId = ProjectContext.getCurrentProject();
        StringBuilder sb = new StringBuilder(" SELECT source, num FROM ( ");
        sb.append(" SELECT 'SITE' AS source, COUNT(req_id) AS num FROM requirement WHERE source = 0 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" UNION ALL ");
        sb.append(" SELECT 'PHONE' AS source, COUNT(req_id) AS num FROM requirement WHERE source = 1 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" UNION ALL ");
        sb.append(" SELECT 'WECHAT' AS source, COUNT(req_id) AS num FROM requirement WHERE source = 2 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" UNION ALL ");
        sb.append(" SELECT 'EMAIL' AS source, COUNT(req_id) AS num FROM requirement WHERE source = 3 AND deleted = 0 AND proj_id = ").append(proId);
        sb.append(" ) a");
        Map<String, Long> sourceMap = (Map<String, Long>) jdbcTemplate.query(sb.toString(), new ResultSetExtractor() {
            public Map<String, Long> extractData(ResultSet rs) throws SQLException, DataAccessException {
                Map<String, Long> sourceMap = new HashMap<String, Long>();
                sourceMap.put(Requirement.RequirementSource.SITE.toString(), 0L);
                sourceMap.put(Requirement.RequirementSource.PHONE.toString(), 0L);
                sourceMap.put(Requirement.RequirementSource.WECHAT.toString(), 0L);
                sourceMap.put(Requirement.RequirementSource.EMAIL.toString(), 0L);
                Long total = 0L;
                while (rs.next()) {
                    total += rs.getLong("num");
                    sourceMap.put(rs.getString("source"), rs.getLong("num"));
                }
                sourceMap.put("source_all", total);
                return sourceMap;
            }
        });
        return sourceMap;
    }

    private Date[] buildDateTimeTOSearch(String dateString) {
        Date[] result = new Date[2];
        String[] createTime = dateString.split("~");
        Date createTimeStart = null;
        Date createTimeEnd = null;
        if (createTime.length == 1) {
            createTimeStart = DateUtil.formatDate(createTime[0]);
            if (createTimeStart != null) {
                createTimeStart = DateUtil.buildDateOnFirstSecond(createTimeStart);
                createTimeEnd = DateUtil.buildDateOnLastSecond(createTimeStart);
            }
        } else if (createTime.length == 2) {
            createTimeStart = DateUtil.formatDate(createTime[0]);
            createTimeEnd = DateUtil.formatDate(createTime[1]);
            if (createTimeStart != null) {
                createTimeStart = DateUtil.buildDateOnFirstSecond(createTimeStart);
            }
            if (createTimeEnd != null) {
                createTimeEnd = DateUtil.buildDateOnLastSecond(createTimeEnd);
            }
        }
        result[0] = createTimeStart;
        result[1] = createTimeEnd;
        return result;
    }

}
