package cn.facilityone.shang.servicecenter.service002.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.configuration.properties.CompanyProperties;
import cn.facilityone.shang.common.configuration.properties.SystemProperties;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.epayment.epayment000.common.PayMenuConstants;
import cn.facilityone.shang.servicecenter.service001.service.RequirementCenterService;
import cn.facilityone.shang.servicecenter.service002.dto.RequirementColumnsDTO;
import cn.facilityone.shang.servicecenter.service002.service.RequirementService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.transfer.core.Exporter;
import cn.facilityone.xia.transfer.core.data.DTO;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Pageable;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 需求查询Resource
 *
 * @version 1.0
 *          Created by renfr.wang on 2015/5/29.
 */
@Path("/service002")
public class RequirementResource {
    private static final String TEMPLATE_SERVICE_PATH = "/business/service/service002-query.ftl";

    @Autowired
    RequirementService requirementService;
    @Autowired
    RequirementCenterService workOrderService;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private CompanyProperties companyProperties;
    @Autowired
    @Qualifier("xlsxExporter")
    private Exporter exporter;

    @Autowired
    private SystemProperties systemProperties;

    /**
     * 初始化
     *
     * @return
     */
    @GET
    @Template(name = TEMPLATE_SERVICE_PATH)
    public Map<String, Object> init() {
        Map<String, Object> map = new HashMap<String, Object>();
        boolean isShowPayorder = systemProperties.isIsNotShowMenu(PayMenuConstants.MENU_ID_PAY);
        map.put("isShowPayorder", isShowPayorder?"1":"0");
        return map;
    }

    /**
     * 获取需求表格数据
     *
     * @param request
     * @return
     */
    @POST
    @Path("requirement/table/{queryString}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findWorkOrder(final DataTableRequest request, @PathParam("queryString") String status) {
        int pageNumber = request.getPageNumber();
        Pageable pageable =
                new DataTableRequest(pageNumber, request.getOffset(), request.getPageSize(),
                        dataTableService.buildColumnSort(request.getColumns()),
                        request.getDraw());
        //Page<Requirement> RequirementPage = requirementService.findColumnWorkOrders(request, pageable,status);

        return new DataTableResponse(requirementService.findRequirements(request, pageable, false), requirementService.getRequirementCount(request), pageNumber, request.getDraw());
    }

    /**
     * 获取需求人信息
     */
    @GET
    @Path("requirement/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result CustomerInfo(@PathParam("Id") Long id) {
        return new Result(workOrderService.findWorkOrderInfo(id));
    }

    /**
     * 导出
     */
    @POST
    @Path("requirement/export")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response exportRequirement(DataTableRequest request) {
        List<RequirementColumnsDTO> requirementColumnsDTOs = requirementService.findRequirements(request, null, true);

        LinkedHashMap<String, String> title = new LinkedHashMap<String, String>();
        title.put("code", XiaMesssageResource.getMessage("Requirement.code"));
        title.put("requestName", XiaMesssageResource.getMessage("Requirement.requestName"));
        title.put("requirementType.fullName", XiaMesssageResource.getMessage("Requirement.Type"));
        title.put("description", XiaMesssageResource.getMessage("Requirement.Content"));
        title.put("requirementStatus", XiaMesssageResource.getMessage("Requirement.Status"));
        title.put("evaluation.quality", XiaMesssageResource.getMessage("Evaluation.quality"));
        title.put("evaluation.speed", XiaMesssageResource.getMessage("Evaluation.speed"));
        title.put("evaluation.attitude", XiaMesssageResource.getMessage("Evaluation.attitude"));
        title.put("evaluation.description", XiaMesssageResource.getMessage("Evaluation.description"));
        title.put("workOrderCode", XiaMesssageResource.getMessage("Requirement.WCode"));
        title.put("createdDate", XiaMesssageResource.getMessage("Requirement.submitTime"));

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("companyName", companyProperties.getName());
        DTO dto = new DTO(map);
        dto.addTitleAndData(title, requirementColumnsDTOs);

        ByteArrayOutputStream out = (ByteArrayOutputStream) exporter
                .export(dto);
        return Response.ok(
                new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.success.export", null, ""),
                        FileUtil.exportFile(out, FileUtil.FILE_TYPE_EXCELX))).build();
    }

    /**
     * 状态数量图标和来源数量图标
     * @return
     */
    @GET
    @Path("statusNums")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getStatusNums() {
        // 状态数量
        Map<String, Long> statusMap = requirementService.getStatusNums();
        // 来源数量
        Map<String, Long> sourceMap = requirementService.getSourceNums();
        Map<String, Long> resultMap = new HashMap<>();
        resultMap.putAll(statusMap);
        resultMap.putAll(sourceMap);
        return new Result(resultMap);
    }
}
