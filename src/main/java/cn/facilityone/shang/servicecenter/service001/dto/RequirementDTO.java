package cn.facilityone.shang.servicecenter.service001.dto;

import cn.facilityone.shang.entity.common.Attachment;
import cn.facilityone.shang.entity.common.Picture;
import cn.facilityone.shang.entity.servercenter.Requirement;

import java.util.List;

/**
 * @version 1.0
 *          Created by renfr.wang on 2015/6/18.
 */
public class RequirementDTO {

    private String Code;

    private String description;

    private String dealContent;

    private String dealType;

    private Long requirementTypeId;

    private boolean fromEmail = false;

    private boolean stillSubmitWithoutLikeReq = true;

    public boolean isFromEmail() {
        return fromEmail;
    }

    public void setFromEmail(boolean fromEmail) {
        this.fromEmail = fromEmail;
    }

    public Long getRequirementTypeId() {
        return requirementTypeId;
    }

    public void setRequirementTypeId(Long requirementTypeId) {
        this.requirementTypeId = requirementTypeId;
    }

    private List<Picture> pictures;

    private Requirement requirement;

    public Requirement getRequirement() {
        return requirement;
    }

    public void setRequirement(Requirement requirement) {
        this.requirement = requirement;
    }

    public String getCode() {
        return Code;
    }

    public void setCode(String code) {
        Code = code;
    }

    public String getDealType() {
        return dealType;
    }

    public void setDealType(String dealType) {
        this.dealType = dealType;
    }

    public List<Picture> getPictures() {
        return pictures;
    }

    public void setPictures(List<Picture> pictures) {
        this.pictures = pictures;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    private List<Attachment> attachments;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDealContent() {
        return dealContent;
    }

    public void setDealContent(String dealContent) {
        this.dealContent = dealContent;
    }

    public Boolean getStillSubmitWithoutLikeReq() {
        return stillSubmitWithoutLikeReq;
    }

    public void setStillSubmitWithoutLikeReq(Boolean stillSubmitWithoutLikeReq) {
        this.stillSubmitWithoutLikeReq = stillSubmitWithoutLikeReq;
    }
    
    
}
