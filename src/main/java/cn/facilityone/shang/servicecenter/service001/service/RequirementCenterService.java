package cn.facilityone.shang.servicecenter.service001.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.servercenter.Evaluation;
import cn.facilityone.shang.entity.servercenter.FollowUp;
import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.servercenter.SatisfactionDegree;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.servicecenter.common.dto.ServiceTypesDTO;
import cn.facilityone.shang.servicecenter.service001.dto.*;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 创建工单接口
 *
 * @version 1.0
 *          Created by renfr.wang
 *          2015/5/13.
 */
public interface RequirementCenterService {
    /**
     * 需求数据
     *
     * @return
     */
    List<WorkOrder> findRequirementInfo();

    /**
     * 搜索需求数据
     */
    List<WorkOrder> searchRequirementInfo(String queryString);

    /**
     * 工单信息
     */
    WorkOrderDTO findWorkOrderInfo(Long id);

    /**
     * 保存需求信息
     */
    String updateWorkOrder(Long id, RequirementDTO wo);

    /**
     * 完成需求信息
     */
    String finishWorkOrder(Long id, WorkOrder wo);

    void sendMessageToRequester(Requirement requirement);

    /**
     * 状态查询需求信息
     */
    List<WorkOrder> searchStatusInfo(String status);

    /**
     * 获取满意度数据
     */
    FollowUp findFollowUpInfo(Long id);

    /**
     * 获取新满意度
     */
    Evaluation findEvaluationInfo(Long id);

    /**
     * 更新满意度
     */
    String updateFollowUp(Long id, FollowUp fw);

    /**
     * 添加满意度
     */
    String CreateFollowUp(Long id, FollowUp fw);

    /**
     * 添加评价
     */
    String addEvaluation(Evaluation evaluation);

    /**
     * 新建需求
     */
    List<Requirement> checkLikeReq(RequirementDTO dto);

    /**
     * 新建需求
     */
    Requirement addRequirement(RequirementDTO dto);
    
    /**
     * 审核需求
     */
    Requirement checkRequirement(RequirementDTO dto);

    /**
     * 获取所有回访
     * @return
     */
    public List<SatisfactionDegree> findDegreeAll();

    /**
     * 工单创建获取优先级
     *
     * @param dto
     * @return
     */
    public List<PrioritysByProcessDTO> findPrioritysForWoCreate(WorkOrderDTO dto);

    /**
     * 获得工单
     *
     * @param dto
     * @return
     */
    public WorkOrder getWorkOrder(WorkOrderDTO dto);

    /**
     * 工单保存需求
     */
    public Requirement saveWorkOrderRequirement(WorkOrderDTO dto);

    /**
     * 获得工单故障设备
     *
     * @param dto
     * @param isAssociated
     * @return
     */
    public List<Equipment> getWorkOrderEquipment(WorkOrderDTO dto,
                                                 Boolean isAssociated);

    /**
     * 保存工单中故障设备
     *
     * @param order
     * @param dto
     * @param serviceTypesDto
     * @param operateUserName
     */
    public void saveWorkOrderEquipment(WorkOrder order, WorkOrderDTO dto,
                                       ServiceTypesDTO serviceTypesDto, String operateUserName);

    /**
     * 查询一个工单，包含懒加载数据
     *
     * @param id
     */
    public WorkOrder findWorkOrderByIDWithAllInfo(Long id);

    WorkOrder findWorkOrderByID(Long workOrderId);

    Object getWorkOrderHistory(Long workOrderId);

    Requirement findRequirementByWorkOrderId(Long workOrderId);

    WorkOrder createWorkOrder(WorkOrder wo, String operateUserName);

    WorkOrder findEntireOne(Long workOrderId);

    WorkOrder findEntireOneWithOutCollectionPart(Long workOrderId);


    /**
     * 获取需求列表
     *
     * @param dto
     * @return
     */
    Page<Requirement> findRequirements(RequirementSearchDto dto);

    /**
     * 根据设备ID 获得设备信息
     */

    public List<Equipment> findEquipmentById(Long id);

    /**
     *获取提需求者信息
     */
    List<CustomerDTO> findDemandInfo(DataTableRequest request);

    /**
     * 工单中信息从需求获取
     */
    WorkOrder findRequirementToSaveWorkOrder(WorkOrder workOrder,Requirement requirement);

    /***
     * 相似工单
     */
    public List<WorkOrder> checkLikeRequirementWorkOrder(WorkOrderDTO dto);

    /***
     * 创建工单
     */
    public List<WorkOrder> createRequirementWorkOrder(WorkOrderDTO dto);
    
    boolean validateForFinish(Long id);

    /**
     * 微信需求审核
     */
    Requirement requirementCheck(RequirementCheckDTO dto);
}
