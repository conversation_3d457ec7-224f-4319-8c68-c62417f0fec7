package cn.facilityone.shang.servicecenter.service001.dto;


import cn.facilityone.shang.entity.common.*;
import cn.facilityone.shang.entity.organize.*;
import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.workorder.*;
import cn.facilityone.shang.servicecenter.common.dto.ServiceTypesDTO;

import java.util.Date;
import java.util.List;

/**
 * WorkOrderDTO
 *
 * @version 1.0
 *          Created by renfr.wang on 2015/5/15.
 */
public class WorkOrderDTO {
    private Long id;

    //工单执行人id
    private Long wolId;

    private Date actualArrivalDateTime;

    private Date actualCompletionDateTime;

    private Double actualWorkingTime;

    private Date completedDateTime;

    private Date completionEscalationTime;

    private String description;

    private String email;

    private Date estimatedArrivalDateTime;

    private Date estimatedCompletionDateTime;

    private Double estimatedWorkingTime;

    private Boolean isComEscalation;

    private Boolean isRespEscalation;
    
    private boolean stillSubmitWithoutLikeWo = true;

    private String phone;

    private String procInstID;

    private String requestor;

    private Long requestId;

    private Date responseEscalationTime;

    private List<String> source;

    private String status;

    private String type;

    private Employee dispatcher;

    private WorkTeam workTeam;

    private Employee supervisor;

    private Site site;

    private Building building;

    private Floor floor;

    private Room room;


    private Priority priority;

    private Organization organization;


    private ServiceType serviceType;

    private WorkOrderProcess workOrderProcess;

    private List<WorkOrderProcess> woProcesss;

    private WorkOrderLaborer laborers;

    private Date createdDate;

    private String createdBy;

    private String docIds;

    private Boolean isValidation;

    private String validationComment;

    private String validationResult;
    //工单类型
    private String workOrderType;
    
    public String getWorkOrderType() {
        return workOrderType;
    }

    public void setWorkOrderType(String workOrderType) {
        this.workOrderType = workOrderType;
    }

    // 执行人id
    private Long laborerId;


    public Long getLaborerId() {
        return laborerId;
    }

    public void setLaborerId(Long laborerId) {
        this.laborerId = laborerId;
    }

    //需求编号
    private String reCode;

    //工单编号

    private String newWorkOrderCode;
    //需求
    private Requirement requirement;

    private RequirementDTO requirementDTO;

    private Long RequirementTypeId;

    private String RequirementDescription;

    //工单详情
    private List<WorkOrder> workOrderList;

    private List<Attachment> attachments;

    private List<Picture> pictures;

    private List<ShortVideoMedia> shortVideoMedias;

    private List<VideoMedia> videoMedias;

    private List<VoiceMedia> voiceMedias;

    private String wFlag;

    private Long resultId;

    private boolean fromEmail = false;
    private boolean fromPatrol = false;

    public boolean isFromPatrol() {
        return fromPatrol;
    }

    public void setFromPatrol(boolean fromPatrol) {
        this.fromPatrol = fromPatrol;
    }

    public List<VoiceMedia> getVoiceMedias() {
        return voiceMedias;
    }

    public void setVoiceMedias(List<VoiceMedia> voiceMedias) {
        this.voiceMedias = voiceMedias;
    }

    public List<VideoMedia> getVideoMedias() {
        return videoMedias;
    }

    public void setVideoMedias(List<VideoMedia> videoMedias) {
        this.videoMedias = videoMedias;
    }

    public List<ShortVideoMedia> getShortVideoMedias() {
        return shortVideoMedias;
    }

    public void setShortVideoMedias(List<ShortVideoMedia> shortVideoMedias) {
        this.shortVideoMedias = shortVideoMedias;
    }

    public String getwFlag() {
        return wFlag;
    }

    public void setwFlag(String wFlag) {
        this.wFlag = wFlag;
    }

    public List<Picture> getPictures() {
        return pictures;
    }

    public void setPictures(List<Picture> pictures) {
        this.pictures = pictures;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public List<WorkOrder> getWorkOrderList() {
        return workOrderList;
    }

    public void setWorkOrderList(List<WorkOrder> workOrderList) {
        this.workOrderList = workOrderList;
    }

    public String getRequirementDescription() {
        return RequirementDescription;
    }

    public void setRequirementDescription(String requirementDescription) {
        RequirementDescription = requirementDescription;
    }

    public Long getRequirementTypeId() {
        return RequirementTypeId;
    }

    public void setRequirementTypeId(Long requirementTypeId) {
        RequirementTypeId = requirementTypeId;
    }

    public RequirementDTO getRequirementDTO() {
        return requirementDTO;
    }

    public void setRequirementDTO(RequirementDTO requirementDTO) {
        this.requirementDTO = requirementDTO;
    }

    public Requirement getRequirement() {
        return requirement;
    }

    public String getReCode() {
        return reCode;
    }

    public void setReCode(String reCode) {
        this.reCode = reCode;
    }

    public void setRequirement(Requirement requirement) {
        this.requirement = requirement;
    }

    public List<ServiceTypesDTO> getServiceTypes() {
        return serviceTypes;
    }

    public void setServiceTypes(List<ServiceTypesDTO> serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    private List<ServiceTypesDTO> serviceTypes;

    public String getNewWorkOrderCode() {
        return newWorkOrderCode;
    }

    public void setNewWorkOrderCode(String newWorkOrderCode) {
        this.newWorkOrderCode = newWorkOrderCode;
    }

    private List<WorkOrderEquipment> equipments;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getActualArrivalDateTime() {
        return actualArrivalDateTime;
    }

    public void setActualArrivalDateTime(Date actualArrivalDateTime) {
        this.actualArrivalDateTime = actualArrivalDateTime;
    }

    public Date getActualCompletionDateTime() {
        return actualCompletionDateTime;
    }

    public void setActualCompletionDateTime(Date actualCompletionDateTime) {
        this.actualCompletionDateTime = actualCompletionDateTime;
    }

    public Double getActualWorkingTime() {
        return actualWorkingTime;
    }

    public void setActualWorkingTime(Double actualWorkingTime) {
        this.actualWorkingTime = actualWorkingTime;
    }

    public Date getCompletedDateTime() {
        return completedDateTime;
    }

    public void setCompletedDateTime(Date completedDateTime) {
        this.completedDateTime = completedDateTime;
    }

    public Date getCompletionEscalationTime() {
        return completionEscalationTime;
    }

    public void setCompletionEscalationTime(Date completionEscalationTime) {
        this.completionEscalationTime = completionEscalationTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getEstimatedArrivalDateTime() {
        return estimatedArrivalDateTime;
    }

    public void setEstimatedArrivalDateTime(Date estimatedArrivalDateTime) {
        this.estimatedArrivalDateTime = estimatedArrivalDateTime;
    }

    public Date getEstimatedCompletionDateTime() {
        return estimatedCompletionDateTime;
    }

    public void setEstimatedCompletionDateTime(Date estimatedCompletionDateTime) {
        this.estimatedCompletionDateTime = estimatedCompletionDateTime;
    }

    public Double getEstimatedWorkingTime() {
        return estimatedWorkingTime;
    }

    public void setEstimatedWorkingTime(Double estimatedWorkingTime) {
        this.estimatedWorkingTime = estimatedWorkingTime;
    }

    public Boolean getIsComEscalation() {
        return isComEscalation;
    }

    public void setIsComEscalation(Boolean isComEscalation) {
        this.isComEscalation = isComEscalation;
    }

    public Boolean getIsRespEscalation() {
        return isRespEscalation;
    }

    public void setIsRespEscalation(Boolean isRespEscalation) {
        this.isRespEscalation = isRespEscalation;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProcInstID() {
        return procInstID;
    }

    public void setProcInstID(String procInstID) {
        this.procInstID = procInstID;
    }

    public String getRequestor() {
        return requestor;
    }

    public void setRequestor(String requestor) {
        this.requestor = requestor;
    }

    public Date getResponseEscalationTime() {
        return responseEscalationTime;
    }

    public void setResponseEscalationTime(Date responseEscalationTime) {
        this.responseEscalationTime = responseEscalationTime;
    }

    public List<String> getSource() {
        return source;
    }

    public void setSource(List<String> source) {
        this.source = source;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Employee getDispatcher() {
        return dispatcher;
    }

    public void setDispatcher(Employee dispatcher) {
        this.dispatcher = dispatcher;
    }

    public WorkTeam getWorkTeam() {
        return workTeam;
    }

    public void setWorkTeam(WorkTeam workTeam) {
        this.workTeam = workTeam;
    }

    public Employee getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(Employee supervisor) {
        this.supervisor = supervisor;
    }

    public Site getSite() {
        return site;
    }

    public void setSite(Site site) {
        this.site = site;
    }

    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public Floor getFloor() {
        return floor;
    }

    public void setFloor(Floor floor) {
        this.floor = floor;
    }

    public Room getRoom() {
        return room;
    }

    public void setRoom(Room room) {
        this.room = room;
    }

    public Priority getPriority() {
        return priority;
    }

    public void setPriority(Priority priority) {
        this.priority = priority;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public ServiceType getServiceType() {
        return serviceType;
    }

    public void setServiceType(ServiceType serviceType) {
        this.serviceType = serviceType;
    }

    public WorkOrderProcess getWorkOrderProcess() {
        return workOrderProcess;
    }

    public void setWorkOrderProcess(WorkOrderProcess workOrderProcess) {
        this.workOrderProcess = workOrderProcess;
    }

    public WorkOrderLaborer getLaborers() {
        return laborers;
    }

    public void setLaborers(WorkOrderLaborer laborers) {
        this.laborers = laborers;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getDocIds() {
        return docIds;
    }

    public void setDocIds(String docIds) {
        this.docIds = docIds;
    }

    public Boolean getIsValidation() {
        return isValidation;
    }

    public void setIsValidation(Boolean isValidation) {
        this.isValidation = isValidation;
    }

    public String getValidationComment() {
        return validationComment;
    }

    public void setValidationComment(String validationComment) {
        this.validationComment = validationComment;
    }

    public String getValidationResult() {
        return validationResult;
    }

    public void setValidationResult(String validationResult) {
        this.validationResult = validationResult;
    }

    public List<WorkOrderEquipment> getEquipments() {
        return equipments;
    }

    public void setEquipments(List<WorkOrderEquipment> equipments) {
        this.equipments = equipments;
    }

    public List<WorkOrderProcess> getWoProcesss() {
        return woProcesss;
    }

    public void setWoProcesss(List<WorkOrderProcess> woProcesss) {
        this.woProcesss = woProcesss;
    }

    public Long getWolId() {
        return wolId;
    }

    public void setWolId(Long wolId) {
        this.wolId = wolId;
    }

    public Long getResultId() {
        return resultId;
    }

    public void setResultId(Long resultId) {
        this.resultId = resultId;
    }

    public boolean isStillSubmitWithoutLikeWo() {
        return stillSubmitWithoutLikeWo;
    }

    public void setStillSubmitWithoutLikeWo(boolean stillSubmitWithoutLikeWo) {
        this.stillSubmitWithoutLikeWo = stillSubmitWithoutLikeWo;
    }

    public boolean isFromEmail() {
        return fromEmail;
    }

    public void setFromEmail(boolean fromEmail) {
        this.fromEmail = fromEmail;
    }

    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }
}
