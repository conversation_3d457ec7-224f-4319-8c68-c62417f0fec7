package cn.facilityone.shang.servicecenter.service001.resource;


import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.configuration.properties.SystemProperties;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.common.util.RequirementCode;
import cn.facilityone.shang.entity.servercenter.Evaluation;
import cn.facilityone.shang.entity.servercenter.FollowUp;
import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.epayment.epayment000.common.PayMenuConstants;
import cn.facilityone.shang.servicecenter.service001.dto.*;
import cn.facilityone.shang.servicecenter.service001.service.RequirementCenterService;
import cn.facilityone.shang.workorder.common.service.WorkOrderEquipmentService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;

import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务中心
 *
 * @version 1.0
 *          Created by renfr.wang
 *          2015/5/13.
 */
@Path("/service001")
public class RequirementCenterResource {

    private static final String TEMPLATE_SERVICE_PATH = "/business/service/service001-servicecenter.ftl";

    @Autowired
    RequirementCenterService requirementCenterService;
    @Autowired
    WorkOrderRepository workOrderRepository;
    @Autowired
    WorkOrderEquipmentService workOrderEquipmentService;
    @Autowired
    private UploadFileService uploadFileService;
    @Autowired
    private RequirementCode requirementCode;
    @Autowired
    private SystemProperties systemProperties;

    /**
     * 初始化
     *
     * @return
     */
    @GET
    @Template(name = TEMPLATE_SERVICE_PATH)
    public Map<String, Object> init() {
        Map<String, Object> map = new HashMap<String, Object>();
        boolean isShowPayorder = systemProperties.isIsNotShowMenu(PayMenuConstants.MENU_ID_PAY);
        map.put("isShowPayorder", isShowPayorder?"1":"0");
        return map;
    }

    /**
     * 获取需求信息
     *
     * @return
     * @throws Exception
     */
    @Deprecated  // Andy
    @GET
    @Path("requirement")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findRequirementInfo() {
        return new Result(requirementCenterService.findRequirementInfo());
    }

    /**
     * 获取需求信息
     *
     * @return
     * @throws Exception
     */
    @POST
    @Path("requirement/search")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findRequirements(RequirementSearchDto dto) {
        Page<Requirement> res = requirementCenterService.findRequirements(dto);
        DataTableResponse dtr = new DataTableResponse();
        if (res != null) {
            dtr.setData(res.getContent());
            Long total = res.getTotalElements();
            dtr.setTotal(total.intValue());
            dtr.setPageNumber(res.getNumber());
        }
        return dtr;
    }

    /**
     * 搜索需求信息
     */
    @Deprecated  // Andy
    @GET
    @Path("requirement/search/{queryString}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result searchRequirementInfo(@PathParam("queryString") String queryString) {
        return new Result(requirementCenterService.searchRequirementInfo(queryString));
    }

    /**
     * 获取需求信息
     */
    @GET
    @Path("requirement/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findWorkOrderInfo(@PathParam("Id") Long id) {
        return new Result(requirementCenterService.findWorkOrderInfo(id));
    }

    /**
     * 保存需求信息
     */
    @PUT
    @Path("requirement/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result updateWorkOrder(@PathParam("Id") Long id, RequirementDTO wo) {
        return new Result(requirementCenterService.updateWorkOrder(id, wo));

    }

    /**
     * 保存需求信息
     */
    @PUT
    @Path("requirement/finish/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result finishWorkOrder(@PathParam("Id") Long id, WorkOrder wo) {
//        boolean validationPass = requirementCenterService.validateForFinish(id);
//        if (!validationPass) {
//            Result result = new Result();
//            result.put("fail", true);
//            return result;
//        }
        return new Result(requirementCenterService.finishWorkOrder(id, wo));

    }
    /**
     * 查询工单是否结束
     */

    @PUT
    @Path("requirement/searchfinish/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result searchfinishWorkOroder(@PathParam("Id") Long id) {
        boolean validationPass = requirementCenterService.validateForFinish(id);
        if (!validationPass) {
            Result result = new Result();
            result.put("fail", true);
            return result;
        }
        return new Result();

    }



    /**
     * 状态查询需求信息
     */
    @Deprecated // Andy
    @GET
    @Path("requirement/status/{queryString}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result searchStatusInfo(@PathParam("queryString") String status) {
        return new Result(requirementCenterService.searchStatusInfo(status));
    }


    /**
     * 获取对应满意度数据
     */
    @GET
    @Path("requirement/satisfaction/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result SatisfactionInfo(@PathParam("Id") Long id) {
        return new Result(requirementCenterService.findFollowUpInfo(id));

    }

    /**
     * 获取回访数据
     */
    @GET
    @Path("requirement/satisfaction")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findDegreeAll() {
        return new Result(requirementCenterService.findDegreeAll());

    }

    /**
     * 更新满意度
     */
    @PUT
    @Path("requirement/satisfaction/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result updateSatisfaction(@PathParam("Id") Long id, FollowUp fw) {
        return new Result(requirementCenterService.updateFollowUp(id, fw));

    }

    /**
     * 添加满意度
     */
    @POST
    @Path("requirement/satisfaction/{Id}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result CreateSatisfaction(@PathParam("Id") Long id, FollowUp fw) {
        return new Result(requirementCenterService.CreateFollowUp(id, fw));

    }

    /**
     * 添加评价
     * @param evaluation
     * @return
     */
    @POST
    @Path("requirement/evaluation")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result createEvaluation(
            Evaluation evaluation
    ) {
        return new Result(requirementCenterService.addEvaluation(evaluation));

    }

    /**
     * 获取需求编号
     */
    @GET
    @Path("requirement/code")
    @Produces(MediaType.APPLICATION_JSON)
    public Result NewRequirementCode() {
        return new Result(requirementCode.getCode());
    }

    /**
     * 新建需求
     */
    @POST
    @Path("requirement")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result addRequirement(RequirementDTO dto) {
        List<Requirement> re = null;
        //相似检测
        if (!dto.getStillSubmitWithoutLikeReq()) {
            re = requirementCenterService.checkLikeReq(dto);
        }
        //仍然提交，或者没有检测到
        if (dto.getStillSubmitWithoutLikeReq() || re == null || re.size() == 0) {
            if(dto.getRequirement().getId()!=null){
                //审核微信需求
                requirementCenterService.checkRequirement(dto);
            }else{
                requirementCenterService.addRequirement(dto);
            }
        }

        Result res = null;
        if (re == null || re.size() == 0) {
            res = new Result(XiaMesssageResource.getMessage("server.result.success.add", null, XiaMesssageResource.getMessage("WorkOrder.requirement")));
        } else {
            res = new Result(re);
        }
        return res;
    }

    @POST
    @Path("Priority")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result findPriorityByWorkOrder(@Valid WorkOrderDTO dto) {
        List<PrioritysByProcessDTO> priorities =
                requirementCenterService.findPrioritysForWoCreate(dto);
        return new Result(priorities);
    }


    /**
     * 执行人
     *
     * @param workOrderId
     * @param request
     * @return
     * @Author:wayne
     */
    @POST
    @Path("equipments/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWorkOrderEquipmentByOrderId(
            @PathParam("workOrderId") Long workOrderId, DataTableRequest request) {
        return new DataTableResponse(10,
                workOrderEquipmentService.findWorkOrderEquipmentByOrderId(workOrderId));
    }

    /**
     * 提需求人信息补全（员工，客户）
     */
    @POST
    @Path("CustomerDTO")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findDemandInfo(DataTableRequest request) {
        int pageNumber = 0;
        if (request != null) {
            pageNumber = request.getPageNumber();
        } else {
            pageNumber = 1;
        }
        List<CustomerDTO> wts = requirementCenterService.findDemandInfo(request);
        int count = 0;
        if (wts != null) {
            count = wts.size();
        }
        return new DataTableResponse(wts, count, pageNumber, request.getDraw());
    }

    /**
     * 需求附件接口
     *
     * @param requirementId
     * @return
     */
    @GET
    @Path("attachments/{requirementId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getAttachments(@PathParam("requirementId") Long requirementId) {
        return new Result(uploadFileService.getAttachmentsByTableNameAndPkeyId(Requirement.class.getSimpleName(), String.valueOf(requirementId)));
    }

    /**
     * 需求图片接口
     *
     * @param requirementId
     * @return
     */
    @GET
    @Path("pictures/{requirementId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getPictures(@PathParam("requirementId") Long requirementId) {
        return new Result(uploadFileService.getPicturesByTableNameAndPkeyId(Requirement.class.getSimpleName(), String.valueOf(requirementId)));
    }


    /**
     * 创建工单
     */
    @POST
    @Path("workOrder")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result addWorkOrder(@Valid WorkOrderDTO dto) {
        List<WorkOrder> woL = null;
        if (!dto.isStillSubmitWithoutLikeWo()) {
            woL = requirementCenterService.checkLikeRequirementWorkOrder(dto);
        }
        if (dto.isStillSubmitWithoutLikeWo() || woL == null || woL.size() == 0) {
            requirementCenterService.createRequirementWorkOrder(dto);
        }
        Result res = null;
        if (woL == null || woL.size() == 0) {
            res = new Result(XiaMesssageResource.getMessage("server.result.success.add", null, XiaMesssageResource.getMessage("WorkOrder")));
        } else {
            res = new Result(woL);
        }
        return res;
    }

    /**
     * 根据ID获得工单设备
     *
     * @param
     * @return List<WoEquipment>
     */
    @POST
    @Path("woEquipment/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWorkOrderEquipmentById(@PathParam("id") Long id,
                                                       DataTableRequest request) {
        return new DataTableResponse(10, requirementCenterService.findEquipmentById(id));
    }


//    /**
//     * 巡检工单报障
//     */
//    @POST
//    @Path("workOrder")
//    @Consumes(MediaType.APPLICATION_JSON)
//    @Produces(MediaType.APPLICATION_JSON)
//    public Result addPatrolWorkOrder(@Valid WorkOrderDTO dto) {
//        return new Result( requirementCenterService.createRequirementWorkOrder(dto));
//
//    }


    /**
      * 微信需求审核
      */
    @POST
    @Path("check")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result requirementCheck(@Valid RequirementCheckDTO dto) {
        Requirement requirement = requirementCenterService.requirementCheck(dto);
        return new Result(requirement);
    }

}
