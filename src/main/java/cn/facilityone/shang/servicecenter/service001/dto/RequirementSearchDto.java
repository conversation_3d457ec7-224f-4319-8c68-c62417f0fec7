package cn.facilityone.shang.servicecenter.service001.dto;

import java.util.List;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;

public class RequirementSearchDto extends DataTableRequest{
    
    private String query;
    
    private String createTime;
    
    private String comTime;
    
    private List<String> status;
    
    private List<String> source;


    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getComTime() {
        return comTime;
    }

    public void setComTime(String comTime) {
        this.comTime = comTime;
    }

    public List<String> getStatus() {
        return status;
    }

    public void setStatus(List<String> status) {
        this.status = status;
    }

    public List<String> getSource() {
        return source;
    }

    public void setSource(List<String> source) {
        this.source = source;
    }

    
}
