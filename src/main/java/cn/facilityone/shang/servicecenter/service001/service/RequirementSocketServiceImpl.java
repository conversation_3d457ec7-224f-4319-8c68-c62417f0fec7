package cn.facilityone.shang.servicecenter.service001.service;

import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.organize.org002.repository.UserRepository;
import cn.facilityone.xia.message.common.NotificationClients;
import cn.facilityone.xia.message.config.MessageSiteProperties;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.security.model.User;
import com.corundumstudio.socketio.SocketIOServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by zack.zhang on 2017/4/18.
 */
@Service
public class RequirementSocketServiceImpl implements RequirementSocketService {
    private final Logger logger = LoggerFactory.getLogger(RequirementSocketServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SocketIOServer server;

    @Override
    public void newRequirementSocket() {
        logger.debug("Start new requirement socket");
        doSend();
    }

    protected boolean doSend() {
        Map<String, Object> socketMap = new HashMap<>();
        socketMap.put("newRequirement", true);
        socketMap.put("number", 1);
        socketMap.put("project", ProjectContext.getCurrentProject());
        boolean isSend = true;
        List<String> uuids = getAllUuids();
        try {
            if (uuids != null && uuids.size() > 0) {
                for (String uuid : uuids) {
                    server.getClient(UUID.fromString(uuid)).sendEvent("NewRequirementChanel", socketMap);
                }
            }
        } catch (Exception e) {
            isSend = false;
            logger.error(e.getMessage(), e);
        }
        return isSend;
    }

    private List<String> getUuids(List<Employee> employees) {
        List<String> uuids = new ArrayList<String>();

        if (employees != null && employees.size() > 0) {
            for (Employee employee : employees) {
                if (null != employee.getUser()) {
                    List<String> _uuids = NotificationClients.getClients(employee.getUser().getId());
                    if (_uuids != null) {
                        uuids.addAll(_uuids);
                    }
                }
            }
        }
        return uuids;
    }

    private List<String> getAllUuids() {
        List<String> uuids = new ArrayList<String>();
        List<User> users = userRepository.findAll();
        if (null != users && users.size() > 0) {
            for (User user : users) {
                List<String> _uuids = NotificationClients.getClients(user.getId());
                if (null != _uuids) {
                    uuids.addAll(_uuids);
                }
            }
        }
        return uuids;
    }

}
