package cn.facilityone.shang.servicecenter.service001.service;

import cn.facilityone.shang.asset.asset002.builder.EquipmentBuilder;
import cn.facilityone.shang.asset.asset002.repository.EquipmentRepository;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.repository.*;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.common.util.LocationUtil;
import cn.facilityone.shang.common.util.RequirementCode;
import cn.facilityone.shang.common.util.WorkOrderCode;
import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.common.*;
import cn.facilityone.shang.entity.organize.Customer;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.patrol.PatrolTaskSpot;
import cn.facilityone.shang.entity.patrol.PatrolTaskSpotResult;
import cn.facilityone.shang.entity.servercenter.*;
import cn.facilityone.shang.entity.servercenter.Requirement.RequirementSource;
import cn.facilityone.shang.entity.servercenter.Requirement.RequirementStatus;
import cn.facilityone.shang.entity.workorder.*;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org003.service.OrganizationService;
import cn.facilityone.shang.organize.org004.repository.SiteRepository;
import cn.facilityone.shang.organize.org004.service.PlaceService;
import cn.facilityone.shang.organize.org006.repository.CustomerRepository;
import cn.facilityone.shang.organize.org006.staticmetamodel.Customer_;
import cn.facilityone.shang.patrol.common.repository.PatrolTaskSpotResultRepository;
import cn.facilityone.shang.patrol.par003.service.PatrolTaskItemService;
import cn.facilityone.shang.servicecenter.common.dto.ServiceTypesDTO;
import cn.facilityone.shang.servicecenter.common.repository.*;
import cn.facilityone.shang.servicecenter.common.service.RequirementActivityService;
import cn.facilityone.shang.servicecenter.common.staticmetamodel.RequirementType_;
import cn.facilityone.shang.servicecenter.common.staticmetamodel.Requirement_;
import cn.facilityone.shang.servicecenter.service001.dto.*;
import cn.facilityone.shang.servicecenter.service004.service.RequirementNoticeSwitchService;
import cn.facilityone.shang.wechat.dto.WechatTemplateMessageContent;
import cn.facilityone.shang.wechat.h5.entity.Point;
import cn.facilityone.shang.wechat.h5.entity.UserInfo;
import cn.facilityone.shang.wechat.h5.entity.UserProject;
import cn.facilityone.shang.wechat.h5.repository.EvaluationRepository;
import cn.facilityone.shang.wechat.h5.repository.UserInfoRepository;
import cn.facilityone.shang.wechat.h5.repository.UserProjectRepository;
import cn.facilityone.shang.wechat.h5.service.PointService;
import cn.facilityone.shang.wechat.h5.service.UserProjectService;
import cn.facilityone.shang.wechat.h5.service.WechatMessageService;
import cn.facilityone.shang.wechat.service.WechatProcessService;
import cn.facilityone.shang.wechat.util.ConstantUtil;
import cn.facilityone.shang.workorder.common.constant.WorkOrderConstant;
import cn.facilityone.shang.workorder.common.repository.PriorityRepository;
import cn.facilityone.shang.workorder.common.repository.ServiceTypeRepository;
import cn.facilityone.shang.workorder.common.repository.WorkOrderToolRepository;
import cn.facilityone.shang.workorder.common.service.WorkOrderMessageService;
import cn.facilityone.shang.workorder.common.service.WorkOrderTaskService;
import cn.facilityone.shang.workorder.wo003.service.WorkOrderProcessService;
import cn.facilityone.shang.workorder.wo008.repository.SatisfactionDegreeRepository;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 需求
 *
 * @version 1.0
 *          Created by renfr.wang
 *          2015/5/13.
 */
@Service
public class RequirementCenterServiceImpl implements RequirementCenterService {

    private static final Logger log = LoggerFactory.getLogger(RequirementCenterServiceImpl.class);

    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private RequirementRepository requirementRepository;
    @Autowired
    private RequirementTypeRepository requirementTypeRepository;
    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private FollowUpRepository followUpRepository;
    @Autowired
    private WorkOrderProcessService workOrderProcessService;
    @Autowired
    private WorkOrderCode workOrderCode;
    @Autowired
    private ServiceTypeRepository serviceTypeRepository;
    @Autowired
    private WorkOrderToolRepository workOrderToolRepository;
    @Autowired
    private WorkOrderLaborerRepository workOrderLaborerRepository;
    @Autowired
    private PriorityRepository priorityRepository;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private PlaceService positionService;
    @Autowired
    private RequirementActivityService requirementActivityService;
    @Autowired
    private EquipmentRepository equipmentRepository;
    @Autowired
    private WorkOrderEquipmentRepository workOrderEquipmentRepository;
    @Autowired
    private SatisfactionDegreeRepository satisfactionDegreeRepository;
    @Autowired
    private PictureRepository pictureRepository;
    @Autowired
    private AttachmentRepository attachmentRepository;
    @Autowired
    private EquipmentBuilder equipmentBuilder;
    @Autowired
    private WorkOrderTaskService workOrderTaskService;
    @Autowired
    private XiaFileRepository xiaFileRepository;
    @Autowired
    private RequirementCode requirementCode;
    @Autowired
    private SiteRepository siteRepository;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private PatrolTaskSpotResultRepository patrolTaskSpotResultRepository;
    @Autowired
    private WechatProcessService wechatProcessService;
    @Autowired
    private WorkOrderMessageService workOrderMessageService;
    @Autowired
    private UserInfoRepository userInfoRepository;
    @Autowired
    private EvaluationRepository evaluationRepository;
    @Autowired
    private RequirementNoticeSwitchService requirementNoticeSwitchService;
    @Autowired
    private WechatMessageService wechatMessageService;
    @Autowired
    private RequirementSocketService requirementSocketService;
    @Autowired
    private UploadFileService fileService;
    @Autowired
    private PatrolTaskItemService patrolTaskItemService;
    @Autowired
    private RequirementActivityRepository requirementActivityRepository;
    @Autowired
    private UserProjectService userProjectService;
    @Autowired
    private UserProjectRepository userProjectRepository;
    @Autowired
    private PointService pointService;


    private static final Long SUPER_ADMIN = 1L;
    /**
     * 需求数据
     *
     * @return
     */
    @Override
    @Deprecated // andy
    public List<WorkOrder> findRequirementInfo() {
        List<WorkOrder> workOrderList = new ArrayList<>();
        List<Requirement> requirementList = requirementRepository.findAll();
        if (requirementList != null) {
            for (Requirement re : requirementList) {
                //获取需求及类型
                Requirement requirement = requirementRepository.findOne(re.getId());
                RequirementType requirementtype = requirementRepository.findRequirementById(requirement.getId());
                Customer customer = requirementRepository.findCustomerById(requirement.getId());
                requirement.setCustomer(customer);
                requirement.setRequirementType(requirementtype);
                WorkOrder wor = new WorkOrder();
                wor.setRequirement(requirement);
                workOrderList.add(wor);
            }
        }
        //已回访的不显示
        Iterator<WorkOrder> itr = workOrderList.iterator();
        while (itr.hasNext()) {
            if (Requirement.RequirementStatus.FOLLOWUP == itr.next().getRequirement().getStatus()) {
                itr.remove();
            }
        }
        workOrderList = resort(workOrderList);
        return workOrderList;
    }

    /**
     * 搜索
     *
     * @return
     */
    @Deprecated // Andy
    @Override
    public List<WorkOrder> searchRequirementInfo(String queryString) {
        List<WorkOrder> workOrderList = workOrderRepository.searchrEquirementList(queryString);
        for (WorkOrder wo : workOrderList) {
            //获取需求及类型
            Requirement requirement = workOrderRepository.findRequirementById(wo.getId());
            RequirementType requirementtype = requirementRepository.findRequirementById(requirement.getId());
            requirement.setRequirementType(requirementtype);
            wo.setRequirement(requirement);
        }
        //排序
        workOrderList = resort(workOrderList);
        return workOrderList;
    }

    /**
     * 获取需求信息
     *
     * @param id
     * @return
     */
    @Override
    @XiaTransactional(readOnly = true)
    public WorkOrderDTO findWorkOrderInfo(Long id) {
        WorkOrderDTO workOrderDTO = new WorkOrderDTO();
        if (id == null) {
            return null;
        }
        Requirement requirement = requirementRepository.findOneLoadReqType(id);
        if (requirement.getRequirementType() != null) {
            requirement.getRequirementType().getName();
        }
        if (requirement.getCustomer() != null) {
            requirement.getCustomer().getName();
        }
        if (requirement.getFollowUp() != null) {
            requirement.getFollowUp().getComments();
        }

        List<WorkOrder> workOrderList = workOrderRepository.findWoByRequirementId(id);
        if (workOrderList != null && workOrderList.size() > 0) {
            Collections.sort(workOrderList, new Comparator<WorkOrder>() {
                public int compare(WorkOrder re1, WorkOrder re2) {
                    int re = re2.getModifiedDate().compareTo(re1.getModifiedDate());
                    return re;
                }
            });
        }
        if (workOrderList != null && workOrderList.size() > 0) {
            for (WorkOrder wor : workOrderList) {
                if (wor.getId() != null) {
                    wor = findWorkOrderByIDWithAllInfo(wor.getId());
                    wor.setRequirement(requirement);
                }
            }
        } else {
            WorkOrder wo = new WorkOrder();
            wo.setRequirement(requirement);
            workOrderList.add(wo);
        }
        //图片和附件
        requirement = getAttchmentsAndPictturesForRequirement(requirement);

        workOrderList = resort(workOrderList);
        workOrderDTO.setWorkOrderList(workOrderList);
        workOrderDTO.setPictures(requirement.getPictures());
        workOrderDTO.setAttachments(requirement.getAttachments());
        workOrderDTO.setVoiceMedias(requirement.getVoiceMedias());
        workOrderDTO.setShortVideoMedias(requirement.getShortVideoMedias());
        workOrderDTO.setVideoMedias(requirement.getVideoMedias());
        
        requirement.setWorkOrder(null);
        return workOrderDTO;
    }

    /**
     * 更新需求信息
     *
     * @param id
     * @param dto
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public String updateWorkOrder(Long id, RequirementDTO dto) {
        if (id == null) {
            return XiaMesssageResource.getMessage("server.result.error.update", null, "");
        }
        Requirement requirement = requirementRepository.findOne(id);
        if (dto.getDescription() != null) {
            requirement.setDescription(dto.getDescription());
        }
        if (dto.getRequirementTypeId() != null) {
            RequirementType requirementTypes = requirementTypeRepository.findOne(dto.getRequirementTypeId());
            requirement.setRequirementType(requirementTypes);
        }
        //需求处理记录
        if (dto.getDealContent() != "") {
            requirementActivityService.addRequirementActivity(requirement, RequirementActivity.ActivityType.PROCESS, dto.getDealContent());
        }
        //需求绑定图片和附件
        List<Picture> pictureList = dto.getPictures();
        List<Attachment> attachmentList = dto.getAttachments();
        requirement = bindPicturesAndAttachments(requirement, pictureList, attachmentList);
        if (dto.getDealType().equals("CREATE")) {
            requirement.setStatus(RequirementStatus.PROCESS);
        }
        requirementRepository.save(requirement);
        return XiaMesssageResource.getMessage("server.result.success.update", null, requirement.getCode());
    }

    /**
     * 绑定用户图片和文件
     *
     * @param requirement
     * @param pictureIds
     * @param attachmentIds
     * @return Requirement
     */
    private Requirement bindPicturesAndAttachments(Requirement requirement, List<Picture> pictureIds, List<Attachment> attachmentIds) {
        List<Picture> pictureGroup = null;
        List<Attachment> attachmentGroup = null;
        if (pictureIds != null && pictureIds.size() > 0) {
            pictureGroup = new ArrayList<>();
            for (Picture picture : pictureIds) {
                if (picture != null && picture.getId() != null) {
                    Picture pictureInstantiation = pictureRepository.findOne(picture.getId());
                    pictureInstantiation.setTableName(Requirement.class.getSimpleName());
                    pictureInstantiation.setpKeyId(requirement.getId().toString());
                    pictureGroup.add(pictureInstantiation);
                }
            }
        }
        if (attachmentIds != null && attachmentIds.size() > 0) {
            attachmentGroup = new ArrayList<>();
            for (Attachment attachment : attachmentIds) {
                if (attachment != null && attachment.getId() != null) {
                    Attachment attachmentInstantiation = attachmentRepository.findOne(attachment.getId());
                    attachmentInstantiation.setTableName(Requirement.class.getSimpleName());
                    attachmentInstantiation.setpKeyId(requirement.getId().toString());
                    attachmentGroup.add(attachmentInstantiation);
                }
            }
        }
        requirement.setPictures(pictureGroup);
        requirement.setAttachments(attachmentGroup);
        return requirement;
    }

    /**
     * 获取对应需求图片和附件
     *
     * @param requirement
     * @return
     */
    private Requirement getAttchmentsAndPictturesForRequirement(Requirement requirement) {
        //List<Picture> pictureList = pictureRepository.findByTableNameAndPKID(Requirement.class.getSimpleName(),requirement.getId().toString());
        //List<Attachment> attachmentList = attachmentRepository.findByTableNameAndPKeyId(Requirement.class.getSimpleName(), requirement.getId().toString());
        //List<VoiceMedia> voiceMedias = voiceMediaRepository.findByTableNameAndPKeyId(Requirement.class.getSimpleName(), requirement.getId().toString());
        //List<VideoMedia> videoMedias = videoMediaRepository.findByTableNameAndPKeyId(Requirement.class.getSimpleName(), requirement.getId().toString());
        //List<ShortVideoMedia> shortVideoMedias = shortVideoMediaRepository.findByTableNameAndPKeyId(Requirement.class.getSimpleName(), requirement.getId().toString());
        
        Map<String,List<XiaFile>> files = fileService.findTypeFileByTableAndPkeyId(Requirement.class.getSimpleName(), requirement.getId().toString());

        requirement.setVideoMedias(VideoMedia.convert(files.get(VideoMedia.TYPE)));
        requirement.setShortVideoMedias(ShortVideoMedia.convert(files.get(ShortVideoMedia.TYPE)));
        requirement.setVoiceMedias(VoiceMedia.convert(files.get(VoiceMedia.TYPE)));
        requirement.setAttachments(Attachment.convert(files.get(Attachment.TYPE)));
        requirement.setPictures(Picture.convert(files.get(Picture.TYPE)));
        return requirement;
    }


    /**
     * 完成需求信息
     *
     * @param id
     * @param wo
     * @return
     */
    @Override
    public String finishWorkOrder(Long id, WorkOrder wo) {
        if (id == null) {
            return XiaMesssageResource.getMessage("server.result.error.finish", null, "");
        }
        Requirement requirement = requirementRepository.findOne(id);
        if (wo.getRequirement() != null && wo.getRequirement().getDescription() != null) {
            requirement.setDescription(wo.getRequirement().getDescription());
        }
        if (wo.getRequirement() != null && wo.getRequirement().getRequirementType() != null) {
            RequirementType requirementTypes = requirementTypeRepository.findOne(wo.getRequirement().getRequirementType().getId());
            requirement.setRequirementType(requirementTypes);
        }
        Date now = new Date();
        requirement.setCompletedDateTime(now);
        requirement.setStatus(Requirement.RequirementStatus.FINISH);
        requirement = requirementRepository.save(requirement);
        requirementActivityService.addRequirementActivity(requirement, RequirementActivity.ActivityType.FINISHED, XiaMesssageResource.getMessage("page.service004.message.complete"));
        if(requirementNoticeSwitchService.isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch.COMPLETE)){
            wechatMessageService.sendMessage(requirement, "您的需求已完成！");
        }

        //send message to requester while requirement finished
        sendMessageToRequester(requirement);

        return XiaMesssageResource.getMessage("server.result.success.finish", null, requirement.getCode());
    }

    @Override
    public void sendMessageToRequester(Requirement requirement) {
//        if (Requirement.RequirementSource.WECHAT.equals(requirement.getSource())) {
//            sendWechatMessageToRequester(requirement);
//        }

        //send email for requirement coming from email
        if (Requirement.RequirementSource.EMAIL.equals(requirement.getSource())) {
            sendEmailToRequester(requirement);
        }

    }

    private void sendEmailToRequester(Requirement requirement) {
        String emailAddress = requirement.getEmail();
        if (org.apache.commons.lang.StringUtils.isEmpty(emailAddress)) {
            log.error("There is no email address found which should not happen");
            return;
        }

        workOrderMessageService.sendEmailWhenOrderFinished(requirement, null);
    }

    private void sendWechatMessageToRequester(Requirement requirement) {
        if (org.apache.commons.lang.StringUtils.isEmpty(requirement.getWechatRequesterId())) {
            log.error("wechat open id is empty");
            return;
        }
        WechatTemplateMessageContent content = new WechatTemplateMessageContent();
        WechatTemplateMessageContent.Data event = new WechatTemplateMessageContent.Data();
        event.setValue(requirement.getDescription() == null ? "" : requirement.getDescription());
        content.setEvent(event);
        WechatTemplateMessageContent.Data finishedTime = new WechatTemplateMessageContent.Data();
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
        finishedTime.setValue(format.format(new Date()));
        content.setFinish_time(finishedTime);
        wechatProcessService.sendTemplateMessage(requirement.getWechatRequesterId(), content);
    }

    /**
     * 查询需求数据
     *
     * @param queryString
     * @return
     */
    @Deprecated // Andy
    @Override
    public List<WorkOrder> searchStatusInfo(String queryString) {
        if ("".equals(queryString)) {
            return null;
        }
        List<Requirement> requirementList = new ArrayList<>();
        Boolean flag = false;
        Boolean flags = false;
        for (int i = 0; i < queryString.length(); i++) {
            String Status = queryString.substring(i, i + 1);
            if ("S".equals(Status) || "F".equals(Status)) {
                flag = true;
                break;
            }
        }
        Long createDay = 0l;
        Long finishDay = 0l;
        String[] statusArray = queryString.split(",");
        for (int i = 0; i < statusArray.length; i++) {
            if (flag) {
                if ("SMonth".equals(statusArray[i])) {
                    createDay = 30l;
                }
                if ("SWeek".equals(statusArray[i])) {
                    createDay = 7l;
                }
                if ("SThreeDay".equals(statusArray[i])) {
                    createDay = 3l;
                }
                if ("SDay".equals(statusArray[i])) {
                    createDay = 1l;
                }
                if (createDay != 0) {
                    flags = true;
                    requirementList.addAll(requirementRepository.findRequirementByCreateTime(createDay));
                }

                if ("FMonth".equals(statusArray[i])) {
                    finishDay = 30l;
                }
                if ("FWeek".equals(statusArray[i])) {
                    finishDay = 7l;
                }
                if ("FThreeDay".equals(statusArray[i])) {
                    finishDay = 3l;
                }
                if ("FDay".equals(statusArray[i])) {
                    finishDay = 1l;
                }
                if (finishDay != 0) {
                    flags = true;
                    requirementList.addAll(requirementRepository.findRequirementByFinishTime(finishDay));
                }
            }
            if (requirementList.size() > 0 && flags) {
                if ("1".equals(statusArray[i])) {
                    Iterator<Requirement> itr = requirementList.iterator();
                    while (itr.hasNext()) {
                        if (Requirement.RequirementStatus.PROCESS != itr.next().getStatus()) {
                            itr.remove();
                        }
                    }
                }
                if ("2".equals(statusArray[i])) {
                    Iterator<Requirement> itr = requirementList.iterator();
                    while (itr.hasNext()) {
                        if (Requirement.RequirementStatus.FINISH != itr.next().getStatus()) {
                            itr.remove();
                        }
                    }
                }
                if ("3".equals(statusArray[i])) {
                    Iterator<Requirement> itr = requirementList.iterator();
                    while (itr.hasNext()) {
                        if (Requirement.RequirementStatus.FOLLOWUP != itr.next().getStatus()) {
                            itr.remove();
                        }
                    }
                }
                if ("0".equals(statusArray[i])) {
                    Iterator<Requirement> itr = requirementList.iterator();
                    while (itr.hasNext()) {
                        if (Requirement.RequirementStatus.CREATE != itr.next().getStatus()) {
                            itr.remove();
                        }
                    }
                }
            } else {
                if ("1".equals(statusArray[i])) {
                    requirementList.addAll(requirementRepository.findRequirementByStatus(Requirement.RequirementStatus.PROCESS));
                }
                if ("2".equals(statusArray[i])) {
                    requirementList.addAll(requirementRepository.findRequirementByStatus(Requirement.RequirementStatus.FINISH));
                }
                if ("3".equals(statusArray[i])) {
                    requirementList.addAll(requirementRepository.findRequirementByStatus(Requirement.RequirementStatus.FOLLOWUP));
                }
                if ("0".equals(statusArray[i])) {
                    requirementList.addAll(requirementRepository.findRequirementByStatus(Requirement.RequirementStatus.CREATE));
                }
            }
        }
        List<WorkOrder> workOrderList = new ArrayList<>();
        for (Requirement requirement : requirementList) {
            RequirementType requirementtype = requirementRepository.findRequirementById(requirement.getId());
            requirement.setRequirementType(requirementtype);
            if (requirement.getCustomer() != null) {
                requirement.setCustomer(requirementRepository.findCustomerById(requirement.getId()));
            }
            WorkOrder workOrder = new WorkOrder();
            workOrder.setRequirement(requirement);
            workOrderList.add(workOrder);
        }
        workOrderList = resort(workOrderList);
        return workOrderList;
    }

    /**
     * 获取满意度数据
     *
     * @param id
     * @return
     */
    @Override
    public FollowUp findFollowUpInfo(Long id) {
        FollowUp followUp = requirementRepository.findFollowUpById(id);
        if (followUp == null) {
            return null;
        }
        followUp.setDegree(followUpRepository.findSatisfactionDegreeById(followUp.getId()));
        return followUp;
    }

    @Override
    public Evaluation findEvaluationInfo(Long id) {
        return requirementRepository.findEvaluateById(id);
    }


    @Override
    public List<SatisfactionDegree> findDegreeAll() {
        List<SatisfactionDegree> sta = satisfactionDegreeRepository.findAll();
        if (sta == null) {
            return null;
        }
        return sta;
    }

    /**
     * 保存满意度
     *
     * @param id
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public String updateFollowUp(Long id, FollowUp fw) {
        if (id == null) {
            return XiaMesssageResource.getMessage("server.result.error.update", null, "");
        }
        FollowUp followUp = followUpRepository.findOne(id);
        followUp.setComments(fw.getComments());
        List<SatisfactionDegree> degree = followUpRepository.findSatisfactionDegreeByDegree(fw.getDegree().getDegree());
        followUp.setDegree(degree.get(0));

        followUpRepository.save(followUp);
        return XiaMesssageResource.getMessage("server.result.success.update", null, followUp.getId());
    }

    /**
     * 添加满意度
     *
     * @param fw
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public String CreateFollowUp(Long id, FollowUp fw) {
        if (id == null) {
            return XiaMesssageResource.getMessage("server.result.error.add", null, "");
        }
        Requirement requirement = requirementRepository.findOne(id);
        FollowUp followUp = new FollowUp();
        followUp.setComments(fw.getComments());
        List<SatisfactionDegree> degree = followUpRepository.findSatisfactionDegreeByDegree(fw.getDegree().getDegree());
        followUp.setDegree(degree.get(0));
        followUpRepository.save(followUp);
        requirement.setFollowUp(followUp);
        requirement.setStatus(RequirementStatus.FOLLOWUP);
        requirementRepository.save(requirement);
        //处理过程
        String degreeComment = degree.get(0).getDegree();
        if (!StringUtils.isEmpty(fw.getComments())) {
            degreeComment = degreeComment + " - " + fw.getComments();
        }
        requirementActivityService.addRequirementActivity(requirement, RequirementActivity.ActivityType.FOLLOW_UP, degreeComment);

        return XiaMesssageResource.getMessage("message.requirement.success_follow_up");
    }

    @Override
    @XiaTransactional(readOnly = false)
    public String addEvaluation(Evaluation evaluation) {
        if(ProjectContext.getCurrentProject() == -1 || ProjectContext.getCurrentProject() == null){
            ProjectContext.setCurrentProject(evaluation.getProject());
        }
//        System.out.println(ProjectContext.getCurrentProject());
        Long reqId = evaluation.getRequirementId();
        String type = evaluation.getEvaluationType().toString();
        if(reqId == null || StringUtils.isEmpty(type)){
            return XiaMesssageResource.getMessage("server.result.error.add", null, "");
        }
        evaluation = evaluationRepository.save(evaluation);
        Requirement requirement = requirementRepository.findOne(reqId);
        // 更新requirement
        requirement.setEvaluation(evaluation);
        //更新需求状态：已评价
        requirement.setStatus(Requirement.RequirementStatus.FOLLOWUP);
        requirementRepository.save(requirement);

        // 更新处理过程
        String desc = null;
        if(null != evaluation){
            StringBuilder sb = new StringBuilder();
            sb.append(XiaMesssageResource.getMessage("page.service004.message.evaluation")
                    .replace("F1",evaluation.getQuality()+"")
                    .replace("F2",evaluation.getSpeed()+"")
                    .replace("F3",evaluation.getAttitude()+""));
            if(!StringUtils.isEmpty(evaluation.getDescription())){
                sb.append("<br/>评价内容："+evaluation.getDescription());
            }
            desc = sb.toString();
//          desc  = "服务质量"+evaluation.getQuality()+"分，服务速度"+evaluation.getSpeed()+"分，服务态度"+evaluation.getAttitude()+"分。";
        }

        if(evaluation.getEvaluationType().equals(Evaluation.EvaluationType.WECHAT)){
            requirementActivityService.addRequirementActivity_wechat(requirement, RequirementActivity.ActivityType.FOLLOW_UP, desc);
        }else{
            requirementActivityService.addRequirementActivity(requirement, RequirementActivity.ActivityType.FOLLOW_UP, desc);
        }

        // 微信端 积分处理
        if(null != requirement && requirement.getSource().equals(Requirement.RequirementSource.WECHAT)){
            if(requirementNoticeSwitchService.isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch.EVALUATE)){
                wechatMessageService.sendMessage(requirement, null);
            }
            Date now = new Date();
            String openId = requirement.getWechatRequesterId();
//            Point point = new Point();
//            point.setPoint(ConstantUtil.PLUSINTEGRAL);
//            point.setRewordType(Point.RewordType.PLUS);
//            point.setOpenId(openId);
//            point.setCreatedDate(new Date());
//            point.setRequirementId(reqId);
//            point.setProjectId(requirement.getProject());
            String desct = "";
            if(null != requirement){
                //微信消息不做国际化
                desct = DateUtil.formatDateTimeNotContainSS(now)+" 您对需求"+requirement.getCode()+"进行了评价。";
            }
//            point.setDescription(desct);
//            pointRepository.save(point);
            // 更新用户积分总数
            UserInfo userInfo = userInfoRepository.findOne(openId);
            if(null != userInfo){
                userInfo.setPoint(userInfo.getPoint() + ConstantUtil.PLUSINTEGRAL);
                userInfoRepository.save(userInfo);
            }
            //将积分保存到对应的项目中

            Long projectId=ProjectContext.getCurrentProject();
            if(projectId == -1 || projectId == null){
                projectId=evaluation.getProject();
            }
            UserProject userProject=userProjectService.findUserProject(openId,projectId);
            if(null!=userProject){
                userProject.setPoint(userProject.getPoint()+ConstantUtil.PLUSINTEGRAL);
                userProjectRepository.save(userProject);
                pointService.savePoint(requirement,Point.RewordType.PLUS,desct,userProject.getPoint());
            }

        }
        return XiaMesssageResource.getMessage("message.requirement.success_follow_up");
    }

    /**
     * 相似需求
     *
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public List<Requirement> checkLikeReq(RequirementDTO requirementDTO) {

        List<Requirement> likeReqs = new ArrayList<Requirement>();

        /*Requirement requirement = buildDdoToRequirement(requirementDTO, false);

        //相似需求
        try {
            likeReqs = srequirementService.findLikeRequirement(requirement, 10, 10, 10);
            // 相似处理
            if (likeReqs != null && likeReqs.size() > 0) {
                List<Long> woIds = new ArrayList<Long>();
                for (Requirement req : likeReqs) {
                    woIds.add(req.getId());
                }
                likeReqs = requirementRepository.findByIdIn(woIds.toArray(new Long[woIds.size()]));
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }*/

        return likeReqs;
    }

    /**
     * 新建需求
     *
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public Requirement addRequirement(RequirementDTO requirementDTO) {

        Requirement requirement = buildDdoToRequirement(requirementDTO, true);
        if (requirement == null) {
            return null;
        }
        requirement = requirementRepository.save(requirement);
        requirement = handleEmailAddress(requirement);

        //处理过程
        requirementActivityService.addRequirementActivity(requirement, RequirementActivity.ActivityType.CREATE, requirement.getDescription());

        //需求绑定图片和附件
        List<Picture> pictureList = requirementDTO.getPictures();
        List<Attachment> attachmentList = requirementDTO.getAttachments();
        requirement = bindPicturesAndAttachments(requirement, pictureList, attachmentList);

        requirement = requirementRepository.save(requirement);
        if (null != requirement.getId()) {
            requirementSocketService.newRequirementSocket();
        }
        return requirement;
    }

    private Requirement buildDdoToRequirement(RequirementDTO requirementDTO, boolean isCode) {
        Requirement re = requirementDTO.getRequirement();
        if (re == null) {
            return null;
        }
        Requirement requirement = new Requirement();
        requirement.setReserStartTime(re.getReserStartTime());
        requirement.setReserEndTime(re.getReserEndTime());
        requirement.setContactPhone(re.getContactPhone());
        requirement.setRequestName(re.getRequestName());
        if (re.getRequirementType() != null) {
            RequirementType requirementType = requirementTypeRepository.findOne(re.getRequirementType().getId());
            if (requirementType == null) {
                return null;
            }
            requirement.setRequirementType(requirementType);
        }
        requirement.setSource(re.getSource());
        requirement.setDescription(re.getDescription());
        requirement.setStatus(Requirement.RequirementStatus.PROCESS);
        if (isCode) {
            requirement.setCode(requirementCode.getCode());
            requirementDTO.setCode(requirement.getCode());
        }
        if (re.getCustomer() != null && re.getCustomer().getId() != null) {
            Customer customer = customerRepository.findOne(re.getCustomer().getId());
            requirement.setRequestName(customer.getName());
            requirement.setCustomer(customer);
            if (customer != null) {
                if ((!"".equals(customer.getPhone()) && customer.getPhone() != null) || (!"".equals(customer.getPhone()) && customer.getPhone() != null)) {
                    if (re.getContactPhone() == null || "".equals(re.getContactPhone())) {
                        requirement.setContactPhone(buildContactString(customer.getPhone(), customer.getMobile()));
                    }
                }
            }
        }

        if (re.getEmployee() != null && re.getEmployee().getId() != null) {
            Employee employee = employeeRepository.findOne(re.getEmployee().getId());
            requirement.setEmployee(employee);
            if (employee != null) {
                if ((!"".equals(employee.getExtension()) && employee.getExtension() != null) || (!"".equals(employee.getPhone()) && employee.getPhone() != null)) {
                    if (re.getContactPhone() == null || "".equals(re.getContactPhone())) {
                        requirement.setContactPhone(buildContactString(employee.getExtension(), employee.getPhone()));
                    }
                }
            }
        }

        boolean isFromEmail = requirementDTO.isFromEmail();
        if (isFromEmail) {
            requirement.setSource(RequirementSource.EMAIL);
            requirement.setEmail(re.getEmail());
        }

        return requirement;
    }

    public String buildContactString(String one, String two) {
        String res = "";
        if (!StringUtils.isEmpty(one)) {
            res += one;
        }
        if (!StringUtils.isEmpty(one) && !StringUtils.isEmpty(two)) {
            res += SystemConst.STR_CONNECT;
        }
        if (!StringUtils.isEmpty(two)) {
            res += two;
        }
        return res;
    }

    /**
     * 新建需求
     *
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public Requirement checkRequirement(RequirementDTO requirementDTO) {

        Requirement requirement = requirementRepository.findOne(requirementDTO.getRequirement().getId());

        requirement.setDescription(requirementDTO.getRequirement().getDescription());
        requirement.setReserStartTime(requirementDTO.getRequirement().getReserStartTime());
        requirement.setReserEndTime(requirementDTO.getRequirement().getReserEndTime());
        RequirementType rt = requirementTypeRepository.findOne(requirementDTO.getRequirement().getRequirementType().getId());
        requirement.setRequirementType(rt);
        requirement.setStatus(Requirement.RequirementStatus.PROCESS);
        requirement = requirementRepository.save(requirement);

        //处理过程
        requirementActivityService.addRequirementActivity(requirement, RequirementActivity.ActivityType.CHECK, XiaMesssageResource.getMessage("page.service004.message.check"));
        if(requirementNoticeSwitchService.isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch.APPROVE)){
            wechatMessageService.sendMessage(requirement, "您的需求审核通过！");
        }

        //需求绑定图片和附件
        List<Picture> pictureList = requirementDTO.getPictures();
        List<Attachment> attachmentList = requirementDTO.getAttachments();
        requirement = bindPicturesAndAttachments(requirement, pictureList, attachmentList);

        return requirement;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder getWorkOrder(WorkOrderDTO dto) {
        WorkOrder order = new WorkOrder();
        order.setDescription(dto.getDescription());
        order.setRequestName(dto.getRequestor());
        order.setContactPhone(dto.getPhone());

        if (dto.getOrganization() != null && dto.getOrganization().getId() != null) {
            order.setOrganization(organizationService.findOne(dto.getOrganization().getId()));
        }
        if (dto.getPriority() != null && dto.getPriority().getId() != null) {
            order.setPriority(priorityRepository.findOne(dto.getPriority().getId()));
        }
        if (dto.getWorkOrderProcess() != null && dto.getWorkOrderProcess().getId() != null) {
            order.setWorkOrderProcess(workOrderProcessService.findOne(dto.getWorkOrderProcess()
                    .getId()));
        }
        order.setSite(siteRepository.findByProject(ProjectContext.getCurrentProject()).get(0));
        if (dto.getBuilding() != null && dto.getBuilding().getId() != null) {
            order.setBuilding(positionService.findBuilding(dto.getBuilding().getId()).getBuilding());
        } else {
            order.setBuilding(null);
        }
        if (dto.getFloor() != null && dto.getFloor().getId() != null) {
            order.setFloor(positionService.findFloor(dto.getFloor().getId()).getFloor());
        } else {
            order.setFloor(null);
        }
        if (dto.getRoom() != null && dto.getRoom().getId() != null) {
            order.setRoom(positionService.findRoom(dto.getRoom().getId()).getRoom());
        } else {
            order.setRoom(null);
        }
        if (dto.getServiceType() != null && dto.getServiceType().getId() != null) {
            order.setServiceType(serviceTypeRepository.findOne(dto.getServiceType().getId()));
        }
        
        if(dto.isFromPatrol()){
            order.setSource(WorkOrder.SourceType.PATROL);
        }else{
            order.setSource(WorkOrder.SourceType.CENTER);
        }
        order.setStatus(WorkOrder.WorkOrderStatus.CREATE);
        //set location
        order.setLocation(LocationUtil.buildString(order.getBuilding(), order.getFloor(), order.getRoom()));
        return order;
    }

    /**
     * 保存工单中需求,工单类型
     *
     * @param dto
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public Requirement saveWorkOrderRequirement(WorkOrderDTO dto) {
        //WorkOrder order = new WorkOrder();
        Requirement requirement = dto.getRequirement();

        if (requirement.getRequirementType() == null) {
            return null;
        }

        //需求
        if (dto.getReCode() != null && !"false".equals(dto.getwFlag())) {
            requirement = requirementRepository.findRequirementByCode(dto.getReCode());
            requirement.setStatus(RequirementStatus.PROCESS);
            if (dto.getRequirementTypeId() != null) {
                requirement.setRequirementType(requirementTypeRepository.findOne(dto.getRequirementTypeId()));
            }
            if (dto.getRequirementDescription() != null) {
                requirement.setDescription(dto.getRequirementDescription());
            }

        } else if (dto.getRequirement() != null && !"false".equals(dto.getwFlag())) {
            RequirementDTO requirementDTO = new RequirementDTO();
            requirementDTO.setRequirement(dto.getRequirement());
            requirementDTO.setFromEmail(dto.isFromEmail());
            requirementDTO.setStillSubmitWithoutLikeReq(dto.isStillSubmitWithoutLikeWo());
            requirement = addRequirement(requirementDTO);
        }
        //solr 
        if (!dto.isStillSubmitWithoutLikeWo()) {
            return requirement;
        }

        requirement = requirementRepository.save(requirement);

        requirement = handleEmailAddress(requirement);

        //pictures and attachments
        List<Picture> pictures = dto.getPictures();
        if (CollectionUtils.isNotEmpty(pictures)) {
            updateFile(pictures, requirement);
        }
        List<Attachment> attachments = dto.getAttachments();
        if (CollectionUtils.isNotEmpty(attachments)) {
            updateFile(attachments, requirement);
        }

        return requirementRepository.save(requirement);
    }

    private Requirement handleEmailAddress(Requirement requirement) {
        if (RequirementSource.EMAIL.equals(requirement.getSource())) {
            return requirement;
        }
        //save email address for requirement
        if (requirement.getCustomer() != null || requirement.getEmployee() != null) {
            requirement = requirementRepository.findWithCustomerAndEmployee(requirement.getId());
            String email = "";
            if (requirement.getCustomer() != null) {
                email = requirement.getCustomer().getEmail();
            }
            if (requirement.getEmployee() != null) {
                email = requirement.getEmployee().getEmail();
            }
            requirement.setEmail(email);
        }

        return requirement;
    }

    /**
     * 保存微信工单中需求,工单类型
     *
     * @param dto
     * @return
     */
    //@XiaTransactional(readOnly = false)
    private Requirement checkWorkOrderRequirement(WorkOrderDTO dto) {
        //WorkOrder order = new WorkOrder();
        Requirement requirement = dto.getRequirement();

        if (requirement.getRequirementType() == null) {
            return null;
        }
        boolean isNeedCheck = false;
        //需求
        requirement = requirementRepository.findOne(requirement.getId());
        if (requirement.getStatus().equals(RequirementStatus.CREATE)) {
            isNeedCheck = true;
        }
        requirement.setStatus(RequirementStatus.PROCESS);
        if (dto.getRequirementTypeId() != null) {
            requirement.setRequirementType(requirementTypeRepository.findOne(dto.getRequirementTypeId()));
        }
        if (dto.getRequirementDescription() != null) {
            requirement.setDescription(dto.getRequirementDescription());
        }
//        requirement.setDescription(dto.getRequirement().getDescription());
        //solr 
        if (!dto.isStillSubmitWithoutLikeWo()) {
            return requirement;
        }

        if (isNeedCheck) {
            //处理过程
            requirementActivityService.addRequirementActivity(requirement, RequirementActivity.ActivityType.CHECK, XiaMesssageResource.getMessage("page.service004.message.check"));
            if(requirementNoticeSwitchService.isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch.APPROVE)){
                wechatMessageService.sendMessage(requirement, "您的需求审核通过！");
            }
        }

        requirementRepository.save(requirement);

        //pictures and attachments
        List<Picture> pictures = dto.getPictures();
        if (CollectionUtils.isNotEmpty(pictures)) {
            updateFile(pictures, requirement);
        }
        List<Attachment> attachments = dto.getAttachments();
        if (CollectionUtils.isNotEmpty(attachments)) {
            updateFile(attachments, requirement);
        }

        return requirement;
    }

    @Override
    @Transactional(readOnly = true)
    public List<PrioritysByProcessDTO> findPrioritysForWoCreate(WorkOrderDTO dto) {
        WorkOrder wo = getWorkOrder(dto);
        if ("SELFCHECK".equals(dto.getType()) || "false".equals(dto.getwFlag())) {
            wo.setType(WorkOrder.WorkOrderType.SELFCHECK);
        } else {
            wo.setType(WorkOrder.WorkOrderType.ONDEMAND);
        }
        return workOrderProcessService.findPriorityByPositionAndOrganizationAndServiceType(wo);
    }

    @Override
    public void saveWorkOrderEquipment(WorkOrder order, WorkOrderDTO dto,
                                       ServiceTypesDTO serviceTypesDto, String operateUserId) {
        List<Equipment> equipments = getWorkOrderEquipment(dto, serviceTypesDto.getAssociated());
        //去除重复设备
        if (equipments.size() > 1) {
            List<Long> equIds = new ArrayList<>();
            for (Equipment equipment : equipments) {
                if (!equIds.contains(equipment.getId())) {
                    equIds.add(equipment.getId());
                }
            }
            equipments.clear();
            for (Long equId : equIds) {
                equipments.add(equipmentRepository.findOne(equId));
            }
        }

        WorkOrder workOrder = workOrderRepository.findOne(order.getId());
        if (equipments != null) {
            for (Equipment equipment : equipments) {
                WorkOrderEquipment workOrderEquipment = new WorkOrderEquipment();
                workOrderEquipment.setWorkOrder(workOrder);
                workOrderEquipment.setEquipment(equipment);
                workOrderEquipmentRepository.save(workOrderEquipment);
            }
        }
    }

    @Override
    public List<Equipment> getWorkOrderEquipment(WorkOrderDTO dto, Boolean isAssociated) {
        List<Equipment> equipments = new ArrayList<Equipment>();
        if (isAssociated) {
            if (dto.getEquipments() != null) {
                for (WorkOrderEquipment equipment : dto.getEquipments()) {
                    Equipment eq = equipmentRepository.findOne(equipment.getEquipmentId());
                    equipments.add(eq);
                }
            }
        }
        return equipments;
    }


    /**
     * 排序,已创建、处理中、已完成/时间排序
     *
     * @param list
     * @return
     */
    public List<WorkOrder> resort(List<WorkOrder> list) {
        if (list.size() == 0) {
            return list;
        }
        List<WorkOrder> workOrderList = new ArrayList<>();
        List<WorkOrder> workOrderListFinish = new ArrayList<>();
        List<WorkOrder> workOrderListPROCESS = new ArrayList<>();
        List<WorkOrder> workOrderListCREATE = new ArrayList<>();
        List<WorkOrder> workOrderListFOLLOWUP = new ArrayList<>();
        List<WorkOrder> workOrderListCancel = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            WorkOrder workOrder = list.get(i);
            if (workOrder.getRequirement().getStatus() == Requirement.RequirementStatus.FOLLOWUP) {
                workOrderListFOLLOWUP.add(workOrder);
            }
        }
        workOrderListFOLLOWUP = dateResort(workOrderListFOLLOWUP);
        workOrderList.addAll(workOrderListFOLLOWUP);
        for (int i = 0; i < list.size(); i++) {
            WorkOrder workOrder = list.get(i);
            if (workOrder.getRequirement().getStatus() == Requirement.RequirementStatus.FINISH) {
                workOrderListFinish.add(workOrder);
            }
        }
        workOrderListFinish = dateResort(workOrderListFinish);
        workOrderList.addAll(workOrderListFinish);
        for (int i = 0; i < list.size(); i++) {
            WorkOrder workOrder = list.get(i);
            if (workOrder.getRequirement().getStatus() == Requirement.RequirementStatus.PROCESS) {
                workOrderListPROCESS.add(workOrder);
            }
        }
        workOrderListPROCESS = dateResort(workOrderListPROCESS);
        workOrderList.addAll(workOrderListPROCESS);
        for (int i = 0; i < list.size(); i++) {
            WorkOrder workOrder = list.get(i);
            if (workOrder.getRequirement().getStatus() == Requirement.RequirementStatus.CREATE) {
                workOrderListCREATE.add(workOrder);
            }
        }
        workOrderListCREATE = dateResort(workOrderListCREATE);
        workOrderList.addAll(workOrderListCREATE);

        for (int i = 0; i < list.size(); i++) {
            WorkOrder workOrder = list.get(i);
            if (workOrder.getRequirement().getStatus() == Requirement.RequirementStatus.CANCEL) {
                workOrderListCancel.add(workOrder);
            }
        }
        workOrderListCancel = dateResort(workOrderListCancel);
        workOrderList.addAll(workOrderListCancel);
        return workOrderList;
    }


    /**
     * 按照创建时间排序
     */
    public List<WorkOrder> dateResort(List<WorkOrder> list) {
        Collections.sort(list, new Comparator<WorkOrder>() {
            public int compare(WorkOrder re1, WorkOrder re2) {
                int re = re1.getRequirement().getCreatedDate().compareTo(re2.getRequirement().getCreatedDate());
                return re;
            }
        });
        return list;
    }


    /**
     * 查询一个工单，包含懒加载数据
     *
     * @param id
     */
    @Override
    @XiaTransactional
    public WorkOrder findWorkOrderByIDWithAllInfo(final Long id) {
        if (id == null) {
            return null;
        }
        WorkOrder wo = workOrderRepository.findOne(new XiaSpecification<WorkOrder>() {
            @Override
            public Root<WorkOrder> toRoot(Root<WorkOrder> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                root.fetch(WorkOrderConstant.SITE, JoinType.LEFT);
                root.fetch(WorkOrderConstant.BUILDING, JoinType.LEFT);
                root.fetch(WorkOrderConstant.FLOOR, JoinType.LEFT);
                root.fetch(WorkOrderConstant.ROOM, JoinType.LEFT);
                root.fetch(WorkOrderConstant.PREVENTIVEMAINTENANCE, JoinType.LEFT);
                root.fetch(WorkOrderConstant.SERVICE_TYPE, JoinType.LEFT);
                root.fetch(WorkOrderConstant.ORGANIZATION, JoinType.LEFT);
                root.fetch(WorkOrderConstant.PRIORITY, JoinType.LEFT);
                root.fetch(WorkOrderConstant.REQUIREMENT, JoinType.LEFT);
                root.fetch(WorkOrderConstant.WORK_TEAM, JoinType.LEFT);
                root.fetch(WorkOrderConstant.ACTUAL_WORKING_TIME,JoinType.LEFT);
                root.fetch(WorkOrderConstant.ESTIMATED_WORKING_TIME,JoinType.LEFT);
                root.fetch(WorkOrderConstant.STOP_TIME,JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<WorkOrder> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                return cb.equal(root.get(WorkOrderConstant.ID), id);
            }
        });

        if (wo == null) {
            return null;
        }
        //get pictures and attachments
        String idString = String.valueOf(id);
        wo.setPictures(pictureRepository.findByTableNameAndPKID(WorkOrderConstant.WORKORDER_TABLE_NAME, idString));
        wo.setAttachments(attachmentRepository.findByTableNameAndPKeyId(WorkOrderConstant.WORKORDER_TABLE_NAME, idString));

        return wo;
    }

    @Override
    public WorkOrder findWorkOrderByID(Long workOrderId) {
        return workOrderRepository.findOne(workOrderId);
    }

    @Override
    public Object getWorkOrderHistory(Long workOrderId) {
//        List<WorkOrderHistory> woh = new LinkedList<WorkOrderHistory>();
//        WorkOrder wo = findWorkOrderByID(id);
//        List<HistoricTaskInstance> htl =
//                workOrderProcessService.findTaskHistory(wo.getProcInstID());
//        for (HistoricTaskInstance ht : htl) {
//            woh.add((WorkOrderHistory) ht);
//        }
        return null;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public Requirement findRequirementByWorkOrderId(Long workOrderId) {
        log.info("find requirement by workOrderId:{}", workOrderId);
        if (workOrderId == null) {
            return null;
        }

        Requirement requirement = requirementRepository.findByWorkOrderId(workOrderId);
        if (requirement != null && requirement.getRequirementType() != null) {
            requirement.getRequirementType().getId();
        }
        if (requirement != null && requirement.getFollowUp() != null) {
            requirement.getFollowUp().getComments();
        }
        return requirement;
    }

    @Override
    public WorkOrder createWorkOrder(WorkOrder wo, String operateUserName) {
        //add request id
//        if (wo.getRequestId() == null) {
//            Employee em = commonUserService.findLoginEmployee();
//            if (em != null) {
//                wo.setRequestId(em.getId());
//            }
//        }
        Long em = commonUserService.findCurrentEmployeeId();
        if (em != null) {
            wo.setCreateById(em);
        }

        if (new Long(-1).equals(wo.getRequestId())) {
            wo.setRequestId(null);
        }

        // 添加工单Code值
        wo.setCode(workOrderCode.getCode(wo.getType()));

        ServiceType serviceType = wo.getServiceType();
        if (serviceType != null) {
            wo.setServiceType(serviceTypeRepository.save(serviceType));
        }
        wo = workOrderRepository.save(wo);
        if (null != wo.getWorkOrderLaborers() && wo.getWorkOrderLaborers().size() > 0) {
            workOrderLaborerRepository.save(wo.getWorkOrderLaborers());
        }
        // 立即启动流程
        wo = workOrderProcessService.startWorkProcess(wo);
        // 同时完成工单创建
//        if (wo.getProcInstID() != null) {
            wo = workOrderTaskService.completeCreate(wo);
//        }

        // 发送socket提示刷新
//        if (null != wo && null != wo.getId()) {
//            workOrderSocketService.newWorkOrderSocket(1, wo);
//        }
        return wo;
    }

    /**
     * find the entire object
     *
     * @param workOrderId
     * @return
     */
    @Override
    @XiaTransactional(readOnly = true)
    public WorkOrder findEntireOne(Long workOrderId) {
        if (workOrderId == null) {
            return null;
        }

        WorkOrder workOrder = workOrderRepository.findEntireOne(workOrderId);
        List<WorkOrderEquipment> equipments = workOrderEquipmentRepository.findByWorkOrderId(workOrderId);
        workOrder.setWorkOrderEquipments(equipments);
        List<WorkOrderTool> tools = workOrderToolRepository.findByWorkOrderId(workOrderId);
        workOrder.setWorkOrderTools(tools);
        List<WorkOrderLaborer> laborerList = workOrderLaborerRepository.findByWorkOrderId(workOrderId);
        workOrder.setWorkOrderLaborers(laborerList);

        return workOrder;
    }

    /**
     * find wort order without collection type property
     *
     * @param workOrderId
     * @return
     */
    @Override
    public WorkOrder findEntireOneWithOutCollectionPart(Long workOrderId) {
        if (workOrderId == null) {
            return null;
        }
        WorkOrder workOrder = workOrderRepository.findEntireOne(workOrderId);
        return workOrder;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public Page<Requirement> findRequirements(final RequirementSearchDto dto) {
        Page<Requirement> reqs = requirementRepository.findAll(new XiaSpecification<Requirement>() {
            @Override
            public Predicate toPredicate(Root<Requirement> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                //姓名，部门，职位
                if (!StringUtils.isEmpty(dto.getQuery())) {
                    From<?, ?> customerFrom = root.join(Requirement_.CUSTOMER, JoinType.LEFT);
                    From<?, ?> RequirementTypeFrom = root.join(Requirement_.REQUIREMENT_TYPE, JoinType.LEFT);
                    predicatesList.add(
                            cb.or(
                                    cb.like(root.<String>get(Requirement_.CODE), "%" + dto.getQuery() + "%"),
                                    cb.like(root.<String>get(Requirement_.REQUEST_NAME), "%" + dto.getQuery() + "%"),
                                    cb.like(root.<String>get(Requirement_.DESCRIPTION), "%" + dto.getQuery() + "%"),
                                    cb.like(root.<String>get(Requirement_.CONTACT_PHONE), "%" + dto.getQuery() + "%"),
                                    cb.like(RequirementTypeFrom.<String>get(RequirementType_.NAME), "%" + dto.getQuery() + "%"),
                                    cb.like(customerFrom.<String>get(Customer_.NAME), "%" + dto.getQuery() + "%"),
                                    cb.like(customerFrom.<String>get(Customer_.TITLE), "%" + dto.getQuery() + "%"),
                                    cb.like(customerFrom.<String>get(Customer_.MOBILE), "%" + dto.getQuery() + "%"),
                                    cb.like(customerFrom.<String>get(Customer_.PHONE), "%" + dto.getQuery() + "%")
                            ));
                }
                //提交时间
                if (!StringUtils.isEmpty(dto.getCreateTime())) {
                    Date[] dates = buildDateTimeTOSearch(dto.getCreateTime());
                    if (dates != null && dates[0] != null && dates[1] != null) {
                        predicatesList.add(cb.between(root.<Date>get(Requirement_.CREATE_DATE), dates[0], dates[1]));
                    }
                }
                //完成时间
                if (!StringUtils.isEmpty(dto.getComTime())) {
                    Date[] dates = buildDateTimeTOSearch(dto.getComTime());
                    if (dates != null && dates[0] != null && dates[1] != null) {
                        predicatesList.add(cb.between(root.<Date>get(Requirement_.COMPLETED_DATE_TIME), dates[0], dates[1]));
                    }
                }
                //需求状态
                if (dto.getStatus() != null && dto.getStatus().size() > 0) {
                    List<RequirementStatus> enums = new ArrayList<RequirementStatus>();
                    for (String status : dto.getStatus()) {
                        enums.add(Enum.valueOf(RequirementStatus.class, status.toUpperCase()));
                    }
                    predicatesList.add(root.<Integer>get(Requirement_.STATUS).in(enums));
                }
                //需求来源
                if (!CollectionUtils.isEmpty(dto.getSource())) {
                    List<RequirementSource> enums = new ArrayList<RequirementSource>();
                    for (String source : dto.getSource()) {
                        enums.add(Enum.valueOf(RequirementSource.class, source.toUpperCase()));
                    }
                    predicatesList.add(root.<Integer>get(Requirement_.SOURCE).in(enums));
                }

                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                query.orderBy(cb.desc(root.<String>get(Requirement_.ID)));
                return query.getRestriction();
            }

            @Override
            public Root<Requirement> toRoot(Root<Requirement> root, CriteriaQuery<?> query,
                                            CriteriaBuilder cb) {
                root.fetch(Requirement_.CUSTOMER, JoinType.LEFT);
                root.fetch(Requirement_.REQUIREMENT_TYPE, JoinType.LEFT);
                return root;
            }
        }, new PageRequest(dto.getPageNumber(), dto.getPageSize()));

        buildLazyData(reqs);

        return reqs;
    }

    private Page<Requirement> buildLazyData(Page<Requirement> reqs) {
        if (reqs.getContent() != null) {
            List<Long> ids = new ArrayList<Long>();
            for (Requirement req : reqs.getContent()) {
                ids.add(req.getId());
            }
            if (ids.size() > 0) {
                List<RequirementType> reqTypes = requirementRepository.findRequirementTypesById(ids.toArray(new Long[ids.size()]));
                Map<Long, RequirementType> reqTM = new HashMap<Long, RequirementType>();
                for (RequirementType rt : reqTypes) {
                    reqTM.put(rt.getId(), rt);
                }
                for (Requirement req : reqs.getContent()) {
                    if (req.getRequirementType() != null) {
                        req.setRequirementType(reqTM.get(req.getRequirementType().getId()));
                    }
                }
            }
        }
        return reqs;
    }

    private Date[] buildDateTimeTOSearch(String dateString) {
        Date[] result = new Date[2];
        String[] createTime = dateString.split("~");
        Date createTimeStart = null;
        Date createTimeEnd = null;
        if (createTime.length == 1) {
            createTimeStart = DateUtil.formatDate(createTime[0]);
            if (createTimeStart != null) {
                createTimeStart = DateUtil.buildDateOnFirstSecond(createTimeStart);
                createTimeEnd = DateUtil.buildDateOnLastSecond(createTimeStart);
            }
        } else if (createTime.length == 2) {
            createTimeStart = DateUtil.formatDate(createTime[0]);
            createTimeEnd = DateUtil.formatDate(createTime[1]);
            if (createTimeStart != null) {
                createTimeStart = DateUtil.buildDateOnFirstSecond(createTimeStart);
            }
            if (createTimeEnd != null) {
                createTimeEnd = DateUtil.buildDateOnLastSecond(createTimeEnd);
            }
        }
        result[0] = createTimeStart;
        result[1] = createTimeEnd;
        return result;
    }


    @Override
    public List<Equipment> findEquipmentById(final Long id) {
        List<Equipment> result = equipmentRepository.findAll(new Specification<Equipment>() {

            @Override
            public Predicate toPredicate(Root<Equipment> root, CriteriaQuery<?> criteriaQuery,
                                         CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                if (id != null) {
                    predicatesList.add(criteriaBuilder.equal(root.get("id"), id));
                }
                criteriaQuery.where(predicatesList.toArray(new Predicate[predicatesList.size()]));

                return criteriaQuery.getRestriction();
            }
        });

        equipmentBuilder.init(result).addEquipmentSystem().addPosition();

        return result;
    }

    @Override
    public List<CustomerDTO> findDemandInfo(DataTableRequest request) {
        List<CustomerDTO> customerDTOList = new ArrayList<>();
        List<Employee> employeeList = employeeRepository.findPositionEmployee();
        List<Customer> customerList = customerRepository.findAll();
        String LinkNum = "";
        if (StringUtils.isEmpty(request.getColumns().get(0).getSearchText())) {
            for (Employee employee : employeeList) {
                CustomerDTO customerDTO = new CustomerDTO();
                customerDTO.setEmployeeId(employee.getId());
                customerDTO.setRequestName(employee.getName());
                if (employee.getExtension() != null && employee.getPhone() != null) {
                    if (!"".equals(employee.getExtension()) && !"".equals(employee.getPhone())) {
                        LinkNum = employee.getExtension() + " / " + employee.getPhone();
                    } else {
                        LinkNum = employee.getExtension() + employee.getPhone();
                    }
                } else if (employee.getExtension() == null) {
                    LinkNum = employee.getPhone();
                } else if (employee.getPhone() == null) {
                    LinkNum = employee.getExtension();
                }

                if (employee.getSite() != null) {

                    customerDTO.setSite(employee.getSite());
                }
                if (employee.getBuilding() != null) {

                    customerDTO.setBuilding(employee.getBuilding());
                }
                if (employee.getFloor() != null) {

                    customerDTO.setFloor(employee.getFloor());
                }
                if (employee.getRoom() != null) {

                    customerDTO.setRoom(employee.getRoom());
                }

                if (employee.getOrganization() != null) {

                    customerDTO.setOrg(employee.getOrganization());
                }

                if (employee.getEmail() != null) {
                    customerDTO.setEmailAddress(employee.getEmail());
                }

                customerDTO.setRequestLink(LinkNum);
                customerDTO.setNameCompany(employee.getName());
                customerDTOList.add(customerDTO);

            }
            for (Customer customer : customerList) {
                CustomerDTO customerDTO = new CustomerDTO();
                customerDTO.setCustomerId(customer.getId());
                customerDTO.setRequestName(customer.getName());
                customerDTO.setNameCompany(customer.getName() + " , " + customer.getCompany());
                if (customer.getEmail() != null) {
                    customerDTO.setEmailAddress(customer.getEmail());
                }
                LinkNum = "";
                if (customer.getPhone() != null && customer.getMobile() != null) {
                    if (!"".equals(customer.getPhone()) && !"".equals(customer.getMobile())) {
                        LinkNum = customer.getPhone() + " / " + customer.getMobile();
                    } else {
                        LinkNum = customer.getPhone() + customer.getMobile();
                    }
                } else if (customer.getPhone() == null) {
                    LinkNum = customer.getMobile();
                } else if (customer.getMobile() == null) {
                    LinkNum = customer.getPhone();
                }
                customerDTO.setRequestLink(LinkNum);

                customerDTOList.add(customerDTO);
            }
            return customerDTOList;
        } else {
            String queryString = request.getColumns().get(0).getSearchText();
            List<CustomerDTO> customerDTOs = new ArrayList<>();
            List<Employee> employees = employeeRepository.findByNameWithAllInfo(queryString);
            List<Customer> customers = customerRepository.findByName(queryString);
            for (Employee employee : employees) {
                CustomerDTO customerDTO = new CustomerDTO();
                customerDTO.setEmployeeId(employee.getId());
                customerDTO.setRequestName(employee.getName());
                LinkNum = "";
                if (employee.getExtension() != null && employee.getPhone() != null) {
                    if (!"".equals(employee.getExtension()) && !"".equals(employee.getPhone())) {
                        LinkNum = employee.getExtension() + " / " + employee.getPhone();
                    } else {
                        LinkNum = employee.getExtension() + employee.getPhone();
                    }
                } else if (employee.getExtension() == null) {
                    LinkNum = employee.getPhone();
                } else if (employee.getPhone() == null) {
                    LinkNum = employee.getExtension();
                }
                if (employee.getSite() != null) {
                    customerDTO.setSite(employee.getSite());
                }
                if (employee.getBuilding() != null) {
                    customerDTO.setBuilding(employee.getBuilding());
                }
                if (employee.getFloor() != null) {
                    customerDTO.setFloor(employee.getFloor());
                }
                if (employee.getRoom() != null) {
                    customerDTO.setRoom(employee.getRoom());
                }

                if (employee.getOrganization() != null) {
                    customerDTO.setOrg(employee.getOrganization());
                }

                if (employee.getEmail() != null) {
                    customerDTO.setEmailAddress(employee.getEmail());
                }

                customerDTO.setRequestLink(LinkNum);
                customerDTO.setNameCompany(employee.getName());
                customerDTOs.add(customerDTO);
            }
            for (Customer customer : customers) {
                CustomerDTO customerDTO = new CustomerDTO();
                customerDTO.setCustomerId(customer.getId());
                customerDTO.setRequestName(customer.getName());
                customerDTO.setNameCompany(customer.getName() + " , " + customer.getCompany());
                if (customer.getEmail() != null) {
                    customerDTO.setEmailAddress(customer.getEmail());
                }
                LinkNum = "";
                if (customer.getPhone() != null && customer.getMobile() != null) {
                    if (!"".equals(customer.getPhone()) && !"".equals(customer.getMobile())) {
                        LinkNum = customer.getPhone() + " / " + customer.getMobile();
                    } else {
                        LinkNum = customer.getPhone() + customer.getMobile();
                    }
                } else if (customer.getPhone() == null) {
                    LinkNum = customer.getMobile();
                } else if (customer.getMobile() == null) {
                    LinkNum = customer.getPhone();
                }
                customerDTO.setRequestLink(LinkNum);
                customerDTOs.add(customerDTO);
            }
            return customerDTOs;
        }
    }

    @Override
    public WorkOrder findRequirementToSaveWorkOrder(WorkOrder order, Requirement requirement) {
        order.setRequirement(requirement);
        order.setRequestName(requirement.getRequestName());
        order.setContactPhone(requirement.getContactPhone());
        if (requirement.getCustomer() != null) {
            try {
                Customer customer = customerRepository.findOne(requirement.getCustomer().getId());
                order.setRequestName(customer.getName());
                order.setContactPhone(customer.getPhone() + "/" + customer.getMobile());
            } catch (Exception e) {
            }
        }

        order.setDescription(requirement.getDescription());
        return order;
    }

    /**
     * 相似工单检测
     *
     * @param dto
     * @return
     */
    @Override
    public List<WorkOrder> checkLikeRequirementWorkOrder(WorkOrderDTO dto) {

        List<WorkOrder> likeWos = new ArrayList<WorkOrder>();

//        WorkOrder order = new WorkOrder();
//        int wopsCount = 0;
//        int flag = -1;
//        //需求
//        Requirement requirement = dto.getRequirement();
//
//        if (dto.getServiceTypes() != null) {
//            for (ServiceTypesDTO serviceTypesDto : dto.getServiceTypes()) {
//                flag += 1;
//
//                // 匹配流程id
//                if (dto.getWoProcesss() != null && dto.getWoProcesss().get(wopsCount) != null) {
//                    WorkOrderProcess orderProcess = new WorkOrderProcess();
//                    orderProcess.setId(dto.getWoProcesss().get(wopsCount).getId());
//                    wopsCount = wopsCount + 1;
//                    dto.setWorkOrderProcess(orderProcess);
//                }
//                order = requirementTOWorkorder(serviceTypesDto, dto, requirement, flag);
//
//                //相似工单
//                try {
//                    likeWos = sworkOrderService.findLikeWorkOrder(order);
//                    // 相似处理
//                    if (likeWos != null && likeWos.size() > 0) {
//                        List<Long> woIds = new ArrayList<Long>();
//                        for (WorkOrder wo : likeWos) {
//                            woIds.add(wo.getId());
//                        }
//                        likeWos = workOrderRepository.findByIdIn(woIds.toArray(new Long[woIds.size()]));
//                        buildLikeWoLazyData(likeWos);
//                        break;
//                    }
//                } catch (Exception e) {
//                    log.error(e.getMessage());
//                }
//            }
//        }
        return likeWos;
    }

//    @XiaTransactional(readOnly = true)
//    private List<WorkOrder> buildLikeWoLazyData(List<WorkOrder> wos) {
//        if (wos != null && wos.size() > 0) {
//            List<Long> ids = new ArrayList<Long>();
//            for (WorkOrder wo : wos) {
//                if (wo.getRequirement() != null) {
//                    ids.add(wo.getRequirement().getId());
//                }
//            }
//            if (ids.size() > 0) {
//                List<Requirement> reqTypes = requirementRepository.findByIdIn(ids.toArray(new Long[ids.size()]));
//                Map<Long, Requirement> reqTM = new HashMap<Long, Requirement>();
//                for (Requirement rt : reqTypes) {
//                    reqTM.put(rt.getId(), rt);
//                }
//                for (WorkOrder wo : wos) {
//                    if (wo.getRequirement() != null) {
//                        wo.setRequirement(reqTM.get(wo.getRequirement().getId()));
//                    }
//                }
//            }
//        }
//        return wos;
//    }


    /**
     * 需求及工单创建
     *
     * @param dto
     * @return
     */
    @Override
    public List<WorkOrder> createRequirementWorkOrder(WorkOrderDTO dto) {

        WorkOrder order = new WorkOrder();
        int wopsCount = 0;
        int flag = -1;
        String relatedWorkOrders = "";
        List<WorkOrder> workOrders = new ArrayList<WorkOrder>();
        String operateUserName = ((XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal()).getName();
        //保存需求
        dto.setStillSubmitWithoutLikeWo(true);
        Requirement requirement = null;
        if (dto.getRequirement().getId() != null) {
            requirement = checkWorkOrderRequirement(dto);
        } else {
            requirement = saveWorkOrderRequirement(dto);
        }
        //工单编号拼接
        StringBuilder orderCode = new StringBuilder();

        if (dto.getServiceTypes() != null) {
            for (ServiceTypesDTO serviceTypesDto : dto.getServiceTypes()) {
                flag += 1;

                // 匹配流程id
                if (dto.getWoProcesss() != null && dto.getWoProcesss().get(wopsCount) != null) {
                    WorkOrderProcess orderProcess = new WorkOrderProcess();
                    orderProcess.setId(dto.getWoProcesss().get(wopsCount).getId());
                    wopsCount = wopsCount + 1;
                    dto.setWorkOrderProcess(orderProcess);
                }

                order = requirementTOWorkorder(serviceTypesDto, dto, requirement, flag);

                //工单创建
                order = createWorkOrder(order, operateUserName);

                if(null != order){
                    if(orderCode.length() == 0){
                        orderCode.append(order.getCode());
                    }else{
                        orderCode.append("," + order.getCode());
                    }
                }

                if (requirement == null) {
                    //pictures and attachments
                    List<Picture> pictures = dto.getPictures();
                    if (CollectionUtils.isNotEmpty(pictures)) {
                        List<Long> id = getIDList(pictures);

                        List<XiaFile> filesInDB = xiaFileRepository.findAll(id);
                        String pkId = String.valueOf(order.getId());
                        for (XiaFile file : filesInDB) {
                            file.setpKeyId(pkId);
                            file.setTableName(WorkOrder.class.getSimpleName());
                        }
                        xiaFileRepository.save(filesInDB);
                    }
                    List<Attachment> attachments = dto.getAttachments();
                    if (CollectionUtils.isNotEmpty(attachments)) {
                        List<Long> id = getIDList(attachments);

                        List<XiaFile> filesInDB = xiaFileRepository.findAll(id);
                        String pkId = String.valueOf(order.getId());
                        for (XiaFile file : filesInDB) {
                            file.setpKeyId(pkId);
                            file.setTableName(WorkOrder.class.getSimpleName());
                        }
                        xiaFileRepository.save(filesInDB);
                    }
                }

                if (dto.getEquipments() != null) {
                    saveWorkOrderEquipment(order, dto, serviceTypesDto, "1");
                }
                if ("".equals(relatedWorkOrders)) {
                    relatedWorkOrders = "" + order.getId() + "";
                } else {
                    relatedWorkOrders += "," + order.getId();
                }
                workOrders.add(order);
            }
        }

        String desc = "";

        if(null != requirement){
            desc = XiaMesssageResource.getMessage("page.service004.message.createwo").replace("F1",orderCode);
//            desc = "需求创建了工单，工单号"+orderCode;
        }

        //处理过程
        requirementActivityService.addRequirementActivity(requirement, RequirementActivity.ActivityType.CREATE_WO, desc);

        if(requirementNoticeSwitchService.isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch.CREATEWO)){
            wechatMessageService.sendMessage(requirement, "您的需求创建了工单，工单号"+orderCode+"！");
        }

        if (dto.getResultId() != null) {
            PatrolTaskSpotResult ptsr = patrolTaskSpotResultRepository.findResultById(dto.getResultId());
            Set<WorkOrder> workOrderSet;
            if (ptsr.getWorkOrders() != null) {
                workOrderSet = ptsr.getWorkOrders();
                workOrderSet.add(order);
            } else {
                workOrderSet = new HashSet<WorkOrder>();
                workOrderSet.add(order);
            }
            ptsr.setWorkOrders(workOrderSet);
            // 异常项创建工单后修改异常项处理结果 --> 已处理
            if(ptsr.getStatus() != null && ptsr.getStatus() == PatrolTaskSpot.PatrolSpotStatus.STATUS_ABNORMAL){
                ptsr.setAbnormalPatrolResult(PatrolTaskSpotResult.AbnormalPatrolResult.STATUS_PROCESSED);
            }
            ptsr.setAbnormalPatrolResult(PatrolTaskSpotResult.AbnormalPatrolResult.STATUS_PROCESSED);
            // 异常处理方式=报障
            ptsr.setOperateType(PatrolTaskSpotResult.OperateType.SUBMIT_REQUEST);
            XiaPrincipal user = (XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal();
            if (null != user) {
                if(SUPER_ADMIN.equals(user.getId())){
                    ptsr.setOperator(user.getRealName());
                    ptsr.setOperatedDate(new Date());
                } else {
                    Employee em = employeeRepository.findOneByUserId(user.getId());
                    if (null != em) {
                        ptsr.setOperator(em.getName());
                        ptsr.setOperatedDate(new Date());
                    }
                }
            }
            patrolTaskSpotResultRepository.save(ptsr);

            //如果有巡检任务（巡检报障），维护巡检任务冗余字段
            PatrolTaskSpotResult patrolTaskSpotResult = patrolTaskSpotResultRepository.findResultWithTaskById(ptsr.getId());
            if(patrolTaskSpotResult!=null &&
                    patrolTaskSpotResult.getPatrolTaskSpot() != null &&
                    patrolTaskSpotResult.getPatrolTaskSpot().getPatrolTask() != null ){
                patrolTaskItemService.udatePatrolTaskStatisticalProperties(patrolTaskSpotResult.getPatrolTaskSpot().getPatrolTask().getId());
            }

        }

        // 发送socket提示刷新
//        if (null != workOrders && workOrders.size() > 0) {
//            workOrderSocketService.newWorkOrderSocket(workOrders.size());
//        }

        return workOrders;
    }

    private WorkOrder requirementTOWorkorder(ServiceTypesDTO serviceTypesDto, WorkOrderDTO dto, Requirement requirement, int flag) {
        WorkOrder order = null;
        // 获得多个服务请求
        ServiceType serviceType = new ServiceType();
        serviceType.setId(serviceTypesDto.getServiceTypeId());
        dto.setServiceType(serviceType);
        // 获得多个优先级
        Priority priority = new Priority();
        priority.setId(serviceTypesDto.getPriorityId());
        dto.setPriority(priority);

        order = getWorkOrder(dto);
        //工单关联需求员工id
        //order.setRequestId(dto.getRequestId()==null?-1L:dto.getRequestId());
        order.setRequestId(dto.getRequestId());
        if (requirement != null) {
            order = findRequirementToSaveWorkOrder(order, requirement);
            //Shang-20170918-01 通过需求创建工单后时，显示创建工单时的描述，而不是创建需求时的描述
            order.setDescription(dto.getRequirement().getDescription());
        } else {
            order = findRequirementToSaveWorkOrder(order, dto.getRequirement());
            order.setRequirement(null);
        }

        if (dto.getSource() != null) {
            for (int n = flag; n < dto.getSource().size(); n++) {
                String type = dto.getSource().get(n);
                if ("SELFCHECK".equals(type)) {
                    order.setType(WorkOrder.WorkOrderType.SELFCHECK);
                } else {
                    order.setType(WorkOrder.WorkOrderType.ONDEMAND);
                }
                break;
            }
        }

        //巡检报障信息
        if (dto.getwFlag() != null) {
            if ("false".equals(dto.getwFlag())) {
                order.setType(WorkOrder.WorkOrderType.SELFCHECK);
                order.setRequirement(null);
                order.setContactPhone(dto.getPhone());
                order.setRequestName(dto.getRequestor());
                order.setDescription(dto.getDescription());
            }
        }
        return order;
    }


    /**
     * 1. requirement contains work order and work order status in 已创建、处理中、暂停、已发布
     * <br/>
     * 2. contains more than one work order and has one which status in 已创建、处理中、暂停、已发布
     * <br />
     * return false
     * <br />
     * add approval status for validation fail
     *
     * @param id
     */
    @Override
    public boolean validateForFinish(Long id) {
        if (id == null) {
            return true;
        }

        WorkOrder.WorkOrderStatus[] statuses = {WorkOrder.WorkOrderStatus.CREATE
                                , WorkOrder.WorkOrderStatus.PROCESS
                                , WorkOrder.WorkOrderStatus.STOP
                                , WorkOrder.WorkOrderStatus.STOP_N
                                , WorkOrder.WorkOrderStatus.DISPATCHE
                                , WorkOrder.WorkOrderStatus.APPROVE
                                , WorkOrder.WorkOrderStatus.TERMINATE
                                , WorkOrder.WorkOrderStatus.FINISH
                 };        
        List<WorkOrder.WorkOrderStatus> statusList = Arrays.asList(statuses);
        List<WorkOrder> workOrders = requirementRepository.findWorkOrderById(id);
        if (CollectionUtils.isEmpty(workOrders)) {
            return true;
        }
        for (WorkOrder workOrder : workOrders) {
            if (statusList.contains(workOrder.getStatus())) {
                return false;
            }
        }

        return true;
    }


    /**
     * update picture in requirement
     *
     * @param files
     * @param requirement
     */
    private List<? extends XiaFile> updateFile(List<? extends XiaFile> files, Requirement requirement) {
        List<Long> id = getIDList(files);

        List<XiaFile> filesInDB = xiaFileRepository.findAll(id);

        String pkId = String.valueOf(requirement.getId());
        for (XiaFile file : filesInDB) {
            file.setpKeyId(pkId);
            file.setTableName(WorkOrderConstant.REQUIREMENT_TABLE_NAME);
        }
        return xiaFileRepository.save(filesInDB);
    }

    /**
     * get id list
     *
     * @param files
     * @return
     */
    private List<Long> getIDList(List<? extends XiaFile> files) {
        List<Long> ids = new ArrayList<>();
        for (XiaFile file : files) {
            Long id = file.getId();
            ids.add(id);
        }
        return ids;
    }

    @Override
    public Requirement requirementCheck(RequirementCheckDTO dto) {
        Requirement requirement = requirementRepository.findOne(dto.getReqId());
        RequirementActivity activity = new RequirementActivity();
        activity.setActivityType(RequirementActivity.ActivityType.CHECK);

        if(dto.getIsPass()==0){//不通过
            requirement.setStatus(RequirementStatus.CANCEL);
            activity.setRequirement(requirement);
            activity.setDescription(XiaMesssageResource.getMessage("server.check.notPass", null, dto.getReason()));
            if(requirementNoticeSwitchService.isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch.UNAPPROVE)){
                wechatMessageService.sendMessage(requirement, "您的需求审核未通过！");
            }
        }else{//通过
            requirement.setStatus(RequirementStatus.PROCESS);
            activity.setRequirement(requirement);
            activity.setDescription(XiaMesssageResource.getMessage("server.check.pass",null, dto.getReason()));
            if(requirementNoticeSwitchService.isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch.APPROVE)){
                wechatMessageService.sendMessage(requirement, "您的需求审核通过！");
            }
        }
        requirementActivityRepository.save(activity);
        return requirementRepository.save(requirement);
    }
}
