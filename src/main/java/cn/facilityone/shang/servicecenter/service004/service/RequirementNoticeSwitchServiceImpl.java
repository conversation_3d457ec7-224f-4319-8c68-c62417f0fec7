package cn.facilityone.shang.servicecenter.service004.service;

import cn.facilityone.shang.entity.servercenter.RequirementNoticeSwitch;
import cn.facilityone.shang.servicecenter.common.repository.RequirementNoticeSwitchRepositroy;
import cn.facilityone.shang.servicecenter.service004.dto.RequirementNoticeSwitchDto;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Author：panda.yang
 * CreateTime：2016/9/29 17:48
 */
@Service
public class RequirementNoticeSwitchServiceImpl implements RequirementNoticeSwitchService{

    @Autowired
    private RequirementNoticeSwitchRepositroy requirementNoticeSwitchRepositroy;

    @Override
    @XiaTransactional(readOnly = false)
    public void saveRequirementNoticeSwitch(int index, int switchs) {
        RequirementNoticeSwitch.NoticeSwitch noticeSwitch = RequirementNoticeSwitch.NoticeSwitch.valueOf(index);
        Long id = ProjectContext.getCurrentProject();
        RequirementNoticeSwitch requirementNoticeSwitch = requirementNoticeSwitchRepositroy.getRequirementNoticeSwitchByStatus(index, id);
        /**
         * 不存在就新增
         */
        if(null != requirementNoticeSwitch){
            requirementNoticeSwitch.setSwitchs(switchs);
            requirementNoticeSwitchRepositroy.save(requirementNoticeSwitch);
        }else{
            requirementNoticeSwitch = new RequirementNoticeSwitch();
            requirementNoticeSwitch.setSwitchs(switchs);
            requirementNoticeSwitch.setStatus(noticeSwitch);
            requirementNoticeSwitchRepositroy.save(requirementNoticeSwitch);
        }
    }

    @Override
    public List<RequirementNoticeSwitchDto> queryRequirementNoticeSwitchList(){
        List<RequirementNoticeSwitchDto> dtoList = new ArrayList<RequirementNoticeSwitchDto>();
        RequirementNoticeSwitchDto requirementNoticeSwitchDto = null;
        List<RequirementNoticeSwitch> requirementNoticeSwitches = requirementNoticeSwitchRepositroy.findAll();
        if(!CollectionUtils.isEmpty(requirementNoticeSwitches)){
            for(RequirementNoticeSwitch requirementNoticeSwitch : requirementNoticeSwitches){
                requirementNoticeSwitchDto = new RequirementNoticeSwitchDto();
                requirementNoticeSwitchDto.setNoticeSwitch(requirementNoticeSwitch);
                requirementNoticeSwitchDto.setStatusName(requirementNoticeSwitch.getStatus().name().toLowerCase());
                dtoList.add(requirementNoticeSwitchDto);
            }
        }
        return dtoList;
    }

    @Override
    public boolean isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch noticeSwitch) {
        boolean isflag = false;
        RequirementNoticeSwitch requirementNoticeSwitch = requirementNoticeSwitchRepositroy.getRequirementNoticeSwitchByStatus(noticeSwitch.ordinal(), ProjectContext.getCurrentProject());
        if(null != requirementNoticeSwitch){
            int switchs = requirementNoticeSwitch.getSwitchs();
            if(switchs == 1){
                isflag = true;
            }
        }
        return isflag;
    }

}
