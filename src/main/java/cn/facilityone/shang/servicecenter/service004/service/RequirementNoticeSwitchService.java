package cn.facilityone.shang.servicecenter.service004.service;

import cn.facilityone.shang.entity.servercenter.RequirementNoticeSwitch;
import cn.facilityone.shang.servicecenter.service004.dto.RequirementNoticeSwitchDto;

import java.util.List;

/**
 * Author：panda.yang
 * CreateTime：2016/9/29 17:48
 */
public interface RequirementNoticeSwitchService {

    void saveRequirementNoticeSwitch(int index, int switchs);

    List<RequirementNoticeSwitchDto> queryRequirementNoticeSwitchList();

    /**
     * 是否开启发送
     */
    boolean isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch noticeSwitch);

}
