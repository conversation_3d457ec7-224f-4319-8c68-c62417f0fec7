package cn.facilityone.shang.workorder.common.builder;

import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.entity.organize.*;
import cn.facilityone.shang.entity.workorder.Priority;
import cn.facilityone.shang.entity.workorder.ServiceType;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderLaborer;
import cn.facilityone.shang.servicecenter.common.dao.WorkOrderLaborerDao;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class WorkOrderBuilder {

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private WorkOrderLaborerDao workOrderLaborerDao;

    private static final ThreadLocal<List<WorkOrder>> workOrderList = new ThreadLocal<>();
    private static final ThreadLocal<List<Long>> workOrderIdList = new ThreadLocal<>();

    public WorkOrderBuilder init(List<WorkOrder> workOrders) {
        workOrderList.set(workOrders);
        List<Long> ids = new ArrayList<>();
        for (WorkOrder workOrder : workOrders) {
            ids.add(workOrder.getId());
        }
        workOrderIdList.set(ids);
        return this;
    }

    @XiaTransactional(readOnly = true)
    public WorkOrderBuilder addServiceType() {
        List<Long> ids = workOrderIdList.get();
        List<WorkOrder> workOrders = workOrderList.get();
        if (ids != null && ids.size() > 0) {
            List<ServiceType> st = workOrderRepository.findServiceTypesByWoIds(ids.toArray(new Long[ids.size()]));
            Map<Long, ServiceType> serviceType = new HashMap<>();
            for (ServiceType t : st) {
                serviceType.put(t.getId(), t);
            }
            for (int i = 0; i < workOrders.size(); i++) {
                if (workOrders.get(i).getServiceType() == null) {
                    workOrders.get(i).setServiceType(null);
                } else {
                    if (workOrders.get(i).getServiceType() != null && workOrders.get(i).getServiceType().getId() != null) {
                        workOrders.get(i).setServiceType(serviceType.get(workOrders.get(i).getServiceType().getId()));
                    }
                }
            }
        }
        return this;
    }

    @XiaTransactional(readOnly = true)
    public WorkOrderBuilder addPriority() {
        List<Long> ids = workOrderIdList.get();
        List<WorkOrder> workOrders = workOrderList.get();
        if (ids != null && ids.size() > 0) {
            List<Priority> st = workOrderRepository.findPrioritiesByWoIds(ids.toArray(new Long[ids.size()]));
            Map<Long, Priority> priority = new HashMap<Long, Priority>();
            for (Priority t : st) {
                priority.put(t.getId(), t);
            }
            for (int i = 0; i < workOrders.size(); i++) {
                if (workOrders.get(i).getPriority() != null) {
                    workOrders.get(i).setPriority(priority.get(workOrders.get(i).getPriority().getId()));
                } else {
                    workOrders.get(i).setPriority(null);
                }
            }
        }
        return this;
    }


    @XiaTransactional(readOnly = true)
    public WorkOrderBuilder addOrganization() {
        List<Long> ids = workOrderIdList.get();
        List<WorkOrder> workOrders = workOrderList.get();
        if (ids != null && ids.size() > 0) {
            List<Organization> st = workOrderRepository.findOrganizationByWoIds(ids.toArray(new Long[ids.size()]));
            Map<Long, Organization> organizations = new HashMap<Long, Organization>();
            for (Organization t : st) {
                organizations.put(t.getId(), t);
            }
            for (int i = 0; i < workOrders.size(); i++) {
                if (workOrders.get(i).getOrganization() != null) {
                    workOrders.get(i).setOrganization(organizations.get(workOrders.get(i).getOrganization().getId()));
                } else {
                    workOrders.get(i).setOrganization(null);
                }
            }
        }
        return this;
    }


    @XiaTransactional(readOnly = true)
    public WorkOrderBuilder addPosition() {
        List<Long> ids = workOrderIdList.get();
        List<WorkOrder> workOrders = workOrderList.get();
        if (ids != null && ids.size() > 0) {
            List<Site> st = workOrderRepository.findSiteByWoIds(ids.toArray(new Long[ids.size()]));
            Map<Long, Site> sites = new HashMap<Long, Site>();
            for (Site t : st) {
                sites.put(t.getId(), t);
            }
            for (int i = 0; i < workOrders.size(); i++) {
                if (workOrders.get(i).getSite() != null) {
                    workOrders.get(i).setSite(sites.get(workOrders.get(i).getSite().getId()));
                } else {
                    workOrders.get(i).setSite(null);
                }
            }

            List<Building> bd = workOrderRepository.findBuildingByWoIds(ids.toArray(new Long[ids.size()]));
            Map<Long, Building> buildings = new HashMap<Long, Building>();
            for (Building t : bd) {
                buildings.put(t.getId(), t);
            }
            for (int i = 0; i < workOrders.size(); i++) {
                if (workOrders.get(i).getBuilding() != null) {
                    workOrders.get(i).setBuilding(buildings.get(workOrders.get(i).getBuilding().getId()));
                } else {
                    workOrders.get(i).setBuilding(null);
                }
            }

            List<Floor> fl = workOrderRepository.findFloorByWoIds(ids.toArray(new Long[ids.size()]));
            Map<Long, Floor> fls = new HashMap<Long, Floor>();
            for (Floor t : fl) {
                fls.put(t.getId(), t);
            }
            for (int i = 0; i < workOrders.size(); i++) {
                if (workOrders.get(i).getFloor() != null) {
                    workOrders.get(i).setFloor(fls.get(workOrders.get(i).getFloor().getId()));
                } else {
                    workOrders.get(i).setFloor(null);
                }
            }

            List<Room> rm = workOrderRepository.findRoomByWoIds(ids.toArray(new Long[ids.size()]));
            Map<Long, Room> rms = new HashMap<Long, Room>();
            for (Room t : rm) {
                rms.put(t.getId(), t);
            }
            for (int i = 0; i < workOrders.size(); i++) {
                if (workOrders.get(i).getRoom() != null) {
                    workOrders.get(i).setRoom(rms.get(workOrders.get(i).getRoom().getId()));
                } else {
                    workOrders.get(i).setRoom(null);
                }
            }
        }
        return this;
    }

    /**
     * @return this after add workorder labours
     */
    @XiaTransactional(readOnly = true)
    public WorkOrderBuilder addLabourList() {

        List<WorkOrder> workOrders = workOrderList.get();
        List<Long> workOrderIds = workOrderIdList.get();

        Map<Long, List<WorkOrderLaborer>> labours = workOrderLaborerDao.findLabourInfoByWorkOrderIds(workOrderIds);
        for (WorkOrder workOrder : workOrders) {
            workOrder.setWorkOrderLaborers(labours.get(workOrder.getId()));
        }

        return this;
    }

    public List<WorkOrder> get() {

        return workOrderList.get();
    }
}
