package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.workorder.WorkOrderTool;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 6/2/2015
 */
public interface WorkOrderToolRepository extends XiaRepository<WorkOrderTool, Long> {

    @Query("select wt from #{#entityName} wt where wt.workOrder.id = ?1")
    List<WorkOrderTool> findByWorkOrderId(Long workOrderId);

    @Modifying
    @Query("delete from #{#entityName} wt where wt.workOrder.id = ?1 and wt.id=?2")
    void deleteByWorkOrderIdAndToolId(Long workOrderId, Long toolId);

    @Modifying
    @Query("delete from #{#entityName} wt where wt.workOrder.id = ?1")
    int deleteAllByWorkOrderId(Long id);

    @Modifying
    @Query("delete from #{#entityName} wt where wt.id in (?1)")
    void deleteByIds(Long[] deletedIds);
}
