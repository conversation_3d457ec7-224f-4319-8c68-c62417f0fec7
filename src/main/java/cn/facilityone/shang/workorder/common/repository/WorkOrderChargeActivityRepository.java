package cn.facilityone.shang.workorder.common.repository;


import cn.facilityone.shang.entity.workorder.WorkOrderChargeActivity;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface WorkOrderChargeActivityRepository extends XiaRepository<WorkOrderChargeActivity, Long> {

    @Query("select d from #{#entityName} d where d.workOrder.id = ?1")
    List<WorkOrderChargeActivity> findByWorkOrderId(Long workOrderId);

    @Query("select d from #{#entityName} d where d.workOrder.id = ?1 and d.activityType = 0")
    List<WorkOrderChargeActivity> findByWoId(Long workOrderId);
}
