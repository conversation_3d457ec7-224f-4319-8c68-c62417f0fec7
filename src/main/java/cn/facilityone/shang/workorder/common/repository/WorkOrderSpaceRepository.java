package cn.facilityone.shang.workorder.common.repository;


import cn.facilityone.shang.entity.workorder.WorkOrderSpace;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface WorkOrderSpaceRepository  extends XiaRepository<WorkOrderSpace, Long> {

    @Query("select ws from #{#entityName} ws  left join fetch ws.building  left join fetch ws.room left join fetch ws.floor left join fetch ws.preventiveMaintenance wpm  left join fetch ws.workOrder wo where wo.id=?1")
    List<WorkOrderSpace> findspaceByWoId(Long WoId);


    @Query("select ws from #{#entityName} ws  left join fetch ws.building  bd left join fetch ws.room left join fetch ws.floor left join fetch ws.workOrder wo where wo.id=?1 and bd.id=?2")
    List<WorkOrderSpace> findByWorkOrderIdAndbuildingId(Long woId,Long buildingId);

    @Query("select ws from #{#entityName} ws  left join fetch ws.building  bd left join fetch ws.room left join fetch ws.floor fl left join fetch ws.workOrder wo where wo.id=?1 and fl.id=?2")
    List<WorkOrderSpace> findByWorkOrderIdAndfloorId(Long woId,Long floorId);

    @Query("select ws from #{#entityName} ws  left join fetch ws.building  bd left join fetch ws.room  rom left join fetch ws.floor left join fetch ws.workOrder wo where wo.id=?1 and rom.id=?2")
    List<WorkOrderSpace> findByWorkOrderIdAndroomId(Long woId,Long roomId);

    @Query("select ws from #{#entityName} ws  left join fetch ws.building  left join fetch ws.room left join fetch ws.floor left join fetch ws.workOrder wo where wo.id=?1 and ws.id=?2")
    WorkOrderSpace findEntireSpaceById(Long spaceId);

    @Modifying
    @Query("delete from #{#entityName} sp where sp.workOrder.id=?1 ")
    void deleteSpace(Long woId);


    @Query("SELECT COUNT(ws.workOrder) from #{#entityName} ws where ws.workOrder.status  NOT IN  (7) and  ws.building.id =?1")
    int findUnWosByBuilding(Long id);

    @Query("SELECT COUNT(ws.workOrder) from #{#entityName} ws where ws.workOrder.status  NOT IN  (7) and  ws.floor.id =?1  ")
    int findUnWosByFloor(Long id);

    @Query("SELECT COUNT(ws.workOrder) from #{#entityName} ws where ws.workOrder.status  NOT IN  (7) and  ws.room.id =?1 ")
    int findUnWosByRoom(Long id);



}
