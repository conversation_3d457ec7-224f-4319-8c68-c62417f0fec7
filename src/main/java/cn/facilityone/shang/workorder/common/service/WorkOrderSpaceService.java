package cn.facilityone.shang.workorder.common.service;


import cn.facilityone.shang.entity.organize.Building;
import cn.facilityone.shang.entity.organize.Floor;
import cn.facilityone.shang.entity.organize.Room;
import cn.facilityone.shang.entity.workorder.WorkOrderSpace;

import java.util.Map;

public interface WorkOrderSpaceService {

    String isSpaceExisted(Long spaceId, Long workOrderId,Long type);

    Building findEntireBuilding(Long buildingIf);


    Floor findEntireFloor(Long FloorId);


    Map findEntireRoom(Long RoomId);

}
