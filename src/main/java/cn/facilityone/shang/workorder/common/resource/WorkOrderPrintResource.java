package cn.facilityone.shang.workorder.common.resource;

import cn.facilityone.shang.common.configuration.properties.CompanyProperties;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.servercenter.FollowUp;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderTool;
import cn.facilityone.shang.epayment.epayment001.service.EPaymentManageService;
import cn.facilityone.shang.projects.pro002.service.ProjectService;
import cn.facilityone.shang.servicecenter.common.service.WorkOrderLaborerService;
import cn.facilityone.shang.servicecenter.service001.service.RequirementCenterService;
import cn.facilityone.shang.stock.stock035.service.MaterialReserveService;
import cn.facilityone.shang.workorder.common.repository.WorkOrderToolRepository;
import cn.facilityone.shang.workorder.common.service.*;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wayne.fu
 * @Date: 8/7/2015
 */

@Path("/wo000/print")
public class WorkOrderPrintResource {

    private static final String TEMPLATE_EA_PATH = "/business/workorder/wo000-print.ftl";

    private static final String TEMPLATE_EA_PATH_NEW = "/business/workorder/wo000-print-new.ftl";

    @Autowired
    private CompanyProperties company;

    @Autowired
    private RequirementCenterService requirementCenterService;

    @Autowired
    private WorkOrderMaterialService workOrderMaterialService;

    @Autowired
    private WorkOrderEquipmentService workOrderEquipmentService;

    @Autowired
    private WorkOrderLaborerService workOrderLaborerService;

    @Autowired
    private WorkOrderTeamService workOrderTeamService;

    @Autowired
    private MaterialReserveService materialReserveService;
    @Autowired
    private ProjectService projectService;

    @Autowired
    private WorkOrderPrintService workOrderPrintService;

    @Autowired
    private WorkOrderActivityService workOrderActivityService;

    @Autowired
    private EPaymentManageService ePaymentManageService;

    @Autowired
    private WorkOrderToolRepository workOrderToolRepository;

    @GET
    @Path("{id}")
    @Produces(MediaType.TEXT_HTML)
    @Template(name = TEMPLATE_EA_PATH)
    public Map<String, Object> print(@PathParam("id") Long id) {

        WorkOrder workOrder = requirementCenterService.findWorkOrderByIDWithAllInfo(id);

        boolean ppmType = false;
        if (workOrder.getType() == WorkOrder.WorkOrderType.PPM) {
            ppmType = true;
        }
        // 工作内容换行显示
        String workContent = workOrder.getWorkContent();
        if (workContent != null) {
            workOrder.setWorkContent(workContent.replace("\n","<br>"));
        }
        Map<String, Object> map = new HashMap<String, Object>();

        if (ppmType) {
            map.put("ppm", true);
            map.put("ppmInfo", workOrderPrintService.getPMStepByWorkOrder(workOrder));
            map.put("ppmLocation", workOrderPrintService.getPMSpaceByWorkOrder(workOrder));
            if (null != workOrder.getRequestId()) {
                workOrder = workOrderPrintService.getRequestorInfo(workOrder);
            }
        }

        //工具
        List<WorkOrderTool> workOrderTools=workOrderToolRepository.findByWorkOrderId(workOrder.getId());
        map.put("workOrderTools",workOrderTools);

        map.put("i18nLocale", XiaMesssageResource.getLocaleString());

        map.put("workOrder", workOrder);

        map.put("company", company);

        map.put("projectName",projectService.findUserCurrentProjectName());

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.FORMAT_PATTERN_DATE);
        map.put("nowDate", sdf.format(new Date()));

//        map.put("materials", workOrderMaterialService.findMaterialDTOByWorkOrderId(id));
        map.put("materials", materialReserveService.findAllOrderQueryBywoId(id));

        map.put("equipments", workOrderEquipmentService.findWorkOrderEquipmentByOrderId(id));

        map.put("laborers", workOrderLaborerService.findByWorkOrderIDAndStatue(id));

        map.put("checkers", workOrderTeamService.findWorkOrderCheckers(id));

        map.put("satisfactionDegree", requirementCenterService.findDegreeAll());

        map.put("followUpinfo", requirementCenterService.findFollowUpInfo(id));

        map.put("chargeDetails", workOrderActivityService.findActivitysFor(id));

        map.put("paymentOrderList",ePaymentManageService.getPaymentOrderList(id));

        Long requirementId = workOrder.getRequirement() == null ? null : workOrder.getRequirement().getId();
        FollowUp followUp = requirementCenterService.findFollowUpInfo(requirementId);
        map.put("degree", followUp == null ? null : followUp.getDegree());
        return map;
    }

    @GET
    @Path("new/{id}")
    @Produces(MediaType.TEXT_HTML)
    @Template(name = TEMPLATE_EA_PATH_NEW)
    public Map<String, Object> printNew(@PathParam("id") Long id) {

        Map<String, Object> map = new HashMap<String, Object>();

        WorkOrder workOrder = requirementCenterService.findWorkOrderByIDWithAllInfo(id);
        //工单
        map.put("workOrder", workOrder);
        //执行人
        map.put("laborer", workOrderLaborerService.findLaborerForPrintByWoId(id));
        //接单人
        map.put("acceptLaborer", workOrderLaborerService.findAcceptLaborerForPrintByWoId(id));
        //项目名称
        map.put("projectName", projectService.findUserCurrentProjectName());
        //维修记录
        map.put("reserveInfo", materialReserveService.findAllReserveMaterialByWoIdAndStatus(id));

        Long requirementId = workOrder.getRequirement() == null ? null : workOrder.getRequirement().getId();
        if (null != requirementId) {
            //评价
            map.put("evaluation", requirementCenterService.findEvaluationInfo(requirementId));
        }
        return map;
    }
}
