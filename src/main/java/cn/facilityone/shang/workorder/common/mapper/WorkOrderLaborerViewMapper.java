package cn.facilityone.shang.workorder.common.mapper;
import cn.facilityone.shang.workorder.common.alias.WorkOrderLaborerView;
import cn.facilityone.xia.persistence.mybatis.XiaMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface WorkOrderLaborerViewMapper extends XiaMapper<WorkOrderLaborerView>{

    /**
     * 根据工作组id、项目Id、需派工日期    获取当前工作组中 可派工人员信息
     * @param workTeamId
     * @param projectId
     * @param date   只须精确到天（如：2015-09-16）
     * @return
     */
    List<WorkOrderLaborerView> findByprojectIdAndWorkTeamId(@Param("workTeamId") Long workTeamId,
                                                            @Param("projectId") Long projectId ,
                                                            @Param("date") Date date);


    Long findByProjectWorkHoursCM(@Param("proId") Long proId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    Long findByProjectWorkHoursPPM(@Param("proId") Long proId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}