package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.workorder.EscalationActivity;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 6/1/2015
 */
public interface EscalationActivityRepository extends XiaRepository<EscalationActivity, Long> {

    @Query("select e from #{#entityName} e where e.workOrder.id = ?1")
    List<EscalationActivity> findByWorkOrderId(Long workOrderId);

    @Query("select e from #{#entityName} e left join fetch e.escalation where e.workOrder.id = ?1")
    List<EscalationActivity> findWithEscalationByWorkOrderId(Long workOrderId);
}
