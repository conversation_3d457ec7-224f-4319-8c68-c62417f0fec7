package cn.facilityone.shang.workorder.common.constant;

import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.workorder.ApprovalExtra;
import cn.facilityone.shang.entity.workorder.WorkOrder;

/**
 * @Author: wayne.fu
 * @Date: 5/25/2015
 */
public class WorkOrderConstant {

    public static final String SQL_LIKE_CHAR = "%";
    public static final String DATE_RANGE_SPLIT_CHAR = "~";

    public static final String REQUIREMENT = "requirement";

    public static final String CUSTOMER = "customer";
    public static final String CUSTOMER_NAME = "name";
    public static final String CUSTOMER_PHONE = "phone";

    public static final String REQUEST_NAME = "requestName";
    public static final String CONTACT_PHONE = "contactPhone";

    public static final String REQUIREMENT_TYPE = "requirementType";
    public static final String REQUIREMENT_TYPE_NAME = "name";

    public static final String DELETE = "deleted";

    public static final String ID = "id";
    public static final String COMMON_ID = "id";

    public static final String SERVICE_TYPE = "serviceType";
    public static final String SERVICE_TYPE_ID = "id";
    public static final String SERVICE_TYPE_NAME = "name";
    public static final String SERVICE_TYPE_FULL_NAME = "fullName";

    public static final String ESTIMATED_WORKING_TIME = "estimatedWorkingTime";
    public static final String INVENTORY_ACTIVITY = "inventoryActivity";
    public static final String STOP_TIME = "stopTime";

    public static final String WORKORDER_PROCESS = "workOrderProcess";
    public static final String WORKORDER_PROCESS_ID = "id";
    public static final String WORKORDER_PROCESS_PROP_SERVICE_TYPE = "serviceType";
    public static final String WORKORDER_PROCESS_PROP_SITE = "site";
    public static final String WORKORDER_PROCESS_PROP_BUILDING = "building";
    public static final String WORKORDER_PROCESS_PROP_FLOOR = "floor";
    public static final String WORKORDER_PROCESS_PROP_ROOM = "room";
    public static final String WORKORDER_PROCESS_PROP_ORGANIZATION = "organization";
    public static final String WORKORDER_PROCESS_PROP_PRIORITY = "priority";
    public static final String WORKORDER_PROCESS_PROP_WORK_TEAM = "workTeams";
    public static final String WORKORDER_PROCESS_PROP_WORK_ORDER_TYPE = "type";

    public static final String PRIORITY = "priority";
    public static final String PRIORITY_ID = "id";

    public static final String ORGANIZATION = "organization";
    public static final String ORGANIZATION_ID = "id";

    public static final String SITE = "site";
    public static final String SITE_ID = "id";

    public static final String BUILDING = "building";
    public static final String BUILDING_ID = "id";

    public static final String ROOM = "room";
    public static final String ROOM_ID = "id";

    public static final String FLOOR = "floor";
    public static final String FLOOR_ID = "id";

    public static final String LABORERS = "laborers";
    public static final String LABORER = "laborer";
    public static final String LABORER_ID = "id";

    public static final String WORK_TEAM = "workTeam";
    public static final String WORK_TEAMS = "workTeams";
    public static final String WORK_TEAM_ID = "id";

    public static final String IS_RESP_ESCALATION = "isRespEscalation";
    public static final String IS_COM_ESCALATION = "isComEscalation";
    public static final String STATUS = "status";
    public static final String STATUS_SITUATION = "statusSituation";
    public static final String SOURCE = "source";
    public static final String TYPE = "type";

    public static final String ACTUAL_COMPLETE_DATETIME = "actualCompletionDateTime";
    public static final String CREATED_DATE = "createdDate";
    public static final String WORKORDEREQUIPMENTS = "workOrderEquipments";
    public static final String WORKORDEREQUIPMENTS_EQ_ID = "equipmentId";
    public static final String EQUIPMENT = "equipment";
    public static final String EQUIPMENT_ID = "id";
    public static final String EQUIPMENTSYSTEM = "equipmentSystem";
    public static final String EQUIPMENTSYSTEM_ID = "id";
    public static final String PM = "pm";
    public static final String PREVENTIVEMAINTENANCE = "preventiveMaintenance";
    public static final String PREVENTIVEMAINTENANCE_ID = "id";
    public static final String PM_NAME = "name";
    public static final String PM_PERIOD = "period";

    public static final String PREVIOUS_STATUS = "previousStatus";

    public static final String WORKORDER_NO = "code";
    public static final String WORK_CONTENT = "workContent";

    public static final int OPERATION_PAUSE = 1;
    public static final int OPERATION_FINISH = 2;
    public static final int OPERATION_TERMINATE = 3;
    public static final int OPERATION_SAVE = 4;
    public static final int OPERATION_PRINT = 5;
    public static final int OPERATION_VALIDATE = 6;
    public static final int OPERATION_CLOSE = 7;
    public static final int OPERATION_APPROVAL_REQUEST = 8;
    public static final int OPERATION_DISPATCH = 9;
    public static final int OPERATION_CONTINUE = 10;
    public static final int OPERATION_PAUSE_CONTINUE_TRUE = 11;
    public static final int OPERATION_PAUSE_CONTINUE_FALSE = 12;

    public static final int CONTINUE_CHOICE_TRUE = 1;
    public static final int CONTINUE_CHOICE_FALSE = 2;

    public static final String APPROVAL_EXTRA_PROP_EXTRA_COLUMN = "extraColumn";

    public static final String APPROVAL_TEMPLATE_TABLE_NAME = ApprovalExtra.class.getSimpleName();
    public static final String WORKORDER_TABLE_NAME = WorkOrder.class.getSimpleName();
    public static final String APPROVAL_TABLE_NAME = ApprovalExtra.class.getSimpleName();
    public static final String APPROVAL_TABLE_NAME_MOBILE = ApprovalExtra.class.getSimpleName()+"_MOBILE";
    public static final String REQUIREMENT_TABLE_NAME = Requirement.class.getSimpleName();

    public static final String APPROVAL_PROP_APPROVAL_TEMPLATE = "template";
    public static final String APPROVAL_PROP_PARAMS = "params";
    public static final String APPROVAL_PROP_WORKORDER = "workOrder";
    public static final String APPROVAL_PROP_REQUESTOR = "requestor";
    public static final String APPROVAL_PROP_RESULT = "result";

    public static final String APPROVAL_RESULT_PROP_APPROVER = "approver";
    public static final String APPROVAL_RESULT_PROP_RESULT = "result";

    public static final String WORK_ORDER_ACTIVITY_PROP_WORK_ORDER = "workOrder";
    public static final String WORK_ORDER_ACTIVITY_PROP_TYPE = "activityType";
    public static final String WORK_ORDER_ACTIVITY_PROP_ID = "id";

    public static final String WORK_ORDER_PROP_STATUS = "status";

    public static final String[] STATUS_COM = {WorkOrder.WorkOrderStatus.CLOSE.name()};

    public static final String WORKORDER_LABORERS = "workOrderLaborers";
    public static final String WORKORDER_LABORERS_PROP_LABORER = "laborer";
    public static final String WORKORDER_LABORERS_PROP_STATUS = "status";

    public static final String PRIORITIES = "prioritys";
    public static final String PRIORITY_NAME = "name";
    public static final String DESCRIPTION = "description";

    public static final String PRIORITIE = "priority";

    public static final int DISPATCHING_TYPE_AUTO = 1;
    public static final int DISPATCHING_TYPE_SMART = 2;
    public static final int DISPATCHING_TYPE_MANUAL = 3;
    public static final String ACTUAL_WORKING_TIME = "actualWorkingTime";
}
