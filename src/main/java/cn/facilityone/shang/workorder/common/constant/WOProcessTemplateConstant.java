package cn.facilityone.shang.workorder.common.constant;

import java.util.HashMap;
import java.util.Map;

public class WOProcessTemplateConstant {



    /***************流程图元素**************************************************/

    /***
     * 创建工单
     */
    public static final String ELEMENT_TASK_WORKORDER_CREATE = "workorder-create";

    /***
     * 排程
     */
    public static final String ELEMENT_TASK_SCHEDULING = "scheduling";

    /***
     * 派工
     */
    public static final String ELEMENT_TASK_DISPATCHING = "dispatching";
    public static final String ELEMENT_TASK_SCHEDULING_DISPATCHING = "scheduling-dispatching";

    /***
     * 自动排程
     */
    public static final String ELEMENT_TASK_DISPATCHING_AUTO = "dispatching-auto";

    /***
     * 接单
     */
    public static final String ELEMENT_TASK_ACCEPT = "accept";

    /***
     * 处理工单
     */
    public static final String ELEMENT_TASK_EXECUTE = "execute";

    /***
     * 完成工单
     */
    public static final String ELEMENT_TASK_COMPLETE = "complete";

    /***
     * 验证
     */
    public static final String ELEMENT_TASK_CHECKING = "checking";

    /***
     * 存档处理
     */
    public static final String ELEMENT_TASK_ARCHIVE = "archive";

    /**
     * 暂停工单
     */
    public static final String ELEMENT_TASK_PAUSE = "pause";

    public static final String ELEMENT_TASK_TERMINATE = "stop";

    /**
     * 通知执行人
     */
    public static final String ELEMENT_TASK_NOTICE_LABORER = "notice-laborer";


    public static String getElementName(String key) {
        Map<String, String> data = new HashMap<String, String>();
        data.put(ELEMENT_TASK_WORKORDER_CREATE, "创建工单");
        data.put(ELEMENT_TASK_SCHEDULING, "排程/派工");
        data.put(ELEMENT_TASK_DISPATCHING_AUTO, "自动排程/派工");
        data.put(ELEMENT_TASK_ACCEPT, "接单");
        data.put(ELEMENT_TASK_COMPLETE, "处理工单");
        data.put(ELEMENT_TASK_CHECKING, "验证工单");
        data.put(ELEMENT_TASK_ARCHIVE, "工单存档");
        data.put(ELEMENT_TASK_PAUSE, "暂停工单");
        String res = "";
        if (data.containsKey(key)) {
            res = data.get(key);
        }
        return res;
    }

    public static final String VALUE_TASK_REJECT = "退单";

    /***************以下为参数*****************************************/

    /**
     * 是否自动派工
     */
    public static final String PARAM_IS_AUTO_DISPATCHING = "isAutoDispatching";
    public static final String IS_AUTO_DISPATCHING_VALUE_TRUE = "true";
    public static final String IS_AUTO_DISPATCHING_VALUE_FALSE = "false";


    /**
     * 是否接单
     */
    public static final String PARAM_IS_ALL_REJECT = "isAllReject";
    public static final String IS_ALL_REJECT_VALUE_FALSE = "false";
    public static final String IS_ALL_REJECT_VALUE_TRUE = "true";


    /**
     * 工单处理
     */
    public static final String PARAM_IS_COMPLETE_SUCCESS = "isCompleteSuccess";

    public static final String IS_COMPLETE_SUCCESS_VALUE_PAUSE = "pause";
    public static final String IS_COMPLETE_SUCCESS_VALUE_COMPLETE = "complete";
    public static final String IS_COMPLETE_SUCCESS_VALUE_STOP = "stop";
    public static final String IS_COMPLETE_SUCCESS_VALUE_ALL_REJECT = "allReject";

    /**
     * pause parameter
     */
    public static final String PARAM_IS_CONTINUE = "isContinue";

    public static final String IS_CONTINUE_VALUE_TRUE = "true";
    public static final String IS_CONTINUE_VALUE_FALSE_DISPATCHING = "false-dispatching";


    /**
     * 是否需要验证
     */
    public static final String PARAM_IS_CHECKING = "isChecking";
    public static final String IS_CHECKING_VALUE_TRUE = "true";
    public static final String IS_CHECKING_VALUE_FALSE = "false";

    /**
     * after dispatching
     */
    public static final String PARAM_IS_STOP = "isStop";
    public static final String IS_STOP_VALUE_TRUE = "true";
    public static final String IS_STOP_VALUE_FALSE = "false";

    /**
     * 验证结果
     */
    public static final String PARAM_CHECKING_RESULT = "checkingResult";
    public static final String CHECKING_RESULT_VALUE_FALSE_CREATE = "false-create";
    public static final String CHECKING_RESULT_VALUE_TRUE = "true";
    public static final String CHECKING_RESULT_VALUE_FALSE_EXECUTE = "false-execute";

    /**
     * 工单ID
     */
    public static final String PARAM_WORKORDER_ID = "workOrderId";

    /**
     * 审批控制参数
     * true or false 选择不同流程走向
     */
    public static final String PARAM_APPROVE = "approve";

    /**
     * 存在activiti的task的variable中的评论
     */
    public static final String PARAM_COMMENT = "comment";

    public static final String VALUE_TRUE = "true";
    public static final String VALUE_FALSE = "false";

    /**
     * work order process notice step
     */
    public static final String STEP_CREATE = "CREATE";
    public static final String STEP_DISPATCH = "DISPATCH";
    public static final String STEP_ALL_REJECT = "ALL_REJECT";
    public static final String STEP_TERMINATE = "TERMINATE";
    public static final String STEP_FINISH = "FINISH";
    public static final String STEP_PAUSE = "PAUSE";
    public static final String STEP_CLOSE = "CLOSE";
    public static final String STEP_COMPLETE = "COMPLETE";
    public static final String STEP_VALIDATE_PASS = "VALIDATE_PASS";
    public static final String STEP_VALIDATE_FAIL = "VALIDATE_FAIL";
    public static final String STEP_ACCEPT = "ACCEPT";
    public static final String STEP_CONTINUE = "CONTINUE";

    public static final String STEP_APPROVAL_REQUEST = "APPROVAL_REQUEST";
    public static final String STEP_APPROVAL_PASS = "APPROVAL_PASS";
    public static final String STEP_APPROVAL_FAIL = "APPROVAL_FAIL";

    public static final String NO_BPM_KEY = "no_bpm_key";

}
