package cn.facilityone.shang.workorder.common.service;

import cn.facilityone.shang.asset.asset002.repository.TimeCountRepository;
import cn.facilityone.shang.common.repository.AttachmentRepository;
import cn.facilityone.shang.common.repository.ExtraColumnRepository;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.entity.common.Attachment;
import cn.facilityone.shang.entity.common.ExtraColumn;
import cn.facilityone.shang.entity.common.TimeCount;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.servercenter.RequirementNoticeSwitch;
import cn.facilityone.shang.entity.workorder.*;
import cn.facilityone.shang.entity.workorder.WorkOrderLaborer.LaborerStatus;
import cn.facilityone.shang.mobile.v1.workorder.service.MWorkOrderServiceImpl;
import cn.facilityone.shang.organize.org001.service.WorkTeamService;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMStepRepository;
import cn.facilityone.shang.preventive.pm002.service.PMDateTodoJobService;
import cn.facilityone.shang.servicecenter.common.repository.WorkOrderLaborerRepository;
import cn.facilityone.shang.servicecenter.common.service.WorkOrderLaborerService;
import cn.facilityone.shang.servicecenter.service004.service.RequirementNoticeSwitchService;
import cn.facilityone.shang.wechat.dto.WechatTemplateMessageContent;
import cn.facilityone.shang.wechat.h5.service.WechatMessageService;
import cn.facilityone.shang.wechat.service.WechatProcessService;
import cn.facilityone.shang.workorder.common.constant.WOProcessTemplateConstant;
import cn.facilityone.shang.workorder.common.constant.WorkOrderConstant;
import cn.facilityone.shang.workorder.common.dto.WorkOrderScheduleRequestDTO;
import cn.facilityone.shang.workorder.common.dto.WorkOrderTaskDTO;
import cn.facilityone.shang.workorder.common.repository.ApprovalRepository;
import cn.facilityone.shang.workorder.common.repository.ApprovalResultRepository;
import cn.facilityone.shang.workorder.common.repository.WorkOrderActivityRepository;
import cn.facilityone.shang.workorder.wo001.dto.WorkOrderManagementRequest;
import cn.facilityone.shang.workorder.wo003.repository.WorkOrderProcessRepository;
import cn.facilityone.shang.workorder.wo003.service.WorkOrderProcessService;
import cn.facilityone.shang.workorder.wo004.dto.WorkOrderApprovalRequestDTO;
import cn.facilityone.shang.workorder.wo004.repository.ApprovalExtraRepository;
import cn.facilityone.shang.workorder.wo004.repository.ApprovalTemplateRepository;
import cn.facilityone.xia.core.exception.BusinessException;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Minutes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.facilityone.shang.entity.workorder.WorkOrder.WorkOrderStatus.PROCESS;
import static cn.facilityone.shang.entity.workorder.WorkOrderLaborer.LaborerStatus.FINISH;
import static cn.facilityone.shang.entity.workorder.WorkOrderLaborer.LaborerStatus.REJECTED;

/**
 * @Author: wayne.fu
 * @Date: 6/10/2015
 */

@Service
public class WorkOrderTaskServiceImpl implements WorkOrderTaskService {
    private static final Logger log = LoggerFactory.getLogger(WorkOrderTaskServiceImpl.class);
    public static final String SYSTEM_STRING = "系统";

    @Autowired
    private WorkOrderLaborerRepository workOrderLaborerRepository;
    @Autowired
    private WorkOrderActivityService workOrderActivityService;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private WorkOrderProcessService workOrderProcessService;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private WorkOrderLaborerService workOrderLaborerService;
    @Autowired
    private TimeCountRepository timeCountRepository;
    @Autowired
    private ApprovalTemplateRepository approvalTemplateRepository;
    @Autowired
    private ApprovalRepository approvalRepository;
    @Autowired
    private ApprovalResultRepository approvalResultRepository;
    @Autowired
    private ExtraColumnRepository extraColumnRepository;
    @Autowired
    private ApprovalExtraRepository approvalExtraRepository;
    @Autowired
    private WorkOrderMessageService workOrderMessageService;
    @Autowired
    private AttachmentRepository attachmentRepository;
    @Autowired
    private WorkOrderProcessRepository workOrderProcessRepository;
    @Autowired
    private PMStepRepository pmStepRepository;
    @Autowired
    private WechatProcessService wechatProcessService;
    @Autowired
    private RequirementNoticeSwitchService requirementNoticeSwitchService;
    @Autowired
    private WechatMessageService wechatMessageService;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private WorkTeamService workTeamService;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private WorkOrderActivityRepository workOrderActivityRepository;
    @Autowired
    private PMDateTodoJobService pmDateTodoJobService;


    /**
     * suspend the work order
     *
     * @param workOrder
     * @param request
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder pauseTask(WorkOrder workOrder, WorkOrderManagementRequest request) {

        // 非空
        Assert.notNull(workOrder, "Work order should not be null in pauseTask");
        Assert.notNull(workOrder.getId(), "Work order id should not be null in pauseTask");
        Assert.notNull(workOrder.getStatus(), "Work order status should not be null in pauseTask");

        log.debug("work order:{} in pauseTask, status:{}", workOrder.getId(), workOrder.getStatus().name());
        //validate work order status
        Assert.isTrue(PROCESS.equals(workOrder.getStatus()), "work order status is not allowed in pauseTask");

        Long workOrderId = workOrder.getId();
        int operation = WorkOrderConstant.OPERATION_PAUSE_CONTINUE_TRUE;
        //complete task PAUSE
        int choice = request.getContinueChoice();
        StringBuilder sb = new StringBuilder();
        if (WorkOrderConstant.CONTINUE_CHOICE_TRUE == choice) {
            sb.append("继续工作");
            operation = WorkOrderConstant.OPERATION_PAUSE_CONTINUE_TRUE;
        } else if (WorkOrderConstant.CONTINUE_CHOICE_FALSE == choice) {
            sb.append("不继续工作");
            operation = WorkOrderConstant.OPERATION_PAUSE_CONTINUE_FALSE;
        }
        // set work order laborer status
        changeWorkOrderLaborerStatus(workOrderId, operation);
        // record activity
        if (StringUtils.isNotBlank(request.getReason())) {
            sb.append("，");
            sb.append(request.getReason());
        }
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.STOP, sb.toString());

        //start to process activiti task
//        String processInstanceId = workOrder.getProcInstID();
        if (WorkOrderConstant.CONTINUE_CHOICE_TRUE == choice) {
            workOrder.setStatus(WorkOrder.WorkOrderStatus.STOP);
            // suspend the current instance
//            workOrderProcessService.suspendProcessInstanceById(processInstanceId);
        } else if (WorkOrderConstant.CONTINUE_CHOICE_FALSE == choice) {
            workOrder.setStatus(WorkOrder.WorkOrderStatus.STOP_N);
            //complete activiti-task and move to PAUSE
//            String toPause = WOProcessTemplateConstant.ELEMENT_TASK_EXECUTE;
//            Map<String, Object> toPauseVariables = new HashMap<>();
//            toPauseVariables.put(WOProcessTemplateConstant.PARAM_IS_COMPLETE_SUCCESS, WOProcessTemplateConstant.IS_COMPLETE_SUCCESS_VALUE_PAUSE);
//            workOrderProcessService.completeByTask(processInstanceId, toPause, toPauseVariables);

            // change activiti-node to ELEMENT_TASK_SCHEDULING
//            String completePause = WOProcessTemplateConstant.ELEMENT_TASK_PAUSE;
//            Map<String, Object> completePauseVariables = new HashMap<>();
//            completePauseVariables.put(WOProcessTemplateConstant.PARAM_IS_CONTINUE, WOProcessTemplateConstant.IS_CONTINUE_VALUE_FALSE_DISPATCHING);
//            workOrderProcessService.completeByTask(processInstanceId, completePause, completePauseVariables);
        } else {
            log.error("Continue choice in pause should not be empty");
            return workOrder;
        }

            //暂停的时间点
            Date endTime = new Date();
            workOrder.setEndTime(endTime);

        workOrder = workOrderRepository.save(workOrder);

        return workOrder;
    }

    /**
     * change workorder laborers status while operating the work order
     *
     * @param workOrderId
     * @param operation
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void changeWorkOrderLaborerStatus(Long workOrderId, int operation) {
        log.debug("Operation {} will change all labour's status in work order :{}", operation,
                workOrderId);
        // 工单当前执行人
        List<WorkOrderLaborer> laborerList =
                workOrderLaborerRepository.findCurrentLaborersByWorkOrderId(workOrderId);
        if (CollectionUtils.isNotEmpty(laborerList)) {
            for (WorkOrderLaborer laborer : laborerList) {
                WorkOrderLaborer.LaborerStatus status =
                        getStatusByOperationAndLaborer(operation, laborer);
                log.info("change laborer:{} status from {} to {} ", laborer.getId(), laborer
                        .getStatus().name(), status.name());
                laborer.setStatus(status);

                // 执行人状态为已上交(完成，终止，暂停不继续)时，更新实际完成时间
                if (operation == WorkOrderConstant.OPERATION_FINISH ||
                        operation == WorkOrderConstant.OPERATION_TERMINATE ||
                        operation == WorkOrderConstant.OPERATION_PAUSE_CONTINUE_FALSE) {
                    // 若app端设定过实际完成时间，则实际完成时间和实际工作时长不再更新
                    if (laborer.getActualCompletionDateTime() == null) {
                        laborer.setActualCompletionDateTime(new Date());
                        Long start = laborer.getActualArrivalDateTime() == null ? 0L : laborer.getActualArrivalDateTime().getTime();
                        Long end = new Date().getTime();
                        if (!start.equals(0L) && end - start > 0) {
                            // 暂停时间(暂停(4)---继续(12))
                            List<WorkOrderActivity>activityList = workOrderActivityRepository.findStopAndContinueByWorkOrderId(workOrderId, laborer.getActualArrivalDateTime());
                            double suspendTime = 0L;
                            if (CollectionUtils.isNotEmpty(activityList)) {
                                WorkOrderActivity activity = null;
                                WorkOrderActivity continueActivity = null;
                                for (int i = 0; i < activityList.size() - 1; i++) {
                                    activity = activityList.get(i);
                                    WorkOrderActivity.ActivityType activityType = activity.getActivityType();
                                    if (activityType == WorkOrderActivity.ActivityType.STOP) {
                                        Date stop = activity.getCreatedDate();
                                        continueActivity = activityList.get(i + 1);
                                        Date continueDate = continueActivity.getCreatedDate();
                                        suspendTime = suspendTime + (continueDate.getTime() - stop.getTime());
                                    }
                                }
                            }

                            // 四舍五入保留2位小数，单位：小时
                            double value = (end - start - suspendTime) / (60 * 60 * 1000);
                            BigDecimal valueBg = new BigDecimal(value).setScale(2, RoundingMode.HALF_UP);
                            laborer.setActualWorkingTime(valueBg.doubleValue());
                        }
                    }
                }
            }
        }
        workOrderLaborerRepository.save(laborerList);
    }

    /**
     * accept the work order
     *
     * @param workOrder
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder acceptWorkOrder(WorkOrder workOrder, Employee employee) {
        if (workOrder == null || employee == null) {
            return null;
        }

        workOrder=workOrderRepository.findOne(workOrder.getId());

        //change workorder laborer status
        List<WorkOrderLaborer> laborers = workOrderLaborerRepository.findByWorkOrderIdAndEmployeeId(workOrder.getId(), employee.getId());

        LaborerStatus[] statuses = {LaborerStatus.WAITING};
        WorkOrderLaborer laborer = getLaborerByStatus(laborers, statuses);

        log.debug("accept workorder --> Employee id:{},workorder id:{}", employee.getId(), workOrder.getId());
        if (laborer == null) {
            laborer = new WorkOrderLaborer();
            laborer.setAssignDateTime(new Date());
        }

        laborer.setLaborer(employee);
        laborer.setWorkOrder(workOrder);
        laborer.setStatus(LaborerStatus.RECEIVED);
        laborer.setActualArrivalDateTime(new Date());
        workOrderLaborerRepository.save(laborer);

        //complete activiti task --- any one of laborers accept the workorder, change workorder status to process
        if (WorkOrder.WorkOrderStatus.DISPATCHE.equals(workOrder.getStatus())) {
            //complete activiti task, move to process
//            String processInstanceId = workOrder.getProcInstID();
//            String toProcess = WOProcessTemplateConstant.ELEMENT_TASK_ACCEPT;
//            Map<String, Object> variables = new HashMap<>();
//            variables.put(WOProcessTemplateConstant.PARAM_IS_ALL_REJECT, WOProcessTemplateConstant.IS_ALL_REJECT_VALUE_FALSE);
//            workOrderProcessService.completeByTask(processInstanceId, toProcess, variables);

            //change work order status to process
            workOrder.setStatus(WorkOrder.WorkOrderStatus.PROCESS);
        }

        if (workOrder.getActualArrivalDateTime() == null) {
            workOrder.setActualArrivalDateTime(new Date());
        }

//        TimeCount timeCount = workOrderRepository.findActualWorkingTimeById(workOrder.getId());
//        if (timeCount == null) {
//            timeCount = new TimeCount();
//            timeCount.setUnit(TimeUnit.MINUTES);
//        }

        List <WorkOrderActivity> workOrderActivityList=workOrderActivityRepository.findByWorkOrderIdOrderByCreatedDate(workOrder.getId());

        //暂停数量
        int pauseCount = 0;
        //接单数量
        int acceptCount = 0;

        for (WorkOrderActivity workOrderActivity :workOrderActivityList){
            if(workOrderActivity.getActivityType().equals(WorkOrderActivity.ActivityType.STOP) ){
                if(workOrderActivity.getDescription().contains("不继续工作")){
                    pauseCount=1;
                    acceptCount=0;
                }
            }

            if(pauseCount>0 && workOrderActivity.getActivityType().equals(WorkOrderActivity.ActivityType.RECEIVE) ){
                acceptCount++;
            }
        }

        int pauseminus = pauseCount - acceptCount;

         if(pauseminus == 1) {
             TimeCount times = workOrder.getStopTime();
             int value = 0;
             if (null != workOrder.getEndTime()) {
                 DateTime stopTime = new DateTime(workOrder.getEndTime());
                 DateTime ordersTime = new DateTime(new Date());
                 value = Minutes.minutesBetween(stopTime, ordersTime).getMinutes();
             }
             if (null == times) {
                 times = new TimeCount();
                 times.setUnit(TimeUnit.MINUTES);
                 times.setValue(Long.valueOf(value));
             } else {
                 times.setValue(Long.valueOf(times.getValue() + value));
             }
             times = timeCountRepository.save(times);
             workOrder.setStopTime(times);

         }
        workOrder = workOrderRepository.save(workOrder);
        //record activity
        String emAccept = employee.getName();
        if (!StringUtils.isEmpty(employee.getPhone())) {
            emAccept = emAccept + " ( " + employee.getPhone() + " ) ";
        } else if (!StringUtils.isEmpty(employee.getExtension())) {
            emAccept = emAccept + " ( " + employee.getExtension() + " ) ";
        }
        //TODO 描述的格式不要改，微信中也用到的，要改的话微信的流程记录也要改
        WorkOrderActivity woav = workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.RECEIVE, emAccept + " - "+XiaMesssageResource.getMessage("page.service004.message.orders"));
        if(requirementNoticeSwitchService.isSwitchsByIndex(RequirementNoticeSwitch.NoticeSwitch.RECEIVE)){
            int woCount = 0;
            workOrderActivityList.add(woav);
            if(CollectionUtils.isNotEmpty(workOrderActivityList)){
                for(WorkOrderActivity wa : workOrderActivityList){
                     if(wa.getActivityType().equals(WorkOrderActivity.ActivityType.RECEIVE)){
                         woCount++;
                     }
                }
            }

//            List<WorkOrderLaborer> workOrderLaborers = workOrder.getWorkOrderLaborers();
//
//            if(!CollectionUtils.isEmpty(workOrderLaborers)){
//                for(WorkOrderLaborer laborer1 : workOrderLaborers){
//                    if(laborer1.getStatus().equals(WorkOrderLaborer.LaborerStatus.RECEIVED)){
//                        woCount++;
//                    }
//                }
//            }

            if(woCount==1){
                String tel = employee.getPhone();
                if(!StringUtils.isEmpty(tel)){
                    tel = ",联系电话"+tel;
                }
                String str = "工单"+workOrder.getCode()+"被"+employee.getName()+"接单"+tel+"！";
                wechatMessageService.sendMessage(workOrder.getRequirement(), str);
            }
        }
        return workOrder;
    }

    /**
     * dispatch workorder laborer
     *
     * @param workOrder
     * @param request
     * @return
     */
    @Override
    public WorkOrder dispatchTask(WorkOrder workOrder, WorkOrderManagementRequest request, int type) {
        // 非空
        Assert.notNull(workOrder, "Work order should not be null in dispatchTask");
        Assert.notNull(workOrder.getId(), "Work order id should not be null in dispatchTask");
        Assert.notNull(workOrder.getStatus(), "Work order status should not be null in dispatchTask");

        log.debug("work order:{} in dispatchTask, status:{}", workOrder.getId(), workOrder.getStatus().name());

        if (request != null) {
            //update date time in work order
            WorkOrderScheduleRequestDTO dto = request.getScheduleRequest();
            Assert.notNull(dto);
            workOrder.setEstimatedArrivalDateTime(dto.getEstimatedArriveDateTime());
            workOrder.setEstimatedCompletionDateTime(dto.getEstimatedFinishDateTime());
            if(null!=dto.getSendWorkContent()){
                workOrder.setSendWorkContent(dto.getSendWorkContent());
            }

            if (dto.getEstimatedWorkingTime() != null) {
                TimeCount timeCount = new TimeCount();
                timeCount.setValue(dto.getEstimatedWorkingTime());
                timeCount.setUnit(dto.getUnit());
                timeCount = timeCountRepository.save(timeCount);
                workOrder.setEstimatedWorkingTime(timeCount);
            }
        }

        //update work order status
        workOrder.setStatus(WorkOrder.WorkOrderStatus.DISPATCHE);
        workOrder = workOrderRepository.save(workOrder);

        // record activity
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.DISPATCHE, type);
        
        //process activiti task
        //complete activiti-task and move to accept
//        String processInstanceId = workOrder.getProcInstID();
//        if(null != processInstanceId){
//            String toAccept = WOProcessTemplateConstant.ELEMENT_TASK_SCHEDULING_DISPATCHING;
//            Map<String, Object> toAcceptVariables = new HashMap<>();
//            toAcceptVariables.put(WOProcessTemplateConstant.PARAM_IS_STOP, WOProcessTemplateConstant.IS_STOP_VALUE_FALSE);
//            workOrderProcessService.completeByTask(processInstanceId, toAccept, toAcceptVariables);
//        }

        return workOrder;
    }

    /**
     * request a approval for the work order
     *
     * @param workOrder
     * @param request
     * @param user
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder requestApproval(WorkOrder workOrder, WorkOrderManagementRequest request, Employee user) {
        // 非空
        Assert.notNull(workOrder, "Work order should not be null in requestApproval");
        Assert.notNull(workOrder.getId(), "Work order id should not be null in requestApproval");
        Assert.notNull(request, "request information should not be null in requestApproval");

        //create approval and approval result
        WorkOrderApprovalRequestDTO approvalRequestDTO = request.getApprovalRequest();
        Assert.notNull(approvalRequestDTO, "approvalRequest should not be null in requestApproval");

        //build activity string
        StringBuilder sb = new StringBuilder();

        //create approval
        Approval approval = new Approval();
        approval.setWorkOrder(workOrder);
        approval.setRequestor(user);
        ApprovalTemplate template = approvalTemplateRepository.findOne(approvalRequestDTO.getTemplateId());
        if (template == null) {
            template = approvalTemplateRepository.findOneHardly(approvalRequestDTO.getTemplateId());
        }
        approval.setTemplate(template);
        approval.setProject(workOrder.getProject());
        approval = approvalRepository.save(approval);
        sb.append(handleApprovalTemplateName(approval.getTemplate().getName()));
        sb.append("；审批人：");
        //build approval result
        List<Long> approverIds = approvalRequestDTO.getApproverIds();
        List<Employee> approvers = null;
        if (CollectionUtils.isNotEmpty(approverIds)) {
            approvers = employeeRepository.findByIdIn(new HashSet<Long>(approverIds));
            List<ApprovalResult> approvalResults = new ArrayList<>();
            for (Employee approver : approvers) {
                ApprovalResult result = new ApprovalResult();
                result.setApproval(approval);
                result.setApprover(approver);
                approvalResults.add(result);
                sb.append(approver.getName());
                sb.append("，");
            }
            approvalResults = approvalResultRepository.save(approvalResults);
        }
        //build template parameter values
        Map<String, String> values = approvalRequestDTO.getParameterValues();
        if (!values.isEmpty()) {
            Set<String> extralColumnIdStrings = values.keySet();
            Long[] extralColumnIds = buildExtralColumnId(extralColumnIdStrings);
            List<ApprovalExtra> approvalExtras = new ArrayList<>();
            for (Long id : extralColumnIds) {
                ApprovalExtra approvalExtra = new ApprovalExtra();
                approvalExtra.setApproval(approval);
                ExtraColumn extraColumn = extraColumnRepository.findOne(id);
                approvalExtra.setExtraColumn(extraColumn);
                setValueForExtraColumn(approvalExtra, extraColumn, values);
                approvalExtras.add(approvalExtra);
            }
            approvalExtraRepository.save(approvalExtras);
        }

        //build attachment parameter values
        Map<String, List<Attachment>> attachments = approvalRequestDTO.getParameterAttachments();
        if (!attachments.isEmpty()) {
            Set<String> extralColumnIdStrings = attachments.keySet();
            Long[] extralColumnIds = buildExtralColumnId(extralColumnIdStrings);
            List<ApprovalExtra> approvalExtras = new ArrayList<>();
            for (Long id : extralColumnIds) {
                ApprovalExtra approvalExtra = new ApprovalExtra();
                approvalExtra.setApproval(approval);
                ExtraColumn extraColumn = extraColumnRepository.findOne(id);
                approvalExtra.setExtraColumn(extraColumn);
                approvalExtras.add(approvalExtra);
            }
            approvalExtras = approvalExtraRepository.save(approvalExtras);

            //update attachment
            List<Attachment> attachmentList = new ArrayList<>();
            for (ApprovalExtra extra : approvalExtras) {
                if (extra.getExtraColumn() != null) {
                    List<Attachment> value = attachments.get(String.valueOf(extra.getExtraColumn().getId()));
                    for (Attachment attachment : value) {
                        attachment.setpKeyId(String.valueOf(extra.getId()));
                        attachment.setTableName(WorkOrderConstant.APPROVAL_TABLE_NAME);
                    }
                    attachmentList.addAll(value);
                }
            }

            attachmentRepository.save(attachmentList);
        }
        //keep workorder status
        WorkOrder.WorkOrderStatus workOrderStatusOld = workOrder.getStatus();
        //update work order status
        workOrder.setStatus(WorkOrder.WorkOrderStatus.APPROVE);
        workOrder.setPreviousStatus(workOrderStatusOld);

        //suspend activiti
//        workOrderProcessService.suspendProcessInstanceById(workOrder.getProcInstID());

        //save changes and record activity
        workOrder = workOrderRepository.save(workOrder);
        sb.setLength(sb.length() - 1);
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.APPROVAL_REQUEST, sb.toString());
        // 通知
        workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_APPROVAL_REQUEST, approvers);

        return workOrder;
    }

    private String handleApprovalTemplateName(String templateName) {
        if (MWorkOrderServiceImpl.DEFAULT_MOBILE_TEMPLATE_NAME.equals(templateName)) {
            return XiaMesssageResource.getMessage(MWorkOrderServiceImpl.DEFAULT_MOBILE_TEMPLATE_NAME);
        }

        return templateName;
    }

    /**
     * approve the request <br />
     * update the work order status and activiti status
     *
     * @param approval
     * @param result
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void approve(Approval approval, ApprovalResult result) {
        if (approval == null || approval.getId() == null) {
            return;
        }

        approval = approvalRepository.findEntireOne(approval.getId());
        WorkOrder workOrder = approval.getWorkOrder();
        ApprovalTemplate approvalTemplate = approval.getTemplate();
        Assert.notNull(workOrder, "work order in approve should not be null");
        Assert.notNull(approvalTemplate, "approval template in approve should not be null");

        if (!WorkOrder.WorkOrderStatus.APPROVE.equals(workOrder.getStatus())) {
            return;
        }

        boolean ppmApprovalInSetting = false;
        if (WorkOrder.WorkOrderType.PPM.equals(workOrder.getType())) {
            PreventiveMaintenance preventiveMaintenance = workOrder.getPreventiveMaintenance();
            if (preventiveMaintenance.getNeedApproval() != null && preventiveMaintenance.getNeedApproval()) {
                ppmApprovalInSetting = true;
            }
        }

        //check whether re-activate work order process by approval type
        if (!ppmApprovalInSetting && checkApprovalTypeAndResult(approval)) {
            //set work order status to old status
            WorkOrder.WorkOrderStatus status = workOrder.getPreviousStatus();
            if (status != null) {
                workOrder.setStatus(status);
                workOrderRepository.save(workOrder);
            }
            //re-activate work order process
//            workOrderProcessService.continueProcessInstanceById(workOrder.getProcInstID());

            approval.setApprovalResult(result.getResult());
            approvalRepository.save(approval);
        }

        //make work order activity description
        StringBuilder sb = new StringBuilder();
        if (ApprovalResult.ApprovalResultType.PASS.equals(result.getResult())) {
            sb.append("通过");
        } else if (ApprovalResult.ApprovalResultType.REJECT.equals(result.getResult())) {
            sb.append("拒绝");
        }

        if (StringUtils.isNotBlank(result.getDescription())) {
            sb.append(",");
            sb.append(result.getDescription());
        }

        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.APPROVE, sb.toString());

        // 审批通过/不通过通知
        List<Employee> requester = new ArrayList<>();
        requester.add(approval.getRequestor());
        if (ApprovalResult.ApprovalResultType.PASS.equals(result.getResult())) {
            workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_APPROVAL_PASS, requester);
        } else if (ApprovalResult.ApprovalResultType.REJECT.equals(result.getResult())) {
            workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_APPROVAL_FAIL, requester);
        }

        if (ppmApprovalInSetting&&WorkOrder.WorkOrderStatus.CREATE.equals(workOrder.getPreviousStatus())) {
            workOrder = workOrderRepository.findOne(workOrder.getId());
            if (ppmApprovalFail(approval)) {
                //set work order status to old status
                WorkOrder.WorkOrderStatus status = workOrder.getPreviousStatus();
                if (status != null) {
                    workOrder.setStatus(status);
                    workOrder = workOrderRepository.save(workOrder);
                }
                //re-activate work order process
//                workOrderProcessService.continueProcessInstanceById(workOrder.getProcInstID());
                WorkOrderManagementRequest request = new WorkOrderManagementRequest();
                request.setReason("审批不通过，工单被终止！");
                this.terminateTask(workOrder, request);
            } else {
                //set work order status to old status
                WorkOrder.WorkOrderStatus status = workOrder.getPreviousStatus();
                if (status != null) {
                    workOrder.setStatus(status);
                    workOrderRepository.save(workOrder);
                }
                //re-activate work order process
//                workOrderProcessService.continueProcessInstanceById(workOrder.getProcInstID());
            }
        }else if(ppmApprovalInSetting&&!WorkOrder.WorkOrderStatus.CREATE.equals(workOrder.getPreviousStatus())){
            WorkOrder.WorkOrderStatus status = workOrder.getPreviousStatus();
            if (status != null) {
                workOrder.setStatus(status);
                workOrderRepository.save(workOrder);
            }
            //re-activate work order process
//            workOrderProcessService.continueProcessInstanceById(workOrder.getProcInstID());
        }
    }

    /**
     * @param approval
     * @return
     */
    private boolean ppmApprovalFail(Approval approval) {
        List<ApprovalResult> approvalResults = approval.getResult();
        if (CollectionUtils.isNotEmpty(approvalResults)) {
            for (ApprovalResult result : approvalResults) {
                if (ApprovalResult.ApprovalResultType.REJECT.equals(result.getResult())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * reject work order
     *
     * @param workOrder
     * @param employee
     * @param reason
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder rejectWorkOrder(WorkOrder workOrder, Employee employee, String reason) {
        if (workOrder == null || employee == null) {
            return null;
        }

        //change workorder laborer status
        List<WorkOrderLaborer> laborers = workOrderLaborerRepository.findByWorkOrderIdAndEmployeeId(workOrder.getId(), employee.getId());

        LaborerStatus[] statuses = {LaborerStatus.RECEIVED, LaborerStatus.WAITING};
        WorkOrderLaborer laborer = getLaborerByStatus(laborers, statuses);

        if (laborer == null) {
            log.debug("work order laborer doesn't exist");
            return null;
        }


        laborer.setStatus(LaborerStatus.REJECTED);
        laborer.setActualArrivalDateTime(new Date());
        laborer.setActualCompletionDateTime(new Date());
        workOrderLaborerRepository.save(laborer);

        if (isAllLaborerReject(workOrder)) {
            //complete activiti task --- All of them reject the work order --back to dispatch
            if (WorkOrder.WorkOrderStatus.DISPATCHE.equals(workOrder.getStatus())) {
                //move activiti to dispatch
//                String processInstanceId = workOrder.getProcInstID();
//                String toCreate = WOProcessTemplateConstant.ELEMENT_TASK_ACCEPT;
//                Map<String, Object> toCreateParameter = new HashMap<>();
//                toCreateParameter.put(WOProcessTemplateConstant.PARAM_IS_ALL_REJECT, WOProcessTemplateConstant.IS_ALL_REJECT_VALUE_TRUE);
//                workOrderProcessService.completeByTask(processInstanceId, toCreate, toCreateParameter);

                //change work order status to create
                workOrder.setStatus(WorkOrder.WorkOrderStatus.CREATE);
                workOrderRepository.save(workOrder);
                //send work order process notice
                workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_ALL_REJECT, workOrder.getDescription());
            } else
                //complete activiti task --- All of them reject the work order --back to dispatch
                if (WorkOrder.WorkOrderStatus.PROCESS.equals(workOrder.getStatus())) {
                    //move activiti to dispatch
//                    String processInstanceId = workOrder.getProcInstID();
//                    String toCreate = WOProcessTemplateConstant.ELEMENT_TASK_EXECUTE;
//                    Map<String, Object> toCreateParameter = new HashMap<>();
//                    toCreateParameter.put(WOProcessTemplateConstant.PARAM_IS_COMPLETE_SUCCESS, WOProcessTemplateConstant.IS_COMPLETE_SUCCESS_VALUE_ALL_REJECT);
//                    workOrderProcessService.completeByTask(processInstanceId, toCreate, toCreateParameter);

                    //change work order status to create
                    workOrder.setStatus(WorkOrder.WorkOrderStatus.CREATE);
                    workOrderRepository.save(workOrder);
                    //send work order process notice
                    workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_ALL_REJECT, workOrder.getDescription());
                }
        }

        //record activity
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.REJECT_ORDER, employee.getName() + " - 退单" + (StringUtils.isBlank(reason) ? "" : "，原因：" + reason));
        return workOrder;
    }

    /**
     * get laborer from list filter by status
     * <br/>
     * only return the first matched
     *
     * @param laborers
     * @param statuses
     * @return
     */
    private WorkOrderLaborer getLaborerByStatus(List<WorkOrderLaborer> laborers, LaborerStatus[] statuses) {
        if (CollectionUtils.isEmpty(laborers)) {
            return null;
        }

        List<LaborerStatus> statusList = Arrays.asList(statuses);

        for (WorkOrderLaborer laborer : laborers) {
            if (statusList.contains(laborer.getStatus())) {
                return laborer;
            }
        }

        return null;
    }

    /**
     * get old work order status
     *
     * @param workOrder
     * @return
     */
    private WorkOrder.WorkOrderStatus getOldStatusForWorkOrder(WorkOrder workOrder) {
        WorkOrderActivity recentRecord = workOrderActivityService.getRecentApprovalRequestRecordByWorkOrderId(workOrder.getId());

        String statusName = recentRecord.getDescription() == null ? "" : recentRecord.getDescription().split(" ")[0];

        for (WorkOrder.WorkOrderStatus status : WorkOrder.WorkOrderStatus.values()) {
            if (status.name().equals(statusName)) {
                return status;
            }
        }
        return null;
    }

    /**
     * type == one: if one pass or reject, return true
     * <br />
     * type == many: all pass or reject, return true
     *
     * @param approval
     * @return
     */
    private boolean checkApprovalTypeAndResult(Approval approval) {
        ApprovalTemplate.ApprovalType type = approval.getTemplate().getType();
        List<ApprovalResult> results = approval.getResult();
        if (ApprovalTemplate.ApprovalType.ONE.equals(type)) {
            for (ApprovalResult result : results) {
                if (result != null && result.getResult() != null) {
                    return true;
                }
            }

            return false;
        }

        if (ApprovalTemplate.ApprovalType.MANY.equals(type)) {
            for (ApprovalResult result : results) {
                if (result == null || result.getResult() == null) {
                    return false;
                }
            }

            return true;
        }

        //useless, just for compiler success
        return false;
    }

    /**
     * @param approvalExtra
     * @param extraColumn
     */
    private void setValueForExtraColumn(ApprovalExtra approvalExtra, ExtraColumn extraColumn, Map<String, String> values) {
        ExtraColumn.FieldType type = extraColumn.getFieldType();
        String value = values.get(String.valueOf(extraColumn.getId()));
        if (StringUtils.isBlank(value)) {
            return;
        }
        if (ExtraColumn.FieldType.CHAR.equals(type)) {
            approvalExtra.setChar1(String.valueOf(value));
        }

        if (ExtraColumn.FieldType.LIST.equals(type)) {
            approvalExtra.setChar1(String.valueOf(value));
        }

        if (ExtraColumn.FieldType.DATE.equals(type)) {
            approvalExtra.setDate1(DateTime.parse(value).toDate());
        }

        if (ExtraColumn.FieldType.NUMERIC.equals(type)) {
            approvalExtra.setNumeric1(Double.valueOf(value));
        }
    }

    /**
     * build Long array for extra Columns from String
     *
     * @param extralColumnIdStrings
     * @return
     */
    private Long[] buildExtralColumnId(Set<String> extralColumnIdStrings) {
        Long[] result = new Long[extralColumnIdStrings.size()];
        Iterator<String> stringIdIterator = extralColumnIdStrings.iterator();
        int i = 0;
        while (stringIdIterator.hasNext()) {
            String id = stringIdIterator.next();
            result[i++] = Long.parseLong(id);
        }
        return result;
    }

    /**
     * add work order laborer from dispatching
     *
     * @param request
     * @param workOrder
     */
    @Override
    public void addWorkOrderLabor(WorkOrderManagementRequest request, WorkOrder workOrder) {
        if (workOrder == null || request == null || request.getScheduleRequest() == null) {
            return;
        }

        WorkOrderScheduleRequestDTO dto = request.getScheduleRequest();
        List<Long> ids = dto.getLaborerIds();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<WorkOrderLaborer> laborers = workOrderLaborerRepository.findByWorkOrderId(workOrder.getId());
            for (WorkOrderLaborer laborer : laborers) {
                if (LaborerStatus.WAITING.equals(laborer.getStatus()) || LaborerStatus.RECEIVED.equals(laborer.getStatus())) {
                    int index = ids.indexOf(laborer.getLaborer().getId());
                    if (index != -1) {
                        ids.remove(index);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(ids)) {
                List<Employee> employees = employeeRepository.findByIdIn(new HashSet<Long>(ids));
                workOrderLaborerService.addLaborerForWorkOrder(workOrder, employees, dto);
            }
        }
    }

    /**
     * get laborer statue by operation
     *
     * @param operation
     * @param laborer
     * @return
     */
    private LaborerStatus getStatusByOperationAndLaborer(int operation, WorkOrderLaborer laborer) {
        Assert.notNull(laborer, "laborer should not be null here");
        switch (operation) {
            case WorkOrderConstant.OPERATION_PAUSE_CONTINUE_FALSE:
                return getStopStatus(laborer);
            case WorkOrderConstant.OPERATION_TERMINATE:
                return getTerminateStatus(laborer);
            case WorkOrderConstant.OPERATION_FINISH:
                return getFinishStatus(laborer);
            default:
                return laborer.getStatus();
        }
    }

    /**
     * get laborer status for terminate operation
     *
     * @param laborer
     * @return
     */
    private LaborerStatus getTerminateStatus(WorkOrderLaborer laborer) {
        LaborerStatus status = laborer.getStatus();
        LaborerStatus result = status;
        switch (status) {
            case WAITING:
                checkAndRemind(laborer);
                break;
            case RECEIVED:
                result = FINISH;
                break;
            default:
                result = status;
                break;
        }
        return result;
    }

    /**
     * remind the operator there are laborers who is in WAITING
     *
     * @param laborer
     */
    private void checkAndRemind(WorkOrderLaborer laborer) {
        if (laborer != null && LaborerStatus.WAITING.equals(laborer.getStatus())) {
            throw new BusinessException(XiaMesssageResource.getMessage("server.wo001.laborerwating"));
        }
    }

    /**
     * get laborer status for stop operation
     *
     * @param laborer
     * @return
     */
    private LaborerStatus getStopStatus(WorkOrderLaborer laborer) {
        LaborerStatus status = laborer.getStatus();
        LaborerStatus result;
        switch (status) {
            case WAITING:
                result = REJECTED;
                break;
            case RECEIVED:
                result = FINISH;
                break;
            default:
                result = status;
                break;
        }
        return result;
    }

    /**
     * get laborer status for finish operation
     *
     * @param laborer
     * @return
     */
    private LaborerStatus getFinishStatus(WorkOrderLaborer laborer) {
        LaborerStatus status = laborer.getStatus();
        LaborerStatus result = status;
        switch (status) {
            case WAITING:
                checkAndRemind(laborer);
                break;
            case RECEIVED:
                result = FINISH;
                break;
            default:
                result = status;
                break;
        }
        return result;
    }

    /**
     * complete the work order
     *
     * @param workOrder
     * @param request
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder completeTask(WorkOrder workOrder, WorkOrderManagementRequest request) {
        // 非空
        Assert.notNull(workOrder, "Work order should not be null in completeTask");
        Assert.notNull(workOrder.getId(), "Work order id should not be null in completeTask");
        Assert.notNull(workOrder.getStatus(), "Work order status should not be null in completeTask");

        log.debug("work order:{} in completeTask, status:{}", workOrder.getId(), workOrder.getStatus().name());
        //check work order status
        Assert.isTrue(PROCESS.equals(workOrder.getStatus()), "Work order status is not allowed in completing task");

        // set work order laborer status
        Long workOrderId = workOrder.getId();
        changeWorkOrderLaborerStatus(workOrderId, WorkOrderConstant.OPERATION_FINISH);

        //send wechat message to wechat requester
        Requirement requirement = workOrderRepository.findRequirementById(workOrderId);
//        if (requirement != null && Requirement.RequirementSource.WECHAT.equals(requirement.getSource())) {
//            sendWechatMessageToRequester(requirement, workOrder);
//        }

        //send email for requirement coming from email
        if (requirement != null && Requirement.RequirementSource.EMAIL.equals(requirement.getSource())) {
            //sendEmailToRequester(requirement, workOrder);
        }

        //record activity
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.FINISH);

        //process activiti task
        //complete activiti-task and move to FINISH
//        String processInstanceId = workOrder.getProcInstID();
//        String toFinish = WOProcessTemplateConstant.ELEMENT_TASK_EXECUTE;
//        Map<String, Object> toPauseVariables = new HashMap<>();
//        toPauseVariables.put(WOProcessTemplateConstant.PARAM_IS_COMPLETE_SUCCESS, WOProcessTemplateConstant.IS_COMPLETE_SUCCESS_VALUE_COMPLETE);
//        workOrderProcessService.completeByTask(processInstanceId, toFinish, toPauseVariables);

        //update work order status
        workOrder.setStatus(WorkOrder.WorkOrderStatus.FINISH);
        // 修改：重新接单完成之后，wo表的实际完成时间也需要更新 - 修改于2016年9月30日12:10:45 - Zack
//        if (workOrder.getActualCompletionDateTime() == null) {
            workOrder.setActualCompletionDateTime(new Date());
//        }
        //update working time
        if (workOrder.getActualArrivalDateTime() != null) {
            TimeCount timeCount = workOrderRepository.findActualWorkingTimeById(workOrder.getId());
            if (timeCount == null) {
                timeCount = new TimeCount();
                timeCount.setUnit(TimeUnit.MINUTES);
            }
            //calculate the value between start and end
            DateTime start = new DateTime(workOrder.getActualArrivalDateTime());
            DateTime end = new DateTime(workOrder.getActualCompletionDateTime());
            int value = Minutes.minutesBetween(start, end).getMinutes();
            timeCount.setValue(Long.valueOf(value));
            timeCount = timeCountRepository.save(timeCount);
            workOrder.setActualWorkingTime(timeCount);
        }

        if (WorkOrder.WorkOrderType.PPM.equals(workOrder.getType())) {
            updatePMStep(workOrder);
        }

        workOrder = workOrderRepository.save(workOrder);

        return workOrder;
    }

    private void sendEmailToRequester(Requirement requirement, WorkOrder workOrder) {
        String emailAddress = requirement.getEmail();
        if(StringUtils.isEmpty(emailAddress)){
            log.error("There is no email address found which should not happen");
            return;
        }

        workOrderMessageService.sendEmailWhenOrderFinished(requirement,workOrder);

    }

    /**
     * when work order finished, send message to wechat requester
     *
     * @param requirement
     */
    private void sendWechatMessageToRequester(Requirement requirement, WorkOrder workOrder) {
        WechatTemplateMessageContent content = new WechatTemplateMessageContent();
        WechatTemplateMessageContent.Data event = new WechatTemplateMessageContent.Data();
        event.setValue(StringUtils.isEmpty(requirement.getDescription()) ? workOrder.getDescription() : requirement.getDescription());
        content.setEvent(event);
        WechatTemplateMessageContent.Data finishedTime = new WechatTemplateMessageContent.Data();
        Date finishedDate = workOrder.getActualCompletionDateTime();
        if (finishedDate == null) {
            finishedDate = new Date();
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
        finishedTime.setValue(format.format(finishedDate));
        content.setFinish_time(finishedTime);
        wechatProcessService.sendTemplateMessage(requirement.getWechatRequesterId(), content);
    }

    private void updatePMStep(WorkOrder workOrder) {
        workOrder = workOrderRepository.findOne(workOrder.getId());
        PreventiveMaintenance pm = workOrder.getPreventiveMaintenance();
        List<PMStep> steps = pmStepRepository.findByPmIdAndWoId(pm.getId(), workOrder.getId());
        if (CollectionUtils.isNotEmpty(steps)) {
            for (PMStep step : steps) {
                step.setFinished(true);
            }

            pmStepRepository.save(steps);
        }
    }

    /**
     * terminate the work order
     *
     * @param workOrder
     * @param request
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder terminateTask(WorkOrder workOrder, WorkOrderManagementRequest request) {
        // 非空
        Assert.notNull(workOrder, "Work order should not be null in completeTask");
        Assert.notNull(workOrder.getId(), "Work order id should not be null in completeTask");
        Assert.notNull(workOrder.getStatus(), "Work order status should not be null in completeTask");

        log.debug("work order:{} in terminateTask, status:{}", workOrder.getId(), workOrder.getStatus().name());

        // set work order laborer status
        Long workOrderId = workOrder.getId();
        changeWorkOrderLaborerStatus(workOrderId, WorkOrderConstant.OPERATION_TERMINATE);

        //process activiti task
        //complete activiti-task and move to TERMINATE
//        String processInstanceId = workOrder.getProcInstID();
//        WorkOrderTaskDTO toTerminateDTO = getTaskDTO(workOrder, WorkOrderConstant.OPERATION_TERMINATE);
//        toTerminateDTO.setProcessInstanceId(processInstanceId);
//        workOrderProcessService.completeByTask(toTerminateDTO);

        //update work order status and actual date time
//        if (workOrder.getActualCompletionDateTime() == null) {
            workOrder.setActualCompletionDateTime(new Date());
//        }
        if (workOrder.getActualArrivalDateTime() != null) {
            TimeCount timeCount = workOrderRepository.findActualWorkingTimeById(workOrder.getId());
            if (timeCount == null) {
                timeCount = new TimeCount();
                timeCount.setUnit(TimeUnit.MINUTES);
            }
            //calculate the value between start and end
            DateTime start = new DateTime(workOrder.getActualArrivalDateTime());
            DateTime end = new DateTime(workOrder.getActualCompletionDateTime());
            int value = Minutes.minutesBetween(start, end).getMinutes();
            timeCount.setValue(Long.valueOf(value));
            timeCount = timeCountRepository.save(timeCount);
            workOrder.setActualWorkingTime(timeCount);
        }
        workOrder.setStatus(WorkOrder.WorkOrderStatus.TERMINATE);

        if(workOrder.getWorkTeam()==null){
            setWorkTeamForCurrentLoginUser(workOrder);
        }

        workOrder = workOrderRepository.save(workOrder);

        // record activity
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.TERMINATE, request == null ? "" : request.getReason());

        return workOrder;
    }

    public void setWorkTeamForCurrentLoginUser(WorkOrder workOrder) {
        if (workOrder.getWorkOrderProcess() == null) {
            return;
        }

        List<WorkTeam> workTeamsInProcess = workOrderProcessRepository.findWorkTeamByProcessHardly(workOrder.getWorkOrderProcess().getId());
        if (CollectionUtils.isEmpty(workTeamsInProcess)) {
            return;
        }

        //current login user
        Long loginUser = commonUserService.findCurrentEmployeeId();
        if(loginUser!=null){
            for (WorkTeam team : workTeamsInProcess) {
                boolean isIncluded = workTeamService.isEmployeeIncludedIn(team.getId(), loginUser);
                if (isIncluded) {
                    workOrder.setWorkTeam(team);
                    break;
                }
            }
        }
        if(workOrder.getWorkTeam()==null){
            workOrder.setWorkTeam(workTeamsInProcess.get(0));
        }
    }

    /**
     * build task DTO by operation
     *
     * @param workOrder
     * @param operation
     * @return
     */
    private WorkOrderTaskDTO getTaskDTO(WorkOrder workOrder, int operation) {
        WorkOrder.WorkOrderStatus status = workOrder.getStatus();
        WorkOrderTaskDTO taskDTO = new WorkOrderTaskDTO();
        Map<String, Object> variables = new HashMap<>();
        if (operation == WorkOrderConstant.OPERATION_TERMINATE) {
            switch (status) {
                case PROCESS:
                    taskDTO.setCurrentTaskNode(WOProcessTemplateConstant.ELEMENT_TASK_EXECUTE);
                    variables.put(WOProcessTemplateConstant.PARAM_IS_COMPLETE_SUCCESS, WOProcessTemplateConstant.IS_COMPLETE_SUCCESS_VALUE_STOP);
                    break;
                case CREATE:
                    taskDTO.setCurrentTaskNode(WOProcessTemplateConstant.ELEMENT_TASK_SCHEDULING_DISPATCHING);
                    variables.put(WOProcessTemplateConstant.PARAM_IS_STOP, WOProcessTemplateConstant.IS_STOP_VALUE_TRUE);
                    break;
                case STOP_N:
                    taskDTO.setCurrentTaskNode(WOProcessTemplateConstant.ELEMENT_TASK_SCHEDULING_DISPATCHING);
                    variables.put(WOProcessTemplateConstant.PARAM_IS_STOP, WOProcessTemplateConstant.IS_STOP_VALUE_TRUE);
                    break;
                default:
                    break;
            }
        }

        if (operation == WorkOrderConstant.OPERATION_VALIDATE) {
            switch (status) {
                case FINISH:
                    taskDTO.setCurrentTaskNode(WOProcessTemplateConstant.ELEMENT_TASK_COMPLETE);
                    variables.put(WOProcessTemplateConstant.PARAM_IS_CHECKING, WOProcessTemplateConstant.IS_CHECKING_VALUE_TRUE);
                    break;
                case TERMINATE:
                    taskDTO.setCurrentTaskNode(WOProcessTemplateConstant.ELEMENT_TASK_TERMINATE);
                    variables.put(WOProcessTemplateConstant.PARAM_IS_CHECKING, WOProcessTemplateConstant.IS_CHECKING_VALUE_TRUE);
                    break;
                default:
                    break;
            }
        }
        if (operation == WorkOrderConstant.OPERATION_CLOSE) {
            switch (status) {
                case FINISH:
                    taskDTO.setCurrentTaskNode(WOProcessTemplateConstant.ELEMENT_TASK_COMPLETE);
                    variables.put(WOProcessTemplateConstant.PARAM_IS_CHECKING, WOProcessTemplateConstant.IS_CHECKING_VALUE_FALSE);
                    break;
                case TERMINATE:
                    taskDTO.setCurrentTaskNode(WOProcessTemplateConstant.ELEMENT_TASK_TERMINATE);
                    variables.put(WOProcessTemplateConstant.PARAM_IS_CHECKING, WOProcessTemplateConstant.IS_CHECKING_VALUE_FALSE);
                    break;
                case VALIDATATION:
                    //close directly
                    taskDTO.setCurrentTaskNode(WOProcessTemplateConstant.ELEMENT_TASK_ARCHIVE);
                    break;
                default:
                    break;
            }
        }
        taskDTO.setVariables(variables);
        return taskDTO;
    }

    /**
     * validate the work order
     *
     * @param workOrder
     * @param request
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder validateTask(WorkOrder workOrder, WorkOrderManagementRequest request) {
        // 非空
        Assert.notNull(workOrder, "Work order should not be null in validateTask");
        Assert.notNull(workOrder.getId(), "Work order id should not be null in validateTask");
        Assert.notNull(workOrder.getStatus(), "Work order status should not be null in validateTask");

        log.debug("work order:{} in validateTask, status:{}", workOrder.getId(), workOrder.getStatus().name());

        //check work order status
        //work order cannot be DISPATCHE status
        Assert.isTrue(checkStatusFor(workOrder, WorkOrderConstant.OPERATION_VALIDATE), "Work order status is not allowed in validateTask");

        // set work order laborer status
        changeWorkOrderLaborerStatus(workOrder.getId(), WorkOrderConstant.OPERATION_VALIDATE);

        //process activiti task
        //complete activiti-task and move to VALIDATE
//        String processInstanceId = workOrder.getProcInstID();
//        WorkOrderTaskDTO toValidate = getTaskDTO(workOrder, WorkOrderConstant.OPERATION_VALIDATE);
//        toValidate.setProcessInstanceId(processInstanceId);
//        workOrderProcessService.completeByTask(toValidate);

        //build activity description
        StringBuilder sb = new StringBuilder();

        //complete validation
        boolean isValidatePass = request.isValidatePass();
//        Map<String, Object> validationVariables = new HashMap<>();
//        String taskKey = WOProcessTemplateConstant.ELEMENT_TASK_CHECKING;
        //check validation result
        if (isValidatePass) {
            sb.append("验证通过");
//            validationVariables.put(WOProcessTemplateConstant.PARAM_CHECKING_RESULT, WOProcessTemplateConstant.CHECKING_RESULT_VALUE_TRUE);
            //update work order status
            workOrder.setStatus(WorkOrder.WorkOrderStatus.VALIDATATION);
        } else {
            sb.append("验证未通过");
            //check is there any worker here
            //if no----> move activiti to create else ---> move to process
            if (isAllLaborerFinished(workOrder)) {
//                validationVariables.put(WOProcessTemplateConstant.PARAM_CHECKING_RESULT, WOProcessTemplateConstant.CHECKING_RESULT_VALUE_FALSE_CREATE);
                //update work order status to create
                workOrder.setStatus(WorkOrder.WorkOrderStatus.CREATE);
            } else {
//                validationVariables.put(WOProcessTemplateConstant.PARAM_CHECKING_RESULT, WOProcessTemplateConstant.CHECKING_RESULT_VALUE_FALSE_EXECUTE);
                //update work order status to process
                workOrder.setStatus(WorkOrder.WorkOrderStatus.PROCESS);
            }
        }
        // record activity
        if (StringUtils.isNotBlank(request.getReason())) {
            sb.append("，");
            sb.append(request.getReason());
        }
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.VALIDATE, sb.toString());
//        workOrderProcessService.completeByTask(processInstanceId, taskKey, validationVariables);

        workOrder = workOrderRepository.save(workOrder);
        Long workOrderId = workOrder.getId();

        // 验证通过/不通过发送消息通知
        List<Employee> lastFinishLaborers = this.getLastLaborers(workOrder);
        if (isValidatePass) {
            workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_VALIDATE_PASS, lastFinishLaborers);
        } else {
            List<Employee> dispatchingPersons = workOrderService.findDispatchingPerson(workOrder);
            dispatchingPersons.addAll(lastFinishLaborers);
            workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_VALIDATE_FAIL, dispatchingPersons);
        }

        // 验证通过生成下一次todo
        if (isValidatePass && null != workOrder.getPreventiveMaintenance()) {
            // 如果该pm_todo的所有工单都完成了，才生成下一次todo
            if (null != workOrder.getPmDateTodo()) {
                PMDateTodo todo = workOrder.getPmDateTodo();
                List<WorkOrder> workOrders = workOrderRepository.findByTodoId(todo.getId());
                boolean allWoFinished = true;
                for (WorkOrder wo : workOrders) {
                    if (wo.getStatus().ordinal() != 6 && wo.getStatus().ordinal() != 7) {
                        allWoFinished = false;
                        break;
                    }
                }
                if (allWoFinished) {
                    pmDateTodoJobService.generateTodoAfterWorkorder(workOrder.getPreventiveMaintenance());
                }
            }
        }

        return workOrder;
    }

    /**
     * 获取最后一次派工，状态为已上交的员工
     *
     * @param workOrder
     * @return
     */
    private List<Employee> getLastLaborers(final WorkOrder workOrder) {
        // 查找到最后一次派工的记录
        WorkOrderActivity workOrderActivity =
                workOrderActivityRepository.findLastDispatchActivity(workOrder.getId(), WorkOrderActivity.ActivityType.DISPATCHE.ordinal());
        // 如果没有操作记录
        if (null == workOrderActivity) {
            return new ArrayList<Employee>();
        }
        Date createdDate = workOrderActivity.getCreatedDate();
        // 查找最后一次派工之后，状态为已上交的laborer记录
        List<Employee> laborers = workOrderLaborerRepository.findLastFinishLaborers(workOrder.getId(), LaborerStatus.FINISH, createdDate);
        return laborers;
    }

    /**
     * is there any laborer working on this order
     *
     * @param workOrder
     * @return
     */
    private boolean isAllLaborerFinished(WorkOrder workOrder) {
        List<WorkOrderLaborer> laborerList =
                workOrderLaborerRepository.findByWorkOrderId(workOrder.getId());
        if (CollectionUtils.isNotEmpty(laborerList)) {
            for (WorkOrderLaborer laborer : laborerList) {
                WorkOrderLaborer.LaborerStatus status = laborer.getStatus();
                if (LaborerStatus.RECEIVED.equals(status) || LaborerStatus.WAITING.equals(status)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * is all laborer reject or finished
     *
     * @param workOrder
     * @return
     */
    private boolean isAllLaborerReject(WorkOrder workOrder) {
        List<WorkOrderLaborer> laborerList =
                workOrderLaborerRepository.findByWorkOrderId(workOrder.getId());
        if (CollectionUtils.isNotEmpty(laborerList)) {
            for (WorkOrderLaborer laborer : laborerList) {
                WorkOrderLaborer.LaborerStatus status = laborer.getStatus();
                if (!(LaborerStatus.REJECTED.equals(status) || LaborerStatus.FINISH.equals(status))) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * check work order status for operation
     *
     * @param workOrder
     * @param operation
     * @return
     */
    private boolean checkStatusFor(WorkOrder workOrder, int operation) {
        if (operation == WorkOrderConstant.OPERATION_VALIDATE) {
            if (WorkOrder.WorkOrderStatus.FINISH.equals(workOrder.getStatus())) {
                return true;
            }

            if (WorkOrder.WorkOrderStatus.TERMINATE.equals(workOrder.getStatus())) {
                return true;
            }
        }
        if (operation == WorkOrderConstant.OPERATION_CLOSE) {
            if (WorkOrder.WorkOrderStatus.FINISH.equals(workOrder.getStatus())) {
                return true;
            }

            if (WorkOrder.WorkOrderStatus.TERMINATE.equals(workOrder.getStatus())) {
                return true;
            }

            if (WorkOrder.WorkOrderStatus.VALIDATATION.equals(workOrder.getStatus())) {
                return true;
            }
        }
        return false;
    }

    /**
     * close the work order
     *
     * @param workOrder
     * @param request
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder closeTask(WorkOrder workOrder, WorkOrderManagementRequest request) {
        // 非空
        Assert.notNull(workOrder, "Work order should not be null in closeTask");
        Assert.notNull(workOrder.getId(), "Work order id should not be null in closeTask");
        Assert.notNull(workOrder.getStatus(), "Work order status should not be null in closeTask");

        log.debug("work order:{} in closeTask, status:{}", workOrder.getId(), workOrder.getStatus().name());

        //check work order status
        Assert.isTrue(checkStatusFor(workOrder, WorkOrderConstant.OPERATION_CLOSE), "Work order status is not allowed in closeTask");

        // 暂存一下工单传进来的状态，如果是PPM工单则需要用到
        WorkOrder.WorkOrderStatus inStatus = workOrder.getStatus();

        Long workOrderId = workOrder.getId();

        // set work order laborer status
        changeWorkOrderLaborerStatus(workOrderId, WorkOrderConstant.OPERATION_CLOSE);

        // record activity
        if (request.isValidatePass()) {
            WorkOrderActivity activity = new WorkOrderActivity();
            activity.setWorkOrder(workOrder);
            activity.setActivityType(WorkOrderActivity.ActivityType.CLOSE);
            activity.setCreatedBy(SYSTEM_STRING);
            activity = workOrderActivityRepository.save(activity);
        } else {
            workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.CLOSE);
        }

        //process activiti task
//        String processInstanceId = workOrder.getProcInstID();
//        WorkOrderTaskDTO toClose = getTaskDTO(workOrder, WorkOrderConstant.OPERATION_CLOSE);
//        toClose.setProcessInstanceId(processInstanceId);
//        workOrderProcessService.completeByTask(toClose);

        //if come from TERMINATE and FINISH, need complete close task
//        if (toClose.getVariables() != null && !toClose.getVariables().isEmpty()) {
//            String taskKey = WOProcessTemplateConstant.ELEMENT_TASK_ARCHIVE;
//            workOrderProcessService.completeByTask(processInstanceId, taskKey, null);
//        }

        //update work order status
        workOrder.setStatus(WorkOrder.WorkOrderStatus.CLOSE);
        workOrder = workOrderRepository.save(workOrder);

        // 生成下一次todo
        if (null != workOrder.getPreventiveMaintenance()) {
            // 如果工单关闭之前是已验证状态，就不再生成todo（因为验证时候会生成一次，避免重复生成）
            if (!WorkOrder.WorkOrderStatus.VALIDATATION.equals(inStatus)) {
                // 如果该pm_todo的所有工单都完成了，才生成下一次todo
                if (null != workOrder.getPmDateTodo()) {
                    PMDateTodo todo = workOrder.getPmDateTodo();
                    List<WorkOrder> workOrders = workOrderRepository.findByTodoId(todo.getId());
                    boolean allWoFinished = true;
                    for (WorkOrder wo : workOrders) {
                        if (wo.getStatus().ordinal() != 6 && wo.getStatus().ordinal() != 7) {
                            allWoFinished = false;
                            break;
                        }
                    }
                    if (allWoFinished) {
                        pmDateTodoJobService.generateTodoAfterWorkorder(workOrder.getPreventiveMaintenance());
                    }
                }
            }
        }

        return workOrder;
    }

    /**
     * Continue task after pause.
     *
     * @param workOrder
     * @param request
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public WorkOrder continueTask(WorkOrder workOrder, WorkOrderManagementRequest request) {
        // 非空
        Assert.notNull(workOrder, "Work order should not be null in continueTask");
        Assert.notNull(workOrder.getId(), "Work order id should not be null in continueTask");
        Assert.notNull(workOrder.getStatus(), "Work order status should not be null in continueTask");

        //status should only be stop
        Assert.isTrue(WorkOrder.WorkOrderStatus.STOP.equals(workOrder.getStatus()), "Only STOP status allowed here");

        //add activity
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.CONTINUE);

        //check if suspend
//        String processInstanceId = workOrder.getProcInstID();
//        if (workOrderProcessService.isProcessSuspend(processInstanceId)) {
//            //reactive activiti instance
//            workOrderProcessService.continueProcessInstanceById(processInstanceId);
//        }

        //change work order status and doing nothing special
        workOrder.setStatus(WorkOrder.WorkOrderStatus.PROCESS);

        TimeCount timeCount = workOrderRepository.findActualWorkingTimeById(workOrder.getId());

        if (timeCount == null) {
            timeCount = new TimeCount();
            timeCount.setUnit(TimeUnit.MINUTES);
        }
        TimeCount times = workOrderRepository.findStopTimeById(workOrder.getId());
        DateTime stopTime =new DateTime(workOrder.getEndTime());
        DateTime recoverTime = new DateTime(new Date());
        int value = Minutes.minutesBetween(stopTime, recoverTime).getMinutes();
        if(null==times){
            timeCount.setValue(Long.valueOf(value));
        }else{
            timeCount.setValue(Long.valueOf(times.getValue()+value));
        }
        timeCount.setUnit(TimeUnit.MINUTES);
        timeCount = timeCountRepository.save(timeCount);
        workOrder.setStopTime(timeCount);
        workOrder = workOrderRepository.save(workOrder);

        return workOrder;
    }

    /**
     * complete create and move to dispatching
     *
     * @param workOrder
     * @return
     */
    @Override
    public WorkOrder completeCreate(WorkOrder workOrder) {
        boolean dispatching = false;
        Employee dispatchingEmployee = null;
        WorkOrderProcess workOrderProcess = workOrderRepository.findOneWithProcess(workOrder.getId()).getWorkOrderProcess();
        if (workOrderProcess != null) {
            List<Employee> employees = workOrderProcessRepository.findLaborerByProcess(workOrderProcess.getId());
            //过滤掉已经离职员工
            if(CollectionUtils.isNotEmpty(employees)){
                Iterator<Employee> it = employees.iterator();
                while(it.hasNext()){
                    Employee employee = it.next();
                    if(!employee.isActivated()){
                        it.remove();
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(employees)) {
                dispatchingEmployee = employees.get(0);
                workOrderLaborerService.addLaborerForWorkOrder(workOrder, employees, null);
                dispatching = true;
            }
        }

//        String processInstanceId = workOrder.getProcInstID();
//        String toDispatch = WOProcessTemplateConstant.ELEMENT_TASK_WORKORDER_CREATE;
//        Map<String, Object> toDispatchVariables = new HashMap<>();
//        if (workOrderProcess.getAutoIssue()) {
//            toDispatchVariables.put(WOProcessTemplateConstant.PARAM_IS_AUTO_DISPATCHING, WOProcessTemplateConstant.IS_AUTO_DISPATCHING_VALUE_TRUE);
//        } else {
//            toDispatchVariables.put(WOProcessTemplateConstant.PARAM_IS_AUTO_DISPATCHING, WOProcessTemplateConstant.IS_AUTO_DISPATCHING_VALUE_FALSE);
//        }
        //work order activity
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.CREATE);

        //自动派工
        if (workOrderProcess.getAutoIssue()) {
            workOrderService.autoDispatchBySignAndWo(workOrder);
        }

//        workOrderProcessService.completeByTask(processInstanceId, toDispatch, toDispatchVariables);

        if (dispatching) {
            workOrder = dispatchTask(workOrder, null, WorkOrderConstant.DISPATCHING_TYPE_AUTO);
            //work team---if work team existed, do nothing; else set work team = work team in work order process
            if (workOrder.getWorkTeam() == null) {
                List<WorkTeam> workTeams = workOrderProcessRepository.findWorkTeamByProcess(workOrderProcess.getId());
                workOrder.setWorkTeam(CollectionUtils.isEmpty(workTeams) ? null : workTeams.get(0));
                workOrder = workOrderRepository.save(workOrder);
            }
            workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_DISPATCH, workOrder.getDescription());
        }
        return workOrder;
    }

    /**
     * ppmjob使用
     * @param workOrder
     * @return
     */
    @Override
    public WorkOrder completeCreateJob(WorkOrder workOrder) {
        boolean dispatching = false;
        Employee dispatchingEmployee = null;
        WorkOrderProcess workOrderProcess = workOrderRepository.findOneWithProcessHardly(workOrder.getId(),workOrder.getProject()).getWorkOrderProcess();
        if (workOrderProcess != null) {
            List<Employee> employees = workOrderProcessRepository.findLaborerByProcessHardly(workOrderProcess.getId(),workOrder.getProject());
            //过滤掉已经离职员工
            if(CollectionUtils.isNotEmpty(employees)){
                Iterator<Employee> it = employees.iterator();
                while(it.hasNext()){
                    Employee employee = it.next();
                    if(!employee.isActivated()){
                        it.remove();
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(employees)) {
                dispatchingEmployee = employees.get(0);
                workOrderLaborerService.addLaborerForWorkOrder(workOrder, employees, null);
                dispatching = true;
            }
        }

//        String processInstanceId = workOrder.getProcInstID();
//        String toDispatch = WOProcessTemplateConstant.ELEMENT_TASK_WORKORDER_CREATE;
//        Map<String, Object> toDispatchVariables = new HashMap<>();
//        if (workOrderProcess.getAutoIssue()) {
//            toDispatchVariables.put(WOProcessTemplateConstant.PARAM_IS_AUTO_DISPATCHING, WOProcessTemplateConstant.IS_AUTO_DISPATCHING_VALUE_TRUE);
//        } else {
//            toDispatchVariables.put(WOProcessTemplateConstant.PARAM_IS_AUTO_DISPATCHING, WOProcessTemplateConstant.IS_AUTO_DISPATCHING_VALUE_FALSE);
//        }
        //work order activity
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.CREATE);

        //自动派工
        if (workOrderProcess.getAutoIssue()) {
            workOrderService.autoDispatchBySignAndWo(workOrder);
        }

//        workOrderProcessService.completeByTask(processInstanceId, toDispatch, toDispatchVariables);

        if (dispatching) {
            workOrder = dispatchTask(workOrder, null, WorkOrderConstant.DISPATCHING_TYPE_AUTO);
            //work team---if work team existed, do nothing; else set work team = work team in work order process
            if (workOrder.getWorkTeam() == null) {
                List<WorkTeam> workTeams = workOrderProcessRepository.findWorkTeamByProcess(workOrderProcess.getId());
                workOrder.setWorkTeam(CollectionUtils.isEmpty(workTeams) ? null : workTeams.get(0));
                workOrder = workOrderRepository.save(workOrder);
            }
            workOrderMessageService.handleWorkOrderProcessNotice(workOrder, WOProcessTemplateConstant.STEP_DISPATCH, workOrder.getDescription());
        }
        return workOrder;
    }

    @Override
    public WorkOrder saveTask(WorkOrder workOrder, WorkOrderManagementRequest request) {
        workOrderActivityService.addActivityFor(workOrder, WorkOrderActivity.ActivityType.CLOSE);
        return workOrder;
    }
}
