package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.workorder.Scheduling;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 6/16/2015
 */
public interface SchedulingRepository extends XiaRepository<Scheduling, Long> {

    @Query("select s from #{#entityName} s left join fetch s.cls where s.employee.id = ?1")
    List<Scheduling> findEntireOneByEmployeeId(Long id);

    @Query("select s from #{#entityName} s left join fetch s.cls where s.employee.id = ?1 and s.date = ?2")
    List<Scheduling> findEntireOneByEmployeeIdAndDate(Long id, Date date);

    @Query("select s.employee from #{#entityName} s  where s.id in (?1)")
    List<Employee> findEmployeeByIdsIn(Long[] ids);

    @Query("select s from #{#entityName} s left join fetch s.employee left join fetch s.cls cls left join fetch cls.scheduleClassTimes where s.employee in (?1) and s.date = ?2")
    List<Scheduling> findAllByEmployeesAndDate(List<Employee> members, Date date);

    List<Scheduling> findByYearAndMonthAndEmployeeIn(String year, String month, List<Employee> laborers);

    @Query("select s from #{#entityName} s left join  s.employee em left join  s.cls cls where  s.date = ?1  and  em.id = ?2 and cls.id = ?3")
    Scheduling findByDateAndEmployeeAndClass(Date date, Long emId, Long clsId);

    @Modifying
    @Query("delete  from #{#entityName} sch  where sch.employee.id in (?1) and sch.year = ?2  and  sch.month = ?3 ")
    void deleteSchsByEmployeesAndYearAndMonth(Long[] ids,String year,String month);

    @Query("select s from #{#entityName} s where s.deleted=0")
    List<Scheduling> findAllWithoutProjectHardly();

    @Query("select s from #{#entityName} s left join fetch s.cls c where s.deleted=0 and s.project=?1 and c.id=?2")
    List<Scheduling> findByProjectAndClsHardly(Long projId, Long clsId);

}
