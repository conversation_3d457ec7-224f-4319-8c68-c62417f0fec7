package cn.facilityone.shang.workorder.common.repository;

import java.util.Date;
import java.util.List;

import cn.facilityone.shang.entity.workorder.Priority;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * @Author: wayne.fu
 * @Date: 5/18/2015
 */
public interface PriorityRepository extends XiaRepository<Priority, Long> {
    
    @Query("select pri from #{#entityName} pri order by pri.sort asc")
    List<Priority> findAllOrderBySortAsc();

    @Query("select pri from #{#entityName} pri where pri.deleted=0 and pri.project=?1 order by pri.sort asc")
    List<Priority> findAllOrderBySortAscHardly(Long proId);

    @Query("select pri from #{#entityName} pri where pri.name = ?1")
    Priority findByName(String name);
    
    /**
     * 更新基础数据专用
     * @param lastModifiedDate
     * @return
     */
    @Query("select pri from #{#entityName} pri where pri.modifiedDate >= ?1 and pri.project=?2 ")
    List<Priority> findByModifiedDateAfterHardly(Date lastModifiedDate,Long currentProjectId);
    
    /**
     * 更新基础数据专用
     * @param lastModifiedDate
     * @return
     */
    @Query("select count(1) from #{#entityName} o where o.modifiedDate >= ?1 and o.project=?2  ")
    Long findCountByModifiedDateAfterHardly(Date lastModifiedDate,Long currentProjectId);

    @Query("select o from #{#entityName} o where o.deleted=0 and o.project=?1  ")
    List<Priority> findByProjectHardly(Long currentProjectId);

    /**
     * 优先级名称唯一性验证专用 - 用list接收，防止以前有多个重名的会报错
     * @param name
     * @return
     */
    @Query("select pri from #{#entityName} pri where pri.name = ?1")
    List<Priority> findListByName(String name);

    @Query("select pri from #{#entityName} pri where pri.name = ?1 and pri.id != ?2")
    List<Priority> findOthersListByName(String name, Long id);
}
