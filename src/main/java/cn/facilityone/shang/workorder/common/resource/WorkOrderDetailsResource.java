package cn.facilityone.shang.workorder.common.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.entity.common.Picture;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.epayment.PaymentOrder;
import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.servercenter.Requirement;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderLaborer;
import cn.facilityone.shang.entity.workorder.WorkOrderSpace;
import cn.facilityone.shang.epayment.epayment001.service.EPaymentManageService;
import cn.facilityone.shang.organize.org002.service.EmployeeService;
import cn.facilityone.shang.preventive.pm001.service.PMStepService;
import cn.facilityone.shang.servicecenter.common.service.RequirementActivityService;
import cn.facilityone.shang.servicecenter.common.service.WorkOrderLaborerService;
import cn.facilityone.shang.servicecenter.service001.service.RequirementCenterService;
import cn.facilityone.shang.workorder.common.constant.WorkOrderConstant;
import cn.facilityone.shang.workorder.common.dto.*;
import cn.facilityone.shang.workorder.common.repository.WorkOrderSpaceRepository;
import cn.facilityone.shang.workorder.common.service.*;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 工单详情
 *
 * <AUTHOR>
 */
@Path("/wo000")
public class WorkOrderDetailsResource {

    @Context
    private HttpServletResponse response;

    @Context
    private HttpServletRequest request;

    @Autowired
    private RequirementCenterService requirementCenterService;
    @Autowired
    private WorkOrderService workOrderService;

    @Autowired
    private UploadFileService uploadFileService;

    @Autowired
    private WorkOrderEquipmentService workOrderEquipmentService;

    @Autowired
    private RequirementActivityService requirementActivityService;

    @Autowired
    private WorkOrderLaborerService workOrderLaborerService;

    @Autowired
    private WorkOrderToolService workOrderToolService;

    @Autowired
    private WorkOrderMaterialService workOrderMaterialService;

    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private PMStepService pmStepService;
    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private WorkOrderActivityService workOrderActivityService;
    @Autowired
    private WorkOrderSpaceService workOrderSpaceService;
    @Autowired
    private EPaymentManageService  ePaymentManageService;


    @GET
    @Path("details/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getWorkOrder(@PathParam("workOrderId") Long workOrderId) {
        WorkOrder workOrder = requirementCenterService.findWorkOrderByIDWithAllInfo(workOrderId);
        return new Result(workOrder);
    }

    @POST
    @Path("labours/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getWorkOrderLaborer(@PathParam("workOrderId") Long workOrderId, DataTableRequest request) {
        List<WorkOrderLaborer> workOrderLaborers = workOrderLaborerService.findByWorkOrderIDAndStatue(workOrderId);
        return new Result(workOrderLaborers);
    }

    @POST
    @Path("labours/schedule/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse scheduleAnd(@PathParam("workOrderId") Long workOrderId, DataTableRequest request) {
        DataTableResponse response = new DataTableResponse();
        response.setData(workOrderLaborerService.findScheduleDTOByWorkOrderId(workOrderId,request));
        return response;
    }

    @GET
    @Path("labours/schedule/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getScheduleData(@PathParam("workOrderId") Long workOrderId) {
        WorkOrderScheduleRequestDTO dto = workOrderLaborerService.getScheduleRequestDTOByWorkOrderId(workOrderId);
        return new Result(dto);
    }

    @GET
    @Path("labours/{labourId}/accept/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result acceptWorkOrder(@PathParam("labourId") Long laborerId, @PathParam("workOrderId") Long workOrderId) {
        WorkOrder workOrder = workOrderLaborerService.acceptWorkOrder(workOrderId, laborerId);
        return new Result(workOrder);
    }

    @POST
    @Path("labours/{labourId}/reject/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result rejectWorkOrder(@PathParam("labourId") Long laborerId, @PathParam("workOrderId") Long workOrderId,String reason) {
        WorkOrder workOrder = workOrderLaborerService.rejectWorkOrder(workOrderId, laborerId, reason);
        return new Result(workOrder);
    }

    @GET
    @Path("requirements/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getRequirement(@PathParam("workOrderId") Long workOrderId) {
        Requirement requirement = requirementCenterService.findRequirementByWorkOrderId(workOrderId);
        if (requirement != null) {
            requirement.setActivities(requirementActivityService.getRequirementActivityByRequirementId(requirement.getId()));
        }
        return new Result(requirement);
    }

    @GET
    @Path("attachments/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getAttachments(@PathParam("workOrderId") Long workOrderId) {
        return new Result(uploadFileService.getAttachmentsByTableNameAndPkeyId(WorkOrderConstant.WORKORDER_TABLE_NAME, String.valueOf(workOrderId)));
    }


    @GET
    @Path("pictures/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getPictures(@PathParam("workOrderId") Long workOrderId) {
        List<Picture> atts =uploadFileService.getPicturesByTableNameAndPkeyId(WorkOrderConstant.WORKORDER_TABLE_NAME, String.valueOf(workOrderId));
        List<Picture> woaAtts = workOrderActivityService.findPicturesByWoId(workOrderId);
        return new Result(removeDoubleFile(atts, woaAtts));
    }

    private Collection<XiaFile> removeDoubleFile(List<? extends XiaFile> aL,List<? extends XiaFile> bL){
        Map<String,XiaFile> datas = new LinkedHashMap<String,XiaFile>();
        if(CollectionUtils.isNotEmpty(aL)){
            for(XiaFile f:aL){
                datas.put(f.getPath(), f);
            }
        }
        if(CollectionUtils.isNotEmpty(bL)){
            for(XiaFile f:bL){
                if(!datas.containsKey(f.getPath())){
                    datas.put(f.getPath(), f);
                }
            }
        }
        return datas.values();
    }

    @GET
    @Path("labours/entire/{labourId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getEntireLabour(@PathParam("labourId") Long labourId) {
        return new Result(employeeService.findEntireEmployee(labourId));
    }

    @GET
    @Path("labours/{labourId}/check/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result checkLaborerExisted(@PathParam("labourId") Long labourId, @PathParam("workOrderId") Long workOrderId) {
        Result result = new Result();
        result.put("result", workOrderLaborerService.isLaborExisted(labourId, workOrderId));
        return result;
    }


    @GET
    @Path("equipments/{equipmentId}/check/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result checkEquipmentExisted(@PathParam("equipmentId") Long equipmentId, @PathParam("workOrderId") Long workOrderId) {
        Result result = new Result();
        result.put("result", workOrderEquipmentService.isEquipmentExisted(equipmentId, workOrderId));
        return result;
    }

    @POST
    @Path("equipments/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWorkOrderEquipmentByOrderId(
            @PathParam("workOrderId") Long workOrderId, DataTableRequest request) {
        return new DataTableResponse(10,
                workOrderEquipmentService.findWorkOrderEquipmentByOrderId(workOrderId));
    }

    @GET
    @Path("equipments/entire/{equipmentId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getEntireEquipment(@PathParam("equipmentId") Long equipmentId) {
        return new Result(workOrderEquipmentService.findEntireEquipment(equipmentId));
    }

    @POST
    @Path("tools/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWorkOrderTools(
            @PathParam("workOrderId") Long workOrderId, DataTableRequest request) {
        return new DataTableResponse(10,
                workOrderToolService.findByWorkOrderId(workOrderId));
    }

    @POST
    @Path("materials/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWorkOrderMaterials(
            @PathParam("workOrderId") Long workOrderId, DataTableRequest request) {
        return new DataTableResponse(10,
                workOrderMaterialService.findMaterialDTOByWorkOrderId(workOrderId));
    }

    @POST
    @Path("chargeDetails/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWorkOrderChargeDetails(
            @PathParam("workOrderId") Long workOrderId, DataTableRequest request) {
        return new DataTableResponse(10,
                workOrderActivityService.findActivitysFor(workOrderId));
    }


    @POST
    @Path("materials/book/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result bookMaterial(
            @PathParam("workOrderId") Long workOrderId, List<WorkOrderMaterialRequest> request) {
        workOrderMaterialService.bookMaterials(request,workOrderId);
        return new Result(XiaMesssageResource.getMessage("server.result.success.update",null,XiaMesssageResource.getMessage("WorkOrder.material")));
    }

    @PUT
    @Path("materials")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result updateMaterials(List<WorkOrderMaterialRequest> request) {
        workOrderMaterialService.updateMaterials(request);
        return new Result("更新成功");
    }

    @POST
    @Path("steps/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getPMSteps(
            @PathParam("workOrderId") Long workOrderId, DataTableRequest request) {
        List<PMStep> steps = pmStepService.findByWoId(workOrderId);
        WorkOrder workOrder = workOrderRepository.findOneWithWorkTeam(workOrderId);
        List<PMStepDTO> stepDTOs = pmStepService.createDTO(steps,workOrder);
        return new DataTableResponse(10, stepDTOs);
    }

    @DELETE
    @Path("materials/{workOrderId}/{materialId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result deleteMaterial(@PathParam("workOrderId") Long workOrderId, @PathParam("materialId") Long materialId) {
        workOrderMaterialService.deleteByWorkOrderIdAndMaterialId(workOrderId, materialId);
        return new Result(XiaMesssageResource.getMessage("server.result.success.delete", null, ""));
    }

    @DELETE
    @Path("equipments/{workOrderId}/{equipmentId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result deleteEquipment(@PathParam("workOrderId") Long workOrderId, @PathParam("equipmentId") Long equipmentId) {
        workOrderEquipmentService.delete(workOrderId, equipmentId);
        return new Result(XiaMesssageResource.getMessage("server.result.success.delete", null, ""));
    }

    @DELETE
    @Path("labours/{workOrderId}/{labourId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result deleteLabour(@PathParam("workOrderId") Long workOrderId, @PathParam("labourId") Long labourId) {
        workOrderLaborerService.delete(workOrderId, labourId);
        return new Result(XiaMesssageResource.getMessage("server.result.success.delete", null, ""));
    }

    @DELETE
    @Path("tools/{workOrderId}/{toolId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result deleteTool(@PathParam("workOrderId") Long workOrderId, @PathParam("toolId") Long toolId) {
        workOrderToolService.delete(workOrderId, toolId);
        return new Result(XiaMesssageResource.getMessage("server.result.success.delete", null, ""));
    }

    @GET
    @Path("pmsteps/{woid}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getPmStep(@PathParam("woid") Long woid) {
        return new Result(pmStepService.findByWoId(woid));
    }

    @GET
    @Path("workorders/{woId}/associated")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getAssociatedWorkOrders(@PathParam("woId") Long workOrderId){
        return new Result(workOrderService.findAssociatedWorkOrdersById(workOrderId));
    }

    @POST
    @Path("workOrderSpaces/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getSpaceByOrderId(
            @PathParam("workOrderId") Long workOrderId,DataTableRequest request) {
        List<WorkOrderSpace> spaces = workOrderService.findspaceByWoId(workOrderId);
        List<WorkOrderSpaceDTO> pmSpaceDto = workOrderService.createDto(spaces);
        return new DataTableResponse(10,pmSpaceDto);
    }

    @DELETE
    @Path("pmspace/{workOrderId}/{pmspaceId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result deletePMSpace(@PathParam("workOrderId") Long workOrderId, @PathParam("pmspaceId") Long pmspaceId) {
        workOrderService.delete(workOrderId, pmspaceId);
        return new Result(XiaMesssageResource.getMessage("server.result.success.delete", null, ""));
    }

    @GET
    @Path("workOrderSpaces/check/{types}/{spaceId}/{workOrderId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result checkSpaceExisted(@PathParam("spaceId") Long spaceId, @PathParam("workOrderId") Long workOrderId,@PathParam("types") Long types) {
        Result result = new Result();
        result.put("result", workOrderSpaceService.isSpaceExisted(spaceId, workOrderId,types));
        return result;
    }

    @GET
    @Path("building/entire/{spaceId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getEntireBuilding(@PathParam("spaceId") Long spaceId) {
        return new Result(workOrderSpaceService.findEntireBuilding(spaceId));

    }

    @GET
    @Path("floor/entire/{spaceId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getEntireFloor(@PathParam("spaceId") Long spaceId) {
        return new Result(workOrderSpaceService.findEntireFloor(spaceId));

    }

    @GET
    @Path("room/entire/{spaceId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getEntireRoom(@PathParam("spaceId") Long spaceId) {
        return new Result(workOrderSpaceService.findEntireRoom(spaceId));
    }

    @POST
    @Path("payment/{workOrderId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getPaymentOrderByOrderId(
            @PathParam("workOrderId") Long workOrderId,DataTableRequest request) {
        List<PaymentOrder> paymentOrder = ePaymentManageService.getPaymentOrderByWoId(workOrderId);
        List<PaymentOrderByWoDTO> pmSpaceDto = workOrderService.createPaymentDto(paymentOrder);
        return new DataTableResponse(10,pmSpaceDto);
    }
}
