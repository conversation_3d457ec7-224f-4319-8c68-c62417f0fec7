package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.workorder.Escalation;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

/**
 * @Author: wayne.fu
 * @Date: 5/22/2015
 */
public interface EscalationRepository extends XiaRepository<Escalation, Long> {

    @Query("select o from #{#entityName} o left join fetch o.value left join fetch o.notice n left join fetch n.notifiers  where o.id in(?1) ")
    List<Escalation> findByIdForJob(Set<Long> escalationIds);
}
