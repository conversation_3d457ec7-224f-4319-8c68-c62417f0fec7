package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.workorder.Approval;
import cn.facilityone.shang.entity.workorder.ApprovalTemplate;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

/**
 * @Author: wayne.fu
 * @Date: 6/19/2015
 */
public interface ApprovalRepository extends XiaRepository<Approval, Long> {

    @Query("select e.workOrder from #{#entityName} e where e.id = ?1")
    WorkOrder findWorkOrderByApprovalId(Long id);

    @Query("select e from #{#entityName} e left join fetch e.workOrder left join fetch e.template left join fetch e.result where e.id = ?1")
    Approval findEntireOne(Long id);


    @Query("select e from #{#entityName} e left join fetch e.params left join fetch e.workOrder where e.workOrder.id = ?1")
    Set<Approval> findByWorkOrderId(Long workOrderId);

    @Query("select e.workOrder from #{#entityName} e where e.id in (?1)")
    List<WorkOrder> findWorkOrderByApprovalIds(List<Long> approvalIds);

    @Query("select e.template from #{#entityName} e where e.id in (?1)")
    List<ApprovalTemplate> findTemplateByApprovalIds(List<Long> approvalIds);
}
