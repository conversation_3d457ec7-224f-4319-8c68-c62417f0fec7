package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.workorder.ProcessNotice;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 5/22/2015
 */
public interface ProcessNoticeRepository extends XiaRepository<ProcessNotice, Long> {

    @Query("select pn from #{#entityName} pn where pn.workOrderProcess.id in (?1)")
    List<ProcessNotice> findAllByProcessIds(List<Long> workOrderProcessIds);

    @Query("select distinct pn from #{#entityName} pn left join fetch pn.notice left join fetch pn.notice.notifiers where pn.workOrderProcess.id = ?1")
    List<ProcessNotice> findAllByProcessId(Long processId);
}
