package cn.facilityone.shang.workorder.common.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.common.ExtraColumn;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 6/18/2015
 */
public interface WorkOrderExtraColumnService {
    Page<ExtraColumn> findByTemplateIdInPage(DataTableRequest request, Long templateId);

    ExtraColumn save(ExtraColumn extraColumn);

    ExtraColumn getOne(Long id);

    void deleteOne(Long id);

    void update(ExtraColumn extraColumn);

    List<ExtraColumn> getAllParametersByTemplateId(Long templateId);
}
