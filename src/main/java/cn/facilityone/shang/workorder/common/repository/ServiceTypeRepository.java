package cn.facilityone.shang.workorder.common.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import cn.facilityone.shang.entity.workorder.ServiceType;
import cn.facilityone.xia.persistence.repository.XiaRepository;

/**
 * @Author: wayne.fu
 * @Date: 5/19/2015
 */
public interface ServiceTypeRepository extends XiaRepository<ServiceType, Long> {

    @Query("select r.id from #{#entityName} r where r.parentServiceType.id = ?1")
    List<Long> findByGroupId(Long roleId);
    
    @Query("select r from #{#entityName} r where r.parentServiceType = ?1")
    List<ServiceType> findByParent(ServiceType st);

    @Query("select r from #{#entityName} r where r.parentServiceType = ?1 and r.project=?2")
    List<ServiceType> findByParentHardly(ServiceType st,Long proId);
    
    @Query("select r from #{#entityName} r where r.parentServiceType is null")
    List<ServiceType> findByParentIsNull();
    
    @Query("select r from #{#entityName} r where r.parentServiceType.id = ?1")
    List<ServiceType> findByParentId(Long id);
    
    /**
     * 更新基础数据专用
     * @param lastModifiedDate
     * @return
     */
    @Query("select st from #{#entityName} st left join fetch st.parentServiceType where st.modifiedDate >= ?1 and st.project=?2 ")
    List<ServiceType> findByModifiedDateAfterHardly(Date lastModifiedDate,Long currentProjectId);
    
    /**
     * 更新基础数据专用
     * @param lastModifiedDate
     * @return
     */
    @Query("select count(1) from #{#entityName} o where o.modifiedDate >= ?1 and o.project=?2 ")
    Long findCountByModifiedDateAfterHardly(Date lastModifiedDate,Long currentProjectId);

    @Query("select r from #{#entityName} r left join fetch r.parentServiceType where r.id in (?1)")
    List<ServiceType> findWithParent(List<Long> rootIds);
    
    @Query("select st from #{#entityName} st left join fetch st.parentServiceType where st.deleted=0 and st.project=?1 ")
    List<ServiceType> findByProjectHardly(Long currentProjectId);

    @Query("select st from #{#entityName} st left join fetch st.parentServiceType where st.deleted=0 and st.project=?1 and st.parentServiceType is null ")
    List<ServiceType> findParentStHardly(Long currentProjectId);

    @Query("select st from #{#entityName} st where st.id in (?1) order by st.fullName asc ")
    List<ServiceType> findByIdsAndSort(List<Long> stypeIds);

    @Query("select st from #{#entityName} st order by st.fullName asc")
    List<ServiceType> findAllSort();

    @Query("select st from #{#entityName} st where st.fullName like %?1% order by st.fullName asc")
    List<ServiceType> findAllSortBySearchText(String searchText);

    @Query("select r from #{#entityName} r inner join fetch r.parentServiceType  where r.id =?1 ")
    List<ServiceType>  findParentId(Long rootIds);

    /**
     * 服务类型名称唯一性验证专用(findParentServiceTypeByName / findServiceTypeByNameAndPid)
     *
     * @param name
     * @return
     */
    @Query("select r from #{#entityName} r where r.name=?1 and r.parentServiceType is null")
    List<ServiceType> findParentServiceTypeByName(String name);
    @Query("select r from #{#entityName} r where r.name=?1 and r.parentServiceType is null and r.id != ?2")
    List<ServiceType> findOthersParentServiceTypeByName(String name, Long id);

    @Query("select r from #{#entityName} r where r.name=?1 and r.parentServiceType.id=?2")
    List<ServiceType> findServiceTypeByNameAndPid(String name, Long pid);
    @Query("select r from #{#entityName} r where r.name=?1 and r.parentServiceType.id=?2 and r.id != ?3")
    List<ServiceType> findOthersServiceTypeByNameAndPid(String name, Long pid, Long id);

    @Query("select r from #{#entityName} r where r.parentServiceType is null and r.project=?1 and r.deleted=0")
    List<ServiceType> findByParentIsNullbyProjectHardly(Long currentProjectId);

    @Query("select r.id from #{#entityName} r where r.parentServiceType.id in(?1) AND r.deleted=0")
    List<Long> findByParentServiceTypeIdHardly(List id);

    @Query("select r from #{#entityName} r where r.project=?1 and r.deleted=0")
    List<ServiceType> findAllHardly(Long proId);

    /**
     *获取最大排序
     */
    @Query(value="SELECT max(LENGTH(full_name) - LENGTH( REPLACE(full_name,'/',''))) FROM service_type where deleted=0 and proj_id=?1 ",nativeQuery = true)
    Integer findMaxLevel(Long currentProjectId);

}
