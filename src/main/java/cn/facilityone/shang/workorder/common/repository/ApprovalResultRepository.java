package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.workorder.ApprovalResult;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 6/19/2015
 */
public interface ApprovalResultRepository extends XiaRepository<ApprovalResult, Long> {

    @Query("select e from #{#entityName} e where e.approver.id = ?1")
    ApprovalResult findByApproverId(Long userId);

    @Query("select e from #{#entityName} e left join fetch e.approver where e.approval.id = ?1")
    List<ApprovalResult> findEntireByApprovalId(Long approvalId);

    @Query("select e from #{#entityName} e where e.approval.id= ?1 and e.approver.id = ?2")
    ApprovalResult findByApproverIdAndUserId(Long approvalId, Long userId);
}
