package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.workorder.WorkOrderMaterial;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * Created by charles.chen on 2015/6/10.
 */
public interface WorkOrderMaterialRepository extends XiaRepository<WorkOrderMaterial, Long> {
    @Query("select r from #{#entityName} r left join fetch r.inventory i left join fetch i.warehouse left join fetch i.material where r.workOrder.id = ?1")
    List<WorkOrderMaterial> findByWorkOrderId(Long workOrderId);

    @Query("select r from #{#entityName} r left join fetch r.inventory i left join fetch i.warehouse left join fetch i.material where r.workOrder.id = ?1 and i.id = ?2")
    WorkOrderMaterial findByWorkOrderIdAndInventoryId(Long workOrderId, Long materialId);

    @Query("select r from #{#entityName} r left join fetch r.inventory left join fetch r.activity where r.id = ?1")
    WorkOrderMaterial findEntireOne(Long materialId);


    @Query("select activity.id from #{#entityName} r left join r.activity activity where r.workOrder.id = ?1")
    List<Long> findActivitysByWoId(Long woId);

    @Modifying
    @Query("delete from #{#entityName} wm where wm.workOrder.id = ?1 ")
    void deleteByWorkOrderId(Long woId);

//    @Query("select r from #{#entityName} r left join fetch r.inventory i left join fetch i.warehouse left join fetch i.material where r.workOrder.id = ?1")
//    Page<WorkOrderMaterial> findByWorkOrderId(Long woId, Pageable pageable);
}
