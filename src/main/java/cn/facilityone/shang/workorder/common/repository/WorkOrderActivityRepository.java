package cn.facilityone.shang.workorder.common.repository;

import cn.facilityone.shang.entity.workorder.WorkOrderActivity;
import cn.facilityone.shang.workorder.wo002.dto.WorkOrderActivityExportDto;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 6/1/2015
 */
public interface WorkOrderActivityRepository extends XiaRepository<WorkOrderActivity, Long> {

    @Query(value = "SELECT wa.* FROM wo_activity wa WHERE wa.wo_activity_id = ( SELECT max(wo_activity_id) FROM wo_activity WHERE wo_id = ?1  )", nativeQuery = true)
    WorkOrderActivity findRecentApprovalRequestRecordByWorkOrderId(Long id);

    @Query("select r from #{#entityName} r where r.activityType = ?1 and r.workOrder.id = ?2")
    List<WorkOrderActivity> findByTypeAndWorkOrderId(WorkOrderActivity.ActivityType type,Long workOrderId);

    @Query("select r from #{#entityName} r where  r.workOrder.id = ?1")
    List<WorkOrderActivity> findByWorkOrderId(Long workOrderId);

    @Query("select r from #{#entityName} r where  r.workOrder.id = ?1 and r.deleted = 0 order by r.id asc")
    List<WorkOrderActivity> findByWorkOrderIdOrderByIdASC(Long workOrderId);
    
    @Query("select new cn.facilityone.shang.workorder.wo002.dto.WorkOrderActivityExportDto(r.id,r.createdDate,r.workOrder.id,r.operator.name) from #{#entityName} r  where r.activityType = ?1 and r.workOrder.id in (?2)")
    List<WorkOrderActivityExportDto> findByTypeAndWorkOrderIds(WorkOrderActivity.ActivityType type,List<Long> workOrderIds);

    /**
     * 更具需求id查询出工单处理流程是接单和完成状态的记录
     * @param id 需求id
     * @return
     */
    @Query(value = "select * from wo_activity where wo_id in (select wo_id from wo where req_id=?1) and type in(2,6)", nativeQuery = true)
    List<WorkOrderActivity> findWorkOrderActivityReceiveAndFinishByRequirementId(Long id);

    @Query("select r from #{#entityName} r where r.workOrder.id = ?1 and r.createdDate > ?2 and r.activityType in (4,12) order by r.createdDate asc")
    List<WorkOrderActivity> findStopAndContinueByWorkOrderId(Long workOrderId, Date actualArrivalDateTime);

    /**
     * 查找到最后一次派工的记录
     *
     * @param workOrderId
     * @param type
     * @return
     */
    //@Query("select r from #{#entityName} r where r.workOrder.id = ?1 and r.activityType = ?2 order by r.createdDate desc limit 0,1")
    @Query(value = "select * from wo_activity where wo_id = ?1 and type = ?2 order by created_date desc limit 1", nativeQuery = true)
    WorkOrderActivity findLastDispatchActivity(Long workOrderId, Integer type);

    @Query("select r from #{#entityName} r where  r.workOrder.id = ?1 order by  r.createdDate asc ")
    List<WorkOrderActivity> findByWorkOrderIdOrderByCreatedDate(Long workOrderId);

    @Query("select new cn.facilityone.shang.workorder.wo002.dto.WorkOrderActivityExportDto(r.id,r.createdDate,r.workOrder.id,r.operator.name) " +
            " from #{#entityName} r  where r.activityType in (?1) and r.workOrder.id in (?2)")
    List<WorkOrderActivityExportDto> findByTypesAndWorkOrderIds(List<WorkOrderActivity.ActivityType> types, List<Long> workOrderIds);


    @Query("select r from #{#entityName} r where r.workOrder.id = ?1 and r.createdDate >=?2  and r.createdDate <=?3 and r.activityType in (4,12) order by r.createdDate asc")
    List<WorkOrderActivity> findByStopAndContinueByWorkOrderIdByOrderByDate(Long workOrderId, Date start,Date end);
}
