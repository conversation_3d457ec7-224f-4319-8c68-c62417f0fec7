package cn.facilityone.shang.workorder.common.mapper;

import cn.facilityone.shang.icc.v1.view.WoDataEnhanced;
import cn.facilityone.shang.workorder.common.alias.WorkLaborerTimeView;
import cn.facilityone.shang.workorder.common.alias.WorkOrderAlias;
import cn.facilityone.shang.workorder.common.alias.WorkOrderCountDayView;
import cn.facilityone.shang.workorder.common.alias.WorkOrderStatusByDateTodoView;
import cn.facilityone.xia.persistence.mybatis.XiaMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface WorkOrderMapper extends XiaMapper<WorkOrderAlias>{

    List<WorkOrderStatusByDateTodoView> findStatusByDatetodoIds(@Param("datetodoids")List<Long> ids);

    Integer findCMCompleteByEm(@Param("emId") Long emId, @Param("start")Date start ,@Param("end") Date end,@Param("proId") Long proId);


    Integer findCMTotalByEm(@Param("emId") Long emId, @Param("start")Date start ,@Param("end") Date end,@Param("proId") Long proId);


    Integer findPmCompleteByEm(@Param("emId") Long emId, @Param("start")Date start ,@Param("end") Date end,@Param("proId") Long proId);


    Integer findPMTotalByEm(@Param("emId") Long emId, @Param("start")Date start ,@Param("end") Date end,@Param("proId") Long proId);

    List<WorkLaborerTimeView> findByLaborerDateTime(@Param("emId") Long emId,@Param("start") Date start, @Param("end") Date end, @Param("type") Integer[] type);


    Integer findWorkOrderNumberDayByEm(@Param("emId") Long emId,@Param("start") Date start, @Param("end") Date end, @Param("type") Integer[] type);

    List<WorkOrderCountDayView> findCountGroupByWoType(@Param("startDate") Date startDate, @Param("pid") Long pid);

    Integer findTotalCountByDate(@Param("startDate") Date startDate,@Param("scanDate") Date scanDate, @Param("pid") Long pid);

    Integer findTotalCompletedCountByDate(@Param("startDate") Date startDate,@Param("scanDate") Date scanDate, @Param("pid") Long pid, @Param("statuses") List<Integer> statuses);

    Integer findWorkCountHour(@Param("startDate")Date startDate,@Param("endDate")Date endDate,@Param("proId")Long proId);

    Long findTodayWorkCount(@Param("startDate")Date startDate,@Param("proId")Long proId);

    List<WoDataEnhanced> findWoData(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("projectId")List projectId);


    Long  findWorkOrderNotResponseInTimeCount(@Param("proId") Long proId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);


    Long  findWorkOrderNotCountResponseInTimeCount(@Param("proId") Long proId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    Long  findWorkOrderTotalCount(@Param("proId") Long proId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    Date findWoActivityTypeMaxTime(@Param("woId") Long woId,@Param("type") Integer type);

    Long findWoActivityTypeCount(@Param("woId") Long woId,@Param("type") Integer type,@Param("date") Date date);

}