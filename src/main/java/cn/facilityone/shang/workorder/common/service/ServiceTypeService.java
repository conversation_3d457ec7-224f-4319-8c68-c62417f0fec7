package cn.facilityone.shang.workorder.common.service;

import cn.facilityone.shang.common.component.ztree.ZTreeNode;
import cn.facilityone.shang.entity.workorder.ServiceType;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 5/19/2015
 */
public interface ServiceTypeService {

    List<ServiceType> findAll();

    List<ZTreeNode> findAllInTreeNode();

    List<Long> findListServiceTypeById(Long id);
    
    ServiceType findOne(Long id);
    
    ServiceType save(ServiceType st);
    
    void deleteOneAndChildren(Long id);

    ServiceType update(ServiceType st);

    boolean checkCanDelete(Long id);
}
