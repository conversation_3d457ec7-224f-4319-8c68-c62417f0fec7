package cn.facilityone.shang.pay.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import cn.facilityone.xia.pay.alipay.model.result.AlipayTradeNotifyResult;
import cn.facilityone.xia.pay.common.model.PayResponse;
import cn.facilityone.xia.pay.common.service.PayCallback;
import cn.facilityone.xia.pay.wechat.model.response.WXPayNotifyResponse;

/**
 * 付款成功回调
 * <AUTHOR>
 * @since 2017年8月1日
 */
@Service("paySuccessCallback")
public class PaySuccessCallbackImpl implements PayCallback{

    private static final Logger log = LoggerFactory.getLogger(PayCallback.class);
    
    @Override
    public void execute(PayResponse response) {
        if(response instanceof WXPayNotifyResponse){
            //https://pay.weixin.qq.com/wiki/doc/api/jsapi_sl.php?chapter=9_7
            log.info("wx pay success");
        }else if(response instanceof AlipayTradeNotifyResult){
            //https://doc.open.alipay.com/docs/doc.htm?spm=a219a.7629140.0.0.NEEy3s&treeId=193&articleId=103296&docType=1
            AlipayTradeNotifyResult result = (AlipayTradeNotifyResult)response;
            //result.getResponse().getTradeStatus();
            //result.getResponse().getBuyerLogonId();
            log.info("ali pay success");
        }else{
            log.error("pay success notify callback error");
        }
    }

}
