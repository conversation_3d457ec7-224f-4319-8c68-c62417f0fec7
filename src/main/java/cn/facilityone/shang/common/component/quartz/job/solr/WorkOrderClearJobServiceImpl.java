package cn.facilityone.shang.common.component.quartz.job.solr;

import java.util.Date;

import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Service;

import cn.facilityone.shang.common.component.quartz.core.dto.JobLog;
import cn.facilityone.shang.common.component.quartz.job.XiaJob;

@Service
public class WorkOrderClearJobServiceImpl extends XiaJob{
    
    @Override
    public void executeService(JobExecutionContext jobExecutionContext, JobLog jobLog) {
        jobLog.info("start");
        //sworkOrderService.clearOutTimeWorkOrder( jobLog);
        //srequirementService.clearOutTimeRequirement(jobLog);
        jobLog.info("end");
    }

    @Override
    public void interruptService() {
        System.out.println("Interrupt ContractExpire "+new Date());
    }

}
