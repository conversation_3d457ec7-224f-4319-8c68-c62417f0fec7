package cn.facilityone.shang.monitoring.monitoring007.resource;

import org.glassfish.jersey.server.mvc.Template;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Yo<PERSON> on 2017/11/22.
 */
@Path("/monitoring007")
public class RunningStatisticsResource {

    private static final String TEMPLATE_PATH = "/business/monitoring/monitoring007-running-statistics.ftl";

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>(0);
    }
}
