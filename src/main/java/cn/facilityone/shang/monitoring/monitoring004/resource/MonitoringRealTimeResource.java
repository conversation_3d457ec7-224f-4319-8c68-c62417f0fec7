package cn.facilityone.shang.monitoring.monitoring004.resource;

import org.glassfish.jersey.server.mvc.Template;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * 实时监测
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-09-14
 */
@Path("/monitoring004")
public class MonitoringRealTimeResource {

    private static final String TEMPLATE_PATH = "/business/monitoring/monitoring004-monitoring-realtime.ftl";

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>(0);
    }

}
