package cn.facilityone.shang.monitoring.monitoring006.resource;

import org.glassfish.jersey.server.mvc.Template;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备异常率报表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-09-15
 */
@Path("/monitoring006")
public class EnergyStatisticsResource {

    private static final String TEMPLATE_PATH = "/business/monitoring/monitoring006-energy-statistics.ftl";

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>(0);
    }

}
