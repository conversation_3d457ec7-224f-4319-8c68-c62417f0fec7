package cn.facilityone.shang.monitoring.common.repository;

import cn.facilityone.shang.entity.monitoring.MonitoringSetting;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 系统设置.
 *
 * <AUTHOR>
 * @date 2017-09-12
 * @version 1.0
 */
public interface MonitoringSettingRepository extends XiaRepository<MonitoringSetting, Long> {

    MonitoringSetting findFirstByOrderByIdAsc();

    @Query("select setting from #{#entityName} setting where setting.deleted = 0 and project = ?1")
    MonitoringSetting findByProjIdHardly(Long projId);

    @Query("select setting from #{#entityName} setting where setting.deleted = 0")
    List<MonitoringSetting> findAllHardly();
}
