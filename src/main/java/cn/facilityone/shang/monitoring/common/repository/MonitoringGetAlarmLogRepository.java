package cn.facilityone.shang.monitoring.common.repository;

import cn.facilityone.shang.entity.monitoring.MonitoringGetAlarmLog;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * 获取告警记录操作记录.
 *
 * <AUTHOR>
 * @date 2017-11-22
 * @version 1.0
 */
public interface MonitoringGetAlarmLogRepository extends XiaRepository<MonitoringGetAlarmLog, Long> {

    @Query(value = "SELECT * FROM monitoring_get_alarm_log log WHERE log.deleted = 0 and log.proj_id = ?1 order by log.monitoring_get_alarm_log_id desc limit 1", nativeQuery = true)
    MonitoringGetAlarmLog findByIdDescHardly(Long projId);

    MonitoringGetAlarmLog findFirstByOrderBySyncTimeDesc();
}
