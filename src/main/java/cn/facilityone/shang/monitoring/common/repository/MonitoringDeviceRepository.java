package cn.facilityone.shang.monitoring.common.repository;

import cn.facilityone.shang.entity.monitoring.MonitoringDevice;
import cn.facilityone.shang.entity.monitoring.MonitoringParam;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 能效通EFOS项目下设备.
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public interface MonitoringDeviceRepository extends XiaRepository<MonitoringDevice, Long> {

    @Query("select device from #{#entityName} device where device.deviceID = ?1 and device.deleted = 0 and device.project = ?2")
    MonitoringDevice findBasicByDeviceIDHardly(String deviceID, Long projId);

    @Query("select device from #{#entityName} device left join fetch device.notifiers notifiers left join fetch notifiers.user left join fetch device.workOrderProcess wopro left join fetch wopro.serviceType left join fetch wopro.priority where device.deviceID = ?1 and device.deleted = 0 and device.project = ?2")
    MonitoringDevice findByDeviceIDHardly(String deviceID, Long projId);

    @Query("select device from #{#entityName} device left join fetch device.notifiers left join fetch device.workOrderProcess where device.id = ?1 ")
    MonitoringDevice findDetailById(Long id);

    @Query(value="select device.monitoringParams from #{#entityName} device where device.id = ?1 "
            ,countQuery="select count(param.id) from MonitoringDevice device left join device.monitoringParams param where device.id = ?1")
    Page<MonitoringParam> findPageById(Long projId, Pageable page);

    List<MonitoringDevice> findByIdIn(List<Long> ids);
}
