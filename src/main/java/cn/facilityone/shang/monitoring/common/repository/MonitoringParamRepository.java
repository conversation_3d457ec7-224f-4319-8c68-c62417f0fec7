package cn.facilityone.shang.monitoring.common.repository;

import cn.facilityone.shang.entity.monitoring.MonitoringParam;
import cn.facilityone.xia.message.entity.MessageSite;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 项目下所有参数.
 *
 * <AUTHOR>
 * @date 2017-10-24
 * @version 1.0
 */
public interface MonitoringParamRepository extends XiaRepository<MonitoringParam, Long> {
}
