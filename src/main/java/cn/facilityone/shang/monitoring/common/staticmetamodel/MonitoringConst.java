package cn.facilityone.shang.monitoring.common.staticmetamodel;

/**
 * 运行监测常量类.
 *
 * <AUTHOR>
 * @date 2017-09-11
 * @version 1.0
 */
public class MonitoringConst {

    /** 获取项目下所有设备信息 */
    public static final String GET_DEVICE_URL = "http://webapi.eegrid.com/device/get?AccessToken=ACCESSTOKEN";

    /** 获取项目下所有参数信息 */
    public static final String GET_PARAME_URL = "http://webapi.eegrid.com/deviceparame/get?AccessToken=ACCESSTOKEN";

    /** 获取项目下设备采集参数 */
    public static final String GET_DEVICE_PARAME_MAPPING_URL = "http://webapi.eegrid.com/datamapping/get?accessToken=ACCESSTOKEN";

    /** 获取项目下当前设定周期内最新告警 */
    public static final String GET_LATEST_ALARM_URL = "http://webapi.eegrid.com/alarm/get?AccessToken=ACCESSTOKEN";

    /** 获取项目下某日所有历史告警 */
    public static final String GET_ALARM_ALLDEV_URL = "http://webapi.eegrid.com/alarm/alldev/get?AccessToken=ACCESSTOKEN";

    public static final String URL_ACCESSTOKEN = "ACCESSTOKEN";

    public static final String JOB_NAME = "monitoring-alarm-sync-job";

    public static final String JOB_GROUP = "monitoring-job";

    public static final String JOB_DESCRIPTION = "monitoring alarm data sync";

    public static final String TRIGGER_NAME = "monitoring-alarm-sync-trigger";

    public static final String TRIGGER_GROUP = "monitoring-trigger";

    public static final int CRON_DEFAULT_FREQUENCY_FIVE_MINUTES = 5;

    public static final Integer SYNC_ERR_TYPE_SETTING = 1;

    public static final Integer SYNC_ERR_TYPE_REQUEST = 2;

    public static final Integer SYNC_ERR_TYPE_SYNC = 3;

    public static final String ALARMLEVEL_GENERAL = "2";
    public static final String ALARMLEVEL_SERIOUS = "3";

    public static final String REQUEST_GET = "get";
    public static final String REQUEST_PUT = "put";
    public static final String SYNC_BTN_URL = "/monitoring001/sync";
    public static final String SET_BTN_URL = "/monitoring001";

    public static final String EQ_MENU_LINK = "/asset002/equipments";
    public static final String WO_MENU_LINK = "/wo002";
}
