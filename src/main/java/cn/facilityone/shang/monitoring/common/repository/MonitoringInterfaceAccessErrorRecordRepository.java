package cn.facilityone.shang.monitoring.common.repository;

import cn.facilityone.shang.entity.monitoring.MonitoringInterfaceAccessErrorRecord;
import cn.facilityone.xia.persistence.repository.XiaRepository;

/**
 * 接口访问异常.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-09-18
 */
public interface MonitoringInterfaceAccessErrorRecordRepository extends XiaRepository<MonitoringInterfaceAccessErrorRecord, Long> {
}
