package cn.facilityone.shang.monitoring.common.repository;

import cn.facilityone.shang.entity.monitoring.MonitoringDataSyncLog;
import cn.facilityone.xia.persistence.repository.XiaRepository;

/**
 * 数据同步操作记录.
 *
 * <AUTHOR>
 * @date 2017-09-11
 * @version 1.0
 */
public interface MonitoringDataSyncLogRepository extends XiaRepository<MonitoringDataSyncLog, Long> {

    MonitoringDataSyncLog findFirstByOrderByCreatedDateDesc();
}
