package cn.facilityone.shang.monitoring.common.repository;

import cn.facilityone.shang.entity.monitoring.MonitoringLatestAlarm;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 最新告警.
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public interface MonitoringLatestAlarmRepository extends XiaRepository<MonitoringLatestAlarm, Long> {

    @Query("select distinct alarm from #{#entityName} alarm left join alarm.workOrder where alarm.deleted = 0 and alarm.project = ?1")
    List<MonitoringLatestAlarm> findByProjIdHardly(Long projId);

    MonitoringLatestAlarm findFirstByOrderByCreatedDateDesc();

}
