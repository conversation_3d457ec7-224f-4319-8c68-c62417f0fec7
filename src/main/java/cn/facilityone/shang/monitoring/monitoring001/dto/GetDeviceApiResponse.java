package cn.facilityone.shang.monitoring.monitoring001.dto;

import java.util.List;

/**
 * 访问获取所有设备信息请求返回值.
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public class GetDeviceApiResponse {

    /**
     * 状态
     */
    private Long state;

    /**
     * 参数信息
     */
    private List<DeviceApi> data;

    /**
     * 失败信息
     */
    private String errorMessage;

    public Long getState() {
        return state;
    }

    public void setState(Long state) {
        this.state = state;
    }

    public List<DeviceApi> getData() {
        return data;
    }

    public void setData(List<DeviceApi> data) {
        this.data = data;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
