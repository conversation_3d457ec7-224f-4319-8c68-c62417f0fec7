package cn.facilityone.shang.monitoring.monitoring001.dto;

import java.util.List;

/**
 * 访问获取某项目历史告警请求返回值.
 *
 * <AUTHOR>
 * @date 2017-11-14
 * @version 1.0
 */
public class GetHistoricalAlarmApiResponse {

    /**
     * 状态
     */
    private Long state;

    /**
     * 历史告警
     */
    private List<HistoricalAlarmApi> data;

    /**
     * 失败信息
     */
    private String errorMessage;

    public Long getState() {
        return state;
    }

    public void setState(Long state) {
        this.state = state;
    }

    public List<HistoricalAlarmApi> getData() {
        return data;
    }

    public void setData(List<HistoricalAlarmApi> data) {
        this.data = data;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
