package cn.facilityone.shang.monitoring.monitoring001.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.monitoring.common.staticmetamodel.MonitoringConst;
import cn.facilityone.shang.monitoring.monitoring001.dto.DeviceSettingDetailDTO;
import cn.facilityone.shang.monitoring.monitoring001.dto.DeviceSettingEditDTO;
import cn.facilityone.shang.monitoring.monitoring001.dto.SyncResult;
import cn.facilityone.shang.monitoring.monitoring001.service.DataSyncService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据同步.
 *
 * <AUTHOR>
 * @date 2017-09-07
 * @version 1.0
 */
@Path("/monitoring001")
public class DataSyncResource {

    private static final String TEMPLATE_PATH = "/business/monitoring/monitoring001-data-sync.ftl";

    @Autowired
    private DataSyncService dataSyncService;

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> index() {
        return new HashMap<String, Object>();
    }

    /**
     * 获取最新同步结果
     */
    @GET
    @Path("sync/result")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getSyncResult() {
        SyncResult syncResult = dataSyncService.getSyncResult();
        return new Result(syncResult);
    }

    /**
     * 获取基础数据同步列表
     */
    @POST
    @Path("table")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getBasicDataTable(DataTableRequest request) {
        DataTableResponse response = dataSyncService.getBasicDataTable(request);
        return response;
    }

    /**
     * 【批量设置告警】按钮权限
     */
    @GET
    @Path("set/permission")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getSetPermission() {
        boolean result = dataSyncService.checkPermission(MonitoringConst.REQUEST_GET, MonitoringConst.SET_BTN_URL);
        return new Result(result);
    }

    /**
     * 【同步】按钮权限
     */
    @GET
    @Path("sync/permission")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getSyncPermission() {
        boolean result = dataSyncService.checkPermission(MonitoringConst.REQUEST_GET, MonitoringConst.SYNC_BTN_URL);
        return new Result(result);
    }

    /**
     * 【同步】按钮
     */
    @GET
    @Path("sync")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result syncBasicData() {
        SyncResult syncResult = dataSyncService.syncBasicData();
        Result result = new Result(Result.CODE_200, XiaMesssageResource.getMessage("server.result.success.sync"), syncResult);
        if (MonitoringConst.SYNC_ERR_TYPE_SETTING.equals(syncResult.getErrType()) || MonitoringConst.SYNC_ERR_TYPE_SYNC.equals(syncResult.getErrType())) {
            result = new Result(Result.CODE_200, syncResult.getMessage(), null);
            result.setStatus(Result.STATUS_FAIL);
        } else if (MonitoringConst.SYNC_ERR_TYPE_REQUEST.equals(syncResult.getErrType())) {
            result = new Result(syncResult);
        }
        return result;
    }

    /**
     * 根据设备编号，获取设备id
     */
    @GET
    @Path("/eq/{code}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getEqByCode(@PathParam("code") String code) {
        Map<String, Object> resultMap = dataSyncService.getEqByCode(code);
        if (resultMap.containsKey("message")) {
            Result result = new Result(Result.CODE_200, resultMap.get("message").toString(), null);
            result.setStatus(Result.STATUS_FAIL);
            return result;
        }
        return new Result(resultMap.get("eqId"));
    }

    /**
     * 【编辑】按钮，获取详情
     */
    @GET
    @Path("/device/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getDetailInfo(@PathParam("id") Long id) {
        DeviceSettingDetailDTO detailSetting = dataSyncService.getDetailInfo(id);
        return new Result(detailSetting);
    }

    /**
     * 获取参数列表
     */
    @POST
    @Path("table/params")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getParamsDataTable(DataTableRequest request) {
        DataTableResponse response = dataSyncService.getParamsDataTable(request);
        return response;
    }

    /**
     * 提醒人备选值
     */
    @GET
    @Path("notifiers")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getNotifiers(@QueryParam("key") String key) {
        List<Employee> notifiers = dataSyncService.getNotifiers(key);
        return new Result(notifiers);
    }

    /**
     * 获取SLA备选列表
     */
    @POST
    @Path("sla")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getSlaTable(DataTableRequest request) {
        DataTableResponse response = dataSyncService.getSlaTable(request);
        return response;
    }

    /**
     * 保存
     */
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result modifyMonitoringObject(DeviceSettingEditDTO deviceSettingEditDTO) {
        boolean update = dataSyncService.modifyDeviceSetting(deviceSettingEditDTO);
        Result result = new Result(XiaMesssageResource.getMessage("server.result.success.update", null, ""));
        if (!update) {
            result = new Result(Result.CODE_200, XiaMesssageResource.getMessage("server.result.error.update", null, ""), null);
            result.setStatus(Result.STATUS_FAIL);
        }
        return result;
    }
}
