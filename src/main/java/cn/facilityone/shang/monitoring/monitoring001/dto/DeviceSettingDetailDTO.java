package cn.facilityone.shang.monitoring.monitoring001.dto;

import cn.facilityone.shang.entity.monitoring.MonitoringParam;
import cn.facilityone.shang.entity.organize.Employee;

import java.util.List;

/**
 * 监测设备详细设置.
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public class DeviceSettingDetailDTO {

    private Long id;

    /**
     * 设备类型编号
     */
    private String typeID;

    /**
     * 设备类型
     */
    private String typeName;

    /**
     * 位置
     */
    private String navName;

    /**
     * 设备编号
     */
    private String deviceID;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * F-ONE系统设备编码
     */
    private String deviceCode;

    /**
     * 设备 － 参数
     */
    private List<MonitoringParam> monitoringParams;

    /**
     * 提醒人
     */
    private List<Long> notifierIds;

    /**
     * 初始提醒人下拉框备选值
     */
    private List<Employee> notifierOption;

    /**
     * 提醒方式
     */
    private String msgType;

    /**
     * SLA
     */
    private Long slaId;

    /**
     * SLA列表
     */
    private List<SlaDTO> slaDTOList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTypeID() {
        return typeID;
    }

    public void setTypeID(String typeID) {
        this.typeID = typeID;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getNavName() {
        return navName;
    }

    public void setNavName(String navName) {
        this.navName = navName;
    }

    public String getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(String deviceID) {
        this.deviceID = deviceID;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public List<MonitoringParam> getMonitoringParams() {
        return monitoringParams;
    }

    public void setMonitoringParams(List<MonitoringParam> monitoringParams) {
        this.monitoringParams = monitoringParams;
    }

    public List<Long> getNotifierIds() {
        return notifierIds;
    }

    public void setNotifierIds(List<Long> notifierIds) {
        this.notifierIds = notifierIds;
    }

    public List<Employee> getNotifierOption() {
        return notifierOption;
    }

    public void setNotifierOption(List<Employee> notifierOption) {
        this.notifierOption = notifierOption;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public Long getSlaId() {
        return slaId;
    }

    public void setSlaId(Long slaId) {
        this.slaId = slaId;
    }

    public List<SlaDTO> getSlaDTOList() {
        return slaDTOList;
    }

    public void setSlaDTOList(List<SlaDTO> slaDTOList) {
        this.slaDTOList = slaDTOList;
    }
}
