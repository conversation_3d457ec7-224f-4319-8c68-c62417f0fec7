package cn.facilityone.shang.monitoring.monitoring001.dto;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 访问获取设备采集参数请求参数.
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public class GetDeviceParamMappingApiRequest {

    /**
     * 项目编号
     */
    @JSONField(name="ProjectCode")
    private String projectCode;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }
}
