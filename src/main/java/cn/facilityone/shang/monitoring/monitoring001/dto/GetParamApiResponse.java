package cn.facilityone.shang.monitoring.monitoring001.dto;

import java.util.List;

/**
 * 访问获取所有参数信息请求返回值.
 *
 * <AUTHOR>
 * @date 2017-10-24
 * @version 1.0
 */
public class GetParamApiResponse {

    /**
     * 状态
     */
    private Long state;

    /**
     * 参数信息
     */
    private List<ParamApi> data;

    /**
     * 失败信息
     */
    private String errorMessage;

    public Long getState() {
        return state;
    }

    public void setState(Long state) {
        this.state = state;
    }

    public List<ParamApi> getData() {
        return data;
    }

    public void setData(List<ParamApi> data) {
        this.data = data;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
