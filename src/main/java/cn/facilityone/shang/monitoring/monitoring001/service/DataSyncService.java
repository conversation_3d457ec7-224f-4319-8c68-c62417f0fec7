package cn.facilityone.shang.monitoring.monitoring001.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.monitoring.monitoring001.dto.DeviceSettingDetailDTO;
import cn.facilityone.shang.monitoring.monitoring001.dto.DeviceSettingEditDTO;
import cn.facilityone.shang.monitoring.monitoring001.dto.SyncResult;

import java.util.List;
import java.util.Map;

/**
 * 数据同步.
 *
 * <AUTHOR>
 * @date 2017-09-11
 * @version 1.0
 */
public interface DataSyncService {

    SyncResult getSyncResult();

    DataTableResponse getBasicDataTable(DataTableRequest request);

    boolean checkPermission(String requestType, String url);

    SyncResult syncBasicData();

    Map<String, Object> getEqByCode(String code);

    boolean checkMenuPermission(String link);

    DeviceSettingDetailDTO getDetailInfo(Long id);

    DataTableResponse getParamsDataTable(DataTableRequest request);

    List<Employee> getNotifiers(String key);

    DataTableResponse getSlaTable(DataTableRequest request);

    boolean modifyDeviceSetting(DeviceSettingEditDTO deviceSettingEditDTO);

    void createAccessErrorRecord(String type, String url, String param, String message, Long projId);
}
