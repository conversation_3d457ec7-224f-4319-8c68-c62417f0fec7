package cn.facilityone.shang.monitoring.monitoring001.dto;

import java.util.List;

/**
 * 访问获取设备采集参数请求返回值.
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public class GetDeviceParamMappingApiResponse {

    /**
     * 状态
     */
    private Long state;

    /**
     * 设备采集参数信息
     */
    private List<DeviceParamMappingApi> data;

    /**
     * 失败信息
     */
    private String errorMessage;

    public Long getState() {
        return state;
    }

    public void setState(Long state) {
        this.state = state;
    }

    public List<DeviceParamMappingApi> getData() {
        return data;
    }

    public void setData(List<DeviceParamMappingApi> data) {
        this.data = data;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
