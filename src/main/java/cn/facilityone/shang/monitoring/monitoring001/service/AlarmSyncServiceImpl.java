package cn.facilityone.shang.monitoring.monitoring001.service;

import cn.facilityone.shang.asset.asset002.repository.EquipmentRepository;
import cn.facilityone.shang.common.component.message.MessageSenderTool;
import cn.facilityone.shang.common.component.message.template.MessageMonitoringAlarmNoticeTemplate;
import cn.facilityone.shang.common.repository.WorkOrderEquipmentRepository;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.staticmetamodel.MobilePush_;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.common.util.HttpClientUtil;
import cn.facilityone.shang.common.util.LocationUtil;
import cn.facilityone.shang.common.util.WorkOrderCode;
import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.monitoring.*;
import cn.facilityone.shang.entity.organize.*;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderEquipment;
import cn.facilityone.shang.entity.workorder.WorkOrderProcess;
import cn.facilityone.shang.monitoring.common.repository.*;
import cn.facilityone.shang.monitoring.common.staticmetamodel.MonitoringConst;
import cn.facilityone.shang.monitoring.monitoring001.dto.*;
import cn.facilityone.shang.workorder.common.service.WorkOrderTaskService;
import cn.facilityone.shang.workorder.wo003.service.WorkOrderProcessService;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.message.common.MessageType;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 从能效通EFOS拉取告警记录.
 *
 * <AUTHOR>
 * @date 2017-09-08
 * @version 1.0
 */
@Service
public class AlarmSyncServiceImpl implements AlarmSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AlarmSyncServiceImpl.class);
    private static final String KEY_TIME_FORMAT = "yyyyMMddHHmmss";

    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private WorkOrderProcessService workOrderProcessService;
    @Autowired
    private WorkOrderTaskService workOrderTaskService;
    @Autowired
    private WorkOrderEquipmentRepository workOrderEquipmentRepository;
    @Autowired
    private WorkOrderCode workOrderCode;
    @Autowired
    private MessageSenderTool messageSenderTool;
    @Autowired
    private MonitoringSettingRepository monitoringSettingRepository;
    @Autowired
    private MonitoringLatestAlarmRepository monitoringLatestAlarmRepository;
    @Autowired
    private MonitoringHistoricalAlarmRepository monitoringHistoricalAlarmRepository;
    @Autowired
    private MonitoringDeviceRepository monitoringDeviceRepository;
    @Autowired
    private MonitoringGetAlarmLogRepository monitoringGetAlarmLogRepository;
    @Autowired
    private DataSyncService dataSyncService;
    @Autowired
    private EquipmentRepository equipmentRepository;

    @Override
    //@XiaTransactional(readOnly = false) 因handleWorkOrderProcessNotice，propagation = Propagation.REQUIRES_NEW，所以此处不能加事务
    public void syncAlarm(Long projId) {
        MonitoringGetAlarmLog monitoringGetAlarmLog = new MonitoringGetAlarmLog();
        Date now = new Date();
        monitoringGetAlarmLog.setSyncTime(now);
        monitoringGetAlarmLog.setProject(projId);

        // 获取系统设置
        MonitoringSetting setting = monitoringSettingRepository.findByProjIdHardly(projId);
        // 系统设置后，生成job时，设置可能还未更新进DB（现已设置成延迟10s启动job）
        if (setting == null || StringUtils.isEmpty(setting.getProjectCode()) || StringUtils.isEmpty(setting.getAccessToken())) {
            return;
        }

        String projectCode = setting.getProjectCode();
        String accessToken = setting.getAccessToken();
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.FORMAT_PATTERN_DATETIME);

        // key = DeviceID + DataCode + AlarmTime（用于告警记录从最新告警 → 历史告警，工单号的保存）
        Map<String, MonitoringLatestAlarm> latestAlarmDbMap = new HashMap<>();
        List<MonitoringLatestAlarm> latestAlarmDbList = null;

        // 获取项目下当前设定周期内最新告警
        String url = MonitoringConst.GET_LATEST_ALARM_URL.replaceAll(MonitoringConst.URL_ACCESSTOKEN, accessToken);
        GetLatestAlarmApiRequest apiRequest = new GetLatestAlarmApiRequest();
        apiRequest.setProjectCode(projectCode);
        String latestAlarmJson = null;
        try {
            latestAlarmJson = HttpClientUtil.sendPost(url, JSON.toJSONString(apiRequest), null);
            LOGGER.debug("projId=" + projId +", 请求=" + url + ", 返回值：" + latestAlarmJson);
        } catch (Exception e) {
            e.printStackTrace();
            dataSyncService.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                    null, projId);
            LOGGER.debug("从能效通EFOS拉取最新告警记录失败");
            return;
        }
        if (!StringUtils.isEmpty(latestAlarmJson)) {
            GetLatestAlarmApiResponse apiResponse = JSON.parseObject(latestAlarmJson, GetLatestAlarmApiResponse.class);
            if (apiResponse.getState() == 0) {
                latestAlarmDbList = monitoringLatestAlarmRepository.findByProjIdHardly(projId);
                // key = DeviceID + _ + DataCode（用于判断相同告警）
                Map<String, List<MonitoringLatestAlarm>> latestAlarmDbLevelMap = new HashMap<>();
                String key = null;
                List<MonitoringLatestAlarm> latestAlarmDbLevelList = null;
                for (MonitoringLatestAlarm lastTimeAlarm : latestAlarmDbList) {
                    latestAlarmDbMap.put(this.buildLatestAlarmKey(lastTimeAlarm.getDeviceID(), lastTimeAlarm.getDataCode(), lastTimeAlarm.getAlarmTime()), lastTimeAlarm);
                    key = lastTimeAlarm.getDeviceID() + SystemConst.STR_ULINE + lastTimeAlarm.getDataCode();
                    latestAlarmDbLevelList = latestAlarmDbLevelMap.get(key);
                    if (latestAlarmDbLevelList == null) {
                        latestAlarmDbLevelList = new ArrayList<>();
                        latestAlarmDbLevelMap.put(key, latestAlarmDbLevelList);
                    }
                    latestAlarmDbLevelList.add(lastTimeAlarm);
                }

                MonitoringLatestAlarm alarm = null;
                // key = DeviceID + _ + DataCode（用于保存此次同步时的相同告警）
                Map<String, LatestAlarmApi> latestAlarmMap = new HashMap<>();
                // key = DeviceID + _ + DataCode（用于保存此次同步时的相同告警的工单）
                Map<String, WorkOrder> latestAlarmWoMap = new HashMap<>();
                String deviceID = null;
                String dataCode = null;
                Date alarmTime = null;
                MonitoringDevice device = null;
                List<Equipment> equipmentList = null;
                Equipment equipment = null;
                boolean lastTimeDone = false;
                WorkOrderProcess workOrderProcess = null;
                WorkOrder workOrder = null;
                for (LatestAlarmApi latestAlarm : apiResponse.getData()) {
                    deviceID = latestAlarm.getDeviceID().toString();
                    dataCode = latestAlarm.getDataCode().toString();
                    alarmTime = null;
                    try {
                        alarmTime = sdf.parse(latestAlarm.getAlarmtime());
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }

                    key = this.buildLatestAlarmKey(deviceID, dataCode, alarmTime);
                    alarm = latestAlarmDbMap.get(key);
                    // 当前周期内获取的告警记录上一次周期内没有（通过DeviceID + DataCode + AlarmTime三个组合判断）
                    if (alarm == null) {
                        alarm = new MonitoringLatestAlarm();
                        BeanUtils.copyProperties(latestAlarm, alarm);

                        if (MonitoringConst.ALARMLEVEL_GENERAL.equals(latestAlarm.getAlarmLevel())) {
                            alarm.setAlarmLevel(MonitoringLatestAlarm.AlarmLevel.GENERAL);
                        } else if (MonitoringConst.ALARMLEVEL_SERIOUS.equals(latestAlarm.getAlarmLevel())) {
                            alarm.setAlarmLevel(MonitoringLatestAlarm.AlarmLevel.SERIOUS);
                        }

                        alarm.setDeviceID(deviceID);
                        alarm.setDataCode(dataCode);
                        alarm.setAlarmTime(alarmTime);
                        alarm.setProject(projId);
                    } else {
                        latestAlarmDbMap.remove(key);
                    }

                    key = deviceID + SystemConst.STR_ULINE + dataCode;
                    device = monitoringDeviceRepository.findByDeviceIDHardly(latestAlarm.getDeviceID().toString(), projId);
                    // 若还未进行同步基础数据设置 或 还未设置对应设备 或 对应的设备在F-ONE系统中不存在，则只简单存储数据
                    if (device != null && !StringUtils.isEmpty(device.getDeviceCode())) {
                        alarm.setDeviceCode(device.getDeviceCode());
                        equipmentList = equipmentRepository.findByCodeHardly(device.getDeviceCode(), projId);
                        if (CollectionUtils.isNotEmpty(equipmentList)) {
                            equipment = equipmentList.get(0);

                            // 一般告警和严重告警合并考虑（认为是相同告警）
                            // 提醒相关用户
                            // 若已生成了提醒 或 有关联告警已生成了提醒, 则跳过此步
                            if (CollectionUtils.isNotEmpty(device.getNotifiers()) && !alarm.isReminded()) {
                                // 同一 deviceID + dataCode的第一条数据进行是否提醒判断，第二条数据(即另一等级)则直接设置成"有关联告警已生成了提醒"
                                if (latestAlarmMap.get(key) == null) {
                                    lastTimeDone = false;
                                    latestAlarmDbLevelList = latestAlarmDbLevelMap.get(key);
                                    if (CollectionUtils.isNotEmpty(latestAlarmDbLevelList)) {
                                        for (MonitoringLatestAlarm lastTime : latestAlarmDbLevelList) {
                                            if (lastTime.isReminded()) {
                                                lastTimeDone = true;
                                                break;
                                            }
                                        }
                                    }

                                    if (!lastTimeDone) {
                                        this.remindEmployee(device, equipment, alarm, projId);
                                        alarm.setReminded(true);
                                    }
                                }
                                alarm.setReminded(true);
                            }

                            // 触发工单
                            workOrderProcess = device.getWorkOrderProcess();
                            // 若已生成了工单 或 有关联告警已生成了工单, 则跳过此步
                            if (workOrderProcess != null) {
                                if (alarm.getWorkOrder() == null) {
                                    workOrder = null;
                                    // 相同告警中的第一条
                                    if (latestAlarmMap.get(key) == null) {
                                        lastTimeDone = false;
                                        latestAlarmDbLevelList = latestAlarmDbLevelMap.get(key);
                                        if (CollectionUtils.isNotEmpty(latestAlarmDbLevelList)) {
                                            for (MonitoringLatestAlarm lastTime : latestAlarmDbLevelList) {
                                                if (lastTime.getWorkOrder() != null) {
                                                    lastTimeDone = true;
                                                    workOrder = lastTime.getWorkOrder();
                                                    break;
                                                }
                                            }
                                        }

                                        if (!lastTimeDone) {
                                            workOrder = this.triggerWorkOrder(device, equipment, alarm, workOrderProcess, projId);
                                        }
                                        alarm.setWorkOrder(workOrder);
                                    } else {
                                        alarm.setWorkOrder(latestAlarmWoMap.get(key));
                                    }
                                } else {
                                    workOrder = alarm.getWorkOrder();
                                }
                            }
                        }
                    }

                    monitoringLatestAlarmRepository.save(alarm);
                    latestAlarmMap.put(key, latestAlarm);
                    latestAlarmWoMap.put(key, workOrder);
                }

                // 删除已恢复报警
                if (!latestAlarmDbMap.isEmpty()) {
                    for (MonitoringLatestAlarm delAlarm : latestAlarmDbMap.values()) {
                        delAlarm.setDeleted(true);
                    }
                    monitoringLatestAlarmRepository.save(latestAlarmDbMap.values());
                }
            } else {
                dataSyncService.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                        "state: " + apiResponse.getState() + ", " + apiResponse.getErrorMessage(), projId);
                LOGGER.debug("接口url=" + url + "， 访问失败！state：" + apiResponse.getState() + "，失败信息：" + apiResponse.getErrorMessage());
                return;
            }
        } else {
            dataSyncService.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                    null, projId);
            LOGGER.debug("接口url=" + url + "， 访问失败！");
            return;
        }

        // 获取项目下某日所有历史告警
        String yesterday = null;
        do {
            url = MonitoringConst.GET_ALARM_ALLDEV_URL.replaceAll(MonitoringConst.URL_ACCESSTOKEN, accessToken);
            GetHistoricalAlarmApiRequest histApiRequest = new GetHistoricalAlarmApiRequest();
            histApiRequest.setProjectCode(projectCode);
            histApiRequest.setDate(yesterday);
            String histAlarmJson = null;
            try {
                histAlarmJson = HttpClientUtil.sendPost(url, JSON.toJSONString(histApiRequest), null);
                LOGGER.debug("projId=" + projId +", 请求=" + url + ", 返回值：" + histAlarmJson);
            } catch (Exception e) {
                e.printStackTrace();
                dataSyncService.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(histApiRequest),
                        null, projId);
                LOGGER.debug("从能效通EFOS拉取历史告警记录失败");
                return;
            }
            if (!StringUtils.isEmpty(histAlarmJson)) {
                GetHistoricalAlarmApiResponse apiResponse = JSON.parseObject(histAlarmJson, GetHistoricalAlarmApiResponse.class);
                if (apiResponse.getState() == 0) {
                    List<MonitoringHistoricalAlarm> historicalAlarmDbList = monitoringHistoricalAlarmRepository.findByProjIdHardly(projId);
                    // key = alarmID
                    Map<String, MonitoringHistoricalAlarm> historicalAlarmDbMap = new HashMap<>();
                    for (MonitoringHistoricalAlarm historicalAlarmDb : historicalAlarmDbList) {
                        historicalAlarmDbMap.put(historicalAlarmDb.getAlarmID(), historicalAlarmDb);
                    }

                    MonitoringHistoricalAlarm histAlarm = null;
                    for (HistoricalAlarmApi latestHistAlarm : apiResponse.getData()) {
                        String alarmID = latestHistAlarm.getAlarmID();
                        if (historicalAlarmDbMap.get(alarmID) == null) {
                            histAlarm = new MonitoringHistoricalAlarm();
                            BeanUtils.copyProperties(latestHistAlarm, histAlarm);
                            if (MonitoringConst.ALARMLEVEL_GENERAL.equals(latestHistAlarm.getAlarmLevel())) {
                                histAlarm.setAlarmLevel(MonitoringLatestAlarm.AlarmLevel.GENERAL);
                            } else if (MonitoringConst.ALARMLEVEL_SERIOUS.equals(latestHistAlarm.getAlarmLevel())) {
                                histAlarm.setAlarmLevel(MonitoringLatestAlarm.AlarmLevel.SERIOUS);
                            }
                            String deviceID = latestHistAlarm.getDeviceID().toString();
                            histAlarm.setDeviceID(deviceID);
                            histAlarm.setDataCode(latestHistAlarm.getDataCode().toString());

                            try {
                                histAlarm.setAlarmTime(sdf.parse(latestHistAlarm.getAlarmtime()));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }

                            MonitoringLatestAlarm latestAlarmDb = latestAlarmDbMap.get(this.buildLatestAlarmKey(histAlarm.getDeviceID(), histAlarm.getDataCode(), histAlarm.getAlarmTime()));
                            if (latestAlarmDb != null) {
                                // 设备编码
                                histAlarm.setDeviceCode(latestAlarmDb.getDeviceCode());
                                // 工单
                                histAlarm.setWorkOrder(latestAlarmDb.getWorkOrder());
                            }
                            // 系统首次同步，未经过【最新告警】，直接进入【告警记录】，则设备Equipment需要额外获取
                            if (StringUtils.isEmpty(histAlarm.getDeviceCode())) {
                                MonitoringDevice device = monitoringDeviceRepository.findBasicByDeviceIDHardly(deviceID, projId);
                                if (device != null) {
                                    histAlarm.setDeviceCode(device.getDeviceCode());
                                }
                            }
                            histAlarm.setProject(projId);
                            monitoringHistoricalAlarmRepository.save(histAlarm);
                        }
                    }
                } else {
                    dataSyncService.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(histApiRequest),
                            "state: " + apiResponse.getState() + ", " + apiResponse.getErrorMessage(), projId);
                    LOGGER.debug("接口url=" + url + "， 访问失败！state：" + apiResponse.getState() + "，失败信息：" + apiResponse.getErrorMessage());
                    return;
                }
            } else {
                dataSyncService.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(histApiRequest),
                        null, projId);
                LOGGER.debug("接口url=" + url + "， 访问失败！");
                return;
            }

            if (StringUtils.isEmpty(yesterday)) {
                MonitoringGetAlarmLog lastTimeLog = monitoringGetAlarmLogRepository.findByIdDescHardly(projId);
                if (lastTimeLog != null) {
                    if (!DateUtils.isSameDay(now, lastTimeLog.getSyncTime())) {
                        Calendar calendar = Calendar.getInstance();
                        // 前一天
                        calendar.add(Calendar.DATE, -1);
                        Date date = calendar.getTime();
                        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.FORMAT_PATTERN_DATE);
                        yesterday = dateFormat.format(date);
                    }
                }
            } else {
                yesterday = null;
            }
        } while (!StringUtils.isEmpty(yesterday));

        monitoringGetAlarmLogRepository.save(monitoringGetAlarmLog);
    }

    private String buildLatestAlarmKey(String deviceID, String dataCode, Date alarmTime) {
        SimpleDateFormat sdf = new SimpleDateFormat(KEY_TIME_FORMAT);
        String key = deviceID + SystemConst.STR_ULINE + dataCode + SystemConst.STR_ULINE + sdf.format(alarmTime);
        return key;
    }

    /**
     * 发送警告提醒
     *
     * @param device 设备
     * @param equipment 设备
     * @param alarm 最新告警
     * @param projId 项目id
     */
    private void remindEmployee(MonitoringDevice device, Equipment equipment, MonitoringLatestAlarm alarm, Long projId) {
        List<Employee> notifiers = device.getNotifiers();
        String msgType = device.getMsgType();

        String code = MessageMonitoringAlarmNoticeTemplate.CODE_MONITORING_ALARM;

        // 移动推送
        Map<String, Map<String, Object>> typeData = new HashMap<>();
        if (msgType.indexOf(MessageType.MPUSH) != -1) {
            Map<String, Object> params = new HashMap<>();
            params.put(MobilePush_.TYPE, MobilePush_.MONITORING);
            params.put(MobilePush_.PROJECT_ID, projId);
            typeData.put(MessageType.MPUSH, params);
        }

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.FORMAT_PATTERN_DATETIME);
        Map<String, Object> commonData = MessageMonitoringAlarmNoticeTemplate.buildData(
                equipment.getCode(), equipment.getName(), sdf.format(alarm.getAlarmTime()), alarm.getAlarmInfo());
        messageSenderTool.send(code, commonData, typeData, notifiers, projId, msgType);
    }

    /**
     * 生成工单
     *
     * @param device 设备
     * @param alarm 最新告警
     * @param projId 项目id
     */
    private WorkOrder triggerWorkOrder(MonitoringDevice device, Equipment equipment, MonitoringLatestAlarm alarm, WorkOrderProcess workOrderProcess, Long projId) {
        WorkOrder workOrder = new WorkOrder();

        workOrder.setType(WorkOrder.WorkOrderType.SELFCHECK);

        // 添加工单Code值
        workOrder.setCode(workOrderCode.getCode(workOrder.getType(), projId));

        workOrder.setRequestName("系统");
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.FORMAT_PATTERN_DATETIME);
        workOrder.setDescription(sdf.format(alarm.getAlarmTime()) + SystemConst.STR_SPACE + alarm.getAlarmInfo());

        workOrder.setServiceType(workOrderProcess.getServiceType());
        workOrder.setWorkOrderProcess(workOrderProcess);
        workOrder.setPriority(workOrderProcess.getPriority());

        Site site = equipment.getSite();
        Building building = equipment.getBuilding();
        Floor floor = equipment.getFloor();
        Room room = equipment.getRoom();
        workOrder.setSite(site);
        workOrder.setBuilding(building);
        workOrder.setFloor(floor);
        workOrder.setRoom(room);

        workOrder.setSource(WorkOrder.SourceType.CENTER);
        workOrder.setStatus(WorkOrder.WorkOrderStatus.CREATE);
        workOrder.setLocation(LocationUtil.buildString(building, floor, room));
        workOrder.setProject(projId);
        workOrder = workOrderRepository.save(workOrder);

        // 立即启动流程
        workOrder = workOrderProcessService.startWorkProcess(workOrder);
        // 同时完成工单创建
        workOrder = workOrderTaskService.completeCreate(workOrder);

        // 关联设备
        WorkOrderEquipment workOrderEquipment = new WorkOrderEquipment();
        workOrderEquipment.setWorkOrder(workOrder);
        workOrderEquipment.setEquipment(equipment);
        workOrderEquipmentRepository.save(workOrderEquipment);

        return workOrder;
    }
}
