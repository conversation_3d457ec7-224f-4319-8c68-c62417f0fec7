package cn.facilityone.shang.monitoring.monitoring001.dto;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 访问获取所有设备信息请求参数.
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public class GetDeviceApiRequest {

    /**
     * 项目编号
     */
    @JSONField(name="ProjectCode")
    private String projectCode;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }
}
