package cn.facilityone.shang.monitoring.monitoring001.dto;

/**
 * 设备采集参数(接口).
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public class DeviceParamMappingApi {

    /**
     * 设备编号
     */
    private Long deviceID;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 参数编号
     */
    private Long dataCode;

    /**
     * 参数名称
     */
    private String dataName;

    public Long getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(Long deviceID) {
        this.deviceID = deviceID;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Long getDataCode() {
        return dataCode;
    }

    public void setDataCode(Long dataCode) {
        this.dataCode = dataCode;
    }

    public String getDataName() {
        return dataName;
    }

    public void setDataName(String dataName) {
        this.dataName = dataName;
    }
}
