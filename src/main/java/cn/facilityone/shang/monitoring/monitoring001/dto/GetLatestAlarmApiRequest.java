package cn.facilityone.shang.monitoring.monitoring001.dto;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 访问获取某项目当前设定周期内最新告警请求参数.
 *
 * <AUTHOR>
 * @date 2017-09-12
 * @version 1.0
 */
public class GetLatestAlarmApiRequest {

    /**
     * 项目编号
     */
    @JSONField(name="ProjectCode")
    private String projectCode;

    /**
     * 周期[单位:分钟，如:10]
     */
    @JSONField(name="Cycle")
    private String cycle;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getCycle() {
        return cycle;
    }

    public void setCycle(String cycle) {
        this.cycle = cycle;
    }
}
