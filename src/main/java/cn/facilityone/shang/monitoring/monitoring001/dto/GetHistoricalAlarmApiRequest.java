package cn.facilityone.shang.monitoring.monitoring001.dto;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 访问获取某项目历史告警请求参数.
 *
 * <AUTHOR>
 * @date 2017-11-14
 * @version 1.0
 */
public class GetHistoricalAlarmApiRequest {

    /**
     * 项目编号
     */
    @JSONField(name="ProjectCode")
    private String projectCode;

    /**
     * 时刻，如(2016-1-12 10:00)
     * 必须：否，默认当天
     */
    @JSONField(name="Date")
    private String date;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}
