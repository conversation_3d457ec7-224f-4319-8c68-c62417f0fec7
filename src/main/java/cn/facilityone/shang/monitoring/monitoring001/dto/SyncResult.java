package cn.facilityone.shang.monitoring.monitoring001.dto;

import cn.facilityone.xia.i18n.XiaMesssageResource;

import java.util.Date;

/**
 * 同步结果.
 *
 * <AUTHOR>
 * @date 2017-09-11
 * @version 1.0
 */
public class SyncResult {

    /**
     * 最新同步时间
     */
    private Date latestSyncTime;

    /**
     * 同步人
     */
    private String syncOperator;

    /**
     * 结果
     */
    private String syncResult;

    /**
     * 错误类型 1=未设置 ProjectCode 或 AccessToken
     *         2=请求失败
     *         3=同步
     */
    private Integer errType;

    /**
     * 失败信息（错误类型=2用）
     */
    private String errorMessage;

    /**
     * 警告消息（错误类型=1&3用）
     */
    private String message;

    public Date getLatestSyncTime() {
        return latestSyncTime;
    }

    public void setLatestSyncTime(Date latestSyncTime) {
        this.latestSyncTime = latestSyncTime;
    }

    public String getSyncOperator() {
        return syncOperator;
    }

    public void setSyncOperator(String syncOperator) {
        this.syncOperator = syncOperator;
    }

    public String getSyncResult() {
        return syncResult;
    }

    public void setSyncResult(boolean result) {
        if (result) {
            this.syncResult = XiaMesssageResource.getMessage("page.common.success");
        } else {
            this.syncResult = XiaMesssageResource.getMessage("page.common.fail");
        }
    }

    public void setSyncResult(String syncResult) {
        this.syncResult = syncResult;
    }

    public Integer getErrType() {
        return errType;
    }

    public void setErrType(Integer errType) {
        this.errType = errType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
