package cn.facilityone.shang.monitoring.monitoring001.dto;

/**
 * 设备信息(接口).
 *
 * <AUTHOR>
 * @date 2017-11-13
 * @version 1.0
 */
public class DeviceApi {

    /**
     * 设备类型编号
     */
    private Long typeID;

    /**
     * 设备类型
     */
    private String typeName;

    /**
     * 位置
     */
    private String navName;

    /**
     * 设备编号
     */
    private Long deviceID;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * F-ONE系统设备编码
     */
    private String deviceCode;

    public Long getTypeID() {
        return typeID;
    }

    public void setTypeID(Long typeID) {
        this.typeID = typeID;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getNavName() {
        return navName;
    }

    public void setNavName(String navName) {
        this.navName = navName;
    }

    public Long getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(Long deviceID) {
        this.deviceID = deviceID;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }
}
