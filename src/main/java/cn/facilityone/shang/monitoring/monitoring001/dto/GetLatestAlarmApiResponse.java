package cn.facilityone.shang.monitoring.monitoring001.dto;

import java.util.List;

/**
 * 访问获取某项目当前设定周期内最新告警请求返回值.
 *
 * <AUTHOR>
 * @date 2017-09-12
 * @version 1.0
 */
public class GetLatestAlarmApiResponse {

    /**
     * 状态
     */
    private Long state;

    /**
     * 最新告警
     */
    private List<LatestAlarmApi> data;

    /**
     * 失败信息
     */
    private String errorMessage;

    public Long getState() {
        return state;
    }

    public void setState(Long state) {
        this.state = state;
    }

    public List<LatestAlarmApi> getData() {
        return data;
    }

    public void setData(List<LatestAlarmApi> data) {
        this.data = data;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
