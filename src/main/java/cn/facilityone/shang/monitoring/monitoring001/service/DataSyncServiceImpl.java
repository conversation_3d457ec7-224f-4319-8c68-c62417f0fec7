package cn.facilityone.shang.monitoring.monitoring001.service;

import cn.facilityone.shang.asset.asset002.repository.EquipmentRepository;
import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableConditions;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.staticmetamodel.WorkOrderProcess_;
import cn.facilityone.shang.common.util.HttpClientUtil;
import cn.facilityone.shang.common.util.LocationUtil;
import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.monitoring.*;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.Organization;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderProcess;
import cn.facilityone.shang.monitoring.common.repository.*;
import cn.facilityone.shang.monitoring.common.staticmetamodel.MonitoringConst;
import cn.facilityone.shang.monitoring.common.staticmetamodel.MonitoringDevice_;
import cn.facilityone.shang.monitoring.monitoring001.dto.*;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org004.staticmetamodel.Building_;
import cn.facilityone.shang.organize.org004.staticmetamodel.Floor_;
import cn.facilityone.shang.organize.org004.staticmetamodel.Room_;
import cn.facilityone.shang.system.sys002.repository.SystemRoleRepository;
import cn.facilityone.shang.workorder.wo003.repository.WorkOrderProcessRepository;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.security.model.Menu;
import cn.facilityone.xia.security.model.Permission;
import cn.facilityone.xia.security.model.Role;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.SecurityContextImpl;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;

import javax.persistence.criteria.*;
import java.util.*;

/**
 * 数据同步.
 *
 * <AUTHOR>
 * @date 2017-09-11
 * @version 1.0
 */
@Service
public class DataSyncServiceImpl implements DataSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataSyncServiceImpl.class);
    private static Map<Long, String> SYNCUSER = Collections.synchronizedMap(new HashMap<Long, String>());
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Autowired
    private MonitoringParamRepository monitoringParamRepository;
    @Autowired
    private MonitoringDataSyncLogRepository monitoringDataSyncLogRepository;
    @Autowired
    private MonitoringInterfaceAccessErrorRecordRepository monitoringInterfaceAccessErrorRecordRepository;
    @Autowired
    private MonitoringSettingRepository monitoringSettingRepository;
    @Autowired
    private MonitoringDeviceRepository monitoringDeviceRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private WorkOrderProcessRepository workOrderProcessRepository;
    @Autowired
    private EquipmentRepository equipmentRepository;
    @Autowired
    private SystemRoleRepository roleRepository;

    @Override
    public SyncResult getSyncResult() {
        SyncResult syncResult = new SyncResult();

        MonitoringDataSyncLog monitoringDataSyncLog = monitoringDataSyncLogRepository.findFirstByOrderByCreatedDateDesc();
        if (monitoringDataSyncLog != null) {
            syncResult.setLatestSyncTime(monitoringDataSyncLog.getCreatedDate());
            syncResult.setSyncOperator(monitoringDataSyncLog.getCreatedBy());
            syncResult.setSyncResult(monitoringDataSyncLog.isResult());
        }

        return syncResult;
    }

    @Override
    public DataTableResponse getBasicDataTable(final DataTableRequest request) {
        Page<MonitoringDevice> monitoringDevicePage = monitoringDeviceRepository.findAll(new Specification<MonitoringDevice>() {
            @Override
            public Predicate toPredicate(Root<MonitoringDevice> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicateList = new ArrayList<Predicate>();

                List<DataTableColumn> dataTableColumnList = request.getColumns();
                if (CollectionUtils.isNotEmpty(dataTableColumnList)) {
                    List<Predicate> lps = dataTableService.buildColumnSearch(dataTableColumnList, root, criteriaBuilder);
                    predicateList.addAll(lps);
                }

                criteriaQuery.where(predicateList.toArray(new Predicate[predicateList.size()]));

                // 按照设备编号升序排列
                List<Order> orders = new ArrayList<Order>();
                Order order = criteriaBuilder.asc(root.get(MonitoringDevice_.DEVICEIDSORT));
                orders.add(order);
                criteriaQuery.orderBy(orders);
                criteriaQuery.distinct(true);

                return criteriaQuery.getRestriction();
            }
        }, request);
        return new DataTableResponse(monitoringDevicePage.getContent(), (int) monitoringDevicePage.getTotalElements(), request.getPageNumber(), request.getDraw());
    }

    @Override
    public boolean checkPermission(String requestType, String url) {
        SecurityContextImpl sc = (SecurityContextImpl) SecurityContextHolder.getContext();

        // 超级管理员不需要验证权限
        if(SystemConst.isSuperAdmin()){
            return true;
        }

        // 权限验证
        Set<Permission> permissionSet = sc.getPermissions();
        if (permissionSet != null) {
            for (Permission permission : permissionSet) {
                if (permission.getPath() != null) {
                    String[] patterns = permission.getPath().split(SystemConst.STR_SEMICOLON);
                    for (String pattern : patterns) {
                        if (pathMatcher.match(pattern, url) &&
                                ("*".equals(permission.getMethod()) ||
                                        requestType.equalsIgnoreCase(permission.getMethod()))) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    @Override
    public SyncResult syncBasicData() {
        SyncResult syncResult = null;

        // 判断当前项目是否有用户正在同步
        Long projId = ProjectContext.getCurrentProject();
        synchronized (new Object()) {
            if (SYNCUSER.containsKey(projId)) {
                syncResult = new SyncResult();
                syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_SYNC);
                syncResult.setMessage(XiaMesssageResource.getMessage("page.monitoring001.msg.sync.fail.ing", null, SYNCUSER.get(projId)));
                return syncResult;
            }
            XiaPrincipal user = (XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal();
            SYNCUSER.put(projId, user.getRealName());
        }

        try {
            syncResult = this.requestSync();
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.debug("同步基础数据失败");

            MonitoringDataSyncLog monitoringDataSyncLog = new MonitoringDataSyncLog();
            monitoringDataSyncLog.setResult(false);
            monitoringDataSyncLog = monitoringDataSyncLogRepository.saveAndFlush(monitoringDataSyncLog);

            syncResult = new SyncResult();
            syncResult.setLatestSyncTime(monitoringDataSyncLog.getCreatedDate());
            syncResult.setSyncOperator(monitoringDataSyncLog.getCreatedBy());
            syncResult.setSyncResult(monitoringDataSyncLog.isResult());
            syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_REQUEST);
        }

        SYNCUSER.remove(projId);
        return syncResult;
    }

    @Override
    public Map<String, Object> getEqByCode(String code) {
        Map<String, Object> result = new HashedMap();

        // 判断当前用户是否有页面权限
        boolean permission = this.checkMenuPermission(MonitoringConst.EQ_MENU_LINK);
        if (!permission) {
            result.put("message", XiaMesssageResource.getMessage("page.monitoring001.eq.menu.permission"));
            return result;
        }


        List<Equipment> equipmentList = equipmentRepository.findByCode(code);
        if (CollectionUtils.isNotEmpty(equipmentList)) {
            result.put("eqId", equipmentList.get(0).getId());
            return result;
        }

        result.put("message", XiaMesssageResource.getMessage("page.monitoring001.eq.exist.check"));
        return result;
    }

    @Override
    public boolean checkMenuPermission(String link) {
        // 超级管理员不需要验证权限
        if (SystemConst.isSuperAdmin()) {
            return true;
        }

        XiaPrincipal user = (XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal();
        Long userId = user.getId();
        List<Role> roleList = roleRepository.findRolesByUserId(userId, ProjectContext.getCurrentProject());
        if (CollectionUtils.isNotEmpty(roleList)) {
            List<Long> roleIds = new ArrayList<Long>();
            for(Role role : roleList){
                roleIds.add(role.getId());
            }
            List<Menu> menuList = roleRepository.findMenusByRoles(roleIds.toArray(new Long[roleIds.size()]), ProjectContext.getCurrentProject());
            if (CollectionUtils.isNotEmpty(menuList)) {
                for (Menu menu : menuList) {
                    if (link.equals(menu.getLink())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public DeviceSettingDetailDTO getDetailInfo(Long id) {
        DeviceSettingDetailDTO detailDTO = new DeviceSettingDetailDTO();

        MonitoringDevice device = monitoringDeviceRepository.findDetailById(id);
        BeanUtils.copyProperties(device, detailDTO);

        // 警告提醒
        List<Employee> notifiers = device.getNotifiers();
        if (CollectionUtils.isNotEmpty(notifiers)) {
            detailDTO.setNotifierOption(notifiers);
            List<Long> notifierIds = new ArrayList<>();
            for (Employee employee : notifiers) {
                notifierIds.add(employee.getId());
            }
            detailDTO.setNotifierIds(notifierIds);
        }

        // 触发工单
        WorkOrderProcess workOrderProcess = device.getWorkOrderProcess();
        if (device.getWorkOrderProcess() != null) {
            List<SlaDTO> slaDTOList = new ArrayList<>();
            workOrderProcess = workOrderProcessRepository.findDetailById(workOrderProcess.getId());

            SlaDTO slaDTO = new SlaDTO();
            slaDTO.setServiceType(workOrderProcess.getServiceType().getFullName());
            slaDTO.setLocation(LocationUtil.buildString(workOrderProcess.getBuilding(), workOrderProcess.getFloor(), workOrderProcess.getRoom()));
            Organization organization = workOrderProcess.getOrganization();
            if (organization != null) {
                slaDTO.setDeptName(organization.getFullName());
            }
            slaDTO.setWorkOrderType(workOrderProcess.getType().toString());
            slaDTO.setPriority(workOrderProcess.getPriority().getName());
            slaDTOList.add(slaDTO);
            detailDTO.setSlaDTOList(slaDTOList);
            detailDTO.setSlaId(workOrderProcess.getId());
        }

        return detailDTO;
    }

    @Override
    public DataTableResponse getParamsDataTable(DataTableRequest request) {
        Long id = null;
        List<DataTableConditions> conditionsList = request.getConditions();
        if (CollectionUtils.isNotEmpty(conditionsList)) {
            for (DataTableConditions condition : conditionsList) {
                if ("id".equals(condition.getField())) {
                    id = Long.valueOf(condition.getValues().get(0));
                }
            }
        }

        if (id != null) {
            Page<MonitoringParam> monitoringParamPage = monitoringDeviceRepository.findPageById(id, request);
            return new DataTableResponse(monitoringParamPage.getContent(), (int) monitoringParamPage.getTotalElements(), request.getPageNumber(), request.getDraw());
        }
        return new DataTableResponse();
    }

    @Override
    public List<Employee> getNotifiers(String key) {
        List<Employee> employeeList;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(key)) {
            employeeList = employeeRepository.findByNameLike(dataTableService.buildLikeText(key));
        } else {
            employeeList = employeeRepository.findAll();
        }
        return employeeList;
    }

    @Override
    public DataTableResponse getSlaTable(final DataTableRequest request) {
        List<DataTableColumn> tableColumn = request.getColumns();
        String location = null;
        for(DataTableColumn column : tableColumn) {
            if("serviceType".equals(column.getName())) {
                column.setName("serviceType.fullName");
            } else if ("location".equals(column.getName())) {
                location = column.getSearchText();
                column.setSearchText("");
            } else if ("deptName".equals(column.getName())) {
                column.setName("organization.fullName");
            } else if ("workOrderType".equals(column.getName())) {
                column.setName("type");
            } else if ("priority".equals(column.getName())) {
                column.setName("priority.name");
            }
        }

        Long slaId = null;
        List<DataTableConditions> conditionsList = request.getConditions();
        if (CollectionUtils.isNotEmpty(conditionsList)) {
            for (DataTableConditions condition : conditionsList) {
                if ("slaId".equals(condition.getField())) {
                    slaId = Long.valueOf(condition.getValues().get(0));
                }
            }
        }

        final String locationSearchText = location;
        final Long slaIdNotIn = slaId;

        Page<WorkOrderProcess> workOrderProcessPage = workOrderProcessRepository.findAll(new Specification<WorkOrderProcess>() {
            @Override
            public Predicate toPredicate(Root<WorkOrderProcess> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                List<Predicate> predicateList = new ArrayList<Predicate>();

                // 工单类型为混合或ZM
                predicateList.add(criteriaBuilder.or(criteriaBuilder.equal(root.<Integer>get(WorkOrderProcess_.WORKORDER_TYPE), WorkOrder.WorkOrderType.MIXTURE.ordinal()),
                        criteriaBuilder.equal(root.<Integer>get(WorkOrderProcess_.WORKORDER_TYPE), WorkOrder.WorkOrderType.SELFCHECK.ordinal())));

                List<DataTableColumn> dataTableColumnList = request.getColumns();
                if (CollectionUtils.isNotEmpty(dataTableColumnList)) {
                    List<Predicate> lps = dataTableService.buildColumnSearch(dataTableColumnList, root, criteriaBuilder);
                    predicateList.addAll(lps);
                }

                root.join(WorkOrderProcess_.SERVICE_TYPE, JoinType.LEFT);
                From building = root.join(WorkOrderProcess_.BUILDING, JoinType.LEFT);
                From floor = root.join(WorkOrderProcess_.FLOOR, JoinType.LEFT);
                From room = root.join(WorkOrderProcess_.ROOM, JoinType.LEFT);
                root.join(WorkOrderProcess_.ORGANIZATION, JoinType.LEFT);
                root.join(WorkOrderProcess_.PRIORITY, JoinType.LEFT);

                if (slaIdNotIn != null) {
                    predicateList.add(criteriaBuilder.notEqual(root.<Long>get(WorkOrderProcess_.ID), slaIdNotIn));
                }

                // 安装位置
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(locationSearchText)) {
                    String likeText = dataTableService.buildLikeText(locationSearchText);
                    predicateList.add(criteriaBuilder.or(criteriaBuilder.like(building.<String>get(Building_.NAME), likeText),
                            criteriaBuilder.like(floor.<String>get(Floor_.NAME), likeText),
                            criteriaBuilder.like(room.<String>get(Room_.NAME), likeText)));
                }

                criteriaQuery.where(predicateList.toArray(new Predicate[predicateList.size()]));

                List<Order> orders = new ArrayList<Order>();
                Order order = criteriaBuilder.asc(root.get(WorkOrderProcess_.ID));
                orders.add(order);
                criteriaQuery.orderBy(orders);
                criteriaQuery.distinct(true);

                return criteriaQuery.getRestriction();
            }
        }, request);

        List<SlaDTO> slaDTOList = new ArrayList<>();
        List<WorkOrderProcess> workOrderProcessList = workOrderProcessPage.getContent();
        if (CollectionUtils.isNotEmpty(workOrderProcessList)) {
            workOrderProcessList = workOrderProcessRepository.findDetailByIdList(this.getWorkOrderProcessIdList(workOrderProcessList), ProjectContext.getCurrentProject());

            SlaDTO slaDTO = null;
            for (WorkOrderProcess process : workOrderProcessList) {
                slaDTO = new SlaDTO();
                slaDTO.setId(process.getId());
                slaDTO.setServiceType(process.getServiceType().getFullName());
                slaDTO.setLocation(LocationUtil.buildString(process.getBuilding(), process.getFloor(), process.getRoom()));
                Organization organization = process.getOrganization();
                if (organization != null) {
                    slaDTO.setDeptName(organization.getFullName());
                }
                slaDTO.setWorkOrderType(process.getType().toString());
                slaDTO.setPriority(process.getPriority().getName());
                slaDTOList.add(slaDTO);
            }
        }

        return new DataTableResponse(slaDTOList, (int) workOrderProcessPage.getTotalElements(), request.getPageNumber(), request.getDraw());
    }

    @Override
    @XiaTransactional(readOnly = false)
    public boolean modifyDeviceSetting(DeviceSettingEditDTO deviceSettingEditDTO) {
        List<Long> ids = deviceSettingEditDTO.getIds();

        List<MonitoringDevice> monitoringDeviceList = monitoringDeviceRepository.findByIdIn(ids);

        if (CollectionUtils.isNotEmpty(monitoringDeviceList)) {
            // 警告提醒
            List<Long> notifierIds = deviceSettingEditDTO.getNotifierIds();
            List<Employee> notifiers = null;
            String msgType = null;
            if (CollectionUtils.isNotEmpty(notifierIds)) {
                notifiers = employeeRepository.findByIdIn(notifierIds);
                msgType = deviceSettingEditDTO.getMsgType();
            }

            Long slaId = deviceSettingEditDTO.getSlaId();
            WorkOrderProcess workOrderProcess = null;
            if (slaId != null) {
                workOrderProcess = workOrderProcessRepository.findOne(slaId);
            }

            for (MonitoringDevice monitoringDevice : monitoringDeviceList) {
                monitoringDevice.setNotifiers(notifiers);
                monitoringDevice.setMsgType(msgType);
                monitoringDevice.setWorkOrderProcess(workOrderProcess);
            }

            monitoringDeviceRepository.save(monitoringDeviceList);
        }

        return true;
    }

    @Override
    public void createAccessErrorRecord(String type, String url, String param, String message, Long projId) {
        MonitoringInterfaceAccessErrorRecord accessErrorRecord = new MonitoringInterfaceAccessErrorRecord();
        accessErrorRecord.setType(type);
        accessErrorRecord.setUrl(url);
        accessErrorRecord.setParam(param);
        accessErrorRecord.setMessage(message);
        accessErrorRecord.setProject(projId);
        monitoringInterfaceAccessErrorRecordRepository.save(accessErrorRecord);
    }

    @XiaTransactional(readOnly = false)
    private SyncResult requestSync() {
        MonitoringDataSyncLog monitoringDataSyncLog = new MonitoringDataSyncLog();
        SyncResult syncResult = new SyncResult();

        MonitoringSetting setting = monitoringSettingRepository.findFirstByOrderByIdAsc();
        if (setting == null || StringUtils.isEmpty(setting.getProjectCode()) || StringUtils.isEmpty(setting.getAccessToken())) {
            syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_SETTING);
            syncResult.setMessage(XiaMesssageResource.getMessage("server.result.error.sync", null, XiaMesssageResource.getMessage("page.monitoring001.msg.sync.fail.setting")));
            return syncResult;
        }

        // 获取项目下所有的设备信息
        this.syncDevice(setting, monitoringDataSyncLog, syncResult);
        if (MonitoringConst.SYNC_ERR_TYPE_REQUEST.equals(syncResult.getErrType())) {
            return syncResult;
        }

        // 获取项目下所有参数信息
        this.syncParam(setting, monitoringDataSyncLog, syncResult);
        if (MonitoringConst.SYNC_ERR_TYPE_REQUEST.equals(syncResult.getErrType())) {
            return syncResult;
        }

        // 获取设备和参数的对应关系
        this.syncDeviceParamMapping(setting, monitoringDataSyncLog, syncResult);
        return syncResult;
    }

    private void syncDevice(MonitoringSetting setting, MonitoringDataSyncLog monitoringDataSyncLog, SyncResult syncResult) {
        String url = MonitoringConst.GET_DEVICE_URL.replaceAll(MonitoringConst.URL_ACCESSTOKEN, setting.getAccessToken());
        GetDeviceApiRequest apiRequest = new GetDeviceApiRequest();
        apiRequest.setProjectCode(setting.getProjectCode());
        String deviceJson = HttpClientUtil.sendPost(url, JSON.toJSONString(apiRequest), null);
        if (deviceJson != null) {
            GetDeviceApiResponse apiResponse = JSON.parseObject(deviceJson, GetDeviceApiResponse.class);
            if (apiResponse.getState() == 0) {
                List<DeviceApi> deviceApiList = apiResponse.getData();
                if (CollectionUtils.isNotEmpty(deviceApiList)) {
                    List<MonitoringDevice> deviceList = monitoringDeviceRepository.findAll();
                    Map<String, MonitoringDevice> localDeviceMap = new HashedMap();
                    for (MonitoringDevice device : deviceList) {
                        localDeviceMap.put(device.getDeviceID(), device);
                    }

                    MonitoringDevice device = null;
                    for (DeviceApi deviceApi : deviceApiList) {
                        String key = deviceApi.getDeviceID().toString();
                        if (localDeviceMap.containsKey(key)) {
                            device = localDeviceMap.get(key);
                            localDeviceMap.remove(key);
                        } else {
                            device = new MonitoringDevice();
                            device.setDeviceID(deviceApi.getDeviceID().toString());
                            device.setDeviceIDSort(deviceApi.getDeviceID());
                        }
                        device.setTypeID(deviceApi.getTypeID().toString());
                        device.setTypeName(deviceApi.getTypeName());
                        device.setNavName(deviceApi.getNavName());
                        device.setDeviceName(deviceApi.getDeviceName());
                        String deviceCode = deviceApi.getDeviceCode();
                        device.setDeviceCode(deviceCode);

                        monitoringDeviceRepository.save(device);
                    }

                    if (CollectionUtils.isNotEmpty(localDeviceMap.keySet())) {
                        for (MonitoringDevice del : localDeviceMap.values()) {
                            monitoringDeviceRepository.delete(del);
                        }
                    }
                }

                monitoringDataSyncLog.setResult(true);
                return;
            } else {
                monitoringDataSyncLog.setResult(false);
                syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_REQUEST);
                syncResult.setErrorMessage("state: " + apiResponse.getState() + ", " +
                        apiResponse.getErrorMessage() + " " + XiaMesssageResource.getMessage("page.monitoring001.msg.sync.fail.contact"));

                this.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                        "state: " + apiResponse.getState() + ", " + apiResponse.getErrorMessage(),
                        ProjectContext.getCurrentProject());
                LOGGER.debug("接口url=" + url + "， 访问失败！state：" + apiResponse.getState() + "，失败信息：" + apiResponse.getErrorMessage());
            }
        } else {
            monitoringDataSyncLog.setResult(false);
            syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_REQUEST);
            syncResult.setErrorMessage("");

            this.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                    null, ProjectContext.getCurrentProject());
            LOGGER.debug("接口url=" + url + "， 访问失败！");
        }
        monitoringDataSyncLog = monitoringDataSyncLogRepository.save(monitoringDataSyncLog);

        syncResult.setLatestSyncTime(monitoringDataSyncLog.getCreatedDate());
        syncResult.setSyncOperator(monitoringDataSyncLog.getCreatedBy());
        syncResult.setSyncResult(monitoringDataSyncLog.isResult());
    }

    private void syncParam(MonitoringSetting setting, MonitoringDataSyncLog monitoringDataSyncLog, SyncResult syncResult) {
        String url = MonitoringConst.GET_PARAME_URL.replaceAll(MonitoringConst.URL_ACCESSTOKEN, setting.getAccessToken());
        GetParamApiRequest apiRequest = new GetParamApiRequest();
        apiRequest.setProjectCode(setting.getProjectCode());
        String deviceParamJson = HttpClientUtil.sendPost(url, JSON.toJSONString(apiRequest), null);
        if (deviceParamJson != null) {
            GetParamApiResponse apiResponse = JSON.parseObject(deviceParamJson, GetParamApiResponse.class);
            if (apiResponse.getState() == 0) {
                List<ParamApi> paramApiList = apiResponse.getData();
                if (CollectionUtils.isNotEmpty(paramApiList)) {
                    List<MonitoringParam> monitoringParamList = monitoringParamRepository.findAll();
                    Map<String, MonitoringParam> localDeviceParamMap = new HashedMap();
                    for (MonitoringParam deviceParam : monitoringParamList) {
                        localDeviceParamMap.put(deviceParam.getDataCode(), deviceParam);
                    }

                    MonitoringParam param = null;
                    for (ParamApi paramApi : paramApiList) {
                        String key = paramApi.getDataCode().toString();
                        if (localDeviceParamMap.containsKey(key)) {
                            param = localDeviceParamMap.get(key);
                            localDeviceParamMap.remove(key);
                        } else {
                            param = new MonitoringParam();
                            param.setDataCode(paramApi.getDataCode().toString());
                        }
                        param.setDataName(paramApi.getDataName());
                        param.setUnit(paramApi.getUnit());

                        monitoringParamRepository.save(param);
                    }

                    if (CollectionUtils.isNotEmpty(localDeviceParamMap.keySet())) {
                        for (MonitoringParam del : localDeviceParamMap.values()) {
                            monitoringParamRepository.delete(del);
                        }
                    }
                }

                monitoringDataSyncLog.setResult(true);
                return;
            } else {
                monitoringDataSyncLog.setResult(false);
                syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_REQUEST);
                syncResult.setErrorMessage("state: " + apiResponse.getState() + ", " +
                        apiResponse.getErrorMessage() + " " + XiaMesssageResource.getMessage("page.monitoring001.msg.sync.fail.contact"));

                this.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                        "state: " + apiResponse.getState() + ", " + apiResponse.getErrorMessage(),
                        ProjectContext.getCurrentProject());
                LOGGER.debug("接口url=" + url + "， 访问失败！state：" + apiResponse.getState() + "，失败信息：" + apiResponse.getErrorMessage());
            }
        } else {
            monitoringDataSyncLog.setResult(false);
            syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_REQUEST);
            syncResult.setErrorMessage("");

            this.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                    null, ProjectContext.getCurrentProject());
            LOGGER.debug("接口url=" + url + "， 访问失败！");
        }
        monitoringDataSyncLog = monitoringDataSyncLogRepository.save(monitoringDataSyncLog);

        syncResult.setLatestSyncTime(monitoringDataSyncLog.getCreatedDate());
        syncResult.setSyncOperator(monitoringDataSyncLog.getCreatedBy());
        syncResult.setSyncResult(monitoringDataSyncLog.isResult());
    }

    private void syncDeviceParamMapping(MonitoringSetting setting, MonitoringDataSyncLog monitoringDataSyncLog, SyncResult syncResult) {
        String url = MonitoringConst.GET_DEVICE_PARAME_MAPPING_URL.replaceAll(MonitoringConst.URL_ACCESSTOKEN, setting.getAccessToken());
        GetDeviceParamMappingApiRequest apiRequest = new GetDeviceParamMappingApiRequest();
        apiRequest.setProjectCode(setting.getProjectCode());
        String deviceParamMappingJson = HttpClientUtil.sendPost(url, JSON.toJSONString(apiRequest), null);
        if (deviceParamMappingJson != null) {
            GetDeviceParamMappingApiResponse apiResponse = JSON.parseObject(deviceParamMappingJson, GetDeviceParamMappingApiResponse.class);
            if (apiResponse.getState() == 0) {
                List<DeviceParamMappingApi> deviceParamMappingApiList = apiResponse.getData();
                Map<String, List<MonitoringParam>> deviceParamMappingMap = new HashedMap();
                if (CollectionUtils.isNotEmpty(deviceParamMappingApiList)) {
                    Map<String, MonitoringParam> paramMap = new HashMap<>();
                    List<MonitoringParam> paramList = monitoringParamRepository.findAll();
                    for (MonitoringParam param : paramList) {
                        paramMap.put(param.getDataCode(), param);
                    }

                    for (DeviceParamMappingApi mappingApi : deviceParamMappingApiList) {
                        String deviceID = mappingApi.getDeviceID().toString();
                        List<MonitoringParam> params = deviceParamMappingMap.get(deviceID);
                        if (CollectionUtils.isEmpty(params)) {
                            params = new ArrayList<>();
                            deviceParamMappingMap.put(deviceID, params);
                        }
                        params.add(paramMap.get(mappingApi.getDataCode().toString()));
                    }
                }
                List<MonitoringDevice> monitoringDeviceList = monitoringDeviceRepository.findAll();
                for (MonitoringDevice device : monitoringDeviceList) {
                    device.setMonitoringParams(deviceParamMappingMap.get(device.getDeviceID()));
                }
                monitoringDeviceRepository.save(monitoringDeviceList);

                monitoringDataSyncLog.setResult(true);
            } else {
                monitoringDataSyncLog.setResult(false);
                syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_REQUEST);
                syncResult.setErrorMessage("state: " + apiResponse.getState() + ", " +
                        apiResponse.getErrorMessage() + " " + XiaMesssageResource.getMessage("page.monitoring001.msg.sync.fail.contact"));

                this.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                        "state: " + apiResponse.getState() + ", " + apiResponse.getErrorMessage(),
                        ProjectContext.getCurrentProject());
                LOGGER.debug("接口url=" + url + "， 访问失败！state：" + apiResponse.getState() + "，失败信息：" + apiResponse.getErrorMessage());
            }
        } else {
            monitoringDataSyncLog.setResult(false);
            syncResult.setErrType(MonitoringConst.SYNC_ERR_TYPE_REQUEST);
            syncResult.setErrorMessage("");

            this.createAccessErrorRecord(SystemConst.HTTP_METHOD_POST, url, JSON.toJSONString(apiRequest),
                    null, ProjectContext.getCurrentProject());
            LOGGER.debug("接口url=" + url + "， 访问失败！");
        }
        monitoringDataSyncLog = monitoringDataSyncLogRepository.save(monitoringDataSyncLog);

        syncResult.setLatestSyncTime(monitoringDataSyncLog.getCreatedDate());
        syncResult.setSyncOperator(monitoringDataSyncLog.getCreatedBy());
        syncResult.setSyncResult(monitoringDataSyncLog.isResult());
    }

    private List<Long> getWorkOrderProcessIdList(List<WorkOrderProcess> workOrderProcessList) {
        List<Long> idList = new ArrayList<>();
        for (WorkOrderProcess workOrderProcess : workOrderProcessList) {
            idList.add(workOrderProcess.getId());
        }
        return idList;
    }
}
