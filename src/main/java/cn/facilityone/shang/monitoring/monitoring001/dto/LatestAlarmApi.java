package cn.facilityone.shang.monitoring.monitoring001.dto;

/**
 * 当前设定周期内最新告警(接口).
 *
 * <AUTHOR>
 * @date 2017-09-12
 * @version 1.0
 */
public class LatestAlarmApi {

    /**
     * 告警编号
     */
    private String alarmID;

    /**
     * 报警等级（一般告警、重要告警），暂不不提供事件信息
     */
    private String alarmLevel;

    /**
     * 报警类型（如：过温）
     */
    private String alarmType;

    /**
     * 设备编号
     */
    private Long deviceID;

    /**
     * 参数编号
     */
    private Long dataCode;

    /**
     * 参数值
     */
    private Float collectData;

    /**
     * 报警时间
     */
    private String alarmtime;

    /**
     * 报警信息
     */
    private String alarmInfo;

    public String getAlarmID() {
        return alarmID;
    }

    public void setAlarmID(String alarmID) {
        this.alarmID = alarmID;
    }

    public String getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(String alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }

    public Long getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(Long deviceID) {
        this.deviceID = deviceID;
    }

    public Long getDataCode() {
        return dataCode;
    }

    public void setDataCode(Long dataCode) {
        this.dataCode = dataCode;
    }

    public Float getCollectData() {
        return collectData;
    }

    public void setCollectData(Float collectData) {
        this.collectData = collectData;
    }

    public String getAlarmtime() {
        return alarmtime;
    }

    public void setAlarmtime(String alarmtime) {
        this.alarmtime = alarmtime;
    }

    public String getAlarmInfo() {
        return alarmInfo;
    }

    public void setAlarmInfo(String alarmInfo) {
        this.alarmInfo = alarmInfo;
    }
}
