package cn.facilityone.shang.monitoring.monitoring005.resource;

import org.glassfish.jersey.server.mvc.Template;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Yogi on 2017/11/22.
 */
@Path("/monitoring005")
public class AlarmStatisticsResource {

    private static final String TEMPLATE_PATH = "/business/monitoring/monitoring005-alarm-statistics.ftl";

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>(0);
    }
}
