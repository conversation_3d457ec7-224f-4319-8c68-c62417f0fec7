package cn.facilityone.shang.monitoring.monitoring003.resource;

import cn.facilityone.shang.monitoring.monitoring003.service.MonitoringRecordService;
import cn.facilityone.xia.core.common.Result;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * Class description goes here.
 * 监测记录
 * <AUTHOR>
 * @version 1.0
 * @date 2017-09-12
 */
@Path("/monitoring003")
public class MonitoringRecordResource {

    private static final String TEMPLATE_PATH = "/business/monitoring/monitoring003-monitoring-record.ftl";

    @Autowired
    private MonitoringRecordService recordService;

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>(0);
    }

    /**
     * 获取最近更新时间
     * @return
     */
    @GET
    @Path("/recentUpdateTime")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getRecentUpdateTime(){
        return new Result(Result.CODE_200, "", recordService.getRecentUpdateTime());
    }

    /**
     * 根据工单id，获取存在的工单id
     */
    @GET
    @Path("/wo/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getWoById(@PathParam("id") Long id) {
        Map<String, Object> resultMap = recordService.getWoById(id);
        if (resultMap.containsKey("message")) {
            Result result = new Result(Result.CODE_200, resultMap.get("message").toString(), null);
            result.setStatus(Result.STATUS_FAIL);
            return result;
        }
        return new Result(resultMap.get("woId"));
    }
}
