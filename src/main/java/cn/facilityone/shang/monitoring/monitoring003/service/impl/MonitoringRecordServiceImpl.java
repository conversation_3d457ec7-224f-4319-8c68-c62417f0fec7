package cn.facilityone.shang.monitoring.monitoring003.service.impl;

import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.entity.monitoring.MonitoringGetAlarmLog;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.monitoring.common.repository.MonitoringGetAlarmLogRepository;
import cn.facilityone.shang.monitoring.common.staticmetamodel.MonitoringConst;
import cn.facilityone.shang.monitoring.monitoring001.service.DataSyncService;
import cn.facilityone.shang.monitoring.monitoring003.service.MonitoringRecordService;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * Class description goes here.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2017-09-12
 */
@Service
public class MonitoringRecordServiceImpl implements MonitoringRecordService {

    @Autowired
    private MonitoringGetAlarmLogRepository getAlarmLogRepository;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private DataSyncService dataSyncService;

    @Override
    public Date getRecentUpdateTime() {
        MonitoringGetAlarmLog alarmLog = getAlarmLogRepository.findFirstByOrderBySyncTimeDesc();
        if(alarmLog == null){
            return null;
        }
        return alarmLog.getSyncTime();
    }

    @Override
    public Map<String, Object> getWoById(Long woId) {
        Map<String, Object> result = new HashedMap();

        // 判断当前用户是否有页面权限
        boolean permission = dataSyncService.checkMenuPermission(MonitoringConst.WO_MENU_LINK);
        if (!permission) {
            result.put("message", XiaMesssageResource.getMessage("page.monitoring003.wo.menu.permission"));
            return result;
        }

        WorkOrder workOrder = workOrderRepository.findOne(woId);
        if (workOrder != null) {
            result.put("woId", workOrder.getId());
            return result;
        }

        result.put("message", XiaMesssageResource.getMessage("page.monitoring003.wo.exist.check"));
        return result;
    }
}
