package cn.facilityone.shang.monitoring.monitoring002.service;

import cn.facilityone.shang.common.component.quartz.core.dto.QuartzJobTrigger;
import cn.facilityone.shang.common.component.quartz.core.service.JobService;
import cn.facilityone.shang.common.component.quartz.job.monitoring.MonitoringAlarmSyncJobServiceImpl;
import cn.facilityone.shang.common.repository.PeriodRepository;
import cn.facilityone.shang.entity.common.Period;
import cn.facilityone.shang.entity.monitoring.MonitoringSetting;
import cn.facilityone.shang.monitoring.common.repository.MonitoringSettingRepository;
import cn.facilityone.shang.monitoring.common.staticmetamodel.MonitoringConst;
import cn.facilityone.shang.monitoring.monitoring002.dto.MonitoringSettingDTO;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 系统设置.
 *
 * <AUTHOR>
 * @date 2017-09-18
 * @version 1.0
 */
@Service
public class MonitoringSettingServiceImpl implements MonitoringSettingService {

    @Autowired
    private MonitoringSettingRepository monitoringSettingRepository;
    @Autowired
    private PeriodRepository periodRepository;
    @Autowired
    JobService jobService;

    @Override
    public MonitoringSettingDTO getMonitoringSetting() {
        MonitoringSettingDTO settingDTO = new MonitoringSettingDTO();
        MonitoringSetting setting = monitoringSettingRepository.findFirstByOrderByIdAsc();
        if (setting != null) {
            Period period = setting.getPeriod();
            settingDTO.setValue(period.getValue());
            settingDTO.setType(period.getType().toString().toLowerCase());
            settingDTO.setProjectCode(setting.getProjectCode());
            settingDTO.setAccessToken(setting.getAccessToken());
        } else {
            // 频率初始值：5分钟
            settingDTO.setValue(5L);
            settingDTO.setType(Period.PeriodType.MINUTE.toString().toLowerCase());
        }
        return settingDTO;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void modifyMonitoringSetting(MonitoringSettingDTO settingDTO) {
        Long value = settingDTO.getValue();
        String type = settingDTO.getType();

        MonitoringSetting setting = monitoringSettingRepository.findFirstByOrderByIdAsc();
        Period period = null;
        int oldMinutes = 5;
        if (setting == null) {
            setting = new MonitoringSetting();
            period = new Period();
        } else {
            period = setting.getPeriod();
            if (Period.PeriodType.MINUTE.equals(period.getType())) {
                oldMinutes = period.getValue().intValue();
            } else if (Period.PeriodType.HOUR.equals(period.getType())) {
                oldMinutes = period.getValue().intValue() * 60;
            }
        }
        period.setValue(value);
        int minutes = value.intValue();
        if (Period.MINUTE.equals(type)) {
            period.setType(Period.PeriodType.MINUTE);
        } else if (Period.HOUR.equals(type)) {
            period.setType(Period.PeriodType.HOUR);
            minutes = (int) (value * 60);
        }
        period = periodRepository.save(period);
        setting.setPeriod(period);

        String projectCode = settingDTO.getProjectCode();
        String accessToken = settingDTO.getAccessToken();
        setting.setProjectCode(projectCode);
        setting.setAccessToken(accessToken);
        monitoringSettingRepository.save(setting);

        Long projId = ProjectContext.getCurrentProject();

        if (!StringUtils.isEmpty(projectCode) && !StringUtils.isEmpty(accessToken)) {
            String triggerName = MonitoringConst.TRIGGER_NAME + projId;
            String triggerGroup = MonitoringConst.TRIGGER_GROUP + projId;
            if (jobService.checkTriggerExist(triggerName, triggerGroup)) {
                // 若频率有修改，才更新job
                if (oldMinutes != minutes) {
                    jobService.updateJobCron(triggerName, triggerGroup, minutes);
                }
            } else {
                QuartzJobTrigger qjt = new QuartzJobTrigger();
                qjt.setJobClass(MonitoringAlarmSyncJobServiceImpl.class.getName());
                qjt.setJobName(MonitoringConst.JOB_NAME);
                qjt.setJobGroup(MonitoringConst.JOB_GROUP);
                qjt.setJobDescription(MonitoringConst.JOB_DESCRIPTION);
                qjt.setTriggerName(MonitoringConst.TRIGGER_NAME);
                qjt.setTriggerGroup(MonitoringConst.TRIGGER_GROUP);
                jobService.addOneMonitoringJob(null, qjt, projId, minutes);
            }
        }
    }
}
