package cn.facilityone.shang.monitoring.monitoring002.resource;

import cn.facilityone.shang.monitoring.monitoring002.dto.MonitoringSettingDTO;
import cn.facilityone.shang.monitoring.monitoring002.service.MonitoringSettingService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统设置.
 *
 * <AUTHOR>
 * @date 2017-09-15
 * @version 1.0
 */
@Path("/monitoring002")
public class MonitoringSettingResource {

    private static final String TEMPLATE_PATH = "/business/monitoring/monitoring002-setting.ftl";

    @Autowired
    private MonitoringSettingService monitoringSettingService;

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>(0);
    }

    /**
     * 获取系统设置
     */
    @GET
    @Path("setting")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getMonitoringSetting() {
        MonitoringSettingDTO settingDTO = monitoringSettingService.getMonitoringSetting();
        return new Result(settingDTO);
    }

    /**
     * 更新系统设置
     */
    @PUT
    @Path("setting")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result modifyMonitoringSetting(MonitoringSettingDTO settingDTO) {
        monitoringSettingService.modifyMonitoringSetting(settingDTO);
        return new Result(XiaMesssageResource.getMessage("server.result.success.update", null, ""));
    }
}
