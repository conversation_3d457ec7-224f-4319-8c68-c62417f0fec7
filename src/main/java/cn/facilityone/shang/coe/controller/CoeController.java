package cn.facilityone.shang.coe.controller;

import cn.facilityone.shang.coe.properties.CoeProperties;
import cn.facilityone.shang.coe.util.EncryptUtils;
import cn.facilityone.xia.core.properties.XiaCoreProperties;
import cn.facilityone.xia.core.properties.XiaExpiresProperties;
import cn.facilityone.xia.security.service.SecurityService;
import org.apache.commons.lang.StringUtils;
import org.apache.oltu.oauth2.as.issuer.MD5Generator;
import org.apache.oltu.oauth2.as.issuer.OAuthIssuerImpl;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

@Path("/coe")
public class CoeController {

    private OAuthIssuerImpl oauthIssuerImpl = new OAuthIssuerImpl(new MD5Generator());

    @Autowired
    private SecurityService securityService;
    @Autowired
    private XiaCoreProperties xiaCoreProperties;
    @Autowired
    private XiaExpiresProperties xiaExpiresProperties;
    @Context
    private HttpServletRequest request;
    @Context
    private HttpServletResponse response;
    @Autowired
    private CoeProperties coeProperties;

    /**
     * coe跳转fm
     *
     * @return
     */
    @GET
    @Path("demoLogin")
    @Produces(MediaType.APPLICATION_JSON)
    public void jump(@QueryParam("token") String tokenString, @QueryParam("toUrl") String toUrl) {
        String accessToken = null;
        try{
            if(StringUtils.isNotBlank(tokenString) && tokenString.indexOf(" ")>-1){
                tokenString = tokenString.replaceAll(" ","+");
            }
            String loginName = new String(EncryptUtils.decrypt(tokenString.getBytes()));
            //本系统处理用户登录
            accessToken = oauthIssuerImpl.accessToken();
            final String refreshToken = oauthIssuerImpl.refreshToken();
            securityService.cacheToken(null, loginName, accessToken, refreshToken, xiaExpiresProperties.getSeesionExpire().intValue(),
                    securityService.getClientIdentifier(request), null, null);
            StringBuilder sb = new StringBuilder();
            sb.append(coeProperties.getDomain());
            sb.append(xiaCoreProperties.getBaseStaticPath());
            boolean authPass = false;
            if (accessToken == null) {
                sb.append("/login");
            } else {
                authPass = true;
                sb.append("/main/index?access_token=").append(accessToken).append("#__aurl=%2Fchart%2Fdashboard%2Findex");
            }
            if (authPass && StringUtils.isNotBlank(toUrl)) {
                toUrl = toUrl.replace("[accessToken]", accessToken);
                response.sendRedirect(toUrl);
            } else {
                response.sendRedirect(sb.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
