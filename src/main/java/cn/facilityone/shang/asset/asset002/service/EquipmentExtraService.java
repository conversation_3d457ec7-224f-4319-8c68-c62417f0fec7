package cn.facilityone.shang.asset.asset002.service;

import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.asset.EquipmentExtra;

import java.util.List;

/**
 * Created by charles.chen on 2015/4/29.
 */
public interface EquipmentExtraService {
    EquipmentExtra create(EquipmentExtra equipmentExtra);

    void update(EquipmentExtra equipmentExtra);

    EquipmentExtra findOne(Long id);

    void delete(Long id);

    void bindExtraForEqu(Equipment equ,List<EquipmentExtra> equipmentExtraList);

}
