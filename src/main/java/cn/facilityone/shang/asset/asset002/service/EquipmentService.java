package cn.facilityone.shang.asset.asset002.service;

import cn.facilityone.shang.asset.asset002.dto.AllEquipmentDto;
import cn.facilityone.shang.asset.asset002.dto.EquipmentDTO;
import cn.facilityone.shang.asset.asset002.dto.EquipmentForContractPageDTO;
import cn.facilityone.shang.asset.asset002.dto.EquipmentDTO;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.organize.Employee;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * Created by charles.chen on 2015/4/29.
 */
public interface EquipmentService {
    Equipment create(HttpServletRequest request,EquipmentDTO equipmentDTO);

    Equipment add(HttpServletRequest request, EquipmentDTO equipmentDTO);

    void update(HttpServletRequest request,EquipmentDTO equipmentDTO);

    Equipment findOne(Long id);

    void delete(Long id);

    /**
     * 根据ID获得设备
     * @param  Long id
     * @return EquipmentDTO
     */
    EquipmentDTO findEquipmentDtoById(Long id);

    EquipmentDTO findEquipmentDetailById(Long id);

    /**
     * 二维码导出
     * @param dataTable request
     * @param response zip
     */
    ByteArrayOutputStream outputQRCode(DataTableRequest dataTableRequest,HttpServletRequest request,HttpServletResponse response);

    /**
     * 二维码导出
     * @param dataTable request
     * @param response zip
     */
    ByteArrayOutputStream exportFile(DataTableRequest dataTableRequest);

    EquipmentForContractPageDTO findEquipmentDtoForContractExpire(DataTableRequest dataTableRequest,Pageable pageable);

    Page<Equipment> findEquipmentPage(DataTableRequest request, Pageable page);

    String generateContentByEquipment(Equipment equipment,String uuid);

    String getQrCodeContentByEquipment(Equipment equipment);

    Equipment createQRPicture(HttpServletRequest request, Equipment equipment);

    boolean hasFileAuth();

    List<Equipment> getAllEqsWithOutPageable(DataTableRequest request);

    int isExitsSpotAndPm(Long eqId);
}
