package cn.facilityone.shang.sign.sign004.service;

import cn.facilityone.shang.entity.sign.SignMode;
import cn.facilityone.shang.sign.sign004.dto.SignModeDTO;

import java.util.List;

/**
 * Created by tina.cao on 2016/9/26.
 */
public interface SignModeSettingService {

    SignMode getRange();

    void updateRange(Integer range);

    List<SignModeDTO> getSignedModeTable(String type);

    boolean checkExist(SignModeDTO signModeDTO);

    void editLoc(SignModeDTO signModeDTO);

    void addLoc(SignModeDTO signModeDTO);

    void editWifiBluetooth(SignModeDTO signModeDTO);

    void addWifiBluetooth(SignModeDTO signModeDTO);

    String changeState(Long id);

    void delete(Long id);
}