package cn.facilityone.shang.stock.stock006.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.stock.stock006.dto.MinimumReportDTO;
import cn.facilityone.shang.stock.stock006.dto.MinimumReportSearchRequest;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 8/13/2015
 */
public interface InventoryMinimumReportService {

    Page<MinimumReportDTO> findAllInventoriesInPage(MinimumReportSearchRequest request);

    List<MinimumReportDTO> toDTO(List<Inventory> inventories);

    int findAllInventoriesCount(MinimumReportSearchRequest request);

    void filterByLastProvider(List<MinimumReportDTO> dtos, MinimumReportSearchRequest request);

    List<MinimumReportDTO> findAllMinimumDTOs(MinimumReportSearchRequest request);

    void checkMinimum(Long projectId);
}
