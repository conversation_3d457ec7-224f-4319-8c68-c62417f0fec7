package cn.facilityone.shang.stock.stock006.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * @author：Kid date:2017/12/19
 */
public class MessageClassificationDto {
    private List<StockMessageDto> emailStockMessageDto;
    private List<StockMessageDto> siteStockMessageDto;
    private List<StockMessageDto> SMSStockMessageDto;
    private List<StockMessageDto> mpushStockMessageDto;

    public MessageClassificationDto() {
        this.emailStockMessageDto = new ArrayList<>();
        this.siteStockMessageDto = new ArrayList<>();
        this.SMSStockMessageDto = new ArrayList<>();
        this.mpushStockMessageDto = new ArrayList<>();
    }

    public List<StockMessageDto> getEmailStockMessageDto() {
        return emailStockMessageDto;
    }

    public void setEmailStockMessageDto(List<StockMessageDto> emailStockMessageDto) {
        this.emailStockMessageDto = emailStockMessageDto;
    }

    public List<StockMessageDto> getSiteStockMessageDto() {
        return siteStockMessageDto;
    }

    public void setSiteStockMessageDto(List<StockMessageDto> siteStockMessageDto) {
        this.siteStockMessageDto = siteStockMessageDto;
    }

    public List<StockMessageDto> getSMSStockMessageDto() {
        return SMSStockMessageDto;
    }

    public void setSMSStockMessageDto(List<StockMessageDto> SMSStockMessageDto) {
        this.SMSStockMessageDto = SMSStockMessageDto;
    }

    public List<StockMessageDto> getMpushStockMessageDto() {
        return mpushStockMessageDto;
    }

    public void setMpushStockMessageDto(List<StockMessageDto> mpushStockMessageDto) {
        this.mpushStockMessageDto = mpushStockMessageDto;
    }
}
