package cn.facilityone.shang.stock.stock006.dto;

import java.text.DecimalFormat;

/**
 * @Author: wayne.fu
 * @Date: 8/13/2015
 */
public class MinimumReportDTO {
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.##");
    private String warehouseName;
    private String materialCode;
    private String materialName;
    private String displayRack;
    private String brand;
    private String model;
    private String unit;
    private Double amount;
    private Double totalInvAmount;
    private Double minAmount;
    private Long inventoryId;
    private Double price;
    private Double lastPrice;
    private Double avgPrice;
    private String lastProvider;

    public MinimumReportDTO() {
    }

    public MinimumReportDTO(Double amount, Double avgPrice, String brand, long inventoryId,
                            Double lastPrice, String lastProvider, String materialName, String materialCode,
                            Double minAmount, String model, Double price, String unit, String warehouseName, Double totalInvAmount,String displayRack) {
        this.amount = amount;
        this.avgPrice = avgPrice;
        this.brand = brand;
        this.inventoryId = inventoryId;
        this.lastPrice = lastPrice;
        this.lastProvider = lastProvider;
        this.materialName = materialName;
        this.minAmount = minAmount;
        this.model = model;
        this.price = price;
        this.unit = unit;
        this.warehouseName = warehouseName;
        this.materialCode = materialCode;
        this.totalInvAmount = totalInvAmount;
        this.displayRack = displayRack;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialCode() {

        return materialCode;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(Double avgPrice) {
        this.avgPrice = avgPrice;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public Double getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(Double lastPrice) {
        this.lastPrice = lastPrice;
    }

    public String getLastProvider() {
        return lastProvider;
    }

    public void setLastProvider(String lastProvider) {
        this.lastProvider = lastProvider;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public Double getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(Double minAmount) {
        this.minAmount = minAmount;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Double getTotalInvAmount() {
        return totalInvAmount;
    }

    public void setTotalInvAmount(Double totalInvAmount) {
        this.totalInvAmount = totalInvAmount;
    }

    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }
}
