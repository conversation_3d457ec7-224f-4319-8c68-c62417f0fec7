package cn.facilityone.shang.stock.stock006.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.configuration.properties.CompanyProperties;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.stock.stock006.dto.MinimumReportDTO;
import cn.facilityone.shang.stock.stock006.dto.MinimumReportSearchRequest;
import cn.facilityone.shang.stock.stock006.service.InventoryMinimumReportService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.transfer.core.Exporter;
import cn.facilityone.xia.transfer.core.data.DTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wayne.fu
 *
 * @version 1.0
 * @since 2015/8/13
 */

@Path("/stock006")
public class MinimumReportResource {

    private static final String TEMPLATE_User_PATH = "/business/stockV2/stock006-minimum-report.ftl";

    @Autowired
    private InventoryMinimumReportService inventoryMinimumReportService;
    @Autowired
    @Qualifier("xlsxExporter")
    private Exporter exporter;
    
    @Autowired
    private CompanyProperties companyProperties;

    @GET
    @Template(name = TEMPLATE_User_PATH)
    public Map<String, Object> init() {
        return new HashMap<>();
    }


    @POST
    @Path("inventories/minimum/table")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getMinimumTable(MinimumReportSearchRequest request) {
        Page<MinimumReportDTO> inventories = inventoryMinimumReportService.findAllInventoriesInPage(request);
        int totalCount = inventoryMinimumReportService.findAllInventoriesCount(request);
        return new DataTableResponse(inventories.getContent(), totalCount, request.getPageNumber(), request.getDraw());
    }

    @POST
    @Path("inventories/minimum/export")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response export(String params) {
        MinimumReportSearchRequest request = null;

        if (StringUtils.isNotBlank(params)) {
            try {
                request = new ObjectMapper().readValue(params, MinimumReportSearchRequest.class);
            } catch (Exception e) {
                return Response.ok(new Result(Response.Status.BAD_REQUEST.getStatusCode(), "IO流错误!", "")).build();
            }
        }
        List<MinimumReportDTO> dtos = inventoryMinimumReportService.findAllMinimumDTOs(request);
        LinkedHashMap<String, String> title = new LinkedHashMap<>();
        title.put("warehouseName", XiaMesssageResource.getMessage("InventoryManagementActivity.warehouseName"));
        title.put("materialName", XiaMesssageResource.getMessage("InventoryManagementActivity.materialName"));
        title.put("displayRack", XiaMesssageResource.getMessage("Inventory.displayRack"));
        title.put("brand", XiaMesssageResource.getMessage("Material.brand"));
        title.put("model", XiaMesssageResource.getMessage("Material.model"));
        title.put("unit", XiaMesssageResource.getMessage("Material.unit"));
        title.put("price", XiaMesssageResource.getMessage("Material.price"));
        title.put("avgPrice", XiaMesssageResource.getMessage("Inventory.avgPrice"));
        title.put("lastPrice", XiaMesssageResource.getMessage("Inventory.lastPrice"));
        title.put("lastProvider", XiaMesssageResource.getMessage("Inventory.lastProvider"));
        title.put("amount", XiaMesssageResource.getMessage("Inventory.amount"));
        title.put("minAmount", XiaMesssageResource.getMessage("Inventory.minAmount"));

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("companyName", companyProperties.getName());
        DTO dto = new DTO(map);
        dto.addTitleAndData(title, dtos);
        ByteArrayOutputStream out = (ByteArrayOutputStream) exporter
                .export(dto);
        return Response.ok(
                new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.success.export"),
                        FileUtil.exportFile(out, FileUtil.FILE_TYPE_EXCELX))).build();
    }
}
