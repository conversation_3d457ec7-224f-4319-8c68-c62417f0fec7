package cn.facilityone.shang.stock.stock006.dto;

import cn.facilityone.shang.entity.inventory.Inventory;

import java.util.List;
import java.util.Set;

/**
 * @author：Kid date:2017/12/18
 */
public class StockMessageDto {
    private  String  warehouseName;
    private  List<Inventory> inventoryList;
    private  Set<Long> emIdList;
    private  String msgTypes;
    private  Long warehouseId;
    private  Long size;

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public List<Inventory> getInventoryList() {
        return inventoryList;
    }

    public void setInventoryList(List<Inventory> inventoryList) {
        this.inventoryList = inventoryList;
    }

    public Set<Long> getEmIdList() {
        return emIdList;
    }

    public void setEmIdList(Set<Long> emIdList) {
        this.emIdList = emIdList;
    }

    public String getMsgTypes() {
        return msgTypes;
    }

    public void setMsgTypes(String msgTypes) {
        this.msgTypes = msgTypes;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }
}
