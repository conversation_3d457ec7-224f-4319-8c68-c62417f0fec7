package cn.facilityone.shang.stock.stock006.service;

import cn.facilityone.shang.asset.asset002.repository.ProviderRepository;
import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.component.message.MessageSenderTool;
import cn.facilityone.shang.common.component.message.template.MessageStockCheckTemplate;
import cn.facilityone.shang.common.component.message.template.MessageStockReserveTemplate;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.common.staticmetamodel.MobilePush_;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.entity.asset.Provider;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.Material;
import cn.facilityone.shang.entity.inventory.MaterialBatch;
import cn.facilityone.shang.entity.inventory.Warehouse;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.projects.pro002.service.ProjectService;
import cn.facilityone.shang.stock.common.repository.InventoryRepository;
import cn.facilityone.shang.stock.common.repository.MaterialBatchRepository;
import cn.facilityone.shang.stock.common.repository.WarehouseRepository;
import cn.facilityone.shang.stock.common.staticmetamodel.InventoryMinimum_;
import cn.facilityone.shang.stock.common.staticmetamodel.Inventory_;
import cn.facilityone.shang.stock.stock006.dto.MessageClassificationDto;
import cn.facilityone.shang.stock.stock006.dto.MinimumReportDTO;
import cn.facilityone.shang.stock.stock006.dto.MinimumReportSearchRequest;
import cn.facilityone.shang.stock.stock006.dto.StockMessageDto;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.message.common.MessageDocument;
import cn.facilityone.xia.message.common.MessageType;
import cn.facilityone.xia.message.service.MessageMobilePushSender;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.security.model.Project;
import cn.facilityone.xia.transfer.ex.data.RowData;
import cn.facilityone.xia.transfer.ex.xlsx.SXSSFExporter;
import cn.facilityone.xia.transfer.ex.xlsx.XlsBigExporter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.ws.rs.core.Context;
import java.io.File;
import java.text.DecimalFormat;
import java.util.*;

import static cn.facilityone.xia.message.common.MessageType.*;

/**
 * @Author: wayne.fu
 * @Date: 8/13/2015
 */
@Service
public class InventoryMinimumReportServiceImpl implements InventoryMinimumReportService {

    private static final Logger log = LoggerFactory.getLogger(InventoryMinimumReportServiceImpl.class);
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.##");

    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private ProviderRepository providerRepository;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private MaterialBatchRepository materialBatchRepository;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private MessageSenderTool messageSenderTool;
    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private UploadFileService fileService;

    @Override
    @XiaTransactional(readOnly = true)
    public Page<MinimumReportDTO> findAllInventoriesInPage(MinimumReportSearchRequest request) {
        String sql = createSQL(request);
        TypedQuery<MinimumReportDTO> result = entityManager.createQuery(sql, MinimumReportDTO.class);

        Double factor = request.getFactor();
//        DecimalFormat df = new DecimalFormat("#");
//        Integer factorInt = Integer.parseInt(df.format(factor * 10));

        result.setParameter("factor", factor*10);
        Long em = commonUserService.findCurrentEmployeeId();
        Long emId = 0L;
        if(em!=null){
            emId = em;
        }
        result.setParameter("userId", emId);
       
        //多项目支持
        result.setParameter("projId", ProjectContext.getCurrentProject());
        List<MinimumReportDTO> inventoryList = result.setFirstResult(request.getOffset()).setMaxResults(request.getPageSize()).getResultList();
        Page<MinimumReportDTO> inventories = new PageImpl<>(inventoryList);

        return inventories;

    }

    private String createSQL(MinimumReportSearchRequest request) {
        StringBuilder commonSQL = new StringBuilder(650);
        commonSQL.append(" SELECT DISTINCT new cn.facilityone.shang.stock.stock006.dto.MinimumReportDTO(i.amount,round(i.avgPrice,2),m.brand,i.id,mb.price,v.name,m.name,m.code ");
        commonSQL.append(" ,i.minAmount,m.model,m.price,m.unit,w.name, i.totalInvAmount,i.displayRack) FROM ");
        commonSQL.append(" Inventory i, Material m, MaterialBatch mb, Provider v, Warehouse w JOIN w.employees wes ");
        commonSQL.append(" WHERE i.warehouse.id = w.id AND i.material.id = m.id ");
        commonSQL.append(" AND mb.id = (SELECT max(mb2.id) FROM MaterialBatch as mb2 ");
        commonSQL.append("  WHERE mb2.inventory.id = i.id) ");
        commonSQL.append(" AND i.totalInvAmount*10 <= i.minAmount * :factor AND mb.provider.id = v.id ");
        commonSQL.append(" AND wes.id = :userId AND i.deleted = 0");
        //多项目支持
        commonSQL.append(" AND i.project = :projId ");
        if (CollectionUtils.isNotEmpty(request.getColumns())) {
            createColumnSQL(commonSQL, request);
        }
        return commonSQL.toString();
    }

    private void createColumnSQL(StringBuilder commonSQL, MinimumReportSearchRequest request) {
        for (DataTableColumn column : request.getColumns()) {
            //illegal columns
            if (StringUtils.isBlank(column.getName())) {
                log.debug("column name is blank!");
                continue;
            }

            try {
                InventoryMinimum_.SQL_TABLE columnProperties = InventoryMinimum_.SQL_TABLE.valueOf(column.getName());
                String[] properties = columnProperties.getProperties();

                if (ArrayUtils.isEmpty(properties)) {
                    log.debug("properties in Table is empty!");
                    continue;
                }

                if (properties.length == 2) {
                    if (StringUtils.isBlank(column.getSearchText())) {
                        continue;
                    } else {
                        commonSQL.append(" AND ");
                        commonSQL.append(properties[0] + "." + properties[1]);
                        commonSQL.append(" LIKE ");
                        commonSQL.append(" '" + dataTableService.buildLikeText(column.getSearchText()) + "' ");
                    }
                } else {
                    log.debug("properties length is invalid");
                    continue;
                }
            } catch (IllegalArgumentException e) {
                log.error("enum type has no constant with the specified name:{}", column.getName());
            }

        }
    }

    private CriteriaQuery getQuery(MinimumReportSearchRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Inventory> query = cb.createQuery(Inventory.class);
        Root<Inventory> root = query.from(Inventory.class);

        Double factor = request.getFactor();
        DecimalFormat df = new DecimalFormat("#");
        Integer factorInt = Integer.parseInt(df.format(factor * 10));

        From fromWarehouse = (From) root.fetch(Inventory_.WAREHOUSE, JoinType.LEFT);
        From fromMaterial = (From) root.fetch(Inventory_.MATERIAL, JoinType.LEFT);
        From fromBatch = (From) root.fetch(Inventory_.MATERIAL_BATCH, JoinType.LEFT);
        //from map
        Map<String, From> fromMap = new HashMap<>();
        fromMap.put(InventoryMinimum_.FROM_WAREHOUSE, fromWarehouse);
        fromMap.put(InventoryMinimum_.FROM_MATERIAL, fromMaterial);
        fromMap.put(InventoryMinimum_.FROM_MATERIAL_BATCH, fromBatch);
        List<Predicate> predicates = new ArrayList<>();
        //default one
        //amount <= min_amount * factor
        predicates.add(cb.lessThanOrEqualTo(cb.prod(root.get(InventoryMinimum_.AMOUNT).as(Integer.class), 10), cb.prod(root.get(InventoryMinimum_.MIN_AMOUNT).as(Integer.class), factorInt)));

        List<DataTableColumn> columnL = request.getColumns();
        if (CollectionUtils.isNotEmpty(columnL)) {
            List<Predicate> lps = buildColumnSearchForTable(fromMap, cb, request);
            predicates.addAll(lps);
        }


        query.distinct(true);
        query.where(predicates.toArray(new Predicate[predicates.size()]));
        query.orderBy(cb.desc(root.get(Inventory_.ID)));

        return query;
    }


    /**
     * filter by last Provider
     *
     * @param dto
     * @param request
     */
    private boolean filterByLastProvider(MinimumReportDTO dto, MinimumReportSearchRequest request) {
        String searchCondition = "";
        if (CollectionUtils.isNotEmpty(request.getColumns())) {
            for (DataTableColumn column : request.getColumns()) {
                if (InventoryMinimum_.TABLE.lastProvider.name().equals(column.getName())) {
                    if (StringUtils.isNotBlank(column.getSearchText())) {
                        searchCondition = column.getSearchText();
                        break;
                    }
                }
            }

            if (StringUtils.isBlank(searchCondition)) {
                return true;
            }


            String provider = dto.getLastProvider();
            if (StringUtils.isNotBlank(provider) && provider.toUpperCase().contains(searchCondition.toUpperCase())) {
                return true;
            } else {
                return false;
            }
        }
        return true;

    }

    /**
     * change inventory list to dto list
     *
     * @param inventories
     * @return
     */
    @Override
    public List<MinimumReportDTO> toDTO(List<Inventory> inventories) {
        if (CollectionUtils.isEmpty(inventories)) {
            return Collections.emptyList();
        }

        List<MinimumReportDTO> dtos = new ArrayList<>();
        for (Inventory inventory : inventories) {
            dtos.add(toDTO(inventory));
        }
        return dtos;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public int findAllInventoriesCount(MinimumReportSearchRequest request) {
        List<MinimumReportDTO> dtos = findAllMinimumDTOs(request);
        return dtos == null ? 0 : dtos.size();
    }

    @Override
    public void filterByLastProvider(List<MinimumReportDTO> dtos, MinimumReportSearchRequest request) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }

        Iterator<MinimumReportDTO> iterator = dtos.iterator();
        while (iterator.hasNext()) {
            MinimumReportDTO dto = iterator.next();
            if (!filterByLastProvider(dto, request)) {
                iterator.remove();
            }
        }
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<MinimumReportDTO> findAllMinimumDTOs(MinimumReportSearchRequest request) {
        String sql = createSQL(request);
        TypedQuery<MinimumReportDTO> result = entityManager.createQuery(sql, MinimumReportDTO.class);

        Double factor = request.getFactor();
//        DecimalFormat df = new DecimalFormat("#");
//        Integer factorInt = Integer.parseInt(df.format(factor * 10));

        result.setParameter("factor", factor*10);
        
        Long em = commonUserService.findCurrentEmployeeId();
        Long emId = 0L;
        if(em!=null){
            emId = em;
        }
        result.setParameter("userId", emId);
        //多项目支持
        result.setParameter("projId", ProjectContext.getCurrentProject());
        return result.getResultList();
    }

    @Override
    @XiaTransactional(readOnly = true)
    public void checkMinimum(Long projectId) {
        List<StockMessageDto> stockMessageDtoArrayList = new ArrayList<>();
        List<Warehouse> warehouseList = warehouseRepository.findWarehouseByProjectHardly(projectId);
        Project project=projectService.findOne(projectId);

        //将仓库内的数据汇总
        for (Warehouse warehouse : warehouseList) {
            try {
                List<Inventory> inventoryList = warehouse.getInventories();
                //如果仓库内没有库存或者仓库没有勾选通知方式，则continue
                if (CollectionUtils.isEmpty(inventoryList) || StringUtils.isEmpty(warehouse.getMsgType())) {
                    continue;
                }
                List<Employee> warehouseAdmin = warehouse.getEmployees();
                Set<Long> warehouseAdminIds = new HashSet<>();
                for (Employee em : warehouseAdmin) {
                    warehouseAdminIds.add(em.getId());
                }

                List<Inventory> inventories = new ArrayList<>();

                for (Inventory inv : inventoryList) {
                    if (inv.isDeleted()) {
                        continue;
                    }
                    Double miniMum = inv.getMinAmount();
                    Double invAmount = inv.getTotalInvAmount();
                    Double totalAmount = inv.getTotalAmount();
                    if (miniMum == null || invAmount == null || totalAmount == null) {
                        continue;
                    }
                    if (miniMum >= invAmount && totalAmount > 0) {
                        inventories.add(inv);
                    }
                }
                //如果所在仓库有需要最小量库存，才需要添加
                if (inventories.size() > 0) {
                    //对所在仓库进行属性设置，为发送消息做准备
                    StockMessageDto stockMessageDto = new StockMessageDto();
                    stockMessageDto.setWarehouseId(warehouse.getId());
                    stockMessageDto.setEmIdList(warehouseAdminIds);
                    stockMessageDto.setWarehouseName(warehouse.getName());
                    stockMessageDto.setSize((long) inventories.size());
                    stockMessageDto.setMsgTypes(warehouse.getMsgType());
                    stockMessageDto.setInventoryList(inventories);
                    stockMessageDtoArrayList.add(stockMessageDto);
                }
            }catch (Exception e){
                log.error("stock job error ,warehouseId={},",warehouse.getId());
            }
        }
        if(CollectionUtils.isEmpty(stockMessageDtoArrayList)){
            return;
        }
        //查询去重
        List<Employee> employeeList=warehouseRepository.findWarehouseEmployeeByProjectIdHardly(projectId);
        for(Employee employee:employeeList){
            MessageClassificationDto messageClassificationDto=new MessageClassificationDto();
            for(StockMessageDto stockMessageDto:stockMessageDtoArrayList){
                Set<Long> emIdSet=stockMessageDto.getEmIdList();
                if(emIdSet.contains(employee.getId())){
                    String msgType=stockMessageDto.getMsgTypes();
                    if(msgType.contains(MPUSH)){
                        messageClassificationDto.getMpushStockMessageDto().add(stockMessageDto);
                    }
                    if(msgType.contains(SMS)){
                        messageClassificationDto.getSMSStockMessageDto().add(stockMessageDto);
                    }
                    if(msgType.contains(EMAIL)){
                        messageClassificationDto.getEmailStockMessageDto().add(stockMessageDto);
                    }
                    if(msgType.contains(SITE)){
                        messageClassificationDto.getSiteStockMessageDto().add(stockMessageDto);
                    }
                }
            }
            this.sendInventoryCheckMessage(employee,messageClassificationDto,projectId,project.getName());
        }
    }

    /**
     * 库存数量小于最小量时发送通知
     *
     */
    private void sendInventoryCheckMessage(Employee employee,MessageClassificationDto messageClassificationDto, Long projectId,String projectName) {
        // 发送移动推送
        List<StockMessageDto> emailStockMessage=messageClassificationDto.getEmailStockMessageDto();
        List<StockMessageDto> mPushStockMessage=messageClassificationDto.getMpushStockMessageDto();
        List<StockMessageDto> siteStockMessage=messageClassificationDto.getSiteStockMessageDto();
        List<StockMessageDto> sMsStockMessage=messageClassificationDto.getSMSStockMessageDto();

        List<Employee> employeeList=new ArrayList<>();
        employeeList.add(employee);

        //邮箱
        if (CollectionUtils.isNotEmpty(emailStockMessage)) {
            String date=DateUtil.formatDateYYYYMMDD(new Date());
            String name=XiaMesssageResource.getMessage("Inventory.Minimum.report")+date+".xlsx";
            XiaFile xiaFile=fileService.getEmptyXlsXiaFile(name);
            File file=new File(fileService.getFilePath(xiaFile)) ;
            buildFileEx(emailStockMessage,file);

            MessageDocument messageDocument=new MessageDocument();
            messageDocument.setFullPath(file.getAbsolutePath());
            messageDocument.setName(xiaFile.getName());

            List<MessageDocument> messageDocumentList=new ArrayList<>();
            messageDocumentList.add(messageDocument);

            messageSenderTool.send(MessageStockCheckTemplate.CODE_STOCK_CHECK,
                    MessageStockCheckTemplate.buildData(projectName,emailStockMessage),null,
                    employeeList,projectId,MessageType.EMAIL,messageDocumentList);
        }
        //移动推送
        if (CollectionUtils.isNotEmpty(mPushStockMessage)) {
            Map<String,Map<String,Object>> typeData = new HashMap<>(1);
            Map<String, Object> params = new HashMap<>(3);
            params.put(MobilePush_.TYPE, MobilePush_.STOCK);
            params.put(MobilePush_.PROJECT_ID, projectId);
            if(mPushStockMessage.size()>1){
                params.put(MobilePush_.WAREHOUSE_ID, 0L);
            }else {
                Long  wareHouseId=mPushStockMessage.get(0).getWarehouseId();
                params.put(MobilePush_.WAREHOUSE_ID, wareHouseId);
            }
            typeData.put(MPUSH, params);
            messageSenderTool.send(MessageStockCheckTemplate.CODE_STOCK_CHECK,
                    MessageStockCheckTemplate.buildData(projectName,mPushStockMessage),
                    typeData,employeeList,projectId,MessageType.MPUSH);
        }
        //站内信
        if (CollectionUtils.isNotEmpty(siteStockMessage)) {
            messageSenderTool.send(MessageStockCheckTemplate.CODE_STOCK_CHECK,
                    MessageStockCheckTemplate.buildData(projectName,siteStockMessage),
                    projectId,MessageType.SITE,employee);
        }
        //短信
        if (CollectionUtils.isNotEmpty(sMsStockMessage)) {
            messageSenderTool.send(MessageStockCheckTemplate.CODE_STOCK_CHECK,
                    MessageStockCheckTemplate.buildData(projectName,sMsStockMessage),
                    projectId,MessageType.SMS,employee);
        }
    }

    private void buildFileEx(List<StockMessageDto> stockMessageDtoList,File file) {
        SXSSFExporter sxssfExporter = new SXSSFExporter(1000, file );

        List<Object> titles = new ArrayList<>();
        titles.add(XiaMesssageResource.getMessage("InventoryManagementActivity.materialCode"));
        titles.add( XiaMesssageResource.getMessage("InventoryManagementActivity.materialName"));
        titles.add( XiaMesssageResource.getMessage("Material.brand"));
        titles.add( XiaMesssageResource.getMessage("Material.model"));
        titles.add( XiaMesssageResource.getMessage("Inventory.amount"));
        titles.add( XiaMesssageResource.getMessage("Inventory.minAmount"));

        for(StockMessageDto stockMessageDto:stockMessageDtoList){
            Sheet sheet = sxssfExporter.createSheet(stockMessageDto.getWarehouseName());
            RowData titleRow = new RowData();
            titleRow.setValues(titles);
            List<RowData> rows =  new ArrayList<>();
            rows.add(titleRow);

            if(CollectionUtils.isNotEmpty(stockMessageDto.getInventoryList())){
                for(Inventory inventory:stockMessageDto.getInventoryList()){
                    Material material=inventory.getMaterial();
                    if(material==null){
                        material=new Material();
                    }

                    RowData dataRow = new RowData();
                    List<Object> date = new ArrayList<>();
                    date.add(material.getCode());
                    date.add(material.getName());
                    date.add(material.getBrand());
                    date.add(material.getModel());
                    date.add(inventory.getTotalInvAmount());
                    date.add(inventory.getMinAmount());
                    dataRow.setValues(date);
                    rows.add(dataRow);
                }
            }
            sxssfExporter.buildRows(rows, sheet);
        }
        sxssfExporter.close();
    }
    /**
     * change single one to dto
     *
     * @param inventory
     * @return
     */
    private MinimumReportDTO toDTO(Inventory inventory) {
        if (inventory == null) {
            return null;
        }

        MinimumReportDTO dto = new MinimumReportDTO();
        dto.setInventoryId(inventory.getId());
        dto.setWarehouseName(inventory.getWarehouse() == null ? null : inventory.getWarehouse().getName());
        Material material = inventory.getMaterial();
        if (material != null) {
            dto.setModel(material.getModel());
            dto.setMaterialName(material.getName());
            dto.setBrand(material.getBrand());
            dto.setUnit(material.getUnit());
            dto.setPrice(material.getPrice());
        }
        dto.setAmount(inventory.getAmount());
        dto.setAvgPrice(Double.parseDouble(DECIMAL_FORMAT.format(inventory.getAvgPrice())));
        dto.setMinAmount(inventory.getMinAmount());

        List<MaterialBatch> batches = materialBatchRepository.findWithProviderByInventoryId(inventory.getId());
        if (CollectionUtils.isEmpty(batches)) {
            dto.setLastPrice(0.0d);
            dto.setLastProvider("");
        } else {
            Collections.sort(batches, new Comparator<MaterialBatch>() {
                @Override
                public int compare(MaterialBatch o1, MaterialBatch o2) {
                    if (o1 == null || o2 == null) {
                        return 0;
                    }
                    if (o1.getCreatedDate().after(o2.getCreatedDate())) {
                        return 1;
                    }
                    if (o1.getCreatedDate().before(o2.getCreatedDate())) {
                        return -1;
                    }
                    return 0;
                }
            });


            Provider provider = batches.get(0).getProvider();
            dto.setLastProvider(provider == null ? "" : provider.getName());
            dto.setLastPrice(batches.get(0).getPrice());
        }

        return dto;
    }

    /**
     * build predicate for table
     *
     * @param fromMap
     * @param cb
     * @param request
     * @return
     */
    private List<Predicate> buildColumnSearchForTable(Map<String, From> fromMap, CriteriaBuilder cb, DataTableRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getColumns())) {
            return Collections.emptyList();
        }

        List<Predicate> results = new ArrayList<>();
        //all columns
        for (DataTableColumn column : request.getColumns()) {
            //illegal columns
            if (StringUtils.isBlank(column.getName())) {
                log.debug("column name is blank!");
                continue;
            }
            try {
                InventoryMinimum_.TABLE columnProperties = InventoryMinimum_.TABLE.valueOf(column.getName());
                String[] properties = columnProperties.getProperties();

                if (ArrayUtils.isEmpty(properties)) {
                    log.debug("properties in Table is empty!");
                    continue;
                }

                //only support x.y
                if (properties.length == 2) {
                    if (StringUtils.isBlank(column.getSearchText())) {
                        continue;
                    } else {
                        From from = fromMap.get(properties[0]);
                        Predicate predicate = cb.like(from.get(properties[1]), dataTableService.buildLikeText(column.getSearchText()));
                        results.add(predicate);
                    }
                } else {
                    log.debug("properties length is invalid");
                    continue;
                }
            } catch (IllegalArgumentException e) {
                log.error("enum type has no constant with the specified name:{}", column.getName());
            }
        }

        return results;
    }
}
