package cn.facilityone.shang.stock.stock005.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 8/12/2015
 */
public class InventoryVerificationResultDTO {
    private List<String> header;
    private List<ColumnRecord> records;
    private List<ColumnRecord> unexpectedRecords;


    public class ColumnRecord {
        public static final int TYPE_BALANCE = 0;
        public static final int TYPE_SURPLUS = 1;
        public static final int TYPE_LOSS = -1;

        private List<String> data;
        private int type;

        public List<String> getData() {
            return data;
        }

        public void setData(List<String> data) {
            this.data = data;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public ColumnRecord() {
        }
    }

    public List<String> getHeader() {
        return header;
    }

    public void setHeader(List<String> header) {
        this.header = header;
    }

    public List<ColumnRecord> getRecords() {
        return records;
    }

    public void setRecords(List<ColumnRecord> records) {
        this.records = records;
    }

    public List<ColumnRecord> getUnexpectedRecords() {
        if (unexpectedRecords == null) {
            this.unexpectedRecords = new ArrayList<>();
        }
        return unexpectedRecords;
    }

    public void setUnexpectedRecords(List<ColumnRecord> unexpectedRecords) {
        this.unexpectedRecords = unexpectedRecords;
    }
}
