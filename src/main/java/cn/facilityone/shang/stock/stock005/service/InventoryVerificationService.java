package cn.facilityone.shang.stock.stock005.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.Warehouse;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.stock005.dto.InventoryExportDTO;
import cn.facilityone.shang.stock.stock005.dto.InventoryVerificationDTO;
import cn.facilityone.shang.stock.stock005.dto.InventoryVerificationDetailDTO;
import cn.facilityone.shang.stock.stock005.dto.InventoryVerificationRequestDTO;
import cn.facilityone.xia.core.common.Result;
import org.springframework.data.domain.Page;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author: wayne.fu
 * @Date: 8/10/2015
 */
public interface InventoryVerificationService {
    Page<Inventory> findInPage(DataTableRequest request);

    List<InventoryVerificationDTO> toDTO(List<Inventory> inventories);

    Page<Inventory> findAllInventories(DataTableRequest request);

    Page<InventoryExportDTO> findExportInventories(DataTableRequest request);

    int getTotalCount(DataTableRequest request);

    boolean checkUploadFileData(XiaFile file);

    Map<String,Object> initImportResultMap(XiaFile file);

    void adjustAmount(Collection<InventoryVerificationDTO> request);

    void adjustAmountV2(Collection<InventoryVerificationRequestDTO> request, Employee loginEm);

    InventoryVerificationDetailDTO getDetails(Long id);

    Result validateUploadFileData(XiaFile file);
}
