package cn.facilityone.shang.stock.stock005.dto;

import cn.facilityone.shang.entity.inventory.MaterialBatch;

import java.util.List;

/**
 * Created by lip.gu on 2016/11/25.
 */
public class InventoryVerificationDetailDTO {
    private String warehouseName;
    private String displayRack;
    private String materialCode;
    private String materialName;
    private String brand;
    private String model;
    private String unit;
    private Double amount;
    private Double lockNum;
    private Double minAmount;
    private List<Long> materialBatcheIds;
    private Long inventoryId;

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getLockNum() {
        return lockNum;
    }

    public void setLockNum(Double lockNum) {
        this.lockNum = lockNum;
    }

    public Double getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(Double minAmount) {
        this.minAmount = minAmount;
    }

    public List<Long> getMaterialBatcheIds() {
        return materialBatcheIds;
    }

    public void setMaterialBatcheIds(List<Long> materialBatcheIds) {
        this.materialBatcheIds = materialBatcheIds;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }
}
