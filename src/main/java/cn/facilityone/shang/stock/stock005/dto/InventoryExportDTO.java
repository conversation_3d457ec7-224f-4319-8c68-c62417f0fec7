package cn.facilityone.shang.stock.stock005.dto;

/**
 * Created by lip.gu on 2016/12/26.
 */
public class InventoryExportDTO {
    private String warehouseName;
    private String displayRack;
    private String materialCode;
    private String materialName;
    private String brand;
    private String model;
    private String unit;
    private String provider;
    private String dueDate;
    private Double price;
    private Double dbAmount;
    private Double realAmount;
    private Long inventoryId;
    private Long materialBatchId;

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getDueDate() {
        return dueDate;
    }

    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getDbAmount() {
        return dbAmount;
    }

    public void setDbAmount(Double dbAmount) {
        this.dbAmount = dbAmount;
    }

    public Double getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(Double realAmount) {
        this.realAmount = realAmount;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public Long getMaterialBatchId() {
        return materialBatchId;
    }

    public void setMaterialBatchId(Long materialBatchId) {
        this.materialBatchId = materialBatchId;
    }
}
