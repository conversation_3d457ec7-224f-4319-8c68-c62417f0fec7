package cn.facilityone.shang.stock.stock005.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.common.util.ValidateUtil;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.inventory.*;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.common.repository.*;
import cn.facilityone.shang.stock.common.staticmetamodel.Inventory_;
import cn.facilityone.shang.stock.common.staticmetamodel.MaterialBatch_;
import cn.facilityone.shang.stock.common.staticmetamodel.Warehouse_;
import cn.facilityone.shang.stock.common.util.StockCode;
import cn.facilityone.shang.stock.stock002.builder.InventoryBuilder;
import cn.facilityone.shang.stock.stock003.builder.MaterialBatchToBuilder;
import cn.facilityone.shang.stock.stock005.dto.*;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.transfer.core.Importer;
import cn.facilityone.xia.transfer.core.data.DTO;
import cn.facilityone.xia.transfer.core.data.Template;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: wayne.fu
 * @Date: 8/10/2015
 */

@Service
public class InventoryVerificationServiceImpl implements InventoryVerificationService {

    private static final Logger log = LoggerFactory.getLogger(InventoryVerificationServiceImpl.class);
    private static final String EXCEL_TYPE = "XLS|XLSX";

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private UploadFileService uploadFileService;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private InventoryManagementActivityRepository inventoryManagementActivityRepository;
    @Autowired
    private MaterialBatchChangeRepository materialBatchChangeRepository;
    @Autowired
    private MaterialBatchRepository materialBatchRepository;
    @Autowired
    private StockCode stockCode;
    @Autowired
    private InventoryBuilder inventoryBuilder;
    @Autowired
    private MaterialBatchToBuilder materialBatchToBuilder;
    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    @Qualifier("xlsxImporter")
    private Importer importer;


    @Override
    @XiaTransactional(readOnly = true)
    public Page<Inventory> findInPage(final DataTableRequest request) {
        CriteriaQuery query = getQuery(request);
        List<Inventory> inventoryList = entityManager.createQuery(query).setFirstResult(request.getOffset()).setMaxResults(request.getPageSize()).getResultList();
        Page<Inventory> inventories = new PageImpl<Inventory>(inventoryList);
        return inventories;
    }

    private CriteriaQuery getQuery(DataTableRequest request) {
        //user predicate
        Long em = commonUserService.findCurrentEmployeeId();
        Long emId = 0L;
        if(em!=null){
            emId = em;
        }
        String sql = "SELECT inv.inventory_id FROM inventory inv " +
                " LEFT JOIN materialbatch mb ON inv.inventory_id = mb.inventory_id" +
                " LEFT JOIN warehouse wh ON inv.warehouse_id = wh.warehouse_id" +
                " LEFT JOIN warehouse_em we ON we.warehouse_id = wh.warehouse_id" +
                " LEFT JOIN em e ON e.em_id = we.em_id" +
                " WHERE inv.deleted = 0 " +
                " AND inv.proj_id = "+ProjectContext.getCurrentProject()+
                " AND e.em_id = "+emId+
                " AND mb.materialbatch_id IS NOT NULL";
        List<Long> ids = entityManager.createNativeQuery(sql).getResultList();

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Inventory> query = cb.createQuery(Inventory.class);
        Root<Inventory> root = query.from(Inventory.class);

        From fromWarehouse = (From) root.fetch(Inventory_.WAREHOUSE, JoinType.LEFT);
        From fromMaterial = (From) root.fetch(Inventory_.MATERIAL, JoinType.LEFT);
        From employee = (From) fromWarehouse.fetch(Warehouse_.EMPLOYEES, JoinType.LEFT);


        Predicate user = cb.equal(employee.get("id"), emId);
        Predicate warehouse = cb.equal(fromWarehouse.get("deleted"), 0);


//        List<DataTableColumn> columnL = request.getColumns();
//        if (CollectionUtils.isEmpty(columnL)) {
//            query.where(user);
//            return query;
//        }

        Map<String, From> fromMap = new HashMap<>();
        fromMap.put(Inventory_.FROM_MATERIAL, fromMaterial);
        fromMap.put(Inventory_.FROM_WAREHOUSE, fromWarehouse);
        fromMap.put(Inventory_.FROM_ROOT, root);

        List<Predicate> lps = buildColumnSearchForTable(fromMap, cb, request);
        lps.add(user);
        lps.add(warehouse);
//        lps.add(cb.notEqual(root.get(Inventory_.TOTAL_AMOUNT), 0));
        lps.add(cb.equal(root.get(Inventory_.DELETED),0));
        if (CollectionUtils.isEmpty(ids)) {
            ids.add(-1l);
        }
        lps.add(root.get("id").in(ids));
        //多项目支持
        lps.add(cb.equal(root.get(Inventory_.PROJECT), ProjectContext.getCurrentProject()));
        query.where(lps.toArray(new Predicate[lps.size()]));
        query.orderBy(cb.desc(root.get(Inventory_.ID)));
        return query;
    }

    /**
     * build predicate for table
     *
     * @param fromMap
     * @param cb
     * @param request
     * @return
     */
    private List<Predicate> buildColumnSearchForTable(Map<String, From> fromMap, CriteriaBuilder cb, DataTableRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getColumns())) {
            return Collections.emptyList();
        }

        List<Predicate> results = new ArrayList<>();
        //all columns
        for (DataTableColumn column : request.getColumns()) {
            //illegal columns
            if (StringUtils.isBlank(column.getName())) {
                log.debug("column name is blank!");
                continue;
            }
            try {
                Inventory_.TABLE columnProperties = Inventory_.TABLE.valueOf(column.getName());
                String[] properties = columnProperties.getProperties();

                if (ArrayUtils.isEmpty(properties)) {
                    log.debug("properties in Table is empty!");
                    continue;
                }

                if (properties.length == 2) {
                    //has query text
                    if (StringUtils.isBlank(column.getSearchText())) {
                        continue;
                    } else {
                        From from = fromMap.get(properties[0]);
                        if (from == null) {
                            log.debug("cannot find from by:{} in map", properties[0]);
                            continue;
                        }
                        Predicate predicate = cb.like(from.get(properties[1]), buildLikeCondition(column.getSearchText()));
                        results.add(predicate);
                    }
                } else {
                    log.debug("properties length is not in [1,2]");
                    continue;
                }
            } catch (IllegalArgumentException e) {
                log.error("enum type has no constant with the specified name:{}", column.getName());
            }
        }

        return results;
    }

    private String buildLikeCondition(String searchText) {
        if (StringUtils.isBlank(searchText)) {
            return "";
        }
        return "%" + searchText + "%";
    }

    /**
     * list to dto
     *
     * @param inventories
     * @return
     */
    @Override
    public List<InventoryVerificationDTO> toDTO(List<Inventory> inventories) {
        if (CollectionUtils.isEmpty(inventories)) {
            return Collections.emptyList();
        }

        List<InventoryVerificationDTO> dtos = new ArrayList<>();
        for (Inventory inventory : inventories) {
            InventoryVerificationDTO dto = toDTO(inventory);
            dtos.add(dto);
        }

        return dtos;
    }

    /**
     * find all inventories
     *
     * @param request
     * @return
     */
    @Override
    @XiaTransactional(readOnly = true)
    public Page<Inventory> findAllInventories(DataTableRequest request) {
        Pageable pageable = null;
        if (request == null) {
            pageable = new PageRequest(0, 10);
        } else {
            pageable = new PageRequest(request.getPageNumber(), request.getPageSize());
        }

         CriteriaQuery query = getQuery(request);

        List<Inventory> inventoryList = entityManager.createQuery(query).getResultList();
        int totalCount = CollectionUtils.isEmpty(inventoryList) ? 0 : inventoryList.size();

        TypedQuery queryAgain = entityManager.createQuery(query);
        queryAgain.setFirstResult(request == null ? 0 : request.getOffset());
        queryAgain.setMaxResults(request == null ? 10 : request.getPageSize());

        return new PageImpl<Inventory>(queryAgain.getResultList(), pageable, totalCount);
    }

    @Override
    @XiaTransactional(readOnly = true)
    public Page<InventoryExportDTO> findExportInventories(DataTableRequest request) {
        List<InventoryExportDTO> inventoryExportDTOs = new ArrayList<>();
        CriteriaQuery query = getQuery(request);

        List<Inventory> inventoryList = entityManager.createQuery(query).getResultList();
        //load lazy data
        inventoryBuilder.init(inventoryList).addMaterialBatches();
        for (Inventory inventory : inventoryList) {
            List<MaterialBatch> materialBatches = inventory.getMaterialBatchs();
            materialBatchToBuilder.init(materialBatches).addProviderName();
            if (inventory.getTotalInvAmount()>0){
                for (MaterialBatch materialBatch : materialBatches) {
                    if (materialBatch.getAmount()>0) {
                        InventoryExportDTO inventoryExportDTO = new InventoryExportDTO();
                        inventoryExportDTO = setInventoryExportDTO(inventory, materialBatch);
                        inventoryExportDTOs.add(inventoryExportDTO);
                    }
                }
            } else {
                Collections.sort(materialBatches, new Comparator<MaterialBatch>() {
                    @Override
                    public int compare(MaterialBatch o1, MaterialBatch o2) {
                        Date o1Date = o1.getDueDate();
                        Date o2Date = o2.getDueDate();

                        if (o1Date == null && o2Date == null) {
                            return 0;
                        }

                        if (o1Date != null) {
                            if (o2Date == null) {
                                return 1;
                            } else {
                                return o2Date.compareTo(o1Date);
                            }
                        } else {
                            return -1;
                        }
                    }
                });
                InventoryExportDTO inventoryExportDTO = new InventoryExportDTO();
                inventoryExportDTO = setInventoryExportDTO(inventory, materialBatches.get(0));
                inventoryExportDTOs.add(inventoryExportDTO);
            }
        }

        return new PageImpl<InventoryExportDTO>(inventoryExportDTOs);
    }

    public InventoryExportDTO setInventoryExportDTO(Inventory inventory, MaterialBatch materialBatch) {
        InventoryExportDTO inventoryExportDTO = new InventoryExportDTO();
        Material material = inventory.getMaterial();
        inventoryExportDTO.setWarehouseName(inventory.getWarehouse().getName());
        inventoryExportDTO.setDisplayRack(inventory.getDisplayRack());
        inventoryExportDTO.setMaterialCode(material.getCode());
        inventoryExportDTO.setMaterialName(material.getName());
        inventoryExportDTO.setBrand(material.getBrand());
        inventoryExportDTO.setModel(material.getModel());
        inventoryExportDTO.setUnit(material.getUnit());
        inventoryExportDTO.setProvider(materialBatch.getProvider().getName());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (null != materialBatch.getDueDate()) {
            inventoryExportDTO.setDueDate(sdf.format(materialBatch.getDueDate()));
        }
        inventoryExportDTO.setPrice(materialBatch.getPrice());
        inventoryExportDTO.setDbAmount(materialBatch.getAmount());
        inventoryExportDTO.setMaterialBatchId(materialBatch.getId());
        return inventoryExportDTO;
    }

    @Override
    public int getTotalCount(final DataTableRequest request) {
        if (request == null) {
            return 0;
        }

        CriteriaQuery query = getQuery(request);
        List<Inventory> inventoryList = entityManager.createQuery(query).getResultList();
        return inventoryList == null ? 0 : inventoryList.size();
    }

    /**
     * return false when file is illegal
     *
     * @param file
     * @return
     */
    @Override
    public boolean checkUploadFileData(XiaFile file) {
        if (file == null || StringUtils.isBlank(file.getName())) {
            log.debug("file is null");
            return false;
        }

        //check file type
        String fileName = file.getName();
        String extension = FilenameUtils.getExtension(fileName);
        if (!EXCEL_TYPE.contains(extension == null ? "" : extension.toUpperCase())) {
            log.debug("file type of file:{} is illegal", fileName);
            return false;
        }

        String path = uploadFileService.getFilePath(file);

        return checkExcelFile(path);
    }

    /**
     * build import result from file
     *
     * @param file
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public Map<String, Object> initImportResultMap(XiaFile file) {
        if (file == null) {
            return Collections.emptyMap();
        }
        InventoryVerificationResultDTO dto = readExcelToDTO(file);

        Map<Long, InventoryVerificationDTO> realData = findRealDataAndUnexpectedData(dto);

        adjustAmount(realData.values());

        Map<String, Object> result = new HashMap<>();
        result.put("headers", dto.getHeader());
        result.put("columns", dto.getRecords());
        result.put("unexpectedDatas", dto.getUnexpectedRecords());
        return result;
    }

    /**
     * filter dtos and make a db data map
     *
     * @param dto
     */
    private Map<Long, InventoryVerificationDTO> findRealDataAndUnexpectedData(InventoryVerificationResultDTO dto) {
        if (dto == null || CollectionUtils.isEmpty(dto.getRecords())) {
            return Collections.emptyMap();
        }

        Iterator<InventoryVerificationResultDTO.ColumnRecord> columnRecords = dto.getRecords().iterator();
        Map<Long, InventoryVerificationDTO> inventoryVerificationDTOMap = new HashMap<>();
        while (columnRecords.hasNext()) {
            InventoryVerificationResultDTO.ColumnRecord columnRecord = columnRecords.next();
            List<String> datas = columnRecord.getData();
            if (CollectionUtils.isEmpty(datas)) {
                columnRecords.remove();
            }

            //get data by index --- default : exported excel
            try {
                InventoryVerificationDTO verificationDTO = new InventoryVerificationDTO();
                verificationDTO.setWarehouseName(datas.get(0));
                verificationDTO.setMaterialCode(datas.get(1));
                verificationDTO.setMaterialName(datas.get(2));
                verificationDTO.setBrand(datas.get(3));
                verificationDTO.setModel(datas.get(4));
                verificationDTO.setUnit(datas.get(5));
                verificationDTO.setDbAmount(Double.parseDouble(datas.get(6)));
                verificationDTO.setRealAmount(Double.parseDouble(datas.get(7)));

                Inventory inventoryInDB = findInventoryByDTO(verificationDTO);
                if (inventoryInDB == null) {
                    dto.getUnexpectedRecords().add(columnRecord);
                    columnRecords.remove();
                } else {
                    verificationDTO.setInventoryId(inventoryInDB.getId());
                    if (inventoryVerificationDTOMap.containsKey(inventoryInDB.getId())) {
                        inventoryVerificationDTOMap.get(inventoryInDB.getId()).addRealAmount(verificationDTO.getRealAmount());
                    } else {
                        inventoryVerificationDTOMap.put(verificationDTO.getInventoryId(), verificationDTO);
                    }
                }
            } catch (Exception e) {
                log.error("data is not allowed", e);
                dto.getUnexpectedRecords().add(columnRecord);
                columnRecords.remove();
            }
        }
        return inventoryVerificationDTOMap;
    }

    /**
     * find inventory by dto
     *
     * @param verificationDTO
     * @return
     */
    private Inventory findInventoryByDTO(InventoryVerificationDTO verificationDTO) {
        if (verificationDTO == null) {
            return null;
        }
        Inventory inventory = inventoryRepository.findByWarehouseAndMaterial(verificationDTO.getWarehouseName(), verificationDTO.getMaterialName(), verificationDTO.getBrand(), verificationDTO.getModel());
        return inventory;
    }

    /**
     * adjust amount for inventory
     *
     * @param request
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void adjustAmount(Collection<InventoryVerificationDTO> request) {
        if (CollectionUtils.isEmpty(request)) {
            return;
        }

        Map<Long, Double> ids = new HashMap<>();
        for (InventoryVerificationDTO dto : request) {
            if (dto.getInventoryId() == null) {
                continue;
            }
            ids.put(dto.getInventoryId(), dto.getRealAmount());
        }

        if (MapUtils.isEmpty(ids)) {
            return;
        }

        //general code for this activity
        String code = stockCode.getStockCodeWithProjectId(InventoryManagementActivity.InventoryActivityType.CHECK, ProjectContext.getCurrentProject());
        List<Inventory> inventories = inventoryRepository.findAllWithBatchAndWarehouse(ids.keySet());

        for (Inventory inventory : inventories) {
            Double realAmount = ids.get(inventory.getId());
            adjustMaterialBatch(inventory, realAmount,code);
            Double adjustAmount = realAmount - inventory.getAmount() - inventory.getLockAmount();
            if(adjustAmount == 0){
                continue;
            }
            if (adjustAmount > 0) {
                //verification result is surplus
                inventory.setAmount(realAmount-inventory.getLockAmount());
            } else {
                //check whether need cut down locked amount
                Double amount = inventory.getAmount() + adjustAmount;
                inventory.setAmount(amount > 0 ? amount : 0);
                if(amount < 0){
                    inventory.setLockAmount(inventory.getLockAmount() + amount);
                }
            }

        }

        inventoryRepository.save(inventories);
    }

    /**
     * adjust amount for inventory
     * 盘点批次
     * @param request
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void adjustAmountV2(Collection<InventoryVerificationRequestDTO> request, Employee loginEm) {
        if (CollectionUtils.isEmpty(request)) {
            return;
        }

        Map<Long, Double> ids = new HashMap<>();
        for (InventoryVerificationRequestDTO dto : request) {
            if (dto.getId() == null) {
                continue;
            }
            ids.put(dto.getId(), dto.getRealAmount());
        }

        if (MapUtils.isEmpty(ids)) {
            return;
        }

        //general code for this activity
        String code = stockCode.getStockCodeWithProjectId(InventoryManagementActivity.InventoryActivityType.CHECK, ProjectContext.getCurrentProject());
        List<MaterialBatch> materialBatches = materialBatchRepository.findWithInventoryByIds(ids.keySet());

        List<MaterialBatchChange> changes = new ArrayList<>();
        List<MaterialBatch> batches = new ArrayList<>();
        Double adjustAmount = 0d;
        for (MaterialBatch materialBatch : materialBatches) {
            Double difference = ids.get(materialBatch.getId()) - materialBatch.getAmount();
            adjustAmount += difference;
//            if (difference == 0) {
//                continue;
//            }
            materialBatch.setAmount(ids.get(materialBatch.getId()));
            if (materialBatch.getRealNum() + difference < 0) {
                materialBatch.setRealNum(0);
            } else {
                materialBatch.setRealNum(materialBatch.getRealNum() + difference);
            }
            materialBatch = materialBatchRepository.save(materialBatch);
            MaterialBatchChange change = new MaterialBatchChange();
            change.setMaterialBatch(materialBatch);
            change.setLockActivity(false);
            if (difference>0){
                change.setChangeNum(difference);
                change.setType(MaterialBatchChange.AdjustType.UP);
            } else {
                change.setChangeNum(Math.abs(difference));
                change.setType(MaterialBatchChange.AdjustType.DOWN);
            }
            materialBatchChangeRepository.save(change);
            changes.add(change);
            batches.add(materialBatch);
        }
        Inventory inventory = materialBatches.get(0).getInventory();
        if ((inventory.getAmount() + adjustAmount)<0){
//            inventory.setLockAmount(inventory.getLockAmount() + inventory.getAmount() + adjustAmount);
            inventory.setAmount(0);
        } else {
            inventory.setAmount(inventory.getAmount() + adjustAmount);
        }
        inventory.setTotalInvAmount(inventory.getTotalInvAmount() + adjustAmount);
        inventory = inventoryRepository.save(inventory);
        //add activity
        addActivity(inventory, Math.abs(adjustAmount), batches, changes,code, loginEm);

    }

    @Override
    @XiaTransactional(readOnly = true)
    public InventoryVerificationDetailDTO getDetails(Long id) {
        InventoryVerificationDetailDTO dto = new InventoryVerificationDetailDTO();
        Inventory inventory = inventoryRepository.findWithMaterialAndWarehouseAndMaterialBatchsById(id);
        Material material = inventory.getMaterial();
        if (material!=null) {
            dto.setMaterialCode(material.getCode());
            dto.setMaterialName(material.getName());
            dto.setBrand(material.getBrand());
            dto.setModel(material.getModel());
            dto.setUnit(material.getUnit());
            dto.setWarehouseName(inventory.getWarehouse().getName());
            dto.setAmount(inventory.getAmount()+inventory.getLockAmount());
            dto.setLockNum(inventory.getLockAmount());
            dto.setMinAmount(inventory.getMinAmount());
            dto.setInventoryId(inventory.getId());
        }
        List<Long> ids = new ArrayList<>();
        List<MaterialBatch> materialBatches = inventory.getMaterialBatchs();
        if (inventory.getTotalInvAmount()>0){
            for (MaterialBatch materialBatch : materialBatches) {
                if (materialBatch.getAmount() > 0) {
                    ids.add(materialBatch.getId());
                }
            }
        } else {
            Collections.sort(materialBatches, new Comparator<MaterialBatch>() {
                @Override
                public int compare(MaterialBatch o1, MaterialBatch o2) {
                    Date o1Date = o1.getDueDate();
                    Date o2Date = o2.getDueDate();

                    if (o1Date == null && o2Date == null) {
                        return 0;
                    }

                    if (o1Date != null) {
                        if (o2Date == null) {
                            return 1;
                        } else {
                            return o2Date.compareTo(o1Date);
                        }
                    } else {
                        return -1;
                    }
                }
            });
            if(materialBatches != null && materialBatches.size() > 0){
                ids.add(materialBatches.get(0).getId());
            }
        }
        dto.setDisplayRack(inventory.getDisplayRack());
        dto.setMaterialBatcheIds(ids);
        return dto;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public Result validateUploadFileData(XiaFile file) {
        List<String> errorM = new LinkedList<String>();
        List<String> warnM = new LinkedList<String>();

        String filePath = uploadFileService.getFilePath(file);
        Map<String,Object> data = new HashMap<String,Object>();
        //获取数据
        try {
            FileInputStream in = new FileInputStream(new File(filePath));
            DTO dto = importer.importData(in, new Template(1L,importTemplatePath()));
            List<InventoryExportDTO> inventoryExportDTOs = this.stockImportData(dto, errorM, warnM);
            data.put("inventoryExportDTOs", inventoryExportDTOs);
            //如果没有异常数据，就执行盘点
            if (errorM.size() == 0 && warnM.size() == 0) {
                this.adjustMaterialBatchs(inventoryExportDTOs);
            }
        } catch (FileNotFoundException e) {
            log.error(SystemConst.STR_EMPTY,e);
            errorM.add(XiaMesssageResource.getMessage("page.stock005.data.exception"));
        } catch (Exception e) {
            log.error(SystemConst.STR_EMPTY,e);
            errorM.add(XiaMesssageResource.getMessage("page.stock005.data.exception"));
        }
        data.put("errorM", errorM);
        data.put("warnM", warnM);
        return new Result(data);
    }

    private void adjustMaterialBatchs(List<InventoryExportDTO> inventoryExportDTOs) {
        Map<Long, List<InventoryVerificationRequestDTO>> batchsMap = new HashMap<>();
        for (InventoryExportDTO inventoryExportDTO : inventoryExportDTOs) {
            MaterialBatch materialBatch = materialBatchRepository.findOne(inventoryExportDTO.getMaterialBatchId());
            if (null == materialBatch) {
                continue;
            }
            InventoryVerificationRequestDTO dto = new InventoryVerificationRequestDTO();
            BeanUtils.copyProperties(materialBatch, dto);
            dto.setRealAmount(inventoryExportDTO.getRealAmount());
            Long key = inventoryExportDTO.getInventoryId();
            if (batchsMap.containsKey(key) && !batchsMap.get(key).contains(dto)) {
                batchsMap.get(key).add(dto);
            } else {
                List<InventoryVerificationRequestDTO> dtos = new ArrayList<>();
                dtos.add(dto);
                batchsMap.put(key, dtos);
            }
        }
        Employee loginEm = commonUserService.findLoginEmployee();
        for (Map.Entry<Long, List<InventoryVerificationRequestDTO>> entry : batchsMap.entrySet()) {
            adjustAmountV2(entry.getValue(), loginEm);
        }
    }

    /**
     * 模版地址
     * @return
     */
    public String importTemplatePath() {
        StringBuffer sb = new StringBuffer();
        sb.append("static");
        sb.append(File.separator);
        sb.append("tpl");
        sb.append(File.separator);
        sb.append("stock_checkout.xls");
        return sb.toString();
    }

    /**
     * 处理数据
     */
    private List<InventoryExportDTO> stockImportData(DTO dto, List<String> errorM, List<String> warnM){
        LinkedHashMap<String, List<Object>> dataList = dto.getDataMaps().get(0);
        int rowdataNum = dataList.get("row0:0").size();
        if (rowdataNum == 0) {
            rowdataNum = 1;
        }
        String stockKey = "";
        Map<String, Warehouse> warehouseMap = getAllWarehouse();
        List<InventoryExportDTO> inventoryExportDTOs = new ArrayList<>();
        InventoryExportDTO inventoryExportDTO = null;
        Map<String,Boolean> isHas = new HashMap<String,Boolean>();
        int beforeRowNum = 5;
        for (int j = 0; j < rowdataNum; j++) {
            try {
                inventoryExportDTO = new InventoryExportDTO();
                //仓库名称
                Object warehouseName = getObject(dataList.get("row0:1"), j);
                //展架
                Object displayRack = getObject(dataList.get("row0:2"), j);
                //物资编码
                Object materialCode = getObject(dataList.get("row0:3"), j);
                //物资名称
                Object materialName = getObject(dataList.get("row0:4"), j);
                //品牌
                Object brand = getObject(dataList.get("row0:5"), j);
                //型号
                Object model = getObject(dataList.get("row0:6"), j);
                //单位
                Object unit = getObject(dataList.get("row0:7"), j);
                //供应商
                Object provider = getObject(dataList.get("row0:8"), j);
                //过期时间
                Object dueDate = getObject(dataList.get("row0:9"), j);
                //单价
                Object price = getObject(dataList.get("row0:10"), j);
                //账面数量
                Object amount = getObject(dataList.get("row0:11"), j);
                //盘点数量
                Object realAmount = getObject(dataList.get("row0:12"), j);
                //批次Id
                Object materialBatchId = getObject(dataList.get("row0:0"), j);

                if(org.springframework.util.StringUtils.isEmpty(warehouseName)
                        && org.springframework.util.StringUtils.isEmpty(materialCode)
                        && org.springframework.util.StringUtils.isEmpty(materialName)
                        && org.springframework.util.StringUtils.isEmpty(brand)
                        && org.springframework.util.StringUtils.isEmpty(model)
                        && org.springframework.util.StringUtils.isEmpty(unit)
                        && org.springframework.util.StringUtils.isEmpty(provider)
                        && org.springframework.util.StringUtils.isEmpty(dueDate)
                        && org.springframework.util.StringUtils.isEmpty(price)
                        && org.springframework.util.StringUtils.isEmpty(amount)
                        && org.springframework.util.StringUtils.isEmpty(realAmount)
                        && org.springframework.util.StringUtils.isEmpty(materialBatchId) ){
                    continue;
                }

                int rowNum = (j+beforeRowNum);
                String rowStr = XiaMesssageResource.getMessage("page.stock005.tip.line").replace("EEEEE", String.valueOf(rowNum));
                StringBuilder warn = new StringBuilder();

                if(!org.springframework.util.StringUtils.isEmpty(materialBatchId)){
                    String materialBatchIdStr = org.springframework.util.StringUtils.trimWhitespace(materialBatchId.toString());
                    inventoryExportDTO.setMaterialBatchId(Long.valueOf(materialBatchIdStr));
                } else {
                    warn.append(XiaMesssageResource.getMessage("page.stock005.fill.id"));
                }

                if(!org.springframework.util.StringUtils.isEmpty(warehouseName)){
                    String warehouseNameStr = org.springframework.util.StringUtils.trimWhitespace(warehouseName.toString());
                    if (warehouseMap.containsKey(warehouseNameStr)) {
                        inventoryExportDTO.setWarehouseName(warehouseNameStr);
                    } else {
                        warn.append(XiaMesssageResource.getMessage("page.stock005.correct.warehouseName"));
                    }
                } else {
                    warn.append(XiaMesssageResource.getMessage("page.stock005.fill.warehouseName"));
                }

                if(!org.springframework.util.StringUtils.isEmpty(materialCode)){
                    String materialCodeStr = org.springframework.util.StringUtils.trimWhitespace(materialCode.toString());
                    String warehouseNameStr = org.springframework.util.StringUtils.trimWhitespace(warehouseName.toString());
                    if (warehouseMap.containsKey(warehouseNameStr)) {
                        Inventory inventory = inventoryRepository.findByWarehouseAndMaterialCode(warehouseMap.get(warehouseNameStr).getName(), materialCodeStr);
                        if (null != inventory) {
                            inventoryExportDTO.setMaterialCode(materialCodeStr);
                            inventoryExportDTO.setInventoryId(inventory.getId());
                        } else {
                            warn.append(XiaMesssageResource.getMessage("page.stock005.batch.not.exist"));
                        }
                        List<MaterialBatch> materialBatches = materialBatchRepository.findWithInventoryByIds(Arrays.asList(inventoryExportDTO.getMaterialBatchId()));
                        if (CollectionUtils.isNotEmpty(materialBatches)) {
                            Inventory inv = materialBatches.get(0).getInventory();
                            if (inv != inventory) {
                                warn.append(XiaMesssageResource.getMessage("page.stock005.batch.project.error").replace("EEEEE", String.valueOf(inventoryExportDTO.getMaterialBatchId())));
                            }
                        } else {
                            warn.append(XiaMesssageResource.getMessage("page.stock005.batch.project.error").replace("EEEEE", String.valueOf(inventoryExportDTO.getMaterialBatchId())));
                        }
                    }
                } else {
                    warn.append(XiaMesssageResource.getMessage("page.stock005.fill.code"));
                }

                if(!org.springframework.util.StringUtils.isEmpty(materialName)){
                    String materialNameStr = org.springframework.util.StringUtils.trimWhitespace(materialName.toString());
                    inventoryExportDTO.setMaterialName(materialNameStr);
                } else {
                    warn.append(XiaMesssageResource.getMessage("page.stock005.fill.materialName"));
                }

                if(!org.springframework.util.StringUtils.isEmpty(brand)){
                    String brandStr = org.springframework.util.StringUtils.trimWhitespace(brand.toString());
                    inventoryExportDTO.setBrand(brandStr);
                }
                if(!org.springframework.util.StringUtils.isEmpty(displayRack)){
                    String displayRackStr = org.springframework.util.StringUtils.trimWhitespace(displayRack.toString());
                    inventoryExportDTO.setDisplayRack(displayRackStr);
                }
                if(!org.springframework.util.StringUtils.isEmpty(model)){
                    String modelStr = org.springframework.util.StringUtils.trimWhitespace(model.toString());
                    inventoryExportDTO.setModel(modelStr);
                }

                if(!org.springframework.util.StringUtils.isEmpty(unit)){
                    String unitStr = org.springframework.util.StringUtils.trimWhitespace(unit.toString());
                    inventoryExportDTO.setUnit(unitStr);
                } else {
                    warn.append(XiaMesssageResource.getMessage("page.stock005.fill.unit"));
                }

                if(!org.springframework.util.StringUtils.isEmpty(provider)){
                    String providerStr = org.springframework.util.StringUtils.trimWhitespace(provider.toString());
                    inventoryExportDTO.setProvider(providerStr);
                } else {
                    warn.append(XiaMesssageResource.getMessage("page.stock005.fill.provider"));
                }

                if(!org.springframework.util.StringUtils.isEmpty(dueDate)){
                    String dueDateStr = org.springframework.util.StringUtils.trimWhitespace(dueDate.toString());
                    //判断日期格式
                    if (ValidateUtil.isDateYYYYMMDD(dueDateStr)) {
                        inventoryExportDTO.setProvider(dueDateStr);
                    } else {
                        warn.append(XiaMesssageResource.getMessage("page.stock005.fill.correct.date"));
                    }
                }

                if(!org.springframework.util.StringUtils.isEmpty(price)){
                    String priceStr = org.springframework.util.StringUtils.trimWhitespace(price.toString());
                    //判断是否是整数或两位以内的小数
                    if (ValidateUtil.isDoubleNumber(priceStr)) {
                        Double priceDou = Double.valueOf(priceStr);
                        inventoryExportDTO.setPrice(DoubleOperationUtils.formatPointDecimal(priceDou, DoubleOperationUtils.POINT_TWO));
                    } else {
                        warn.append(XiaMesssageResource.getMessage("page.stock005.fill.ten.correct.number"));
                    }
                }

                if(!org.springframework.util.StringUtils.isEmpty(amount)){
                    String amountStr = org.springframework.util.StringUtils.trimWhitespace(amount.toString());
                    //判断是否是整数或两位以内的小数
                    if (ValidateUtil.isDoubleNumber(amountStr)) {
                        Double amountDou = Double.valueOf(amountStr);
                        inventoryExportDTO.setDbAmount(DoubleOperationUtils.formatPointDecimal(amountDou, DoubleOperationUtils.POINT_TWO));
                    } else {
                        warn.append(XiaMesssageResource.getMessage("page.stock005.fill.eleven.correct.number"));
                    }
                }

                if(!org.springframework.util.StringUtils.isEmpty(realAmount)){
                    String realAmountStr = org.springframework.util.StringUtils.trimWhitespace(realAmount.toString());
                    //判断是否是整数或两位以内的小数
                    if (ValidateUtil.isDoubleNumber(realAmountStr)) {
                        Double realAmountDou = Double.valueOf(realAmountStr);
                        inventoryExportDTO.setRealAmount(DoubleOperationUtils.formatPointDecimal(realAmountDou, DoubleOperationUtils.POINT_TWO));
                    } else {
                        warn.append(XiaMesssageResource.getMessage("page.stock005.fill.twelve.correct.number"));
                    }
                } else {
                    warn.append(XiaMesssageResource.getMessage("page.stock005.fill.check.number"));
                }

                if (!StringUtils.isEmpty(warn.toString())){
                    warnM.add(rowStr+warn.toString()+"\n");
                }
                inventoryExportDTOs.add(inventoryExportDTO);
            } catch (Exception e) {
                errorM.add(XiaMesssageResource.getMessage("page.stock005.line.data.exception").replace("EEEEE", String.valueOf(beforeRowNum+j)));
                log.error("",e);
            }
        }
        return inventoryExportDTOs;
    }

    private Map<String, Warehouse> getAllWarehouse() {
        Map<String,Warehouse> whM = new HashMap<String,Warehouse>();
        List<Warehouse> whL = warehouseRepository.findAll();
        if(whL!=null && whL.size()>0){
            for(Warehouse wh:whL){
                whM.put(wh.getName(), wh);
            }
        }
        return whM;
    }

    private Object getObject(List<Object> oL,int index){
        Object o = new String("");
        if(oL!=null && oL.size() > index ){
            o = oL.get(index);
            if(o==null){
                o = new String("");
            }
        }
        return o;
    }

    /**
     * adjust the batches amount
     *
     * @param inventory
     * @param realAmount
     */
    private void adjustMaterialBatch(Inventory inventory, Double realAmount,String code) {

        if (CollectionUtils.isEmpty(inventory.getMaterialBatchs())) {
            return;
        }

        Double adjustAmount = realAmount - inventory.getAmount() - inventory.getLockAmount();
        if (adjustAmount == 0) {
            return;
        }

        if (adjustAmount > 0) {
            findToAdd(inventory, adjustAmount,code);
        } else {
            findToCutDown(inventory, Math.abs(adjustAmount),code);
        }

    }

    /**
     * @param inventory
     * @param adjustAmount
     */
    private void findToCutDown(Inventory inventory, Double adjustAmount,String code) {
        //keep the amount for activity
        Double adjustAmountCopy = adjustAmount;

        List<MaterialBatch> materialBatches = inventory.getMaterialBatchs();
        Collections.sort(materialBatches, new Comparator<MaterialBatch>() {
            @Override
            public int compare(MaterialBatch o1, MaterialBatch o2) {
                Date o1Date = o1.getDueDate();
                Date o2Date = o2.getDueDate();

                if (o1Date == null && o2Date == null) {
                    return 0;
                }

                if (o1Date != null) {
                    if (o2Date == null) {
                        return 1;
                    } else {
                        return o2Date.compareTo(o1Date);
                    }
                } else {
                    return -1;
                }
            }
        });

        List<MaterialBatch> batchesChanged = new ArrayList<>();
        List<MaterialBatchChange> changes = new ArrayList<>();
        //find which to cut down
        for (MaterialBatch batch : materialBatches) {
            if (batch.getRealNum() >= adjustAmount) {
                batch.setRealNum(batch.getRealNum() - adjustAmount);
                batchesChanged.add(batch);
                MaterialBatchChange change = new MaterialBatchChange();
                change.setChangeNum(adjustAmount);
                change.setMaterialBatch(batch);
                change.setType(MaterialBatchChange.AdjustType.DOWN);
                change.setLockActivity(false);
                changes.add(change);
                adjustAmount = 0d;
                break;
            } else {
                //cut down all in the batch
                Double amount = batch.getRealNum();
                adjustAmount = adjustAmount - batch.getRealNum();
                batch.setRealNum(0d);
                batchesChanged.add(batch);
                MaterialBatchChange change = new MaterialBatchChange();
                change.setChangeNum(amount);
                change.setMaterialBatch(batch);
                change.setType(MaterialBatchChange.AdjustType.DOWN);
                change.setLockActivity(false);
                changes.add(change);
            }
        }
        if (adjustAmount > 0) {
            for (MaterialBatch batch : materialBatches) {
                if (batch.getLockNum() >= adjustAmount) {
                    batch.setLockNum(batch.getRealNum() - adjustAmount);
                    break;
                } else {
                    //cut down all in the batch
                    adjustAmount = adjustAmount - batch.getLockNum();
                    batch.setLockNum(0d);
                }
            }
        }
        materialBatchRepository.save(materialBatches);
        changes = materialBatchChangeRepository.save(changes);
        addActivity(inventory, adjustAmountCopy, batchesChanged, changes,code, commonUserService.findLoginEmployee());
    }

    /**
     * Expiration time closest will be added
     *
     * @param inventory
     * @param adjustAmount
     */
    @XiaTransactional(readOnly = false)
    private void findToAdd(Inventory inventory, Double adjustAmount,String code) {
        List<MaterialBatch> materialBatches = inventory.getMaterialBatchs();
        Collections.sort(materialBatches, new Comparator<MaterialBatch>() {
            @Override
            public int compare(MaterialBatch o1, MaterialBatch o2) {
                Date o1Date = o1.getDueDate();
                Date o2Date = o2.getDueDate();
                if (o1Date == null && o2Date == null) {
                    return 0;
                }

                if (o1Date != null) {
                    if (o2Date == null) {
                        return -1;
                    } else {
                        return o1Date.compareTo(o2Date);
                    }
                } else {
                    return 1;
                }
            }
        });

        //get one and adjust amount
        MaterialBatch batch = materialBatches.get(0);
        batch.setRealNum(batch.getRealNum() + adjustAmount);
        batch = materialBatchRepository.save(batch);
        MaterialBatchChange change = new MaterialBatchChange();
        change.setChangeNum(adjustAmount);
        change.setMaterialBatch(batch);
        change.setType(MaterialBatchChange.AdjustType.UP);
        change.setLockActivity(false);
        materialBatchChangeRepository.save(change);
        List<MaterialBatchChange> changes = new ArrayList<>();
        changes.add(change);
        List<MaterialBatch> batches = new ArrayList<>();
        batches.add(batch);

        //add activity
        addActivity(inventory, adjustAmount, batches, changes,code, commonUserService.findLoginEmployee());
    }

    /**
     * create a record for inventory verification
     *
     * @param inventory
     * @param adjustAmount
     * @param batches
     * @param changes
     */
    @XiaTransactional(readOnly = false)
    private void addActivity(Inventory inventory, Double adjustAmount, List<MaterialBatch> batches, List<MaterialBatchChange> changes,String code, Employee employee) {
        InventoryManagementActivity activity = new InventoryManagementActivity();
        activity.setAmount(adjustAmount);
        activity.setLaborer(employee);
        activity.setHandles(employee);
        activity.setOperateDate(new Date());
        activity.setType(InventoryManagementActivity.InventoryActivityType.CHECK);
        activity.setWarehouse(inventory.getWarehouse());
        activity.setMaterialBatchs(batches);
        activity.setMaterialBatchChanges(changes);
        activity.setNo(code);
        inventoryManagementActivityRepository.save(activity);

        for (MaterialBatchChange change : changes) {
            change.setInventoryManagementActivity(activity);
        }

        materialBatchChangeRepository.save(changes);
    }

    /**
     * read excel file to dto
     * <p/>
     * excel file header must order by table header
     *
     * @param file
     * @return
     */
    private InventoryVerificationResultDTO readExcelToDTO(XiaFile file) {
        String path = uploadFileService.getFilePath(file);
        InventoryVerificationResultDTO dto = new InventoryVerificationResultDTO();
        try {
            //init workbook
            FileInputStream fileInputStream = new FileInputStream(path);
            Workbook wb = WorkbookFactory.create(fileInputStream);
            Sheet sheet = wb.getSheetAt(0);

            //depends on default template header row
            //0 base counting
            final int HEADER_ROW_INDEX = 3;
            Row header = sheet.getRow(HEADER_ROW_INDEX);
            List<String> headerData = new ArrayList<>();
            //header row
            for (int i = header.getFirstCellNum(), j = header.getLastCellNum(); i <= j; i++) {
                Cell cell = header.getCell(i);
                if (cell == null) {
                    break;
                }
                String value = getValueFromCell(cell);
                if (StringUtils.isBlank(value)) {
                    break;
                }
                headerData.add(value);
            }

            dto.setHeader(headerData);

            final int INDEX_HEADER_END = headerData.size() - 1;
            final int INDEX_HEADER_START = header.getFirstCellNum();

            List<InventoryVerificationResultDTO.ColumnRecord> records = new ArrayList<>();
            //data
            LOOP_ROW:
            for (int i = HEADER_ROW_INDEX + 1, j = sheet.getLastRowNum(); i <= j; i++) {
                Row currentRow = sheet.getRow(i);
                InventoryVerificationResultDTO.ColumnRecord record = dto.new ColumnRecord();
                List<String> datas = new ArrayList<>();
                LOOP_CELL:
                for (int m = INDEX_HEADER_START, n = INDEX_HEADER_END; m <= n; m++) {
                    Cell cell = currentRow.getCell(m);
                    if (cell == null) {
                        datas.add("");
                    } else {
                        String value = getValueFromCell(cell);
                        datas.add(value);
                    }
                }
                record.setData(datas);
                record.setType(getTypeOfColumnData(datas));
                records.add(record);
            }
            dto.setRecords(records);
        } catch (FileNotFoundException e) {
            log.error("File cannot found in path:{}", path);
        } catch (Exception e) {
            log.error("Cannot parse excel file", e);
        }
        return dto;
    }

    /**
     * simply return string value of cell
     * <p/>
     * return null for default
     *
     * @param cell
     * @return
     */
    private String getValueFromCell(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getRichStringCellValue().getString();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    break;
                } else {
                    return String.format("%.0f", cell.getNumericCellValue());
                }
            case Cell.CELL_TYPE_BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case Cell.CELL_TYPE_FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }

        return null;
    }

    private int getTypeOfColumnData(List<String> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return InventoryVerificationResultDTO.ColumnRecord.TYPE_BALANCE;
        }
        String dbAmountString = datas.get(Inventory_.TABLE.dbAmount.ordinal());
        int dbAmount = StringUtils.isBlank(dbAmountString) ? 0 : Integer.parseInt(dbAmountString);

        String realAmountString = datas.get(Inventory_.TABLE.realAmount.ordinal());
        int realAmount = StringUtils.isBlank(realAmountString) ? 0 : Integer.parseInt(realAmountString);

        if (dbAmount - realAmount == 0) {
            return InventoryVerificationResultDTO.ColumnRecord.TYPE_BALANCE;
        }

        return dbAmount - realAmount > 0 ? InventoryVerificationResultDTO.ColumnRecord.TYPE_LOSS : InventoryVerificationResultDTO.ColumnRecord.TYPE_SURPLUS;
    }

    /**
     * check whether excel file is matching inventory verification table
     *
     * @param path
     * @return
     */
    private boolean checkExcelFile(String path) {
        if (StringUtils.isBlank(path)) {
            log.debug("excel file path:{} is illegal!", path);
            return false;
        }
        try {
            FileInputStream fileInputStream = new FileInputStream(path);
            Workbook wb = WorkbookFactory.create(fileInputStream);
            Sheet sheet = wb.getSheetAt(0);
            //depends on default template header row
            //0 base counting
            final int HEADER_ROW_INDEX = 3;
            Row header = sheet.getRow(HEADER_ROW_INDEX);
            MaterialBatch_.TABLE_CHECK_VALIDATION[] tableColumns = MaterialBatch_.TABLE_CHECK_VALIDATION.values();
            //column index start from 0
            int m = 0;
            for (int i = header.getFirstCellNum(), j = header.getLastCellNum(); i <= j; i++) {
                Cell cell = header.getCell(i);
                if (cell == null) {
                    break;
                }
                String value = cell.getStringCellValue();
                if (StringUtils.isBlank(value)) {
                    break;
                }

                if (m >= tableColumns.length) {
                    break;
                }

                if (!value.equalsIgnoreCase(XiaMesssageResource.getMessage(tableColumns[m++].getProperty()))) {
                    return false;
                }
            }
            if (m < tableColumns.length) {
                return false;
            }
        } catch (FileNotFoundException e) {
            log.error("File cannot found in path:{}", path);
            return false;
        } catch (Exception e) {
            log.error("Cannot parse excel file", e);
            return false;
        }
        return true;
    }


    /**
     * single to dto
     *
     * @param inventory
     * @return
     */
    private InventoryVerificationDTO toDTO(Inventory inventory) {
        InventoryVerificationDTO dto = new InventoryVerificationDTO();
        dto.setWarehouseName(inventory.getWarehouse() == null ? "" : inventory.getWarehouse().getName());
        Material material = inventory.getMaterial();
        dto.setInventoryId(inventory.getId());
        if (material != null) {
            dto.setMaterialCode(material.getCode());
            dto.setMaterialName(material.getName());
            dto.setModel(material.getModel());
            dto.setBrand(material.getBrand());
            dto.setUnit(material.getUnit());
        }
        dto.setDbAmount(inventory.getTotalInvAmount());
        if(null != inventory.getDisplayRack()){
            dto.setDisplayRack(inventory.getDisplayRack());
        }
        return dto;
    }
}
