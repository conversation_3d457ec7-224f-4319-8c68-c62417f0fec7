package cn.facilityone.shang.stock.stock005.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.configuration.properties.CompanyProperties;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.projects.pro002.service.ProjectService;
import cn.facilityone.shang.stock.stock005.dto.InventoryExportDTO;
import cn.facilityone.shang.stock.stock005.dto.InventoryVerificationDTO;
import cn.facilityone.shang.stock.stock005.dto.InventoryVerificationRequestDTO;
import cn.facilityone.shang.stock.stock005.service.InventoryVerificationService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.transfer.core.Exporter;
import cn.facilityone.xia.transfer.core.data.DTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: wayne.fu
 * @Date: 8/10/2015
 */
@Path("/stock005")
public class InventoryVerificationResource {
    private static final String TEMPLATE_IV_PATH = "/business/stockV2/stock005-goodsCheck.ftl";


    @Autowired
    private InventoryVerificationService inventoryVerificationService;
    @Autowired
    @Qualifier("xlsxExporter")
    private Exporter exporter;
    
    @Autowired
    private CompanyProperties companyProperties;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private ProjectService projectService;

    @GET
    @Template(name = TEMPLATE_IV_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>();
    }


    @POST
    @Path("inventories/verification/table")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWareHouseTable(DataTableRequest request) {
        Page<Inventory> inventories = inventoryVerificationService.findAllInventories(request);
        List<InventoryVerificationDTO> dtos = inventoryVerificationService.toDTO(inventories==null?null:inventories.getContent());
        int totalCount = ((Long)inventories.getTotalElements()).intValue();
        return new DataTableResponse(dtos, totalCount, request.getPageNumber(), request.getDraw());
    }

    @GET
    @Path("inventories/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    public Result getDetails(@PathParam("id") Long id) {
        return new Result(inventoryVerificationService.getDetails(id));
    }

    @POST
    @Path("inventories/verification/adjust")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result adjustAmount(List<InventoryVerificationDTO> request) {
        inventoryVerificationService.adjustAmount(request);
        return new Result(XiaMesssageResource.getMessage("server.result.success.update",null,""));
    }

    @POST
    @Path("inventories/verification/adjustV2")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result adjustAmountV2(List<InventoryVerificationRequestDTO> request) {
        inventoryVerificationService.adjustAmountV2(request, commonUserService.findLoginEmployee());
        return new Result(XiaMesssageResource.getMessage("server.result.success.update",null,""));
    }

    @POST
    @Path("inventories/verification/export")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response export(String params) throws IOException, InvalidFormatException {
        DataTableRequest request = null;

        if (StringUtils.isNotBlank(params)) {
            try {
                request = new ObjectMapper().readValue(params, DataTableRequest.class);
            } catch (Exception e) {
                return Response.ok(new Result(Response.Status.BAD_REQUEST.getStatusCode(), "IO流错误!", "")).build();
            }
        }
        Page<InventoryExportDTO> inventories = inventoryVerificationService.findExportInventories(request);
//        List<Inventory> allInventories = inventoryVerificationService.findAllInventories(request);
//        List<InventoryVerificationDTO> dtos = inventoryVerificationService.toDTO(inventories.getContent());
        LinkedHashMap<String, String> title = new LinkedHashMap<String, String>();
        title.put("materialBatchId", XiaMesssageResource.getMessage("page.stock005.batch.id"));
        title.put("warehouseName", XiaMesssageResource.getMessage("InventoryManagementActivity.warehouseName"));
        title.put("displayRack", XiaMesssageResource.getMessage("Inventory.displayRack"));
        title.put("materialCode",  XiaMesssageResource.getMessage("InventoryManagementActivity.materialCode"));
        title.put("materialName", XiaMesssageResource.getMessage("InventoryManagementActivity.materialName"));
        title.put("brand", XiaMesssageResource.getMessage("Material.brand"));
        title.put("model", XiaMesssageResource.getMessage("Material.model"));
        title.put("unit", XiaMesssageResource.getMessage("Material.unit"));
        title.put("provider", XiaMesssageResource.getMessage("MaterialBatch.provider"));
        title.put("dueDate", XiaMesssageResource.getMessage("MaterialBatch.dueDate"));
        title.put("price", XiaMesssageResource.getMessage("MaterialBatch.price"));
        title.put("dbAmount", XiaMesssageResource.getMessage("Inventory.dbAmount"));
        title.put("realAmount", XiaMesssageResource.getMessage("Inventory.realAmount"));

        Map<String, Object> map = new HashMap<String, Object>();
        String projectName = projectService.findUserCurrentProjectName();
        map.put("companyName", companyProperties.getName()+"("+projectName+")");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        map.put("year", sdf.format(date));
        DTO dto = new DTO(map);
        dto.addTitleAndData(title, inventories.getContent());
        cn.facilityone.xia.transfer.core.data.Template tpl = new cn.facilityone.xia.transfer.core.data.Template(3L, "static/tpl/stock_checkout.xls");
        ByteArrayOutputStream out = (ByteArrayOutputStream) exporter
                .export(dto,tpl);
        return Response.ok(
                new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.success.export"),
                        FileUtil.exportFile(out, FileUtil.FILE_TYPE_EXCELX))).build();
    }

    @POST
    @Path("inventories/inc/validate")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result checkFilesData(XiaFile file) {
        boolean validate = inventoryVerificationService.checkUploadFileData(file);
        if (!validate) {
            List<String> errorM = new LinkedList<String>();
            Map<String,Object> data = new HashMap<String,Object>();
            errorM.add(XiaMesssageResource.getMessage("message.import.new.template"));
            data.put("errorM", errorM);
            return new Result(data);
        }
        Result result = inventoryVerificationService.validateUploadFileData(file);
        return result;
    }
}
