package cn.facilityone.shang.stock.stock005.dto;

/**
 * @Author: wayne.fu
 * @Date: 8/10/2015
 */
public class InventoryVerificationDTO {
    private String warehouseName;
    private String displayRack;
    private String materialCode;
    private String materialName;
    private String brand;
    private String model;
    private String unit;
    private Double dbAmount;
    private Double realAmount;
    private Long inventoryId;

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Double getDbAmount() {
        return dbAmount;
    }

    public void setDbAmount(Double dbAmount) {
        this.dbAmount = dbAmount;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Double getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(Double realAmount) {
        this.realAmount = realAmount;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public void addRealAmount(Double realAmount) {
        this.realAmount += realAmount;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }
}
