package cn.facilityone.shang.stock.stock005.resource;

import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.stock.stock005.service.InventoryVerificationService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wayne.fu
 * @Date: 8/12/2015
 */

@Path("/stock005/inventories/import")
public class InventoryVerificationImportResource {
    public static final String TEMPLATE_IV_IMPORT_PATH = "/business/stockV2/stock005-inventory-import.ftl";
    private static XiaFile file;

    @Autowired
    private InventoryVerificationService inventoryVerificationService;

    @GET
    @Template(name = TEMPLATE_IV_IMPORT_PATH)
    @Produces(MediaType.TEXT_HTML)
    public Map<String, Object> init() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("i18nLocale", XiaMesssageResource.getLocaleString());

        if (file != null) {
            Map<String, Object> datas = inventoryVerificationService.initImportResultMap(file);
            map.putAll(datas);
        }

        return map;
    }

    @POST
    @Path("check")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result checkFileData(XiaFile file) {
        boolean validate = inventoryVerificationService.checkUploadFileData(file);
        Result result = new Result();
        result.put("validate", validate);

        //keep the latest file upload
        if (validate) {
            setFile(file);
        } else {
            setFile(null);
        }
        return result;
    }

    private synchronized void setFile(XiaFile file) {
        this.file = file;
    }
}
