package cn.facilityone.shang.stock.stock034.dto;

import java.util.List;

/**
 * Created by zack.zhang on 2016/12/27.
 */
public class MaterialMoveDto {

    public Long primaryWarehouseId;
    public Long targetWarehouseId;
    public Long primaryWarehouseManagerId;
    public Long targetWarehouseManagerId;
    public Long moveEmployeeId;
    public List<InventoryMoveDto> inventoryArrays;
    public String remarks;

    public void setPrimaryWarehouseId(Long primaryWarehouseId) {
        this.primaryWarehouseId = primaryWarehouseId;
    }

    public void setTargetWarehouseId(Long targetWarehouseId) {
        this.targetWarehouseId = targetWarehouseId;
    }

    public void setPrimaryWarehouseManagerId(Long primaryWarehouseManagerId) {
        this.primaryWarehouseManagerId = primaryWarehouseManagerId;
    }

    public void setTargetWarehouseManagerId(Long targetWarehouseManagerId) {
        this.targetWarehouseManagerId = targetWarehouseManagerId;
    }

    public void setMoveEmployeeId(Long moveEmployeeId) {
        this.moveEmployeeId = moveEmployeeId;
    }

    public void setInventoryArrays(List<InventoryMoveDto> inventoryArrays) {
        this.inventoryArrays = inventoryArrays;
    }

    public Long getPrimaryWarehouseId() {

        return primaryWarehouseId;
    }

    public Long getTargetWarehouseId() {
        return targetWarehouseId;
    }

    public Long getPrimaryWarehouseManagerId() {
        return primaryWarehouseManagerId;
    }

    public Long getTargetWarehouseManagerId() {
        return targetWarehouseManagerId;
    }

    public Long getMoveEmployeeId() {
        return moveEmployeeId;
    }

    public List<InventoryMoveDto> getInventoryArrays() {
        return inventoryArrays;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
