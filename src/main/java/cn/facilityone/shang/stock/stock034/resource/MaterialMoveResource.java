package cn.facilityone.shang.stock.stock034.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.stock034.dto.MaterialMoveDto;
import cn.facilityone.shang.stock.stock034.service.MaterialMoveService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zack.zhang on 2016/11/21.
 */
@Path("/stock034")
public class MaterialMoveResource {

    private static final String TEMPLATE_POSITION_PATH = "/business/stockV2/stock034-materialMove.ftl";

    @Autowired
    private MaterialMoveService materialMoveService;

    @GET
    @Template(name = TEMPLATE_POSITION_PATH)
    public Map<String, Object> init() {
        return new HashMap<>();
    }

    @POST
    @Path("materials/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getMaterialList(@PathParam("id") Long warehouseId, DataTableRequest request) {
        int pageNumber = 0;
        if (request != null) {
            pageNumber = request.getPageNumber();
        } else {
            pageNumber = 1;
        }
        Pageable pageable = new DataTableRequest(pageNumber, request.getOffset(), request.getPageSize(), new Sort(Sort.Direction.DESC, "id"), request.getDraw());
        Page<Inventory> inventoryPage = materialMoveService.getInventoryPage(warehouseId, pageable, request);
        return new DataTableResponse(inventoryPage.getContent(), (int) inventoryPage.getTotalElements(), pageNumber, request.getDraw());
    }

    @POST
    @Path("move")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result inventoryMove(MaterialMoveDto dto) {
        InventoryManagementActivity activity = materialMoveService.moveInventory(dto);
        activity.setMaterialBatchChanges(null);
        activity.setMaterialBatchs(null);
        return new Result(200, XiaMesssageResource.getMessage("page.stock034.move.message"), activity);
    }

    @POST
    @Path("ems")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getEmployeesWithUsers() {
        List<Employee> employees = materialMoveService.getEmployeesWithUsers();
        return new Result(employees);
    }
}