package cn.facilityone.shang.stock.stock034.dto;

import java.util.List;

/**
 * Created by zack.zhang on 2016/11/23.
 */
public class InventoryMoveDto {

    private List<BatchDto> batchData;
    private Long inventoryId;
    private Double totalMoveAmount;
    private String materialCode;

    public void setTotalMoveAmount(Double totalMoveAmount) {
        this.totalMoveAmount = totalMoveAmount;
    }

    public Double getTotalMoveAmount() {

        return totalMoveAmount;
    }

    public void setBatchData(List<BatchDto> batchData) {
        this.batchData = batchData;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public List<BatchDto> getBatchData() {

        return batchData;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public String getMaterialCode() {
        return materialCode;
    }
}
