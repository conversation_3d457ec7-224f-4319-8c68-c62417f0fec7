package cn.facilityone.shang.stock.stock034.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.stock034.dto.InventoryMoveDto;
import cn.facilityone.shang.stock.stock034.dto.MaterialMoveDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by zack.zhang on 2016/11/22.
 */
public interface MaterialMoveService {

    public Page<Inventory> getInventoryPage(Long warehouseId, Pageable pageable, DataTableRequest request);

    public InventoryManagementActivity moveInventory(MaterialMoveDto dto);

    public List<Employee> getEmployeesWithUsers();
}
