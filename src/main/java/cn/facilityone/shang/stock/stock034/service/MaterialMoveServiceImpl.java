package cn.facilityone.shang.stock.stock034.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.entity.inventory.*;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.inventory.respository.MaterialRepository;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.stock.common.repository.*;
import cn.facilityone.shang.stock.common.util.StockCode;
import cn.facilityone.shang.stock.stock003.service.MaterialService;
import cn.facilityone.shang.stock.stock034.dto.BatchDto;
import cn.facilityone.shang.stock.stock034.dto.InventoryMoveDto;
import cn.facilityone.shang.stock.stock034.dto.MaterialMoveDto;
import cn.facilityone.xia.core.exception.BusinessException;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.security.Principal;
import java.util.*;

/**
 * Created by zack.zhang on 2016/11/22.
 */
@Service
public class MaterialMoveServiceImpl implements MaterialMoveService {

    @Autowired
    private MaterialRepository materialRepository;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private MaterialBatchRepository materialBatchRepository;
    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    private InventoryManagementActivityRepository inventoryManagementActivityRepository;
    @Autowired
    private MaterialBatchChangeRepository materialBatchChangeRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private StockCode stockCode;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private MaterialService materialService;

    @Override
    @XiaTransactional(readOnly = true)
    public Page<Inventory> getInventoryPage(final Long warehouseId, final Pageable pageable, DataTableRequest request) {
        final List<DataTableColumn> columnList = request.getColumns();
        Page<Inventory> inventoryPage = inventoryRepository.findAll(new XiaSpecification<Inventory>() {
            @Override
            public Root<Inventory> toRoot(Root<Inventory> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                root.fetch("material", JoinType.LEFT);
                root.fetch("warehouse", JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<Inventory> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                predicatesList.add(cb.equal(root.<Long>get("warehouse"), warehouseId));

                predicatesList.add(cb.notEqual(root.<Double>get("totalInvAmount"), 0d));

                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                return query.getRestriction();
            }
        }, pageable);
        List<Inventory> inventories = inventoryPage.getContent();
        for (Inventory inventory : inventories) {
            inventory.setTotalInvAmount(DoubleOperationUtils.changeDecimal(inventory.getTotalInvAmount(),2));
            inventory.setLockAmount(DoubleOperationUtils.changeDecimal(inventory.getLockAmount(),2));
            inventory.setMinAmount(DoubleOperationUtils.changeDecimal(inventory.getMinAmount(),2));
        }
        return inventoryPage;
    }

    /**
     * 存库存 --> 库存id --> 存批次 --> 批次id --> 存库存操作change --> 库存操作change的id --> 根据批次id和库存操作change的id存批次操作
     *
     * @param dto
     * @return
     */
    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity moveInventory(MaterialMoveDto dto) {
        String remarks = dto.getRemarks();
        Long primaryWarehouseId = dto.getPrimaryWarehouseId();
        Long targetWarehouseId = dto.getTargetWarehouseId();
        Long moveEmId = dto.getMoveEmployeeId();
        Long primaryWarehouseManagerId = dto.getPrimaryWarehouseManagerId();
        Long targetWarehouseManagerId = dto.getTargetWarehouseManagerId();
        List<InventoryMoveDto> inventoryMoveDtos = dto.getInventoryArrays();
        //验证要移库的物资数量，在对应批次中是否有足够的可以移数量
        Boolean canMove=true;
        for (InventoryMoveDto inventoryDto:inventoryMoveDtos) {
            List<BatchDto> batchDtos = inventoryDto.getBatchData();
            if(batchDtos.size()>0){
                for (BatchDto batchDto:batchDtos) {
                    MaterialBatch materialBatch = materialBatchRepository.findMaterialBatchByIdFetchInventory(batchDto.batchId);
                    BigDecimal moveNum = new BigDecimal(batchDto.getNumber());
                    BigDecimal totalNum = new BigDecimal(materialBatch.getAmount());
                    if(totalNum.compareTo(moveNum)<0){
                        canMove =false;
                        break;
                    }
                }
            }
        }
        if(!canMove){
            throw new BusinessException(XiaMesssageResource.getMessage("m.message.can.not.operation.materialBatch"));
        }

        Employee moveEmployee = employeeRepository.findOne(moveEmId);
        Employee primaryWarehouseManager = employeeRepository.findOne(primaryWarehouseManagerId);
        Employee targetWarehouseManager = employeeRepository.findOne(targetWarehouseManagerId);

        Warehouse primaryWarehouse = warehouseRepository.findOne(primaryWarehouseId);
        Warehouse targetWarehouse = warehouseRepository.findOne(targetWarehouseId);

        Double moveAmount = 0d;

        List<MaterialBatch> outBatches = new ArrayList<>();
        List<MaterialBatch> inBatches = new ArrayList<>();

        Map<MaterialBatch, Double> batchMap = new HashMap<>();
        Map<MaterialBatch, Double> batchMapIn = new HashMap<>();

        for (InventoryMoveDto inventoryMoveDto : inventoryMoveDtos) {
            Long invId = inventoryMoveDto.getInventoryId();
            String materialCode = inventoryMoveDto.getMaterialCode();
            List<BatchDto> batchDtos = inventoryMoveDto.getBatchData();

            Inventory oldInventory = inventoryRepository.findByIdAndMaterialCode(primaryWarehouseId, materialCode);
            Inventory newInventory = inventoryRepository.findByIdAndMaterialCode(targetWarehouseId, materialCode);

            //是否要生成二维码
            boolean createQRCode = false;
            if (newInventory == null) {
                newInventory = new Inventory();
                newInventory.setMaterial(oldInventory.getMaterial());
                newInventory.setWarehouse(targetWarehouse);
                newInventory.setAvgPrice(oldInventory.getAvgPrice());
                newInventory.setAmount(0);
                newInventory.setLockAmount(0);
                newInventory.setMinAmount(0);
                newInventory.setTotalAmount(0);
                createQRCode = true;
            }

            // 批次数量为空进行判断
            Double invChangeAmount = (null == inventoryMoveDto.getTotalMoveAmount()) ? 0d : inventoryMoveDto.getTotalMoveAmount();
            moveAmount += invChangeAmount;

            // 没有批次就按照过期时间取最近快过期的批次
            if (batchDtos.size() <= 0) {
                if (invChangeAmount > 0d) {
                    Double thisChangeAmount = invChangeAmount;
                    List<MaterialBatch> materialBatches = oldInventory.getMaterialBatchs();
                    Collections.sort(materialBatches, new Comparator<MaterialBatch>() {
                        @Override
                        public int compare(MaterialBatch o1, MaterialBatch o2) {
                            Date o1Date = o1.getDueDate();
                            Date o2Date = o2.getDueDate();

                            if (o1Date == null && o2Date == null) {
                                return 0;
                            }

                            if (o1Date != null) {
                                if (o2Date == null) {
                                    return 1;
                                } else {
                                    return o2Date.compareTo(o1Date);
                                }
                            } else {
                                return -1;
                            }
                        }
                    });
                    for (MaterialBatch batch : materialBatches) {
                        BatchDto batchDto = new BatchDto();
                        if (thisChangeAmount >= batch.getRealNum()) {
                            batchDto.setNumber(batch.getRealNum());
                            batchDto.setBatchId(batch.getId());
                            thisChangeAmount -= batch.getRealNum();
                        } else if (thisChangeAmount < batch.getRealNum()) {
                            batchDto.setNumber(thisChangeAmount);
                            batchDto.setBatchId(batch.getId());
                            thisChangeAmount = 0d;
                        }
                        batchDtos.add(batchDto);
                    }
                }
            }

            // 修改旧的库存
            // oldInventory.setTotalAmount(oldInventory.getTotalAmount() - invChangeAmount);
            // 总量
            oldInventory.setTotalInvAmount(DoubleOperationUtils.doubleSub(oldInventory.getTotalInvAmount(), invChangeAmount, DoubleOperationUtils.POINT_TWO));
            // 有效数量
            Double newAmount = DoubleOperationUtils.doubleSub(oldInventory.getTotalInvAmount(), oldInventory.getLockAmount(), DoubleOperationUtils.POINT_TWO);
            if (newAmount < 0d) {
                newAmount = 0d;
            }
            oldInventory.setAmount(newAmount);
            oldInventory = inventoryRepository.save(oldInventory);

            // 保存新的库存
            newInventory.setTotalAmount(DoubleOperationUtils.doubleAdd(newInventory.getTotalAmount(), invChangeAmount, DoubleOperationUtils.POINT_TWO));
            newInventory.setTotalInvAmount(DoubleOperationUtils.doubleAdd(newInventory.getTotalInvAmount(), invChangeAmount, DoubleOperationUtils.POINT_TWO));
            newInventory.setAmount(DoubleOperationUtils.doubleSub(newInventory.getTotalInvAmount(), newInventory.getLockAmount(), DoubleOperationUtils.POINT_TWO));
            newInventory = inventoryRepository.save(newInventory);
            if (createQRCode) {
                newInventory = materialService.createQRPicture(request, newInventory.getWarehouse().getId(),newInventory);
            }

            for (BatchDto batchDto : batchDtos) {
                Long batchId = batchDto.getBatchId();
                Double changeAmount = batchDto.getNumber();
                // 出库物资批次变化
                MaterialBatch outBatch = materialBatchRepository.findOne(batchDto.getBatchId());
                // 总数量
                outBatch.setAmount(DoubleOperationUtils.doubleSub(outBatch.getAmount(), changeAmount, DoubleOperationUtils.POINT_TWO));
                // 有效数量
                Double realNum = DoubleOperationUtils.doubleSub(outBatch.getRealNum(), changeAmount, DoubleOperationUtils.POINT_TWO);
                if (realNum < 0d) {
                    realNum = 0d;
                }
                outBatch.setRealNum(realNum);
                MaterialBatch savedOldBatch = materialBatchRepository.save(outBatch);
                outBatches.add(savedOldBatch);
                batchMap.put(savedOldBatch, changeAmount);

                // 入库物资批次变化（新增，入库时间即为当前移库时间）
                MaterialBatch newBatch = new MaterialBatch();
                newBatch.setCreatedDate(new Date());
                newBatch.setDueDate(outBatch.getDueDate());
                newBatch.setAmount(batchDto.getNumber());
                newBatch.setLockNum(0);
                newBatch.setPrice(outBatch.getPrice());
                newBatch.setRealNum(batchDto.getNumber());
                newBatch.setUnit(outBatch.getUnit());
                newBatch.setProvider(outBatch.getProvider());
                newBatch.setInventory(newInventory);
                MaterialBatch savedNewBatch = materialBatchRepository.save(newBatch);
                inBatches.add(savedNewBatch);
                batchMapIn.put(savedNewBatch, changeAmount);
            }
        }

        Long projId = ProjectContext.getCurrentProject();

        String ounNo = stockCode.getStockCodeWithProjectId(InventoryManagementActivity.InventoryActivityType.MOVEOUT, projId);
        String inNo = stockCode.getStockCodeWithProjectId(InventoryManagementActivity.InventoryActivityType.MOVEIN, projId);

        //操作员工
        Employee employeeUser = null;
        Principal principal = SecurityContextHolder.getContext().getUserPrincipal();
        if (null != principal) {
            XiaPrincipal user = (XiaPrincipal) principal;
            Long userId = user.getId();
            employeeUser = employeeRepository.findOneByUserId(userId);
        }

        InventoryManagementActivity activityOut = new InventoryManagementActivity();
        activityOut.setAmount(moveAmount);
        activityOut.setType(InventoryManagementActivity.InventoryActivityType.MOVEOUT);
        activityOut.setOperateDate(new Date());
        activityOut.setMaterialBatchs(outBatches);
        activityOut.setWarehouse(primaryWarehouse);
        activityOut.setHandles(moveEmployee);
        activityOut.setLaborer(employeeUser);
        activityOut.setManagers(primaryWarehouseManager);
        activityOut.setNo(ounNo);
        activityOut.setConnectNo(inNo);
        activityOut.setRemarks(remarks);
        activityOut.setDescription(this.buildMoveInDescription("", inNo));
        InventoryManagementActivity outActivity = inventoryManagementActivityRepository.save(activityOut);

        InventoryManagementActivity activityIn = new InventoryManagementActivity();
        activityIn.setAmount(moveAmount);
        activityIn.setType(InventoryManagementActivity.InventoryActivityType.MOVEIN);
        activityIn.setOperateDate(new Date());
        activityIn.setMaterialBatchs(inBatches);
        activityIn.setWarehouse(targetWarehouse);
        activityIn.setHandles(moveEmployee);
        activityIn.setLaborer(employeeUser);
        activityIn.setManagers(targetWarehouseManager);
        activityIn.setNo(inNo);
        activityIn.setConnectNo(ounNo);
        activityIn.setRemarks(remarks);
        activityIn.setDescription(this.buildMoveOutDescription("", ounNo));
        InventoryManagementActivity inActivity = inventoryManagementActivityRepository.save(activityIn);

        // 出库物资批次调整变化
        for (Map.Entry<MaterialBatch, Double> entry : batchMap.entrySet()) {
            MaterialBatchChange batchChange = new MaterialBatchChange();
            batchChange.setChangeNum(entry.getValue());
            batchChange.setMaterialBatch(entry.getKey());
            batchChange.setType(MaterialBatchChange.AdjustType.DOWN);
            batchChange.setInventoryManagementActivity(outActivity);
            materialBatchChangeRepository.save(batchChange);
        }

        // 入库物资批次调整变化
        for (Map.Entry<MaterialBatch, Double> entry : batchMapIn.entrySet()) {
            MaterialBatchChange batchChange = new MaterialBatchChange();
            batchChange.setChangeNum(entry.getValue());
            batchChange.setMaterialBatch(entry.getKey());
            batchChange.setType(MaterialBatchChange.AdjustType.UP);
            batchChange.setInventoryManagementActivity(inActivity);
            materialBatchChangeRepository.save(batchChange);
        }

        return outActivity;
    }

    private String buildMoveInDescription(String desc,String code){
        StringBuffer stringBuffer = null;
        if (desc != null) {
            stringBuffer = new StringBuffer(desc);
        } else {
            stringBuffer = new StringBuffer();
        }
        stringBuffer.append(" ").append(XiaMesssageResource.getMessage("page.stock003.stockBack.connectMoveInNo")).append(" : ").append(code).append("。");
        return stringBuffer.toString();
    }

    private String buildMoveOutDescription(String desc,String code){
        StringBuffer stringBuffer = null;
        if (desc != null) {
            stringBuffer = new StringBuffer(desc);
        } else {
            stringBuffer = new StringBuffer();
        }
        stringBuffer.append(" ").append(XiaMesssageResource.getMessage("page.stock003.stockBack.connectMoveOutNo")).append(" : ").append(code).append("。");
        return stringBuffer.toString();
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<Employee> getEmployeesWithUsers() {
        List<Employee> employees = employeeRepository.findAllWithUser();
        return employees;
    }

}
