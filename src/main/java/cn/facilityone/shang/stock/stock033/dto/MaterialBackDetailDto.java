package cn.facilityone.shang.stock.stock033.dto;

import java.util.Date;

/**
 * Author：panda.yang
 * CreateTime：2016/11/30 17:12
 */
public class MaterialBackDetailDto {

    private  Long materialId;
    private  String materialCode;
    private  String materialName;
    private  String displayRack;
    private  String materialBrand;
    private  String materialModel;
    private  String materialUnit;
    private Double changeNum;
    private Double backNum;
    private Double price;
    private Long materialMatchId;
    private String materialMatchChangeIds;

    private String vendorName;

    private String duedate;

    private String materialMatchDetail;
    //金额
    private Double amount;

    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getDuedate() {
        return duedate;
    }

    public void setDuedate(String duedate) {
        this.duedate = duedate;
    }

    public String getMaterialMatchDetail() {
        return materialMatchDetail;
    }

    public void setMaterialMatchDetail(String materialMatchDetail) {
        this.materialMatchDetail = materialMatchDetail;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public Long getMaterialMatchId() {
        return materialMatchId;
    }

    public void setMaterialMatchId(Long materialMatchId) {
        this.materialMatchId = materialMatchId;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getMaterialMatchChangeIds() {
        return materialMatchChangeIds;
    }

    public void setMaterialMatchChangeIds(String materialMatchChangeIds) {
        this.materialMatchChangeIds = materialMatchChangeIds;
    }

    public Double getBackNum() {
        return backNum;
    }

    public void setBackNum(Double backNum) {
        this.backNum = backNum;
    }

    public Double getChangeNum() {
        return changeNum;
    }

    public void setChangeNum(Double changeNum) {
        this.changeNum = changeNum;
    }

    public String getMaterialBrand() {
        return materialBrand;
    }

    public void setMaterialBrand(String materialBrand) {
        this.materialBrand = materialBrand;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public String getMaterialModel() {
        return materialModel;
    }

    public void setMaterialModel(String materialModel) {
        this.materialModel = materialModel;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit;
    }
}
