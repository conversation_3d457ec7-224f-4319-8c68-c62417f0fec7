package cn.facilityone.shang.stock.stock033.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.organize.org002.service.EmployeeService;
import cn.facilityone.shang.stock.stock033.dto.MaterialBackDetailDto;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by panda.yang on 2016/11/10.
 */
@Service
public class MaterialBackServiceImpl implements MaterialBackService {
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private CommonUserService employeeService;
    @Override
    public List<InventoryManagementActivity> findAllBacksInPage(DataTableRequest request) {
        StringBuilder sql = new StringBuilder("select ima " +
                " from InventoryManagementActivity ima " +
                " left join fetch ima.warehouse  " +
                " left join fetch ima.handles " +
                " left join fetch ima.managers " +
                " left join fetch ima.supervisor " +
                " left join fetch ima.organization,Warehouse wh" +
//                " left join fetch ima.warehouse.employees " +
                " where  wh.id=ima.warehouse.id and ima.type in (:type) and ima.deleted = 0 and ima.project=:projectId  ");
        List<Long> warehousesId = getLoginEmplyeeWarehouses();
        if(CollectionUtils.isNotEmpty(warehousesId)){
            sql.append(" and wh.id in (:warehouses) ");
        }else{
            sql.append(" and wh.id=:warehouses ");
        }
        List<Long> ids = getInventoryManagementActivityBacked();
        if(CollectionUtils.isNotEmpty(ids)){
            sql.append(" and ima.id not in (:actIds)");
        }
        this.buildSqlColumns(sql, "ima", request.getColumns());
        sql.append(" order by ima.createdDate desc");
        TypedQuery<InventoryManagementActivity> result = entityManager.createQuery(sql.toString(), InventoryManagementActivity.class);
        result.setParameter("type", this.getParamType());
        result.setParameter("projectId", ProjectContext.getCurrentProject());
        if(CollectionUtils.isNotEmpty(warehousesId)){
            result.setParameter("warehouses", warehousesId);
        }else{
            result.setParameter("warehouses", 0l);
        }
        if(CollectionUtils.isNotEmpty(ids)){
            result.setParameter("actIds", ids);
        }
        List<InventoryManagementActivity> activities = result.setFirstResult(request.getOffset()).setMaxResults(request.getPageSize()).getResultList();
        return activities;
    }

    /**
     * 列查询
     * @param sb
     * @param prefix
     * @param columns
     */
    private void buildSqlColumns (StringBuilder sb, String prefix, List<DataTableColumn> columns) {
        for (DataTableColumn column : columns) {
            String searchText = column.getSearchText();
            if (StringUtils.isNotEmpty(searchText)) {
                if (DataTableColumn.SEARCH_TYPE_DATE.equals(column.getSearchType())) {
                    sb.append(" and " + prefix + "." + column.getName() + " like '" + searchText + "%' ");
                } else if (DataTableColumn.SEARCH_TYPE_ENUM.equals(column.getSearchType())) {
                    InventoryManagementActivity.InventoryActivityType type = Enum.valueOf(InventoryManagementActivity.InventoryActivityType.class, searchText);
                    sb.append(" and " + prefix + "." + column.getName() + " = " + type.ordinal());
                } else {
                    sb.append(" and " + prefix + "." + column.getName() + " like '%" + searchText + "%'");
                }
            }
        }
    }

    public List<Long> getInventoryManagementActivityBacked(){
        StringBuilder sb = new StringBuilder();
        sb.append("select ima.activity_id " );
        sb.append( " from (SELECT mc.activity_id," );
        sb.append( " sum(mc.changeNum) AS changeNum," );
        sb.append( " sum(mc.backNum) AS backNum" );
        sb.append( " FROM materialbatch_change mc" );
        sb.append( " LEFT JOIN inventory_management_activity ima ON ima.ima_id = mc.activity_id" );
        sb.append( " WHERE mc.deleted = 0 and ima.proj_id=" + ProjectContext.getCurrentProject() );
        sb.append( " and mc.type in (1,2,9) "  );
        sb.append( " GROUP BY mc.activity_id) ima where backNum=changeNum ");
        sb.append( " UNION ALL SELECT ima_id " );
        sb.append( " FROM inventory_management_activity" );
        sb.append( " WHERE proj_id ="+ProjectContext.getCurrentProject()+"" );
        sb.append( " AND type IN (1, 2) AND ima_id NOT IN (" );
        sb.append( " SELECT ima.ima_id " );
        sb.append( " FROM inventory_management_activity ima " );
        sb.append( " LEFT JOIN materialbatch_activity ma ON ima.ima_id = ma.ima_id " );
        sb.append( " LEFT JOIN materialbatch mb ON mb.materialbatch_id = ma.materialbatch_id " );
        sb.append( " LEFT JOIN inventory inv ON inv.inventory_id=mb.inventory_id " );
        sb.append( " WHERE inv.deleted=0 AND ima.proj_id=" + ProjectContext.getCurrentProject()+" AND ima.type IN (1,2)) " );
        List<Object> list = entityManager.createNativeQuery(sb.toString()).getResultList();
        List<Long> tarlist = new ArrayList<Long>();
        for(Object obj : list){
             if(null != obj){
                 tarlist.add(Long.parseLong(obj.toString()));
             }
        }

        return tarlist;
    }

    public List<Long> getLoginEmplyeeWarehouses(){
        Long loginEm = employeeService.findCurrentEmployeeId();
        List<Long> tarlist = new ArrayList<Long>();
        if(null != loginEm){
            StringBuilder sb = new StringBuilder();
            sb.append("select we.warehouse_id from warehouse_em we left join warehouse w on w.warehouse_id=we.warehouse_id where w.deleted=0 and we.em_id= " + loginEm);
            List<Object> list = entityManager.createNativeQuery(sb.toString()).getResultList();
            for(Object obj : list){
                if(null != obj){
                    tarlist.add(Long.parseLong(obj.toString()));
                }
            }
        }
        return tarlist;
    }

    @Override
    public int findAllBacksCount(DataTableRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(ac.id) from InventoryManagementActivity ac ,Warehouse wh " +
                " where wh.id=ac.warehouse.id and ac.deleted = 0 and ac.project=:projectId and ac.type in (:type) ");
        List<Long> warehousesId = getLoginEmplyeeWarehouses();
        if(CollectionUtils.isNotEmpty(warehousesId)){
            sb.append(" and wh.id in (:warehouses) ");
        }else{
            sb.append(" and wh.id = :warehouses ");
        }
        List<Long> ids = getInventoryManagementActivityBacked();
        if(CollectionUtils.isNotEmpty(ids)){
            sb.append(" and ac.id not in (:actIds)");
        }
        this.buildSqlColumns(sb, "ac", request.getColumns());
        Query query = entityManager.createQuery(sb.toString())
                .setParameter("projectId", ProjectContext.getCurrentProject())
                .setParameter("type", this.getParamType());
        if(CollectionUtils.isNotEmpty(warehousesId)){
            query.setParameter("warehouses", warehousesId);
        }else{
            query.setParameter("warehouses", 0l);
        }
        if(CollectionUtils.isNotEmpty(ids)){
            query.setParameter("actIds", ids);
        }
        Object obj = query.getSingleResult();
        int total = 0;
        if(null != obj){
            total = Integer.parseInt(obj.toString());
        }
        return total;
    }

    @Override
    public List<MaterialBackDetailDto> findOutMaterialBatchChange(Long id) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ma.material_id,ma.material_code,ma.material_name,ma.brand,ma.model,ma.unit, " );
        sb.append("sum(mc.changeNum) as changeNum,sum(mc.backNum) as backNum,GROUP_CONCAT(mc.materialbatch_change_id) as materialMatchChangeIds, " );
        sb.append("mb.price,mb.materialbatch_id,inv.display_rack " );
        sb.append("from materialbatch_change mc " );
        sb.append("left join materialbatch mb on mc.materialbatch_id = mb.materialbatch_id " );
        sb.append("left join inventory inv on inv.inventory_id = mb.inventory_id " );
        sb.append("left join material ma on ma.material_id = inv.material_id " );
        sb.append("left join inventory_management_activity ima on ima.ima_id = mc.activity_id " );
        sb.append(" where inv.deleted=0 and mc.deleted = 0 and ima.ima_id = "+id);
        sb.append( " GROUP BY ma.material_id");

        List<Object[]> list = entityManager.createNativeQuery(sb.toString()).getResultList();
        List<MaterialBackDetailDto> detailDtos = new ArrayList<MaterialBackDetailDto>();
        MaterialBackDetailDto detailDto = null;
        for(Object[] obj : list){
            detailDto = new MaterialBackDetailDto();
            detailDto.setMaterialId(obj[0]==null?null:Long.parseLong(obj[0].toString()));
            detailDto.setMaterialCode(obj[1]==null?null:obj[1].toString());
            detailDto.setMaterialName(obj[2]==null?null:obj[2].toString());
            detailDto.setMaterialBrand(obj[3]==null?null:obj[3].toString());
            detailDto.setMaterialModel(obj[4]==null?null:obj[4].toString());
            detailDto.setMaterialUnit(obj[5]==null?null:obj[5].toString());
            detailDto.setChangeNum(DoubleOperationUtils.formatPointDecimal(obj[6]==null?0d:Double.parseDouble(obj[6].toString()),DoubleOperationUtils.POINT_TWO));
            detailDto.setBackNum(DoubleOperationUtils.formatPointDecimal(obj[7]==null?0d:Double.parseDouble(obj[7].toString()),DoubleOperationUtils.POINT_TWO));
            detailDto.setMaterialMatchChangeIds(obj[8]==null?null:obj[8].toString());
            detailDto.setPrice(DoubleOperationUtils.formatPointDecimal(obj[9]==null?0d:Double.parseDouble(obj[9].toString()),DoubleOperationUtils.POINT_TWO));
            detailDto.setMaterialMatchId(obj[10]==null?null:Long.parseLong(obj[10].toString()));
            detailDto.setDisplayRack(obj[11]==null?null:obj[11].toString());
            detailDtos.add(detailDto);
        }
        return detailDtos;
    }

    public List<InventoryManagementActivity.InventoryActivityType> getParamType(){
        List<InventoryManagementActivity.InventoryActivityType> types = new ArrayList<InventoryManagementActivity.InventoryActivityType>();
        types.add(InventoryManagementActivity.InventoryActivityType.OUT);
        types.add(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT);
//        types.add(InventoryManagementActivity.InventoryActivityType.MOVEOUT);
        return types;
    }
}
