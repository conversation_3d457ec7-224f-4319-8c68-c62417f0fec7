package cn.facilityone.shang.stock.stock033.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.stock.stock033.dto.MaterialBackDetailDto;

import java.util.List;

/**
 * Created by panda.yang on 2016/11/10.
 */
public interface MaterialBackService {

    List<InventoryManagementActivity> findAllBacksInPage(DataTableRequest request);

    int findAllBacksCount(DataTableRequest request);

    List<MaterialBackDetailDto> findOutMaterialBatchChange(Long id);
}
