package cn.facilityone.shang.stock.stock033.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.stock.stock033.dto.MaterialBackDetailDto;
import cn.facilityone.shang.stock.stock033.dto.MaterialBackDto;
import cn.facilityone.shang.stock.stock033.service.MaterialBackService;
import cn.facilityone.shang.stock.stock036.dto.ReserverDetailDto;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Path("/stock033")
public class MaterialBackResource {
    private static final String TEMPLATE_POSITION_PATH = "/business/stockV2/stock033-materialBack.ftl";

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private MaterialBackService materialBackService;

    @GET
    @Template(name = TEMPLATE_POSITION_PATH)
    public Map<String, Object> init() {
        Map<String, Object> map = new HashMap<>();
        XiaPrincipal user = (XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal();
        Long userId = user.getId();
        Employee employee = employeeRepository.findOneByUserId(userId);
        if(null != employee){
            map.put("employeeid", String.valueOf(employee.getId()));
            map.put("employeename", employee.getName());
        }
        return map;
    }

    @POST
    @Path("table")
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findReserves(DataTableRequest request) {
        List<InventoryManagementActivity> reserves = materialBackService.findAllBacksInPage(request);
        int totalCount = materialBackService.findAllBacksCount(request);
        return new DataTableResponse(reserves, totalCount, request.getPageNumber(), request.getDraw());
    }

    /**
     * 出库详情列表
     */
    @POST
    @Path("detailtable/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findetailtable(@PathParam("id") Long id) {
        List<MaterialBackDetailDto> reserves = materialBackService.findOutMaterialBatchChange(id);
        return new Result(reserves);
    }
}
