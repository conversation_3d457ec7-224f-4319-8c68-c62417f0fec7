package cn.facilityone.shang.stock.stock008.service;

import cn.facilityone.shang.asset.asset002.repository.QRCodeRepository;
import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.entity.common.QRCode;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.stock.common.repository.InventoryRepository;
import cn.facilityone.shang.stock.stock002.dto.InventoryTableDTO;
import cn.facilityone.shang.stock.stock002.dto.MaterialsExportDto;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import cn.facilityone.xia.transfer.core.Exporter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.criteria.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.*;

/**
 * Created by leo on 2017/12/27.
 */
@Service
public class InventoryQueryServiceImpl implements InventoryQueryService {

    @Autowired
    private EntityManager entityManager;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private UploadFileService uploadFileService;
    @Autowired
    private QRCodeRepository qrCodeRepository;



    /**
     * table列表
     * @param request
     * @return
     */
    @Override
    @XiaTransactional(readOnly = true)
    public InventoryTableDTO findInventoryPage(DataTableRequest request) {
        InventoryTableDTO inventoryTableDTO= new InventoryTableDTO();

        boolean b1 = true;
        boolean b2 = true;
        boolean b3 = true;
        boolean b4 = true;
        boolean b5 = true;
        boolean b6 = true;
        boolean b7 = true;
        boolean b8 = true;
        boolean b9 = true;
        boolean b10 = true;
        String search1 = request.getColumns().get(0).getSearchText();
        if (search1.equals("")){
            b1=false;
        }
        String search2 = request.getColumns().get(1).getSearchText();
        if (search2.equals("")){
            b2=false;
        }
        String search3 = request.getColumns().get(2).getSearchText();
        if (search3.equals("")){
            b3=false;
        }
        String search4 = request.getColumns().get(3).getSearchText();
        if (search4.equals("")){
            b4=false;
        }
        String search5 = request.getColumns().get(4).getSearchText();
        if (search5.equals("")){
            b5=false;
        }
        String search6 = request.getColumns().get(5).getSearchText();
        if (search6.equals("")){
            b6=false;
        }
        String search7 = request.getColumns().get(6).getSearchText();
        if (search7.equals("") ){
            b7=false;
        }
        String search8 = request.getColumns().get(7).getSearchText();
        if (search8.equals("") || !NumberUtils.isDigits(search8)){
            b8=false;
        }
        String search9 = request.getColumns().get(8).getSearchText();
        if (search9.equals("") || !NumberUtils.isDigits(search9)){
            b9=false;
        }
        String search10 = request.getColumns().get(9).getSearchText();
        if (search10.equals("") || !NumberUtils.isDigits(search10)){
            b10=false;
        }
        StringBuilder sql = new StringBuilder(650);
        sql.append("SELECT  distinct w.warehouse_name,m.material_code, m.material_name, m.brand, m.model,i.total_invamount,i.locak_amount,i.amount_min,i.inventory_id,i.display_rack,m.unit " +
                "FROM inventory i " +
                "LEFT JOIN material m ON m.material_id = i.material_id " +
                "LEFT JOIN warehouse w ON i.warehouse_id = w.warehouse_id " +
                "LEFT JOIN warehouse_em we ON w.warehouse_id = we.warehouse_id " +
                "LEFT JOIN em e ON we.em_id = e.em_id " +
                "WHERE i.proj_id="+ ProjectContext.getCurrentProject()+" AND i.deleted=0 AND m.deleted=0 AND w.deleted=0 ");
        if (b1) {
            sql.append(" AND w.warehouse_name LIKE '%" + search1 + "%'");
        }
        if (b2) {
            sql.append(" AND i.display_rack LIKE '%" + search2 + "%'");
        }
        if (b3) {
            sql.append(" AND m.material_code LIKE '%" + search3 + "%'");
        }
        if (b4) {
            sql.append(" AND m.material_name LIKE '%" + search4 + "%'");
        }
        if (b5) {
            sql.append(" AND m.brand LIKE '%" + search5 + "%'");
        }
        if (b6) {
            sql.append(" AND m.model LIKE '%" + search6 + "%'");
        }
        if (b8) {
            sql.append(" AND i.total_invamount= "+search8+"");
        }
        if (b9) {
            sql.append(" AND i.locak_amount= "+search9+"");
        }
        if (b10) {
            sql.append(" AND i.amount_min= "+search10+"");
        }
        if (b7) {
            sql.append(" AND m.unit LIKE '%" + search7 + "%'");
        }
        sql.append(" order by i.inventory_id desc");
        sql.append(" LIMIT "+request.getOffset()+","+request.getPageSize()+"");

        Query query = entityManager.createNativeQuery(sql.toString());
        List<Object> objects = query.getResultList();
        for (Object o : objects) {
            Object [] results = (Object [])o;
            InventoryTableDTO.Data data = new InventoryTableDTO.Data();
            data.setWarehouseName((String)results[0]);
            data.setCode((String)results[1]);
            data.setInventoryName((String)results[2]);
            data.setBrand((String)results[3]);
            data.setModel((String)results[4]);
            if (results[5] != null) {
                data.setTotalInvAmount(String.valueOf(DoubleOperationUtils.changeDecimal((Double) results[5], 2)));
            }
            if (results[6] != null) {
                data.setLockAmount(String.valueOf(DoubleOperationUtils.changeDecimal((Double) results[6],2)));
            }
            if (results[7] != null) {
                data.setMinAmount(String.valueOf(DoubleOperationUtils.changeDecimal((Double) results[7],2)));
            }
            BigInteger object = (BigInteger) results[8];
            data.setId(object.longValue());
            data.setDisplayRack((String) results[9]);
            if (results[10] != null) {
                data.setUnit((String) results[10]);
            }
            inventoryTableDTO.getDatas().add(data);
        }

        StringBuilder countSql = new StringBuilder(650);
        countSql.append("SELECT COUNT( distinct i.inventory_id) AS count " +
                "FROM inventory i " +
                "LEFT JOIN material m ON m.material_id = i.material_id " +
                "LEFT JOIN warehouse w ON i.warehouse_id = w.warehouse_id " +
                "LEFT JOIN warehouse_em we ON w.warehouse_id = we.warehouse_id " +
                "LEFT JOIN em e ON we.em_id = e.em_id " +
                "WHERE i.proj_id="+ProjectContext.getCurrentProject()+" AND i.deleted=0 AND m.deleted=0 AND w.deleted=0");
        if (b1) {
            countSql.append(" AND w.warehouse_name LIKE '%" + search1 + "%'");
        }
        if (b2) {
            countSql.append(" AND i.display_rack LIKE '%" + search2 + "%'");
        }
        if (b3) {
            countSql.append(" AND m.material_code LIKE '%" + search3 + "%'");
        }
        if (b4) {
            countSql.append(" AND m.material_name LIKE '%" + search4 + "%'");
        }
        if (b5) {
            countSql.append(" AND m.brand LIKE '%" + search5 + "%'");
        }
        if (b6) {
            countSql.append(" AND m.model LIKE '%" + search6 + "%'");
        }
        if (b8) {
            countSql.append(" AND i.total_invamount= "+search8+"");
        }
        if (b9) {
            countSql.append(" AND i.locak_amount= "+search9+"");
        }
        if (b10) {
            countSql.append(" AND i.amount_min= "+search10+"");
        }
        if (b7) {
            sql.append(" AND m.unit LIKE '%" + search7 + "%'");
        }
        Query countQuery = entityManager.createNativeQuery(countSql.toString());
        BigInteger object = (BigInteger) countQuery.getSingleResult();
        inventoryTableDTO.setTotalPage(object.intValue());

        return inventoryTableDTO;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<MaterialsExportDto> getMaterials(DataTableRequest request) {
        List<MaterialsExportDto> materialExportDtos = this.findInventoryExport(request);
        return materialExportDtos;
    }

    private List<MaterialsExportDto> findInventoryExport(DataTableRequest request) {
        List<MaterialsExportDto> materialsExportDtos= new ArrayList<>();
        List<String> idsStrs = request.getConditions().get(0).getValues();
        List<Long> ids = new ArrayList<>();
        for (String idsStr : idsStrs) {
            ids.add(Long.parseLong(idsStr));
        }

        boolean b1 = true;
        boolean b2 = true;
        boolean b3 = true;
        boolean b4 = true;
        boolean b5 = true;
        boolean b6 = true;
        boolean b7 = true;
        boolean b8 = true;
        boolean b9 = true;
        boolean b10 = true;
        String search1 = request.getColumns().get(0).getSearchText();
        if (search1.equals("")){
            b1=false;
        }
        String search2 = request.getColumns().get(1).getSearchText();
        if (search2.equals("")){
            b2=false;
        }
        String search3 = request.getColumns().get(2).getSearchText();
        if (search3.equals("")){
            b3=false;
        }
        String search4 = request.getColumns().get(3).getSearchText();
        if (search4.equals("")){
            b4=false;
        }
        String search5 = request.getColumns().get(4).getSearchText();
        if (search5.equals("")){
            b5=false;
        }
        String search6 = request.getColumns().get(5).getSearchText();
        if (search6.equals("")){
            b6=false;
        }
        String search7 = request.getColumns().get(6).getSearchText();
        if (search7.equals("") ){
            b7=false;
        }
        String search8 = request.getColumns().get(7).getSearchText();
        if (search8.equals("") || !NumberUtils.isDigits(search8)){
            b8=false;
        }
        String search9 = request.getColumns().get(8).getSearchText();
        if (search9.equals("") || !NumberUtils.isDigits(search9)){
            b9=false;
        }
        String search10 = request.getColumns().get(9).getSearchText();
        if (search10.equals("") || !NumberUtils.isDigits(search10)){
            b10=false;
        }
        StringBuilder sql = new StringBuilder(650);
        sql.append("SELECT  distinct w.warehouse_name,m.material_code, m.material_name, m.brand, m.model,i.total_invamount,i.locak_amount,i.amount_min,i.inventory_id,i.display_rack,m.unit " +
                "FROM inventory i " +
                "LEFT JOIN material m ON m.material_id = i.material_id " +
                "LEFT JOIN warehouse w ON i.warehouse_id = w.warehouse_id " +
                "LEFT JOIN warehouse_em we ON w.warehouse_id = we.warehouse_id " +
                "LEFT JOIN em e ON we.em_id = e.em_id " +
                "WHERE i.proj_id="+ ProjectContext.getCurrentProject()+" AND i.deleted=0 AND m.deleted=0 AND w.deleted=0 ");
        if (b1) {
            sql.append(" AND w.warehouse_name LIKE '%" + search1 + "%'");
        }
        if (b2) {
            sql.append(" AND i.display_rack LIKE '%" + search2 + "%'");
        }
        if (b3) {
            sql.append(" AND m.material_code LIKE '%" + search3 + "%'");
        }
        if (b4) {
            sql.append(" AND m.material_name LIKE '%" + search4 + "%'");
        }
        if (b5) {
            sql.append(" AND m.brand LIKE '%" + search5 + "%'");
        }
        if (b6) {
            sql.append(" AND m.model LIKE '%" + search6 + "%'");
        }
        if (b8) {
            sql.append(" AND i.total_invamount= "+search8+"");
        }
        if (b9) {
            sql.append(" AND i.locak_amount= "+search9+"");
        }
        if (b10) {
            sql.append(" AND i.amount_min= "+search10+"");
        }
        if (b7) {
            sql.append(" AND m.unit LIKE '%" + search7 + "%'");
        }
        if(ids.size()>0){
            sql.append(" AND i.inventory_id in "+listToString(ids)+"");
        }
        sql.append(" order by i.inventory_id desc");

        Query query = entityManager.createNativeQuery(sql.toString());
        List<Object> objects = query.getResultList();
        for (Object o : objects) {
            Object [] results = (Object [])o;
            MaterialsExportDto materialsExportDto = new MaterialsExportDto();
            materialsExportDto.setWarehouseName((String)results[0]);
            materialsExportDto.setCode((String)results[1]);
            materialsExportDto.setMaterialName((String) results[2]);
            materialsExportDto.setBrand((String)results[3]);
            materialsExportDto.setModel((String)results[4]);
            materialsExportDto.setUnit((String)results[10]);
            materialsExportDto.setDisplayRack((String) results[9]);

            if (results[5] != null) {
                materialsExportDto.setAmount(String.valueOf(results[5]));
            }
            if (results[6] != null) {
                materialsExportDto.setLockNumber(String.valueOf(results[6]));
            }
            if (results[7] != null) {
                materialsExportDto.setMinAmount(String.valueOf(results[7]));
            }
            materialsExportDtos.add(materialsExportDto);
        }

        return materialsExportDtos;
    }
    /**
     * 把List转化成字符串形式
     * @param list
     * @return
     */
    public String listToString(List<Long> list) {
        StringBuilder result = new StringBuilder("(");
        for (int i = 0; i < list.size(); i++) {
            if (i == list.size() - 1) {
                result.append(list.get(i));
            } else {
                result.append(list.get(i) + ",");
            }
        }
        result.append(")");
        return result.toString();
    }

    @Override
    @XiaTransactional(readOnly = true)
    public ByteArrayOutputStream outputQRCode(final DataTableRequest dataTableRequest, HttpServletRequest request, HttpServletResponse response, final List ids) throws IOException {
        // 获取所有文件
        List<Inventory> inventorylist = inventoryRepository.findAll(new XiaSpecification<Inventory>() {
            @Override
            public Root<Inventory> toRoot(Root<Inventory> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                return root;
            }

            @Override
            public Predicate toPredicate(Root<Inventory> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {

                List<Predicate> predicateArrayList=new ArrayList<>();

                From<?, ?> frommaterial = root.join("material", JoinType.LEFT);
                From<?, ?> fromWarehouse = root.join("warehouse", JoinType.LEFT);
//                From<?, ?> warehouseem = fromWarehouse.join("employees", JoinType.LEFT);
                List<DataTableColumn> dataTableColumnList =dataTableRequest.getColumns();

                for(DataTableColumn dataTableColumn:dataTableColumnList){

                    String  searchText=dataTableColumn.getSearchText();

                    if("".equals(searchText)){
                        continue;
                    }

                    if("warehouseName".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.like(fromWarehouse.<String>get("name"),"%"+searchText +"%"));
                    }else if("code".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.like(frommaterial.<String>get("code"),"%"+searchText +"%"));

                    }else if("inventoryName".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.like(frommaterial.<String>get("name"),"%"+searchText+"%"));

                    }else if("brand".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.like(frommaterial.<String>get("brand"),"%"+searchText +"%"));

                    }else if("model".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.like(frommaterial.<String>get("model"),"%"+searchText +"%"));

                    }else if("totalInvAmount".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.equal(root.<String>get("totalInvAmount"),searchText));

                    }else if("lockAmount".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.equal(root.get("lockAmount"),searchText ));

                    }else if("minAmount".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.equal(root.<String>get("minAmount"),searchText ));
                    }else if("displayRack".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.equal(root.<String>get("displayRack"),searchText ));
                    }else if("unit".equals(dataTableColumn.getName())){
                        predicateArrayList.add(criteriaBuilder.equal(frommaterial.<String>get("unit"),searchText ));
                    }
                }
                if(ids.size()>0){
                    predicateArrayList.add(root.<String>get("id").in(ids));

                }
                predicateArrayList.add(criteriaBuilder.equal(root.get("project"),ProjectContext.getCurrentProject()));
                predicateArrayList.add(criteriaBuilder.equal(root.get("deleted"),false));
                predicateArrayList.add(criteriaBuilder.equal(fromWarehouse.get("deleted"),false));
//                predicateArrayList.add(criteriaBuilder.equal(warehouseem.get("id"), commonUserService.findLoginEmployee()==null?-1:commonUserService.findLoginEmployee().getId()));
                criteriaQuery.where(predicateArrayList.toArray(new Predicate[predicateArrayList.size()]));

                return criteriaQuery.getRestriction();
            }
        });
        if (CollectionUtils.isNotEmpty(inventorylist)) {
            List<String> filePaths = new ArrayList<>();
            Map<String, Integer> hasFile = new LinkedHashMap<>();
            for (Inventory inventory : inventorylist) {
                QRCode qrCode= qrCodeRepository.findByTableNameAndPKeyId(Inventory.class.getSimpleName(), inventory.getId().toString());
                if (qrCode != null) {
                    String filePath =
                            uploadFileService.getFilePath(qrCode);
                    String newPath =uploadFileService.getPath(qrCode)+
                            FileUtil.fileNameRebuild(inventory.getWarehouse().getName()+ SystemConst.STR_ULINE+inventory.getMaterial().getCode());
                    if (hasFile.containsKey(newPath)) {
                        int in = hasFile.get(newPath);
                        in = in + 1;
                        hasFile.put(newPath, in);

                        newPath = newPath + SystemConst.STR_MINUS + in;
                    } else {
                        hasFile.put(newPath, 0);
                    }
                    newPath = newPath + ".png";
                    FileUtil.copyFile(filePath, newPath);
                    filePaths.add(newPath);
                }
            }
            String outputFileName = "物资-二维码";
            Random r = new Random();
            String zipFileName = outputFileName + "-" + r.nextInt(10000);
            String zipFilePath = FileUtil.createDataPath(uploadFileService.getBaseDir(), new Date()) + File.separatorChar;
            // 压缩文件
            FileUtil.fileToZip(filePaths, zipFilePath, zipFileName, true);

            File file =new File(zipFilePath + zipFileName + ".zip");
            ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();

            if(file.exists()){
                FileInputStream fileInputStream =new FileInputStream(zipFilePath + zipFileName + ".zip");
                int n=0;
                while ((n=fileInputStream.read())!=-1){
                    byteArrayOutputStream.write(n);
                }
                fileInputStream.close();
            }

            for (String files : filePaths) {
                FileUtil.removeFile(files);
            }


            return byteArrayOutputStream;

        }

        return  new ByteArrayOutputStream();

    }

}
