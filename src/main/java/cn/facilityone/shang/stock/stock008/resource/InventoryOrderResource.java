package cn.facilityone.shang.stock.stock008.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.common.util.LocationUtil;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderSpace;
import cn.facilityone.shang.stock.stock002.dto.InventoryTableDTO;
import cn.facilityone.shang.stock.stock002.dto.MaterialsExportDto;
import cn.facilityone.shang.stock.stock002.service.InventoryService;
import cn.facilityone.shang.stock.stock008.service.InventoryQueryService;
import cn.facilityone.shang.workorder.common.constant.WorkOrderConstant;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.transfer.core.Exporter;
import cn.facilityone.xia.transfer.core.data.DTO;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@Path("/stock008")
public class InventoryOrderResource {
    private static final String TEMPLATE_POSITION_PATH = "/business/stockV2/stock008-inventoryQuery.ftl";

    @Autowired
    private InventoryQueryService inventoryQueryService;
    @Autowired
    private InventoryService inventoryService;
    @Context
    private HttpServletRequest request;
    @Context
    private HttpServletResponse response;
    @Autowired
    @Qualifier("xlsxExporter")
    private Exporter exporter;

    @GET
    @Template(name = TEMPLATE_POSITION_PATH)
    public Map<String, Object> init() {
        return new HashMap<>();
    }


    @POST
    @Path("table")
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findReserves(DataTableRequest request) {
        InventoryTableDTO inventoryTableDTO = inventoryQueryService.findInventoryPage(request);
        return new DataTableResponse(inventoryTableDTO.getTotalPage(), inventoryTableDTO.getDatas());

    }
    @POST
    @Path("inventorys/export/materials")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response exportMaterials(DataTableRequest request) {
        List<MaterialsExportDto> data = inventoryQueryService.getMaterials(request);

        LinkedHashMap<String, String> title = new LinkedHashMap<String, String>();
        title.put("warehouseName",  XiaMesssageResource.getMessage("InventoryManagementActivity.warehouseName")+XiaMesssageResource.getMessage("page.stock002.required"));
        title.put("displayRack",  XiaMesssageResource.getMessage("Inventory.displayRack"));
        title.put("code",  XiaMesssageResource.getMessage("InventoryManagementActivity.materialCode")+XiaMesssageResource.getMessage("page.stock002.required"));
        title.put("materialName",  XiaMesssageResource.getMessage("InventoryManagementActivity.materialName")+XiaMesssageResource.getMessage("page.stock002.required"));
        title.put("brand",  XiaMesssageResource.getMessage("Material.brand"));
        title.put("model",  XiaMesssageResource.getMessage("Material.model"));
        title.put("unit",  XiaMesssageResource.getMessage("Material.unit")+XiaMesssageResource.getMessage("page.stock002.required"));
        title.put("amount",  XiaMesssageResource.getMessage("page.stock002.amount"));
        title.put("lockNumber",  XiaMesssageResource.getMessage("MaterialBatch.lockNum"));
        title.put("minAmount",  XiaMesssageResource.getMessage("Inventory.minAmount"));

        String companyName = XiaMesssageResource.getMessage("page.stock008.material.list");

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("companyName", companyName);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        map.put("year", sdf.format(date));
        DTO dto = new DTO(map);
        dto.addTitleAndData(title, data);
        cn.facilityone.xia.transfer.core.data.Template tpl = new cn.facilityone.xia.transfer.core.data.Template(3L, "static/tpl/template_month.xlsx");
        ByteArrayOutputStream out = (ByteArrayOutputStream) exporter.export(dto,tpl);
        return Response.ok( new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.success.export"),
                FileUtil.exportFile(out, FileUtil.FILE_TYPE_EXCELX))).build();
    }
    @POST
    @Path("inventorys/outputqrcode")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response outputQRcode(DataTableRequest dataTableRequest) throws IOException {
        List<String> idsStrs = dataTableRequest.getConditions().get(0).getValues();
        List<Long> ids = new ArrayList<>();
        for (String idsStr : idsStrs) {
            ids.add(Long.parseLong(idsStr));
        }
            ByteArrayOutputStream byteArrayOutputStream=inventoryQueryService.outputQRCode(dataTableRequest,request, response,ids);
        if(byteArrayOutputStream.size()==0){
            return Response.ok(new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.notfound.qrcode"),
                    "")).build();
        }
        return Response.ok( new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.success.export"),
                FileUtil.exportFile(byteArrayOutputStream, ".zip"))).build();

    }


}
