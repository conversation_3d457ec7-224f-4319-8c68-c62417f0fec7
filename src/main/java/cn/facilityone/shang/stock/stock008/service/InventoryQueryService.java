package cn.facilityone.shang.stock.stock008.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.stock.stock002.dto.InventoryTableDTO;
import cn.facilityone.shang.stock.stock002.dto.MaterialsExportDto;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Created by leo on 2017/12/27.
 */
public interface InventoryQueryService {
    /**
     * table列表
     * @param request
     * @return
     */

    InventoryTableDTO findInventoryPage(DataTableRequest request);
    /**
     * 物资导出
     */
    List<MaterialsExportDto> getMaterials(DataTableRequest request);

    /**
     * 二维码导出
     * @param dataTableRequest
     * @param request
     * @param response
     * @param ids
     * @return
     * @throws IOException
     */
    ByteArrayOutputStream outputQRCode (DataTableRequest dataTableRequest, HttpServletRequest request, HttpServletResponse response, List ids) throws IOException;



}
