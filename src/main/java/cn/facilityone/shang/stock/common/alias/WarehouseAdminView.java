package cn.facilityone.shang.stock.common.alias;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

public class WarehouseAdminView {

    private Long whId;
    private String adminName;
    private Long adminId;
    
    public static Map<Long,String> convert(List<WarehouseAdminView> views){
        Map<Long,String> res = new HashMap<>();
        if(CollectionUtils.isNotEmpty(views)){
            String an = null;
            for(WarehouseAdminView view : views){
                an = "";
                if(res.containsKey(view.getWhId())){
                    an = res.get(view.getWhId());
                }
                if(an == ""){
                    an += view.getAdminName();
                    res.put(view.getWhId(), an);
                }else {
                    an += ","+view.getAdminName();
                    res.put(view.getWhId(), an);
                }
            }
        }
        return res;
    }
    
    public static Map<Long,List<WarehouseAdminView>> convertAll(List<WarehouseAdminView> views){
        Map<Long,List<WarehouseAdminView>> res = new HashMap<>();
        if(CollectionUtils.isNotEmpty(views)){
            for(WarehouseAdminView view : views){
                if(res.containsKey(view.getWhId())){
                    res.get(view.getWhId()).add(view);
                }else{
                    List<WarehouseAdminView> wavL = new LinkedList<>();
                    wavL.add(view);
                    res.put(view.getWhId(), wavL);
                }
            }
        }
        return res;
    }


    public Long getWhId() {
        return whId;
    }


    public void setWhId(Long whId) {
        this.whId = whId;
    }


    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }


    public Long getAdminId() {
        return adminId;
    }


    public void setAdminId(Long adminId) {
        this.adminId = adminId;
    }

}
