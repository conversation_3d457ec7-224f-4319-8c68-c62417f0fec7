package cn.facilityone.shang.stock.common.mapper;

import cn.facilityone.shang.stock.common.alias.InventoryAlias;
import cn.facilityone.shang.stock.stock009.dto.StockReportDTO;
import cn.facilityone.xia.persistence.mybatis.XiaMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/12/20
 */
@Mapper
public interface InventoryMapper extends XiaMapper<InventoryAlias> {

    Date findMinYearMonthReport(@Param("projId") Long projId);

    List<StockReportDTO> findInventoryInfo(@Param("projId") Long id, @Param("endDate") String endDate);
}
