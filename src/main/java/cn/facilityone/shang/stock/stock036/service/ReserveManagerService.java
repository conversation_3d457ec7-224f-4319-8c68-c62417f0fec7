package cn.facilityone.shang.stock.stock036.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.stock.stock036.dto.ReserverDetailDto;
import cn.facilityone.shang.stock.stock036.dto.ReverseManagerDto;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * Author：panda.yang
 * CreateTime：2016/11/24 11:38
 */
public interface ReserveManagerService {

    List<ReverseManagerDto> findAllReservesInPage(DataTableRequest request, List<InventoryManagementActivity.InventoryActivityType> types, String logintype);

    int findAllReservesCount(DataTableRequest request, List<InventoryManagementActivity.InventoryActivityType> types, String logintype);

    void beoutlibStock(Long id, String content);

    void rejectedStock(Long id, String content);

    List<ReserverDetailDto> findDetailReserve(Long id);

    public void sendMessage(Long imaId, String statusString);

    /**
     * 更具物资编码和仓库id判断库存记录是否存在待审核、待出库的预定单
     * @param materialCode 物资编码
     * @param warehouseId 仓库id
     * @return
     */
    boolean isExitsReserveInventoryManagementActivity(String materialCode, Long warehouseId);

    List<ReverseManagerDto> findReservesManagersInPage(DataTableRequest request, String logintype);
}
