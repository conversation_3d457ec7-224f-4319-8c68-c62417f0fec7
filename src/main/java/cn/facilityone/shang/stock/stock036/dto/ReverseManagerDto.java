package cn.facilityone.shang.stock.stock036.dto;

import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.Organization;
import cn.facilityone.shang.entity.workorder.WorkOrder;

import java.util.Date;

/**
 * Author：panda.yang
 * CreateTime：2016/11/24 11:22
 */
public class ReverseManagerDto {

    private Long id;

    private String activityNo;

    //预定人
    private String handlesName;

    private String woCode;

    private String activityType;

    private Date createdDate;

    private Date operateDate;

    private String warehouseName;

    private Long warehouseId;

    private Employee managers;

    private Employee handles;

    private Employee supervisor;

    private Organization organization;

    private String remarks;

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Employee getHandles() {
        return handles;
    }

    public void setHandles(Employee handles) {
        this.handles = handles;
    }

    public Employee getManagers() {
        return managers;
    }

    public void setManagers(Employee managers) {
        this.managers = managers;
    }

    public Employee getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(Employee supervisor) {
        this.supervisor = supervisor;
    }

    private InventoryManagementActivity.InventoryActivityType type;

    public InventoryManagementActivity.InventoryActivityType getType() {
        return type;
    }

    public void setType(InventoryManagementActivity.InventoryActivityType type) {
        this.type = type;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getActivityNo() {
        return activityNo;
    }

    public void setActivityNo(String activityNo) {
        this.activityNo = activityNo;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getHandlesName() {
        return handlesName;
    }

    public void setHandlesName(String handlesName) {
        this.handlesName = handlesName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getWoCode() {
        return woCode;
    }

    public void setWoCode(String woCode) {
        this.woCode = woCode;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
