package cn.facilityone.shang.stock.stock036.dto;

/**
 * Author：panda.yang
 * CreateTime：2016/11/25 20:31
 */
public class ReserverDetailDto {
    //批次调整id
    private  Long id;
    //库存id
    private  Long inventoryId;
    //物资编码
    private String materialCode;
    //物资展架
    private String displayRack;
    //物资名称
    private String materialName;
    //品牌
    private String brand;
    //型号
    private String model;
    //单位
    private String unit;
    //价格
    private double price;
    //有效数量(初始数量)
    private double amount;
    //锁定数量(已预定数量)
    private double lockAmount;
    //预定数量
    private double resAmount;
    //最低库存
    private double minAmount;
    //物资是否删除
    private boolean deleted = false;

    private double totalInvAmount;
    //备注
    private double remarks;

    public ReserverDetailDto() {}

    public ReserverDetailDto(double amount, String brand, Long id, double lockAmount, String materialCode, String materialName, String model,
                             double resAmount, String unit, Long inventoryId, double price, double minAmount, boolean deleted,double totalInvAmount) {
        this.amount = amount;
        this.brand = brand;
        this.id = id;
        this.lockAmount = lockAmount;
        this.materialCode = materialCode;
        this.materialName = materialName;
        this.model = model;
        this.resAmount = resAmount;
        this.unit = unit;
        this.inventoryId = inventoryId;
        this.price = price;
        this.minAmount = minAmount;
        this.deleted=deleted;
        this.totalInvAmount=totalInvAmount;
    }

    public ReserverDetailDto(double amount, String brand, Long id, double lockAmount, String materialCode, String materialName, String model,
                             double resAmount, String unit, Long inventoryId, double price, double minAmount, boolean deleted,double totalInvAmount,String displayRack) {
        this.amount = amount;
        this.brand = brand;
        this.id = id;
        this.lockAmount = lockAmount;
        this.materialCode = materialCode;
        this.materialName = materialName;
        this.model = model;
        this.resAmount = resAmount;
        this.unit = unit;
        this.inventoryId = inventoryId;
        this.price = price;
        this.minAmount = minAmount;
        this.deleted=deleted;
        this.totalInvAmount=totalInvAmount;
        this.displayRack = displayRack;
    }


    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }

    public double getTotalInvAmount() {
        return totalInvAmount;
    }

    public void setTotalInvAmount(double totalInvAmount) {
        this.totalInvAmount = totalInvAmount;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public double getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(double minAmount) {
        this.minAmount = minAmount;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public double getLockAmount() {
        return lockAmount;
    }

    public void setLockAmount(double lockAmount) {
        this.lockAmount = lockAmount;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public double getResAmount() {
        return resAmount;
    }

    public void setResAmount(double resAmount) {
        this.resAmount = resAmount;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public double getRemarks() {
        return remarks;
    }

    public void setRemarks(double remarks) {
        this.remarks = remarks;
    }
}
