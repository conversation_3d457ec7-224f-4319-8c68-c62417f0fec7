package cn.facilityone.shang.stock.stock036.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.message.MessageSenderTool;
import cn.facilityone.shang.common.component.message.template.MessageStockReserveApprovalTemplate;
import cn.facilityone.shang.common.component.message.template.MessageStockReservePasslTemplate;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.staticmetamodel.MobilePush_;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.inventory.InventoryActivity;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.Warehouse;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.inventory.respository.InventoryActivityRepository;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.stock.common.repository.InventoryManagementActivityRepository;
import cn.facilityone.shang.stock.common.repository.MaterialBatchChangeRepository;
import cn.facilityone.shang.stock.stock003.service.InventoryManagementActivityService;
import cn.facilityone.shang.stock.stock036.dto.ReserverDetailDto;
import cn.facilityone.shang.stock.stock036.dto.ReverseManagerDto;
import cn.facilityone.xia.core.exception.BusinessException;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.message.common.MessageType;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author：panda.yang
 * CreateTime：2016/11/24 11:39
 */
@Service
public class ReserveManagerServiceImpl implements ReserveManagerService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private InventoryManagementActivityRepository inventoryManagementActivityRepository;

    @Autowired
    private InventoryActivityRepository inventoManagementActivity;

    @Autowired
    private InventoryManagementActivityService inventoryManagementActivityService;

    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private CommonUserService userService;

    @Autowired
    private MessageSenderTool messageSenderTool;

    @Autowired
    private MaterialBatchChangeRepository materialBatchChangeRepository;

    @Override
    public List<ReverseManagerDto> findAllReservesInPage(DataTableRequest request, List<InventoryManagementActivity.InventoryActivityType> types, String logintype) {
        Long emId = userService.findCurrentEmployeeId();
        StringBuilder sql = new StringBuilder("select ac from InventoryManagementActivity ac ");
        sql.append(" left join fetch ac.warehouse ");
        sql.append(" left join fetch ac.managers  ");
        sql.append(" left join fetch ac.supervisor  ");
        sql.append(" left join fetch ac.handles  ");
        sql.append(" left join fetch ac.organization  ");
        sql.append(" where ac.deleted = 0 and ac.project=:projectId ");
        sql.append(" and ac.type in (:type) and ac." + logintype + ".id=:supId");
        if(InventoryManagementActivity.MANAGERS.equals(logintype)){
            sql.append(" and ac.connectNo is null ");
        }
        this.buildSqlColumns(sql, "ac" ,request.getColumns());
        sql.append(" order by ac.type asc,ac.operateDate desc");
        TypedQuery<InventoryManagementActivity> result = entityManager.createQuery(sql.toString(), InventoryManagementActivity.class);
        result.setParameter("projectId", ProjectContext.getCurrentProject());
        result.setParameter("type", types);
        //主管、仓库管理员审批、我的预定
        result.setParameter("supId", emId);
        List<InventoryManagementActivity> inventoryList = result.setFirstResult(request.getOffset()).setMaxResults(request.getPageSize()).getResultList();

        List<ReverseManagerDto> dtos = new ArrayList<ReverseManagerDto>();
        ReverseManagerDto managerDto = null;
        for (InventoryManagementActivity activity : inventoryList) {
            managerDto = new ReverseManagerDto();
            managerDto.setId(activity.getId());
            managerDto.setActivityNo(activity.getNo());
            managerDto.setActivityType(activity.getType().getType());
            managerDto.setCreatedDate(activity.getCreatedDate());
            managerDto.setOperateDate(activity.getOperateDate());
            managerDto.setRemarks(activity.getRemarks());
            //已出库
            if(null != activity.getConnectNo()){
                managerDto.setType(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT);
            }else{
                managerDto.setType(activity.getType());
            }
            managerDto.setSupervisor(activity.getSupervisor());
            managerDto.setManagers(activity.getManagers());
            managerDto.setOrganization(activity.getOrganization());
            Warehouse warehouse = activity.getWarehouse();
            if (null != warehouse) {
                managerDto.setWarehouseName(warehouse.getName());
                managerDto.setWarehouseId(warehouse.getId());
            }
            Employee handles = activity.getHandles();
            managerDto.setHandles(handles);
            if (null != handles) {
                managerDto.setHandlesName(handles.getName());
            }
            if (null != activity.getPkeyId()) {
                WorkOrder wo = workOrderRepository.findOne(Long.parseLong(activity.getPkeyId()));
                managerDto.setWoCode(wo.getCode());
            }
            dtos.add(managerDto);
        }
        return dtos;
    }

    /**
     * 列查询
     * @param sb
     * @param prefix
     * @param columns
     */
    private void buildSqlColumns (StringBuilder sb, String prefix, List<DataTableColumn> columns) {
        for (DataTableColumn column : columns) {
            String searchText = column.getSearchText();
            if (StringUtils.isNotEmpty(searchText)) {
                if (DataTableColumn.SEARCH_TYPE_DATE.equals(column.getSearchType())) {
                    sb.append(" and " + prefix + "." + column.getName() + " like '" + searchText + "%' ");
                }else if(DataTableColumn.SEARCH_TYPE_ENUM.equals(column.getSearchType())){
                    InventoryManagementActivity.InventoryActivityType type = Enum.valueOf(InventoryManagementActivity.InventoryActivityType.class, searchText);
                    String columnName = column.getName();
                    //已出库
                    if(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT.equals(type)){
                        sb.append(" and "+prefix+"."+columnName+" = "+InventoryManagementActivity.InventoryActivityType.AUDITSUC.ordinal());
                        sb.append(" and "+prefix+".connectNo is not null ");
                        //待出库
                    }else if(InventoryManagementActivity.InventoryActivityType.AUDITSUC.equals(type)){
                        sb.append(" and "+prefix+"."+columnName+" = "+type.ordinal());
                        sb.append(" and "+prefix+".connectNo is null ");
                    }else{
                        sb.append(" and "+prefix+"."+columnName+" = "+type.ordinal());
                    }
                }else {
                    if ("activityNo".equals(column.getName())) {
                        sb.append(" and " + prefix + ".no" + " like '%" + searchText + "%' ");
                    }else if ("warehouseName".equals(column.getName())) {
                        sb.append(" and " + prefix + ".warehouse.name" + " like '%" + searchText + "%' ");
                    }else if ("handlesName".equals(column.getName())) {
                        sb.append(" and " + prefix + ".handles.name" + " like '%" + searchText + "%' ");
                    }else if ("woCode".equals(column.getName())) {
                        List<WorkOrder> workOrders = workOrderRepository.findWoLikeWoCode(searchText);
                        String text = "(";
                        if (workOrders.size() <= 0) {
                            text += "-1";
                        } else {
                            for (int i = 0; i < workOrders.size(); i++) {
                                Long pkid = workOrders.get(i).getId();
                                if (i == workOrders.size() - 1) {
                                    text += pkid;
                                } else {
                                    text += pkid + ",";
                                }
                            }
                        }
                        text += ")";
                        sb.append(" and " + prefix + ".pkeyId" + " in " + text);
                    }else{
                        sb.append(" and " + prefix + "."+ column.getName() + " like '%" + searchText + "%' ");
                    }
                }
            }
        }
    }

    @Override
    public int findAllReservesCount(DataTableRequest request, List<InventoryManagementActivity.InventoryActivityType> types, String logintype) {
        Long emId = userService.findCurrentEmployeeId();
        StringBuilder sb = new StringBuilder();
        sb.append("select count(ac.id) from InventoryManagementActivity ac " +
                " where ac.deleted = 0 and ac.project=:projectId and ac.type in (:type)  and ac." + logintype + ".id=:supId ");
        if(InventoryManagementActivity.MANAGERS.equals(logintype)){
            sb.append(" and ac.connectNo is null ");
        }
        this.buildSqlColumns(sb, "ac" ,request.getColumns());
        Object obj = entityManager.createQuery(sb.toString())
                .setParameter("projectId", ProjectContext.getCurrentProject())
                .setParameter("type", types)
                .setParameter("supId", emId)
                .getSingleResult();

        int total = 0;

        if (null != obj) {
            total = Integer.parseInt(obj.toString());
        }
        return total;
    }

    /**
     * 预定成功
     *
     * @param id
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void beoutlibStock(Long id, String content) {
        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityRepository.findOne(id);

        //检查预订单状态
        InventoryManagementActivity outActivity=inventoryManagementActivityRepository.findByIdLazyLoad(id);
        if (null!=outActivity) {
            if(!outActivity.getType().equals(InventoryManagementActivity.InventoryActivityType.RESERVE)){
                throw new BusinessException(XiaMesssageResource.getMessage("message.stock.error.list-status"));
            }
        }

        inventoryManagementActivity.setType(InventoryManagementActivity.InventoryActivityType.AUDITSUC);
        inventoryManagementActivityRepository.save(inventoryManagementActivity);
        //预定记录
        InventoryActivity inventoryActivity = new InventoryActivity();
        inventoryActivity.setReserveNo(inventoryManagementActivity.getNo());
        inventoryActivity.setActivityType(InventoryActivity.InventoryActivityType.RESERVE);
        inventoryActivity.setStatus(InventoryActivity.InventoryActivityStatus.BEOUTLIB);
        inventoryActivity.setComment(content);
        inventoManagementActivity.save(inventoryActivity);

        // 发送通知
        this.sendMessage(id, XiaMesssageResource.getMessage("page.stock036.pass"));
        //发送通知(物资预定审核通过后，通知库存管理员)
        this.sendMessageByStockReservePass(id);
    }

    /**
     * 预定失败
     *
     * @param id
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void rejectedStock(Long id, String content) {
        //检查预订单状态
        InventoryManagementActivity outActivity=inventoryManagementActivityRepository.findByIdLazyLoad(id);
        if (null!=outActivity) {
            if(!outActivity.getType().equals(InventoryManagementActivity.InventoryActivityType.RESERVE)){
                throw new BusinessException(XiaMesssageResource.getMessage("message.stock.error.list-status"));
            }
        }

        //取消预定
        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityService.handleCancelStockReserve(id, InventoryManagementActivity.InventoryActivityType.REJECTED);
        //预定记录
        InventoryActivity inventoryActivity = new InventoryActivity();
        inventoryActivity.setReserveNo(inventoryManagementActivity.getNo());
        inventoryActivity.setActivityType(InventoryActivity.InventoryActivityType.RESERVE);
        inventoryActivity.setStatus(InventoryActivity.InventoryActivityStatus.REJECTED);
        inventoryActivity.setComment(content);
        inventoManagementActivity.save(inventoryActivity);

        // 发送通知
        this.sendMessage(id, XiaMesssageResource.getMessage("page.stock036.notpass"));
    }

    @Override
    public List<ReserverDetailDto> findDetailReserve(Long id) {
        String sql = "select DISTINCT new cn.facilityone.shang.stock.stock036.dto.ReserverDetailDto(inv.amount, ma.brand, mc.id, inv.lockAmount, " +
                "    ma.code, ma.name, ma.model, sum(mc.changeNum), ma.unit, inv.id, mb.price, inv.minAmount, inv.deleted,inv.totalInvAmount,inv.displayRack) " +
                " FROM MaterialBatchChange mc " +
                " , InventoryManagementActivity ia   " +
                " , MaterialBatch mb   " +
                " , Inventory inv   " +
                " , Material  ma  " +
                " where mc.inventoryManagementActivity.id = ia.id and mc.materialBatch.id = mb.id " +
                " and mb.inventory.id = inv.id and inv.material.id = ma.id and ia.id=:iaid group by ma.id";
        TypedQuery<ReserverDetailDto> result = entityManager.createQuery(sql, ReserverDetailDto.class);
        result.setParameter("iaid", id);

        List<ReserverDetailDto> inventoryList = result.getResultList();

        return inventoryList;
    }

    @Override
    public void sendMessage(Long imaId, String statusString) {
        // 获取该审批记录
        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityRepository.findOne(imaId);
        // 获取预订人
        Employee handles = inventoryManagementActivity.getHandles();
        List<Employee> noticePersons = new ArrayList<>();
        noticePersons.add(handles);
        // 审批单号
        String activityNo = inventoryManagementActivity.getNo();
        // 发送站内信
        Map<String,Map<String,Object>> typeData = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        params.put(MobilePush_.TYPE, MobilePush_.STOCK);
        params.put(MobilePush_.PROJECT_ID, ProjectContext.getCurrentProject());
        params.put(MobilePush_.RESERVATION_ID, imaId);
        typeData.put(MessageType.MPUSH, params);
        
        String types = MessageType.buildTypes(MessageType.EMAIL,MessageType.MPUSH,MessageType.SITE);
        messageSenderTool.send(MessageStockReserveApprovalTemplate.CODE_STOCK_NOTICE, 
                MessageStockReserveApprovalTemplate.buildData(activityNo, statusString),
                typeData, noticePersons, inventoryManagementActivity.getProject(), types);
        
    }


    public void sendMessageByStockReservePass(Long imaId) {
        // 获取该审批记录
        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityRepository.findOne(imaId);
        // 获取预订人
        Employee handles = inventoryManagementActivity.getHandles();
        //获取审批人
        Employee Supervisor = inventoryManagementActivity.getSupervisor();
        //获取仓库管理员
        Employee managers = inventoryManagementActivity.getManagers();

        //物资预定审核通过后通知库存管理员
        List<Employee> passPersons = new ArrayList<>();
        passPersons.add(managers);

        // 审批单号
        String activityNo = inventoryManagementActivity.getNo();

        // 发送消息
        Map<String,Map<String,Object>> typeData = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        params.put(MobilePush_.TYPE, MobilePush_.STOCK);
        params.put(MobilePush_.PROJECT_ID, ProjectContext.getCurrentProject());
        params.put(MobilePush_.RESERVATION_ID, imaId);
        typeData.put(MessageType.MPUSH, params);

        String types = MessageType.buildTypes(MessageType.EMAIL,MessageType.MPUSH,MessageType.SITE);
        messageSenderTool.send(MessageStockReservePasslTemplate.CODE_STOCK_RESERVE_PASS,
                MessageStockReservePasslTemplate.buildData(activityNo,Supervisor,handles),
                typeData, passPersons, inventoryManagementActivity.getProject(), types);

    }

    @Override
    public boolean isExitsReserveInventoryManagementActivity(String materialCode, Long warehouseId) {

        String sql = "select count(ima.ima_id) from inventory_management_activity ima " +
                " left join materialbatch_activity mba on ima.ima_id=mba.ima_id" +
                " left join  materialbatch mb on mb.materialbatch_id=mba.materialbatch_id" +
                " left join inventory inv on inv.inventory_id=mb.inventory_id" +
                " left join material ma on ma.material_id=inv.material_id" +
                " where ma.material_code='"+materialCode+"' and ima.deleted=0  and inv.deleted=0 " +
                " and ima.type in (4,11) and ima.connect_no is null and ima.proj_id="+ProjectContext.getCurrentProject()+
                " and inv.warehouse_id="+warehouseId;
        boolean isflag = false;
        Object obj = entityManager.createNativeQuery(sql).getSingleResult();
        if(null != obj){
            Integer total = Integer.parseInt(obj.toString());
            if(total > 0){
                isflag = true;
            }
        }
        return isflag;
    }

    @Override
    public List<ReverseManagerDto> findReservesManagersInPage(DataTableRequest request, String logintype) {
        Long emId = userService.findCurrentEmployeeId();
        //顺序：待审批、待出库、已出库、已驳回、已取消，其次按时间逆序。
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT ima.ima_id,ima.no,ima.type,ima.created_date,ima.operate_date," +
                " ima.supervisor_id,ima.managers_id,wh.warehouse_name,wh.warehouse_id,ima.handles_id,w.wo_code,ima.connect_no,ima.remarks ");
        sb.append(" FROM inventory_management_activity ima");
        sb.append(" left join em se on ima.supervisor_id=se.em_id" );
        sb.append(" left join em me on ima.managers_id=me.em_id" );
        sb.append(" left join em he on ima.handles_id=he.em_id" );
        sb.append(" left join warehouse wh on ima.warehouse_id=wh.warehouse_id" );
        sb.append(" left join wo w on w.wo_id=ima.pkey_id");
        sb.append(" where ima.deleted=0 and ima.proj_id = "+ ProjectContext.getCurrentProject());
        sb.append(" and ima.type in (4,5,10,11,12) ");
        sb.append(" and ima."+logintype+"=" + emId);
        this.buildSqlManagersColumns(sb, "ima" ,request.getColumns());
        sb.append(" ORDER BY" );
        sb.append(" ima.type = 4 DESC," );
        sb.append(" ima.type = 11 AND ima.connect_no IS NULL DESC," );
        sb.append(" ima.type = 11 AND ima.connect_no IS not NULL DESC," );
        sb.append(" ima.type = 10 DESC," );
        sb.append(" ima.type = 5 DESC," );
        sb.append(" ima.type = 12 DESC," );
        sb.append(" ima.created_date desc");
        List<Object[]> inventoryList = entityManager.createNativeQuery(sb.toString())
                .setFirstResult(request.getOffset()).setMaxResults(request.getPageSize()).getResultList();
        List<ReverseManagerDto> dtos = new ArrayList<ReverseManagerDto>();
        ReverseManagerDto managerDto = null;
        Map<Long,Employee> employeeMap = new HashMap<>();
        for (Object[] obj : inventoryList) {
            managerDto = new ReverseManagerDto();
            managerDto.setId(Long.parseLong(getObject(obj[0], "0")));
            managerDto.setActivityNo(getObject(obj[1], null));
            InventoryManagementActivity.InventoryActivityType inventoryActivityType = InventoryManagementActivity.InventoryActivityType.
                    valueOfOrdinal(Integer.parseInt(getObject(obj[2], "0")));
            managerDto.setActivityType(inventoryActivityType.getType());
            managerDto.setCreatedDate(DateUtil.formatDateTimeNotContainSS(getObject(obj[3], null)));
            managerDto.setOperateDate(DateUtil.formatDate(getObject(obj[4], null)));
            //已出库
            String connectNo = getObject(obj[11], null);
            if(null != connectNo){
                managerDto.setType(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT);
            }else{
                managerDto.setType(inventoryActivityType);
            }
            managerDto.setRemarks(getObject(obj[12], null));
            //主管
            Long supervisorId = Long.parseLong(getObject(obj[5], "0"));
            Employee employeeSupervisor = new Employee();
            if(employeeMap.containsKey(supervisorId)){
                employeeSupervisor = employeeMap.get(supervisorId);
            }else{
                 employeeSupervisor = employeeRepository.findOne(supervisorId);
            }
            employeeMap.put(supervisorId,employeeSupervisor);
            //保管人等
            Long managersId = Long.parseLong(getObject(obj[6], "0"));
            Employee employeeManagers = new Employee();
            if(employeeMap.containsKey(managersId)){
                 employeeManagers = employeeMap.get(managersId);
            }else {
                employeeManagers = employeeRepository.findOne(managersId);
            }
            employeeMap.put(managersId,employeeManagers);
            managerDto.setSupervisor(employeeSupervisor);
            managerDto.setManagers(employeeManagers);
            managerDto.setWarehouseName(getObject(obj[7], null));
            managerDto.setWarehouseId(Long.parseLong(getObject(obj[8], "0")));
            Long handlesId = Long.parseLong(getObject(obj[9], "0"));
            Employee handles = new Employee();
            if(employeeMap.containsKey(handlesId)){
                 handles = employeeMap.get(handlesId);
            }else {
                 handles = employeeRepository.findOne(handlesId);
            }
            employeeMap.put(handlesId,handles);
            managerDto.setHandles(handles);
            if (null != handles) {
                managerDto.setHandlesName(handles.getName());
            }
            managerDto.setWoCode(getObject(obj[10], null));
            dtos.add(managerDto);
        }
        return dtos;
    }

    private void buildSqlManagersColumns (StringBuilder sb, String prefix, List<DataTableColumn> columns) {
        for (DataTableColumn column : columns) {
            String searchText = column.getSearchText();
            if (StringUtils.isNotEmpty(searchText)) {
                if (DataTableColumn.SEARCH_TYPE_DATE.equals(column.getSearchType())) {
                    sb.append(" and " + prefix + ".operate_date like '" + searchText + "%' ");
                }else if(DataTableColumn.SEARCH_TYPE_ENUM.equals(column.getSearchType())){
                    InventoryManagementActivity.InventoryActivityType type = Enum.valueOf(InventoryManagementActivity.InventoryActivityType.class, searchText);
                    String columnName = "type";
                    //已出库
                    if(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT.equals(type)){
                        sb.append(" and "+prefix+"."+columnName+" = "+InventoryManagementActivity.InventoryActivityType.AUDITSUC.ordinal());
                        sb.append(" and "+prefix+".connect_no is not null ");
                        //待出库
                    }else if(InventoryManagementActivity.InventoryActivityType.AUDITSUC.equals(type)){
                        sb.append(" and "+prefix+"."+columnName+" = "+type.ordinal());
                        sb.append(" and "+prefix+".connect_no is null ");
                    }else{
                        sb.append(" and "+prefix+"."+columnName+" = "+type.ordinal());
                    }
                }else {
                    if ("activityNo".equals(column.getName())) {
                        sb.append(" and " + prefix + ".no" + " like '%" + searchText + "%' ");
                    }else if ("warehouseName".equals(column.getName())) {
                        sb.append(" and wh.warehouse_name like '%" + searchText + "%' ");
                    }else if ("handlesName".equals(column.getName())) {
                        sb.append(" and he.em_name like '%" + searchText + "%' ");
                    }else if ("woCode".equals(column.getName())) {
                        sb.append(" and w.wo_code like '%" + searchText + "%' ");
                    }else if ("managers.name".equals(column.getName())) {
                        sb.append(" and me.em_name like '%" + searchText + "%' ");
                    }else if ("supervisor.name".equals(column.getName())) {
                        sb.append(" and se.em_name like '%" + searchText + "%' ");
                    }
                }
            }
        }
    }

    public String getObject(Object obj, String defaultValue){
        if(null == obj){
           return defaultValue;
        }else{
            return obj.toString();
        }
    }
}
