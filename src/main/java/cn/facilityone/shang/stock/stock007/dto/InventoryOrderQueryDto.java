package cn.facilityone.shang.stock.stock007.dto;

import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.stock.stock033.dto.MaterialBackDetailDto;

import java.util.List;

/**
 * Author：panda.yang
 * CreateTime：2016/12/2 16:59
 */
public class InventoryOrderQueryDto {

    private List<MaterialBackDetailDto> detailDtos;

    private InventoryManagementActivity primaryActivity;

    private InventoryManagementActivity targetActivity;

    public List<MaterialBackDetailDto> getDetailDtos() {
        return detailDtos;
    }

    public void setDetailDtos(List<MaterialBackDetailDto> detailDtos) {
        this.detailDtos = detailDtos;
    }

    public InventoryManagementActivity getPrimaryActivity() {
        return primaryActivity;
    }

    public void setPrimaryActivity(InventoryManagementActivity primaryActivity) {
        this.primaryActivity = primaryActivity;
    }

    public InventoryManagementActivity getTargetActivity() {
        return targetActivity;
    }

    public void setTargetActivity(InventoryManagementActivity targetActivity) {
        this.targetActivity = targetActivity;
    }
}
