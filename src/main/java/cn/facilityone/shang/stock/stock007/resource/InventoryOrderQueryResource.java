package cn.facilityone.shang.stock.stock007.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.stock.stock007.dto.InventoryManagementActivityDto;
import cn.facilityone.shang.stock.stock007.service.InventoryOrderQueryService;
import cn.facilityone.shang.stock.stock033.dto.MaterialBackDetailDto;
import cn.facilityone.xia.core.common.Result;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Path("/stock007")
public class InventoryOrderQueryResource {
    private static final String TEMPLATE_POSITION_PATH = "/business/stockV2/stock007-inventoryOrderQuery.ftl";

    @Autowired
    private InventoryOrderQueryService inventoryOrderQueryService;

    @GET
    @Template(name = TEMPLATE_POSITION_PATH)
    public Map<String, Object> init() {
        return new HashMap<>();
    }


    @POST
    @Path("table")
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findReserves(DataTableRequest request) {
        List<InventoryManagementActivityDto> reserves = inventoryOrderQueryService.findAllOrderQueryPage(request);
        int totalCount = inventoryOrderQueryService.findAllCount(request);
        return new DataTableResponse(reserves, totalCount, request.getPageNumber(), request.getDraw());
    }

    /**
     * 单号详情列表
     */
    @POST
    @Path("detailtable/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findetailtable(@PathParam("id") Long id) {
        return new Result(inventoryOrderQueryService.getMaterialBatchDetail(id));
    }

}
