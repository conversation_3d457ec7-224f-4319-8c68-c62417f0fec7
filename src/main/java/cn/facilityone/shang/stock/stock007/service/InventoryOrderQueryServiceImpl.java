package cn.facilityone.shang.stock.stock007.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.common.util.StringUtil;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.stock.common.repository.InventoryManagementActivityRepository;
import cn.facilityone.shang.stock.stock007.dto.InventoryManagementActivityDto;
import cn.facilityone.shang.stock.stock007.dto.InventoryOrderQueryDto;
import cn.facilityone.shang.stock.stock033.dto.MaterialBackDetailDto;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.util.SystemOutLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by pangda.yang on 2016/11/10.
 */
@Service
public class InventoryOrderQueryServiceImpl implements InventoryOrderQueryService {

    private static final Logger log = LoggerFactory.getLogger(InventoryOrderQueryServiceImpl.class);
    
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private InventoryManagementActivityRepository inventoryManagementActivityRepository;

    @Override
    @XiaTransactional(readOnly = true)
    public List<InventoryManagementActivityDto> findAllOrderQueryPage(DataTableRequest request) {
        StringBuilder sb = new  StringBuilder();
        sb.append("select ima  from InventoryManagementActivity ima " +
                " left join fetch ima.warehouse " +
                " left join fetch ima.handles " +
                " left join fetch ima.managers " +
                " left join fetch ima.supervisor " +
                " left join fetch ima.laborer " +
                " left join fetch ima.organization " );
        sb.append(" where  ima.deleted = 0 and ima.project=:projectId and ima.type not in (:type) " );
//                " or ima.type=:type2 and ima.connectNo is null) ");
        this.buildSqlcolumns(sb, "ima",  request.getColumns());
        sb.append(" order by ima.createdDate desc");
        TypedQuery<InventoryManagementActivity> result = entityManager.createQuery(sb.toString(), InventoryManagementActivity.class);
        result.setParameter("projectId", ProjectContext.getCurrentProject());
        result.setParameter("type", this.getParamType());
//        result.setParameter("type2", InventoryManagementActivity.InventoryActivityType.AUDITSUC);
        List<InventoryManagementActivity> activities = result.setFirstResult(request.getOffset()).setMaxResults(request.getPageSize()).getResultList();
        List<InventoryManagementActivityDto> activityDtos = new ArrayList<InventoryManagementActivityDto>();
        InventoryManagementActivityDto activityDto = null;
        for(InventoryManagementActivity activity : activities){
            activityDto = new InventoryManagementActivityDto();
            BeanUtils.copyProperties(activity, activityDto);
            activityDto.setStatus(activity.getType());

            if(InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE.equals(activity.getType())){
                activityDto.setType(InventoryManagementActivity.InventoryActivityType.RESERVE);
            }else if(InventoryManagementActivity.InventoryActivityType.AUDITSUC.equals(activity.getType())){
                activityDto.setType(InventoryManagementActivity.InventoryActivityType.RESERVE);
                if(StringUtils.isNotEmpty(activity.getConnectNo())){
                    activityDto.setStatus(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT);
                }
            }else if(InventoryManagementActivity.InventoryActivityType.REJECTED.equals(activity.getType())){
                activityDto.setType(InventoryManagementActivity.InventoryActivityType.RESERVE);
            }else if(InventoryManagementActivity.InventoryActivityType.CANCEL_OUT.equals(activity.getType())){
                activityDto.setType(InventoryManagementActivity.InventoryActivityType.RESERVE);
            }

            if(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT.equals(activity.getType())){
//                activityDto.setType(InventoryManagementActivity.InventoryActivityType.RESERVE);
//                activityDto.setNo(activity.getConnectNo());
                activityDto.setStatus(null);
            }

            activityDtos.add(activityDto);
        }
        return activityDtos;
    }

    public void buildSqlcolumns(StringBuilder sb, String prefix, List<DataTableColumn> columns){
       for(DataTableColumn column : columns){
           String searchText = column.getSearchText();
           if(StringUtils.isNotEmpty(searchText)){
               if(DataTableColumn.SEARCH_TYPE_ENUM.equals(column.getSearchType())){
                   InventoryManagementActivity.InventoryActivityType type = Enum.valueOf(InventoryManagementActivity.InventoryActivityType.class, searchText);
                   String columnName = column.getName();
                   if("status".equals(columnName)){
                       columnName = "type";
                       if(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT.equals(type)){
                           sb.append(" and "+prefix+"."+columnName+" = "+InventoryManagementActivity.InventoryActivityType.AUDITSUC.ordinal());
                           sb.append(" and "+prefix+".connectNo is not null");
                       }else if(InventoryManagementActivity.InventoryActivityType.AUDITSUC.equals(type)){
                           sb.append(" and "+prefix+"."+columnName+" = "+InventoryManagementActivity.InventoryActivityType.AUDITSUC.ordinal());
                           sb.append(" and "+prefix+".connectNo is null");
                       }else{
                           sb.append(" and "+prefix+"."+columnName+" = "+type.ordinal());
                       }
                   }else{
                       if(InventoryManagementActivity.InventoryActivityType.RESERVE.equals(type)){
                           sb.append(" and "+prefix+"."+column.getName()+" in ("+type.ordinal()+"," +
                                   InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE.ordinal()+"," +
                                   InventoryManagementActivity.InventoryActivityType.AUDITSUC.ordinal()+"," +
                                   InventoryManagementActivity.InventoryActivityType.CANCEL_OUT.ordinal()+"," +
                                   InventoryManagementActivity.InventoryActivityType.REJECTED.ordinal()+" )");
                       }else{
                           sb.append(" and "+prefix+"."+columnName+" = "+type.ordinal());
                       }
                   }
               }else if(DataTableColumn.SEARCH_TYPE_DATE_RANGE.equals(column.getSearchType())){
                   String[] strCols = searchText.split(SystemConst.STR_TILDE);
                   Date endDate = null;
                   if (strCols.length == 2 && !org.apache.commons.lang3.StringUtils.isEmpty(strCols[1])) {
                       try {
                           endDate = DateUtils.parseDateStrictly(strCols[1].trim(), SystemConst.DATE_YYYY_MM_DD_HYPHEN);
                           endDate = DateUtils.addDays(endDate, 1);
                       } catch (ParseException e) {
                           log.error("parse date {} error : {}",strCols[1].trim(),e.getMessage());
                       }
                   }
                   sb.append(" and "+prefix+"."+column.getName()+" between '"+strCols[0].toString()+"' and '"+ DateUtil.formatDateYYYYMMDD(endDate)+"'");
               }else{
                   sb.append(" and "+prefix+"."+column.getName()+" like '%"+searchText+"%' ");
               }
           }
       }
    }

    @Override
    @XiaTransactional(readOnly = true)
    public int findAllCount(DataTableRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(ac.id) from InventoryManagementActivity ac " +
                " where ac.deleted = 0 and ac.project=:projectId and ac.type not in (:type) ");
        this.buildSqlcolumns(sb, "ac",  request.getColumns());
        Object obj = entityManager.createQuery(sb.toString())
                .setParameter("projectId", ProjectContext.getCurrentProject())
                .setParameter("type", this.getParamType())
//                .setParameter("type2", InventoryManagementActivity.InventoryActivityType.AUDITSUC)
                .getSingleResult();
        int total = 0;
        if(null != obj){
            total = Integer.parseInt(obj.toString());
        }
        return total;
    }

    public List<InventoryManagementActivity.InventoryActivityType> getParamType(){
        List<InventoryManagementActivity.InventoryActivityType> types = new ArrayList<InventoryManagementActivity.InventoryActivityType>();
        types.add(InventoryManagementActivity.InventoryActivityType.ADJUST);
        types.add(InventoryManagementActivity.InventoryActivityType.CHECK);
//        types.add(InventoryManagementActivity.InventoryActivityType.AUDITSUC);
        types.add(InventoryManagementActivity.InventoryActivityType.MOVEIN);
        return types;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public InventoryOrderQueryDto getMaterialBatchDetail(Long id){
        InventoryOrderQueryDto dto = new InventoryOrderQueryDto();
        InventoryManagementActivity activity = inventoryManagementActivityRepository.findOneById(id);
        String groupStr = "";
        if(InventoryManagementActivity.InventoryActivityType.IN.equals(activity.getType())){
            groupStr = ",mb.materialbatch_id";
        }
        dto.setDetailDtos(this.findMaterialBatchDetail(id, groupStr));

        if(null != activity){
           if(activity.getType().equals(InventoryManagementActivity.InventoryActivityType.MOVEOUT)){
               dto.setPrimaryActivity(activity);
               String connectNo = activity.getConnectNo();
               Date createDate = activity.getCreatedDate();
               String createStr = DateUtil.formatDateTimeNotContainSS(createDate);
               if(null != connectNo){
                   List<InventoryManagementActivity> activities = inventoryManagementActivityRepository.findByNO(connectNo);
                   if(CollectionUtils.isNotEmpty(activities)){
                       for(InventoryManagementActivity taractivity : activities){
                           if(createStr.equals(DateUtil.formatDateTimeNotContainSS(taractivity.getCreatedDate()))){
                               dto.setTargetActivity(taractivity);
                               break;
                           }
                       }
                   }
               }
           }else{
               dto.setTargetActivity(activity);
               String connectNo = activity.getConnectNo();
               Date createDate = activity.getCreatedDate();
               String createStr = DateUtil.formatDateTimeNotContainSS(createDate);
               if(null != connectNo){
                   List<InventoryManagementActivity> activities = inventoryManagementActivityRepository.findByNO(connectNo);
                   if(CollectionUtils.isNotEmpty(activities)){
                       for(InventoryManagementActivity taractivity : activities){
                           if(createStr.equals(DateUtil.formatDateTimeNotContainSS(taractivity.getCreatedDate()))){
                               dto.setPrimaryActivity(taractivity);
                               break;
                           }
                       }
                   }
               }
           }
        }
        return dto;
    }

    public List<MaterialBackDetailDto> findMaterialBatchDetail(Long id, String groupStr) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ma.material_id,ma.material_code,ma.material_name,ma.brand,ma.model,ma.unit, " );
        sb.append("sum(mc.changeNum) as changeNum,sum(mc.backNum) as backNum,GROUP_CONCAT(mc.materialbatch_change_id) as materialMatchChangeIds, " );
        sb.append("mb.price,mb.materialbatch_id,ve.vendor_name,mb.duedate, " );
        sb.append("GROUP_CONCAT(ve.vendor_name,' ',mb.created_date,'desctype  descprice',FORMAT(mb.price,2),'descunit  descnum',FORMAT(mc.changenum,2) SEPARATOR '<br/>') as materialMatchDetail " );
        sb.append(", sum(mb.price*mc.changenum) as batchcost, inv.display_rack ");
        sb.append("from materialbatch_change mc " );
        sb.append("left join materialbatch mb on mc.materialbatch_id = mb.materialbatch_id " );
        sb.append("left join inventory inv on inv.inventory_id = mb.inventory_id " );
        sb.append("left join material ma on ma.material_id = inv.material_id " );
        sb.append("left join inventory_management_activity ima on ima.ima_id = mc.activity_id " );
        sb.append("left join vendor ve on ve.vendor_id = mb.provide_id " );
        sb.append(" where mc.deleted = 0 and ima.ima_id = "+id);
        sb.append( " GROUP BY ma.material_id"+groupStr);

        List<Object[]> list = entityManager.createNativeQuery(sb.toString()).getResultList();
        List<MaterialBackDetailDto> detailDtos = new ArrayList<MaterialBackDetailDto>();
        MaterialBackDetailDto detailDto = null;
        for(Object[] obj : list){
            detailDto = new MaterialBackDetailDto();
            detailDto.setMaterialId(obj[0]==null?null:Long.parseLong(obj[0].toString()));
            detailDto.setMaterialCode(obj[1]==null?null:obj[1].toString());
            detailDto.setMaterialName(obj[2]==null?null:obj[2].toString());
            detailDto.setMaterialBrand(obj[3]==null?null:obj[3].toString());
            detailDto.setMaterialModel(obj[4]==null?null:obj[4].toString());
            detailDto.setMaterialUnit(obj[5]==null?null:obj[5].toString());
            Double number = obj[6]==null?0d:Double.parseDouble(obj[6].toString());
            detailDto.setChangeNum(number);
            detailDto.setBackNum(obj[7]==null?null:Double.parseDouble(obj[7].toString()));
            detailDto.setMaterialMatchChangeIds(obj[8]==null?null:obj[8].toString());
            Double price = obj[9]==null?0d:Double.parseDouble(obj[9].toString());
            detailDto.setPrice(price);
            detailDto.setMaterialMatchId(obj[10]==null?null:Long.parseLong(obj[10].toString()));
            detailDto.setVendorName(obj[11]==null?null:obj[11].toString());
            detailDto.setDuedate(obj[12]==null?null:obj[12].toString());
            detailDto.setMaterialMatchDetail(obj[13]==null?null:obj[13].toString());
            detailDto.setAmount(Double.parseDouble(obj[14]==null?"0":obj[14].toString()));
            detailDto.setDisplayRack(obj[15]==null?"":obj[15].toString());
            detailDtos.add(detailDto);
        }
        return detailDtos;
    }

}
