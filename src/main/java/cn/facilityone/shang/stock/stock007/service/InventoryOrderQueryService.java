package cn.facilityone.shang.stock.stock007.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.stock.stock007.dto.InventoryManagementActivityDto;
import cn.facilityone.shang.stock.stock007.dto.InventoryOrderQueryDto;
import cn.facilityone.shang.stock.stock033.dto.MaterialBackDetailDto;

import java.util.List;

/**
 * Created by pangda.yang
 */
public interface InventoryOrderQueryService {

    List<InventoryManagementActivityDto> findAllOrderQueryPage(DataTableRequest request);

    int findAllCount(DataTableRequest request);

    InventoryOrderQueryDto getMaterialBatchDetail(Long id);
}
