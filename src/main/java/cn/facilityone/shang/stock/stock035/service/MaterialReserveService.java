package cn.facilityone.shang.stock.stock035.service;

import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.common.alias.WoReserveMateralView;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;

import java.util.List;

/**
 * Created by panda.yang on 2016/11/10.
 */
public interface MaterialReserveService {

    /**
     * 获取对应员工的工作组主管
     * @param emId 员工id
     * @return
     */
    List<Employee> findSupervisorsByEmid(Long emId);

    public void sendMessage(InventoryManagementActivity activity, Long emId);

    /**
     * 获取对应工单的预定物资记录
     * @param woId 工单id
     * @return
     */
    List<InventoryManagementActivity> findAllOrderQueryBywoId(Long woId);

    List<WoReserveMateralView> findAllReserveMaterialByWoIdAndStatus(Long woId);

    /**
     * 删除预定
     * @param id 预订单id
     */
    void deleteInventoryManagementActivityById(Long id);

    /**
     * 获取对应预订单的预定详情记录
     * @param id 预订单id
     * @return
     */
    List<MaterialBatchChange> findMaterialBatchListByActivityId(Long id);

    /**
     * 编辑预定单
     * @param dto
     * @return
     */
    InventoryManagementActivity editStockReserveActivity(StockInDTO dto);
}
