package cn.facilityone.shang.stock.stock035.service;

import cn.facilityone.shang.common.component.message.MessageSenderTool;
import cn.facilityone.shang.common.component.message.template.MessageStockCheckTemplate;
import cn.facilityone.shang.common.component.message.template.MessageStockReserveTemplate;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.staticmetamodel.MobilePush_;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatch;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org002.service.EmployeeService;
import cn.facilityone.shang.organize.org003.repository.OrganizationRepository;
import cn.facilityone.shang.stock.common.alias.WoReserveMateralView;
import cn.facilityone.shang.stock.common.repository.*;
import cn.facilityone.shang.stock.stock002.service.InventoryService;
import cn.facilityone.shang.stock.stock003.dto.ActivityCollectionDTO;
import cn.facilityone.shang.stock.stock003.dto.StockDataDTO;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;
import cn.facilityone.shang.stock.stock003.service.InventoryManagementActivityService;
import cn.facilityone.shang.stock.stock003.service.MaterialBatchService;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.message.common.MessageType;
import cn.facilityone.xia.message.service.MessageMobilePushSender;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.*;

/**
 * Created by panda.yang on 2016/11/10.
 */
@Service
public class MaterialReserveServiceImpl implements MaterialReserveService {

    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private InventoryManagementActivityRepository inventoryManagementActivityRepository;
    @Autowired
    private MessageSenderTool messageSenderTool;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private MaterialBatchChangeRepository materialBatchChangeRepository;

    @Autowired
    private MaterialBatchService materialBatchService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private MaterialBatchRepository materialBatchRepository;

    @Autowired
    private InventoryManagementActivityService inventoryManagementActivityService;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private WarehouseRepository warehouseRepository;

    @Autowired
    private CommonUserService employeeService;

    @Override
    public List<Employee> findSupervisorsByEmid(Long emId) {
        String wtsql = "select count(em_id) "  +
                "  from work_team_supervisor where work_team_id in (select work_team_id " +
                "  from work_team_member where em_id="+emId+")";

        Object obj = entityManager.createNativeQuery(wtsql).getSingleResult();

        int count = 0;

        if(obj != null){
          count = Integer.parseInt(obj.toString());
        }

        String sql = "select * from em where deleted=0 and activated=1 and proj_id="+ProjectContext.getCurrentProject();

        if(count > 0){
            sql = "select * from em where deleted=0 and activated=1 and em_id in (select distinct em_id " +
                    " from work_team_supervisor where work_team_id in (select work_team_id " +
                    " from work_team_member where em_id="+emId+")) and proj_id="+ProjectContext.getCurrentProject();
        }

        List<Employee> list = entityManager.createNativeQuery(sql, Employee.class).getResultList();
        return list;
    }

    @Override
    public void sendMessage(InventoryManagementActivity activity, Long emId) {
        // 通知人
        Employee manager = employeeRepository.findOneWithUser(emId);
        List<Employee> noticePersons = new ArrayList<>();
        noticePersons.add(manager);
        // 审批单号
        String no = activity.getNo();

        Map<String,Map<String,Object>> typeData = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        params.put(MobilePush_.TYPE, MobilePush_.STOCK);
        params.put(MobilePush_.PROJECT_ID, ProjectContext.getCurrentProject());
        params.put(MobilePush_.RESERVATION_ID, activity.getId());
        typeData.put(MessageType.MPUSH, params);
        
        String types = MessageType.buildTypes(MessageType.EMAIL,MessageType.MPUSH,MessageType.SITE);
        messageSenderTool.send(MessageStockReserveTemplate.CODE_STOCK_RESERVE, 
                MessageStockReserveTemplate.buildData(no),
                typeData, noticePersons, activity.getProject(), types);
    }

    @Override
    public List<InventoryManagementActivity> findAllOrderQueryBywoId(Long woId) {
        StringBuilder sb = new  StringBuilder();
        sb.append("select ima  from InventoryManagementActivity ima " +
                " left join fetch ima.warehouse " +
                " left join fetch ima.handles " +
                " left join fetch ima.managers " +
                " left join fetch ima.supervisor " +
                " left join fetch ima.organization " );
        sb.append(" where  ima.deleted = 0 and ima.project=:projectId and ima.pkeyId = :woId " );
        sb.append(" order by ima.createdDate desc");
        TypedQuery<InventoryManagementActivity> result = entityManager.createQuery(sb.toString(), InventoryManagementActivity.class);
        result.setParameter("projectId", ProjectContext.getCurrentProject());
        result.setParameter("woId", woId==null?"0":String.valueOf(woId));
        List<InventoryManagementActivity> activities = result.getResultList();
        for(InventoryManagementActivity activity : activities){
            if(StringUtils.isNotEmpty(activity.getConnectNo()) &&
                    activity.getType().equals(InventoryManagementActivity.InventoryActivityType.AUDITSUC)){
                activity.setType(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT);
            }
        }
        return activities;
    }

    @Override
    public List<WoReserveMateralView> findAllReserveMaterialByWoIdAndStatus(Long woId) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ma.material_name, ma.unit, mc.changeNum from inventory_management_activity ima ");
        sb.append("left join materialbatch_change mc on ima.ima_id = mc.activity_id ");
        sb.append("left join materialbatch mb on mc.materialbatch_id = mb.materialbatch_id ");
        sb.append("left join inventory inv on inv.inventory_id = mb.inventory_id ");
        sb.append("left join material ma on ma.material_id = inv.material_id ");
        sb.append(" where  ima.deleted = 0 and ima.type in (2,4,11) and ima.proj_id= " + ProjectContext.getCurrentProject() + " and ima.pkey_id = " + woId);
        List<Object[]> list = entityManager.createNativeQuery(sb.toString()).getResultList();
        List<WoReserveMateralView> materalViewList = new ArrayList<>();
        WoReserveMateralView view = null;
        for (Object[] obj : list) {
            view = new WoReserveMateralView();
            view.setName(obj[0] == null ? null : obj[0].toString());
            view.setUnit(obj[1] == null ? null : obj[1].toString());
            Double number = obj[2] == null ? 0d : Double.parseDouble(obj[2].toString());
            view.setNum(number);
            materalViewList.add(view);
        }
        return materalViewList;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void deleteInventoryManagementActivityById(Long id) {
        inventoryManagementActivityRepository.delete(id);
    }

    @Override
    public List<MaterialBatchChange> findMaterialBatchListByActivityId(Long id){
        return materialBatchChangeRepository.findMaterialBatchListByActivityId(id);
    }

    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity editStockReserveActivity(StockInDTO dto){
        return this.editActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.RESERVE);
    }

    public InventoryManagementActivity handleStockReserve(StockInDTO dto) {
        ActivityCollectionDTO collectionDTO = inventoryManagementActivityService.buildStockReserveDto(dto, "");
        //最终存储集合
        Set<MaterialBatch> materialBatches=collectionDTO.getBatchSets();
        List<MaterialBatchChange> materialBatchChanges=collectionDTO.getChanges();
        List<Inventory> inventorys=collectionDTO.getInventorys();

        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);

        //新建预定操作记录
        InventoryManagementActivity inventoryManagementActivity = this.editActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.RESERVE);
        inventoryManagementActivity.setMaterialBatchs(new ArrayList<MaterialBatch>(materialBatches));
        //绑定工单
        Long woId = dto.getWoId();
        if(null != woId){
            inventoryManagementActivity.setPkeyId(String.valueOf(woId));
            inventoryManagementActivity.setTableName(WorkOrder.class.getSimpleName());
        }
        inventoryManagementActivity=inventoryManagementActivityRepository.save(inventoryManagementActivity);
        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(inventoryManagementActivity);
        }
        materialBatchChangeRepository.saveInBatch(materialBatchChanges);

        return inventoryManagementActivity;

    }

    public InventoryManagementActivity editActivityByDtoAndType(StockInDTO dto,InventoryManagementActivity.InventoryActivityType type){
        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityRepository.findOne(dto.getActivityId());
//        if(null == dto.getAmount() || dto.getAmount()==0){
//            inventoryManagementActivity.setAmount(this.getTotalAmountFromDto(dto));
//        }
//        if(!StringUtils.isBlank(inventoryManagementActivity.getDescription())){
//            inventoryManagementActivity.setDescription(XiaMesssageResource.getMessage("page.pm002.connectWoCode")+":"+inventoryManagementActivity.getDescription());
//        }
//        inventoryManagementActivity.setWarehouse(warehouseRepository.findOne(dto.getWarehouse().getId()));
//        inventoryManagementActivity.setLaborer(employeeService.getCurrentLoginEmployee());
        if(null == inventoryManagementActivity.getLaborer()){
            inventoryManagementActivity.setLaborer(employeeService.findLoginEmployee());
        }
        //仓库管理员
        if(null != dto.getManagers()){
            inventoryManagementActivity.setManagers(employeeRepository.findOne(dto.getManagers().getId()));
        }
        if(null!=dto.getRemarks()){
            inventoryManagementActivity.setRemarks(dto.getRemarks());
        }
        //经手人/领用人/退库人/移库人
        if(null != dto.getHandles()){
            inventoryManagementActivity.setHandles(employeeRepository.findOne(dto.getHandles().getId()));
        }

        //主管
        if(null != dto.getSupervisorId()){
            inventoryManagementActivity.setSupervisor(employeeRepository.findOne(dto.getSupervisorId()));
        }

        //部门
        if(null != dto.getOrganizationId()){
            inventoryManagementActivity.setOrganization(organizationRepository.findOne(dto.getOrganizationId()));
        }
        inventoryManagementActivity.setOperateDate(dto.getOperateDate());
        return inventoryManagementActivity;
    }

    private Double getTotalAmountFromDto(StockInDTO dto){
        Double amount = 0d;
        List<StockDataDTO> datas = dto.getStockDatas();
        for(StockDataDTO data : datas){
            amount+=data.getAmount();
        }
        return amount;
    }


}
