package cn.facilityone.shang.stock.stock035.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org003.service.OrganizationService;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;
import cn.facilityone.shang.stock.stock003.dto.StockMessageDto;
import cn.facilityone.shang.stock.stock003.service.InventoryManagementActivityService;
import cn.facilityone.shang.stock.stock035.service.MaterialReserveService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Path("/stock035")
public class MaterialReserResource {
    private static final String TEMPLATE_POSITION_PATH = "/business/stockV2/stock035-reserve.ftl";

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private MaterialReserveService materialReserveService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private InventoryManagementActivityService inventoryManagementActivityService;

    @GET
    @Template(name = TEMPLATE_POSITION_PATH)
    public Map<String, Object> init() {
        Map<String, Object> map = new HashMap<>();
        XiaPrincipal user = (XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal();
        Long userId = user.getId();
        Employee employee = employeeRepository.findOneByUserId(userId);
        if(null != employee){
            map.put("employeeid", String.valueOf(employee.getId()));
            map.put("employeename", employee.getName());
        }
        return map;
    }

    @GET
    @Path("organizations")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getOrganizationTree() {
        return new Result().data(organizationService.findOrganizationsTree());
    }

    @GET
    @Path("supervisors/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findSupervisorsByEmid(@PathParam("id") Long id) {
        return new Result(materialReserveService.findSupervisorsByEmid(id));
    }

    @POST
    @Path("wotable/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findAllOrderQueryBywoId(@PathParam("id") Long id) {
        List<InventoryManagementActivity> reserves = materialReserveService.findAllOrderQueryBywoId(id);
        int totalCount = reserves.size();
        return new DataTableResponse(totalCount, reserves);
    }

    /**
     * 取消预定
     * @return
     */
    @DELETE
    @Path("reserve/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result delEquipmentSystem(@PathParam("id") Long id) {
//        materialReserveService.deleteInventoryManagementActivityById(id);
        inventoryManagementActivityService.handleCancelStockReserve(id, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE);
        return new Result("取消预定成功");
    }

    /**
     * 工单预定详情
     */
    @GET
    @Path("resservelist/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findMaterialBatchListByActivityId(@PathParam("id") Long id) {
        return new Result(materialReserveService.findMaterialBatchListByActivityId(id));
    }

    /**
     * 编辑库存预定
     */
    @POST
    @Path("stockreserveEdit")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result saveStockReserve(StockInDTO dto) {

        InventoryManagementActivity inventoryManagementActivity = materialReserveService.editStockReserveActivity(dto);

        if(null != dto){
            // 当主管改变时候发送通知给新主管
           if(!dto.getSupervisorId().equals(dto.getFromSupervisorId())){
              materialReserveService.sendMessage(inventoryManagementActivity, dto.getSupervisorId());
           }
        }

        return new Result(Result.CODE_200, XiaMesssageResource.getMessage("MS000027", null, ""),
                new StockMessageDto(inventoryManagementActivity==null?"":inventoryManagementActivity.getNo()));
    }

}
