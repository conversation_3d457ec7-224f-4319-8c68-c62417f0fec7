package cn.facilityone.shang.stock.stock004.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.entity.inventory.*;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.common.staticmetamodel.InventoryActivityAndMaterialBatch_;
import cn.facilityone.shang.stock.common.staticmetamodel.InventoryManagementActivity_;
import cn.facilityone.shang.stock.common.staticmetamodel.Inventory_;
import cn.facilityone.shang.stock.common.staticmetamodel.MaterialBatch_;
import cn.facilityone.shang.stock.common.staticmetamodel.Warehouse_;
import cn.facilityone.shang.stock.stock004.dto.InventoryRecordDTO;
import cn.facilityone.shang.stock.stock004.dto.InventoryRecordExportDTO;
import cn.facilityone.shang.stock.stock004.dto.RecordSearchRequest;
import cn.facilityone.shang.workorder.common.constant.WorkOrderConstant;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wayne.fu
 * @Date: 8/14/2015
 */
@Service
public class OperationRecordServiceImpl implements OperationRecordService {

    private static final Logger log = LoggerFactory.getLogger(OperationRecordServiceImpl.class);
    private static final SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");

    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private CommonUserService commonUserService;

//    @Override
//    public List<InventoryActivityAndMaterialBatch> findActivityInPage(RecordSearchRequest request) {
//        CriteriaQuery<InventoryActivityAndMaterialBatch> query = createQueryPredicate(request);
//        List<InventoryActivityAndMaterialBatch> activities = entityManager.createQuery(query).setFirstResult(request.getOffset()).setMaxResults(request.getPageSize()).getResultList();
//        for (InventoryActivityAndMaterialBatch iamb : activities) {
//            if (InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE.equals(iamb.getActivity().getType())
//                    || InventoryManagementActivity.InventoryActivityType.REJECTED.equals(iamb.getActivity().getType())
//                    || InventoryManagementActivity.InventoryActivityType.AUDITSUC.equals(iamb.getActivity().getType())) {
//                iamb.getActivity().setType(InventoryManagementActivity.InventoryActivityType.RESERVE);
//            }
//        }
//        return activities;
//    }

    /**
     * create query predicate
     *
     * @param request
     * @return
     */
//    private CriteriaQuery<InventoryActivityAndMaterialBatch> createQueryPredicate(RecordSearchRequest request) {
//        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
//        CriteriaQuery<InventoryActivityAndMaterialBatch> query = cb.createQuery(InventoryActivityAndMaterialBatch.class);
//        Root<InventoryActivityAndMaterialBatch> root = query.from(InventoryActivityAndMaterialBatch.class);
//
//        From activity = (From) root.fetch(InventoryActivityAndMaterialBatch_.ACTIVITY, JoinType.LEFT);
//        From warehouses = (From) activity.fetch(InventoryManagementActivity_.WAREHOUSE, JoinType.LEFT);
//        From laborers = (From) activity.fetch(InventoryManagementActivity_.LABORER, JoinType.LEFT);
//        From operator = (From) activity.fetch(InventoryManagementActivity_.HANDLES, JoinType.LEFT);
//        From batches = (From) root.fetch(InventoryActivityAndMaterialBatch_.MATERIAL_BATCH, JoinType.LEFT);
//        From providers = (From) batches.fetch(MaterialBatch_.PROVIDER, JoinType.LEFT);
//        From inventories = (From) batches.fetch(MaterialBatch_.INVENTORY, JoinType.LEFT);
//        From materials = (From) inventories.fetch(Inventory_.MATERIAL, JoinType.LEFT);
//        From employee = (From) warehouses.fetch(Warehouse_.EMPLOYEES, JoinType.LEFT);
//
//        List<Predicate> predicates = new ArrayList<>();
////        Employee em = commonUserService.findLoginEmployee();
////        Long emId = 0L;
////        if(em!=null){
////            emId = em.getId();
////        }
////        predicates.add(cb.equal(employee.get("id"), emId));
//        //多项目支持
//        predicates.add(cb.equal(activity.get(Inventory_.PROJECT), ProjectContext.getCurrentProject()));
//
//        if (request != null || CollectionUtils.isNotEmpty(request.getColumns())) {
//            Map<String, From> froms = new HashMap<>();
//            froms.put(InventoryActivityAndMaterialBatch_.FROM_INVENTORIES, inventories);
//            froms.put(InventoryActivityAndMaterialBatch_.FROM_MATERIALS, materials);
//            froms.put(InventoryActivityAndMaterialBatch_.FROM_WAREHOUSES, warehouses);
//            froms.put(InventoryActivityAndMaterialBatch_.FROM_LABORERS, laborers);
//            froms.put(InventoryActivityAndMaterialBatch_.FROM_HANDLES, operator);
//            froms.put(InventoryActivityAndMaterialBatch_.FROM_PROVIDERS, providers);
//            froms.put(InventoryActivityAndMaterialBatch_.FROM_INVENTORY_ACTIVITIES, activity);
//            predicates.addAll(buildColumnSearch(cb, root, request, froms));
//        }
//
//        query.distinct(true);
//        query.where(predicates.toArray(new Predicate[predicates.size()]));
//        query.orderBy(cb.desc(root.get(InventoryActivityAndMaterialBatch_.ACTIVITY)));
//        return query;
//    }

    /**
     * build column search predicate
     *
     * @param cb
     * @param root
     * @param request
     * @param froms
     */
//    private List<Predicate> buildColumnSearch(CriteriaBuilder cb, Root<InventoryActivityAndMaterialBatch> root, RecordSearchRequest request, Map<String, From> froms) {
//        List<Predicate> results = new ArrayList<>();
//        //all columns
//        for (DataTableColumn column : request.getColumns()) {
//            //illegal columns
//            if (StringUtils.isBlank(column.getName())) {
//                log.debug("column name is blank!");
//                continue;
//            }
//            try {
//                InventoryActivityAndMaterialBatch_.TABLE columnProperties = InventoryActivityAndMaterialBatch_.TABLE.valueOf(column.getName());
//                String[] properties = columnProperties.getProperties();
//
//                if (ArrayUtils.isEmpty(properties)) {
//                    log.debug("properties in Table is empty!");
//                    continue;
//                }
//
//                //lazy search
//                if (StringUtils.isNotBlank(column.getSearchText()) && properties.length == 2) {
//                    From from = froms.get(properties[0]);
//                    if (from == null) {
//                        log.error("cannot found from predicate in map by:{}", properties[0]);
//                        continue;
//                    }
//                    if (isDateSearch(properties[1])) {
//                        buildDateSearch(cb, results, column, properties[1], from);
//                    }
//                    if (isEnumSearch(properties[1])) {
//                        buildEnumSearch(cb, results, column, properties[1], from);
//                    } else if (isNumericSearch(properties[1])) {
//                        buildNumericSearch(cb, results, column, properties[1], from);
//                    } else {
//                        Predicate predicate = cb.like(from.get(properties[1]), dataTableService.buildLikeText(column.getSearchText()));
//                        results.add(predicate);
//                    }
//                } else if (properties.length == 1 && StringUtils.isNotBlank(column.getSearchText())) {
//                    if (isDateSearch(properties[0])) {
//                        buildDateSearch(cb, results, column, properties[0], root);
//                    }
//                    if (isEnumSearch(properties[0])) {
//                        buildEnumSearch(cb, results, column, properties[0], root);
//                    } else if (isNumericSearch(properties[0])) {
//                        buildNumericSearch(cb, results, column, properties[0], root);
//                    } else {
//                        Predicate predicate = cb.like(root.get(properties[0]).as(String.class), dataTableService.buildLikeText(column.getSearchText()));
//                        results.add(predicate);
//                    }
//                } else {
//                    log.debug("properties:{} length is not in [1,2] or search text:{} is empty", properties, column.getSearchText());
//                    continue;
//                }
//            } catch (IllegalArgumentException e) {
//                log.error("enum type has no constant with the specified name:{}", column.getName());
//            }
//        }
//
//        return results;
//    }

    private boolean isNumericSearch(String property) {
        if (StringUtils.isNotBlank(property) && property.startsWith(InventoryActivityAndMaterialBatch_.NUMERIC_SEARCH_SEPARATOR)) {
            return true;
        }
        return false;
    }

    private void buildNumericSearch(CriteriaBuilder cb, List<Predicate> results, DataTableColumn column, String property, From from) {
        String propertyAfter = property.replace(InventoryActivityAndMaterialBatch_.NUMERIC_SEARCH_SEPARATOR, "");
        results.add(cb.equal(from.get(propertyAfter), column.getSearchText()));
    }

    private void buildEnumSearch(CriteriaBuilder cb, List<Predicate> results, DataTableColumn column, String property, From from) {
        String propertyAfter = property.replace(InventoryActivityAndMaterialBatch_.ENUM_SEARCH_SEPARATOR, "");
        if (column.getSearchText().equals(InventoryManagementActivity.ActivityType_RESERVE)){
            List<InventoryManagementActivity.InventoryActivityType> types = new ArrayList<>();
            types.add(InventoryManagementActivity.InventoryActivityType.RESERVE);
            types.add(InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE);
            types.add(InventoryManagementActivity.InventoryActivityType.REJECTED);
            types.add(InventoryManagementActivity.InventoryActivityType.AUDITSUC);
            results.add(from.<InventoryManagementActivity.InventoryActivityType>get(propertyAfter).in(types));
        } else {
            results.add(cb.equal(from.get(propertyAfter), InventoryManagementActivity.InventoryActivityType.valueOf(column.getSearchText())));
        }
    }

    private boolean isEnumSearch(String property) {
        if (StringUtils.isNotBlank(property) && property.startsWith(InventoryActivityAndMaterialBatch_.ENUM_SEARCH_SEPARATOR)) {
            return true;
        }
        return false;
    }

    private void buildDateSearch(CriteriaBuilder cb, List<Predicate> results, DataTableColumn column, String property, From from) {
        String dateRange = column.getSearchText();
        String[] range = dateRange.split(WorkOrderConstant.DATE_RANGE_SPLIT_CHAR);
        try {
            Date start = DATE_FORMATTER.parse(range[0].trim());
            Date end = DATE_FORMATTER.parse(range[1].trim());
            String propertyAfter = property.replace(InventoryActivityAndMaterialBatch_.DATE_SEARCH_SEPARATOR, "");
            results.add(cb.between(from.get(propertyAfter).as(Date.class), DateUtil.buildDateOnFirstSecond(start), DateUtil.buildDateOnLastSecond(end)));
        } catch (ParseException e) {
            log.error("Parse date error -- string:{}", dateRange, e);
        }
    }

    private boolean isDateSearch(String property) {
        if (StringUtils.isNotBlank(property) && property.contains(InventoryActivityAndMaterialBatch_.DATE_SEARCH_SEPARATOR)) {
            return true;
        }
        return false;
    }

//    @Override
//    public List<InventoryRecordDTO> toDTO(List<InventoryActivityAndMaterialBatch> activities) {
//        if (CollectionUtils.isEmpty(activities)) {
//            return Collections.emptyList();
//        }
//
//        List<InventoryRecordDTO> dtos = new ArrayList<>();
//        for (InventoryActivityAndMaterialBatch inventoryActivityAndMaterialBatch : activities) {
//            InventoryRecordDTO dto = toDTO(inventoryActivityAndMaterialBatch);
//            if (dto != null) {
//                dtos.add(dto);
//            }
//        }
//
//        return dtos;
//    }

    @Override
    public List<InventoryRecordDTO> findRecords(RecordSearchRequest request) {
        StringBuilder sb = new  StringBuilder();
        sb.append("select new cn.facilityone.shang.stock.stock004.dto.InventoryRecordDTO(ia.no, ia.type, wh.name, ma.code, " +
                " ma.name, ma.brand, ma.model, v.name, mc.changeNum, mb.unit, mb.price, mc.changeNum*mb.price, " +
                " em1.name, ia.operateDate, em2.name, ia.createdDate, ia.description, mc.type,inv.displayRack) " +
                " from MaterialBatchChange mc " +
                " left join mc.inventoryManagementActivity ia " +
                " left join mc.materialBatch mb " +
                " left join mc.materialBatch.provider v" +
                " left join mb.inventory inv   " +
                " left join inv.material ma  " +
                " left join inv.warehouse wh " +
                " left join ia.handles em1 " +
                " left join ia.laborer em2 " +
                " where ia.project=:proId and ia.deleted=0 ");
        this.buildSqlColumnSearch(sb, request.getColumns());
        sb.append(" order by ia.id desc ");
        TypedQuery<InventoryRecordDTO> result = entityManager.createQuery(sb.toString(), InventoryRecordDTO.class);
        result.setParameter("proId", ProjectContext.getCurrentProject());

        List<InventoryRecordDTO> list = result.setFirstResult(request.getOffset()).setMaxResults(request.getPageSize()).getResultList();
        for (InventoryRecordDTO dto : list) {
            if (InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE.equals(dto.getType())
                    || InventoryManagementActivity.InventoryActivityType.REJECTED.equals(dto.getType())
                    || InventoryManagementActivity.InventoryActivityType.AUDITSUC.equals(dto.getType())
                    || InventoryManagementActivity.InventoryActivityType.CANCEL_OUT.equals(dto.getType())) {
                dto.setType(InventoryManagementActivity.InventoryActivityType.RESERVE);
            }
            if (dto.getType() == InventoryManagementActivity.InventoryActivityType.RESERVE) {
                dto.setProvider("");
                dto.setPrice(null);
                dto.setTotalPrice(null);
            }
            double operationNumber = dto.getOperationNumber();
            if (null!=dto.getPrice()){
                dto.setPrice(DoubleOperationUtils.changeDecimal(dto.getPrice(),2));
            }
            if (null!=dto.getTotalPrice()){
                dto.setTotalPrice(DoubleOperationUtils.changeDecimal(dto.getTotalPrice(),2));
            }
            dto.setOperationNumber(DoubleOperationUtils.changeDecimal(operationNumber,2));
            if (dto.getAdjustType() == MaterialBatchChange.AdjustType.DOWN) {
                dto.setOperationNumber(-dto.getOperationNumber());
            }
        }
        return list;
    }

    public void buildSqlColumnSearch(StringBuilder sb, List<DataTableColumn> columns) {
        String types = "("+InventoryManagementActivity.InventoryActivityType.RESERVE.ordinal()+"," +
                InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE.ordinal()+"," +
                InventoryManagementActivity.InventoryActivityType.AUDITSUC.ordinal()+"," +
                InventoryManagementActivity.InventoryActivityType.CANCEL_OUT.ordinal()+"," +
                InventoryManagementActivity.InventoryActivityType.REJECTED.ordinal()+")";
         for(DataTableColumn column : columns){
            String searchText = column.getSearchText();
            if(StringUtils.isNotEmpty(searchText)){
                if(DataTableColumn.SEARCH_TYPE_ENUM.equals(column.getSearchType())){
                    InventoryManagementActivity.InventoryActivityType type = Enum.valueOf(InventoryManagementActivity.InventoryActivityType.class, searchText);
                    String columnName = column.getName();
                    if("type".equals(columnName)){
                        if(InventoryManagementActivity.InventoryActivityType.RESERVE.equals(type)){
                            sb.append(" and "+"ia."+column.getName()+" in "+types);
                        }else{
                            sb.append(" and "+"ia."+columnName+" = "+type.ordinal());
                        }
                    }
                }else if(DataTableColumn.SEARCH_TYPE_DATE_RANGE.equals(column.getSearchType())){
                    String[] strCols = searchText.split(SystemConst.STR_TILDE);
                    Date endDate = null;
                    if (strCols.length == 2 && !org.apache.commons.lang3.StringUtils.isEmpty(strCols[1])) {
                        try {
                            endDate = DateUtils.parseDateStrictly(strCols[1].trim(), SystemConst.DATE_YYYY_MM_DD_HYPHEN);
                            endDate = DateUtils.addDays(endDate, 1);
                        } catch (ParseException e) {
                            log.error("parse date {} error : {}",strCols[1].trim(),e.getMessage());
                        }
                    }
                    sb.append(" and "+"ia."+column.getName()+" between '"+strCols[0].toString()+"' and '"+ DateUtil.formatDateYYYYMMDD(endDate)+"'");
                }else if ("no".equals(column.getName()) || "description".equals(column.getName())){
                    sb.append(" and "+"ia."+column.getName()+" like '%"+searchText+"%' ");
                }else if ("warehouseName".equals(column.getName())) {
                    sb.append(" and "+"wh.name like '%"+searchText+"%' ");
                }else if ("materialCode".equals(column.getName())) {
                    sb.append(" and "+"ma.code like '%"+searchText+"%' ");
                }else if ("materialName".equals(column.getName())) {
                    sb.append(" and "+"ma.name like '%"+searchText+"%' ");
                }else if ("brand".equals(column.getName()) || "model".equals(column.getName())) {
                    sb.append(" and "+"ma."+column.getName()+" like '%"+searchText+"%' ");
                }else if ("provider".equals(column.getName())) {
                    sb.append(" and "+"v.name like '%"+searchText+"%' ");
                    sb.append(" and "+"ia.type not in "+types);
                }else if ("unit".equals(column.getName())) {
                    sb.append(" and "+"mb."+column.getName()+" like '%"+searchText+"%' ");
                }else if ("operatorName".equals(column.getName())) {
                    sb.append(" and "+"em2.name like '%"+searchText+"%' ");
                }else if ("laborerName".equals(column.getName())) {
                    sb.append(" and "+"em1.name like '%"+searchText+"%' ");
                }else if ("operationNumber".equals(column.getName())) {
                    sb.append(" and "+"mc.changeNum like '%"+searchText+"%' ");
                }else if ("price".equals(column.getName())) {
                    sb.append(" and "+"mb.price like '%"+searchText+"%' ");
                    sb.append(" and "+"ia.type not in "+types);
                } else if ("totalPrice".equals(column.getName())) {
                    sb.append(" and "+"mc.changeNum*mb.price like '%"+searchText+"%' ");
                }else if ("displayRack".equals(column.getName())) {
                    sb.append(" and "+"inv.displayRack like '%"+searchText+"%' ");
                }
            }
        }
    }

    @Override
    public List<InventoryRecordExportDTO> setDTO(List<InventoryRecordDTO> activities) {
        if (CollectionUtils.isEmpty(activities)) {
            return Collections.emptyList();
        }

        List<InventoryRecordExportDTO> dtos = new ArrayList<>();
        for (InventoryRecordDTO inventoryRecordDTO : activities) {
            InventoryRecordExportDTO dto = setDTO(inventoryRecordDTO);
            if (dto != null) {
                dtos.add(dto);
            }
        }

        return dtos;
    }

//    @Override
//    public int getTotalCount(RecordSearchRequest request) {
//        List<InventoryActivityAndMaterialBatch> inventoryActivityAndMaterialBatchList = findAllRecords(request);
//        if (CollectionUtils.isEmpty(inventoryActivityAndMaterialBatchList)) {
//            return 0;
//        }
//        List<InventoryRecordDTO> dtos = toDTO(inventoryActivityAndMaterialBatchList);
//        return dtos == null ? 0 : dtos.size();
//    }

    @Override
    public int getTotalNumber(RecordSearchRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(distinct mc.id) " +
                " from MaterialBatchChange mc " +
                " left join mc.inventoryManagementActivity ia " +
                " left join mc.materialBatch mb " +
                " left join mc.materialBatch.provider v" +
                " left join mb.inventory inv   " +
                " left join inv.material ma  " +
                " left join inv.warehouse wh " +
                " left join ia.handles em1 " +
                " left join ia.laborer em2 " +
                " where ia.project=:proId and ia.deleted=0 ");
        this.buildSqlColumnSearch(sb, request.getColumns());
        Object obj = entityManager.createQuery(sb.toString()).setParameter("proId", ProjectContext.getCurrentProject()).getSingleResult();
        int total = 0;
        if(null != obj){
            total = Integer.parseInt(obj.toString());
        }
        return total;
    }

//    @Override
//    public List<InventoryActivityAndMaterialBatch> findAllRecords(RecordSearchRequest request) {
//        CriteriaQuery<InventoryActivityAndMaterialBatch> query = createQueryPredicate(request);
//        List<InventoryActivityAndMaterialBatch> inventoryActivityAndMaterialBatchList = entityManager.createQuery(query).getResultList();
//        return inventoryActivityAndMaterialBatchList;
//    }

    /**
     * single activityAndMaterialBatch to dtos
     *
     * @param activityAndMaterialBatch
     * @return
     */
//    private InventoryRecordDTO toDTO(InventoryActivityAndMaterialBatch activityAndMaterialBatch) {
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        MaterialBatch materialBatch = activityAndMaterialBatch.getBatch();
//        InventoryManagementActivity activity = activityAndMaterialBatch.getActivity();
//        if (materialBatch == null || activity == null) {
//            return null;
//        }
//
//        Warehouse warehouse = activity.getWarehouse();
//        String warehouseName = "";
//        if (warehouse != null) {
//            warehouseName = warehouse.getName();
//        }
//
//        Employee laborer = activity.getLaborer();
//        String laborerName = "";
//        if (laborer != null) {
//            laborerName = laborer.getName();
//        }
//        Employee operator = activity.getHandles();
//        String operatorName="";
//        if (operator != null) {
//            operatorName = operator.getName();
//        }
//        InventoryRecordDTO dto = new InventoryRecordDTO();
//        dto.setOperationNumber(DoubleOperationUtils.changeDecimal(getChangeNumber(activity, materialBatch),2));
////        dto.setCreatedBy(activity.getCreatedBy());
//        if(null!=activity.getCreatedDate()){
//            try {
//                dto.setCreatedDate(simpleDateFormat.parse(buildDateAndTime(activity.getCreatedDate())));
//            } catch (ParseException e) {
//                e.printStackTrace();
//            }
//        }
//        dto.setWarehouseName(warehouseName);
//        dto.setDescription(activity.getDescription());
//        dto.setLaborerName(laborerName);
//        dto.setOperatorName(operatorName);
//        dto.setNo(activity.getNo());
//        if (activity.getOperateDate() != null) {
////            dto.setOperateDate(DATE_FORMATTER.format(activity.getOperateDate()));
//            dto.setOperateDate(activity.getOperateDate());
//        }
//        dto.setType(activity.getType());
//        if (dto.getType() != InventoryManagementActivity.InventoryActivityType.RESERVE) {
//            dto.setProvider(materialBatch.getProvider() == null ? "" : materialBatch.getProvider().getName());
//            dto.setPrice(materialBatch.getPrice());
//            dto.setTotalPrice(DoubleOperationUtils.doubleMul(dto.getOperationNumber(),dto.getPrice() == 0d ? 0d : dto.getPrice(),2));
//        }
//        Inventory inventory = materialBatch.getInventory();
//        if (inventory != null && inventory.getMaterial() != null) {
//            Material material = inventory.getMaterial();
//            dto.setMaterialCode(material.getCode());
//            dto.setMaterialName(material.getName());
//            dto.setModel(material.getModel());
//            dto.setBrand(material.getBrand());
////            dto.setPrice(material.getPrice());
//            dto.setUnit(material.getUnit());
//        }
//
//        return dto;
//    }

    private InventoryRecordExportDTO setDTO(InventoryRecordDTO inventoryRecordDTO) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String warehouseName = "";
        if (inventoryRecordDTO.getWarehouseName() != null) {
            warehouseName = inventoryRecordDTO.getWarehouseName();
        }

        String laborerName = "";
        if (inventoryRecordDTO.getLaborerName() != null) {
            laborerName = inventoryRecordDTO.getLaborerName();
        }
        String operatorName="";
        if (inventoryRecordDTO.getOperatorName() != null) {
            operatorName = inventoryRecordDTO.getOperatorName();
        }
        InventoryRecordExportDTO dto = new InventoryRecordExportDTO();
        dto.setOperationNumber(DoubleOperationUtils.changeDecimal(inventoryRecordDTO.getOperationNumber(),2));
        if(null!=inventoryRecordDTO.getCreatedDate()){
            try {
                dto.setCreatedDate(simpleDateFormat.parse(buildDateAndTime(inventoryRecordDTO.getCreatedDate())));
            } catch (ParseException e) {
                log.error("parse date {} error : {}",inventoryRecordDTO.getCreatedDate(),e.getMessage());
            }
        }
        dto.setWarehouseName(warehouseName);
        dto.setDescription(inventoryRecordDTO.getDescription());
        dto.setLaborerName(laborerName);
        dto.setOperatorName(operatorName);
        dto.setNo(inventoryRecordDTO.getNo());
        if (inventoryRecordDTO.getOperateDate() != null) {
            dto.setOperateDate(DATE_FORMATTER.format(inventoryRecordDTO.getOperateDate()));
        }
        if (inventoryRecordDTO.getType()== InventoryManagementActivity.InventoryActivityType.REJECTED
                || inventoryRecordDTO.getType()== InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE
                || inventoryRecordDTO.getType()== InventoryManagementActivity.InventoryActivityType.AUDITSUC
                || inventoryRecordDTO.getType()== InventoryManagementActivity.InventoryActivityType.CANCEL_OUT) {
            inventoryRecordDTO.setType(InventoryManagementActivity.InventoryActivityType.RESERVE);
        }
        if (inventoryRecordDTO.getType() != InventoryManagementActivity.InventoryActivityType.RESERVE) {
            dto.setProvider(inventoryRecordDTO.getProvider() == null ? "" : inventoryRecordDTO.getProvider());
            dto.setPrice(inventoryRecordDTO.getPrice());
            dto.setTotalPrice(DoubleOperationUtils.doubleMul(dto.getOperationNumber(),dto.getPrice() == null ? 0d : dto.getPrice(),2));
        }
        dto.setType(XiaMesssageResource.getMessage("InventoryManagementActivity.type."+inventoryRecordDTO.getType().toString()+""));

        dto.setMaterialCode(inventoryRecordDTO.getMaterialCode());
        dto.setMaterialName(inventoryRecordDTO.getMaterialName());
        dto.setModel(inventoryRecordDTO.getModel());
        dto.setBrand(inventoryRecordDTO.getBrand());
        dto.setUnit(inventoryRecordDTO.getUnit());
        if(null != inventoryRecordDTO.getDisplayRack()){
            dto.setDisplayRack(inventoryRecordDTO.getDisplayRack());
        }

        return dto;
    }

    public String buildDateAndTime(Date d){
        String result = null;
        if(d!=null){
            result = DateUtil.parseDate(d, "yyyy-MM-dd HH:mm:ss");
        }
        return result;
    }

    /**
     * get changed number for the batch
     *
     * @param activity
     * @param materialBatch
     * @return
     */
    private double getChangeNumber(InventoryManagementActivity activity, MaterialBatch materialBatch) {
        StringBuilder sb = new StringBuilder(400);
        sb.append(" SELECT mc.changenum,mc.type FROM ");
        sb.append(" materialbatch b, materialbatch_activity ma,inventory_management_activity ima,materialbatch_change mc ");
        sb.append(" where b.materialbatch_id = ma.materialbatch_id and ma.ima_id = ima.ima_id ");
        sb.append(" and mc.materialbatch_id  = b.materialbatch_id and mc.activity_id = ima.ima_id ");
        sb.append("  and  b.materialbatch_id = ?1 and ima.ima_id = ?2 ");

        Query query = entityManager.createNativeQuery(sb.toString());
        query.setParameter(1, materialBatch.getId());
        query.setParameter(2, activity.getId());

        List<Object> numbers = query.getResultList();

        if (CollectionUtils.isEmpty(numbers)) {
            return 0;
        }

        double result = 0;
        boolean isAdded = true;
        for (Object number : numbers) {
            Object[] objects = (Object[]) number;
            result += (Double) objects[0];
            int type = (int) objects[1];
            if (type == 1) {
                isAdded = false;
            }
        }

        return isAdded ? result : result * -1;
    }
}
