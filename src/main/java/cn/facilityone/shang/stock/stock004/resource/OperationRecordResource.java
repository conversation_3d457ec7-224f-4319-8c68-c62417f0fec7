package cn.facilityone.shang.stock.stock004.resource;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import cn.facilityone.shang.stock.stock004.dto.InventoryRecordExportDTO;
import org.apache.commons.lang.StringUtils;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.configuration.properties.CompanyProperties;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.stock.stock004.dto.InventoryRecordDTO;
import cn.facilityone.shang.stock.stock004.dto.RecordSearchRequest;
import cn.facilityone.shang.stock.stock004.service.OperationRecordService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.transfer.core.Exporter;
import cn.facilityone.xia.transfer.core.data.DTO;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 记录管理
 * Created by wayne.fu on 15/7/27.
 */

@Path("/stock004")
public class OperationRecordResource {

    private static final String TEMPLATE_User_PATH = "/business/stockV2/stock004-operateRecord.ftl";

    @Autowired
    private OperationRecordService operationRecordService;

    @Autowired
    @Qualifier("xlsxExporter")
    private Exporter exporter;
    
    @Autowired
    private CompanyProperties companyProperties;

    @GET
    @Template(name = TEMPLATE_User_PATH)
    public Map<String, Object> init() {
        Map<String, Object> data = new HashMap<String, Object>();
        Map<String, String> lang = XiaMesssageResource.getSupportLanguages();
        data.put("languages", lang);
        return data;
    }

    @POST
    @Path("records/table")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getRecordTable(RecordSearchRequest request) {
//        List<InventoryActivityAndMaterialBatch> activities = operationRecordService.findActivityInPage(request);
//        List<InventoryRecordDTO> dtos = operationRecordService.toDTO(activities);
        List<InventoryRecordDTO> dtoList = operationRecordService.findRecords(request);
//        int totalCount = operationRecordService.getTotalCount(request);
        int totalNumber = operationRecordService.getTotalNumber(request);
        return new DataTableResponse(dtoList,totalNumber,request.getPageNumber()-1,request.getDraw());
    }


    @POST
    @Path("records/export")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response export(String params) {
        RecordSearchRequest request = null;
        if (StringUtils.isNotBlank(params)) {
            try {
                request = new ObjectMapper().readValue(params, RecordSearchRequest.class);
                request.setOffset(0);
                request.setPageSize(1000);
            } catch (Exception e) {
                return Response.ok(new Result(Response.Status.BAD_REQUEST.getStatusCode(), "IO流错误!", "")).build();
            }
        }
        List<InventoryRecordDTO> allInventories = operationRecordService.findRecords(request);
        List<InventoryRecordExportDTO> dtos = operationRecordService.setDTO(allInventories);
        LinkedHashMap<String, String> title = new LinkedHashMap<String, String>();
        title.put("no", XiaMesssageResource.getMessage("InventoryManagementActivity.no"));
        title.put("type", XiaMesssageResource.getMessage("InventoryManagementActivity.type"));
        title.put("warehouseName", XiaMesssageResource.getMessage("InventoryManagementActivity.warehouseName"));
        title.put("displayRack", XiaMesssageResource.getMessage("Inventory.displayRack"));
        title.put("materialCode", XiaMesssageResource.getMessage("Material.code"));
        title.put("materialName", XiaMesssageResource.getMessage("InventoryManagementActivity.materialName"));
        title.put("brand", XiaMesssageResource.getMessage("Material.brand"));
        title.put("model", XiaMesssageResource.getMessage("Material.model"));
        title.put("provider", XiaMesssageResource.getMessage("Equipment.provider"));
        title.put("operationNumber", XiaMesssageResource.getMessage("InventoryManagementActivity.operationNumber"));
        title.put("unit", XiaMesssageResource.getMessage("Material.unit"));
        title.put("price", XiaMesssageResource.getMessage("Material.price"));
        title.put("totalPrice", XiaMesssageResource.getMessage("page.stock004.totalPrice"));
        title.put("laborerName",XiaMesssageResource.getMessage("InventoryManagementActivity.laborer"));
        title.put("operateDate", XiaMesssageResource.getMessage("InventoryManagementActivity.operationDate"));
        title.put("operatorName", XiaMesssageResource.getMessage("InventoryManagementActivity.createdBy"));
        title.put("createdDate",XiaMesssageResource.getMessage("InventoryManagementActivity.createdDate"));
        title.put("description", XiaMesssageResource.getMessage("InventoryManagementActivity.description"));
        
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("companyName", companyProperties.getName());
        DTO dto = new DTO(map);
        dto.addTitleAndData(title, dtos);
        ByteArrayOutputStream out = (ByteArrayOutputStream) exporter
                .export(dto);
        return Response.ok(
                new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.success.export"),
                        FileUtil.exportFile(out, FileUtil.FILE_TYPE_EXCELX))).build();
    }


}
