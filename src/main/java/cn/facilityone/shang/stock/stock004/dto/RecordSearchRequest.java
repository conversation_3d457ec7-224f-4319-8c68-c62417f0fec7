package cn.facilityone.shang.stock.stock004.dto;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;

import java.util.Date;
import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 8/14/2015
 */
public class RecordSearchRequest extends DataTableRequest {
    private List<InventoryManagementActivity.InventoryActivityType> type;
    private Long laborer;
    private Date inDate;
    private Date outDate;
    private Date bookDate;
    private Date backDate;

    public Date getBackDate() {
        return backDate;
    }

    public void setBackDate(Date backDate) {
        this.backDate = backDate;
    }

    public Date getBookDate() {
        return bookDate;
    }

    public void setBookDate(Date bookDate) {
        this.bookDate = bookDate;
    }

    public Date getInDate() {
        return inDate;
    }

    public void setInDate(Date inDate) {
        this.inDate = inDate;
    }

    public Long getLaborer() {
        return laborer;
    }

    public void setLaborer(Long laborer) {
        this.laborer = laborer;
    }

    public Date getOutDate() {
        return outDate;
    }

    public void setOutDate(Date outDate) {
        this.outDate = outDate;
    }

    public List<InventoryManagementActivity.InventoryActivityType> getType() {
        return type;
    }

    public void setType(List<InventoryManagementActivity.InventoryActivityType> type) {
        this.type = type;
    }
}
