package cn.facilityone.shang.stock.stock004.service;

import cn.facilityone.shang.stock.stock004.dto.InventoryRecordDTO;
import cn.facilityone.shang.stock.stock004.dto.InventoryRecordExportDTO;
import cn.facilityone.shang.stock.stock004.dto.RecordSearchRequest;

import java.util.List;

/**
 * @Author: wayne.fu
 * @Date: 8/14/2015
 */
public interface OperationRecordService {
    //List<InventoryActivityAndMaterialBatch> findActivityInPage(RecordSearchRequest request);

   // List<InventoryRecordDTO> toDTO(List<InventoryActivityAndMaterialBatch> activities);

    List<InventoryRecordDTO> findRecords(RecordSearchRequest request);

    List<InventoryRecordExportDTO> setDTO(List<InventoryRecordDTO> activities);

    //int getTotalCount(RecordSearchRequest request);

    int getTotalNumber(RecordSearchRequest request);

   // List<InventoryActivityAndMaterialBatch> findAllRecords(RecordSearchRequest request);
}
