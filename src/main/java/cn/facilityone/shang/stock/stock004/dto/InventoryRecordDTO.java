package cn.facilityone.shang.stock.stock004.dto;

import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;

import java.util.Date;

/**
 * @Author: wayne.fu
 * @Date: 8/17/2015
 */
public class InventoryRecordDTO {
    private String no;
    private InventoryManagementActivity.InventoryActivityType type;
    private String warehouseName;
    private String materialCode;
    private String materialName;
    private String displayRack;
    private String brand;
    private String model;
    private String provider;
    private double operationNumber;
    private String unit;
    private Double price;
    private Double totalPrice;
    //    private double amount;
    private String laborerName;
    private Date operateDate;

    private String operatorName;
    private Date createdDate;
//    private String createdBy;
    private String description;
    private MaterialBatchChange.AdjustType adjustType;

//    public double getAmount() {
//        return amount;
//    }
//
//    public void setAmount(double amount) {
//        this.amount = amount;
//    }
    public InventoryRecordDTO() {

    }
    public InventoryRecordDTO(String no, InventoryManagementActivity.InventoryActivityType type, String warehouseName, String materialCode, String materialName, String brand, String model,
                         String provider, double operationNumber, String unit, double price, double totalPrice, String laborerName,Date operateDate,
                         String operatorName, Date createdDate, String description, MaterialBatchChange.AdjustType adjustType,String displayRack) {
    this.no = no;
    this.type = type;
    this.warehouseName = warehouseName;
    this.materialCode = materialCode;
    this.materialName = materialName;
    this.brand = brand;
    this.model = model;
    this.provider = provider;
    this.operationNumber = operationNumber;
    this.unit = unit;
    this.price = price;
    this.totalPrice = totalPrice;
    this.laborerName=laborerName;
    this.operateDate=operateDate;
    this.operatorName=operatorName;
    this.createdDate=createdDate;
    this.description=description;
    this.adjustType=adjustType;
    this.displayRack = displayRack;
}

    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

//    public String getCreatedBy() {
//        return createdBy;
//    }
//
//    public void setCreatedBy(String createdBy) {
//        this.createdBy = createdBy;
//    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLaborerName() {
        return laborerName;
    }

    public void setLaborerName(String laborerName) {
        this.laborerName = laborerName;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public double getOperationNumber() {
        return operationNumber;
    }

    public void setOperationNumber(double operationNumber) {
        this.operationNumber = operationNumber;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public InventoryManagementActivity.InventoryActivityType getType() {
        return type;
    }

    public void setType(InventoryManagementActivity.InventoryActivityType type) {
        this.type = type;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public MaterialBatchChange.AdjustType getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(MaterialBatchChange.AdjustType adjustType) {
        this.adjustType = adjustType;
    }

}
