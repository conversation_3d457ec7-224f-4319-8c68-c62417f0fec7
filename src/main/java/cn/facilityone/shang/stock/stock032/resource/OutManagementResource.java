package cn.facilityone.shang.stock.stock032.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.stock.stock003.service.InventoryManagementActivityService;
import cn.facilityone.shang.stock.stock032.service.OutManagementService;
import cn.facilityone.shang.stock.stock036.dto.CheckReserveDto;
import cn.facilityone.shang.stock.stock036.dto.ReserverDetailDto;
import cn.facilityone.shang.stock.stock036.dto.ReverseManagerDto;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Path("/stock032")
public class OutManagementResource {
    private static final String TEMPLATE_POSITION_PATH = "/business/stockV2/stock032-outManagement.ftl";

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private OutManagementService outManagementService;

    @Autowired
    private InventoryManagementActivityService inventoryManagementActivityService;

    @GET
    @Template(name = TEMPLATE_POSITION_PATH)
    public Map<String, Object> init() {
        Map<String, Object> map = new HashMap<>();
        XiaPrincipal user = (XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal();
        Long userId = user.getId();
        Employee employee = employeeRepository.findOneByUserId(userId);
        if(null != employee){
            map.put("employeeid", String.valueOf(employee.getId()));
            map.put("employeename", employee.getName());
        }
        return map;
    }

    @POST
    @Path("table")
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findReserves(DataTableRequest request) {
        List<ReverseManagerDto> reserves = outManagementService.findAllReservesInPage(request);
        int totalCount = outManagementService.findAllReservesCount(request);
        return new DataTableResponse(reserves, totalCount, request.getPageNumber(), request.getDraw());
    }

    /**
     * 取消出库
     */
    @POST
    @Path("cancelorder/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result rejectedStock(@PathParam("id") Long id, CheckReserveDto dto) {
        //判断是否取消出库
        boolean isoutStockOut = inventoryManagementActivityService.findIsoutStockOut(id);
        //判断是否已出库
        boolean isOut = inventoryManagementActivityService.findIsoutbound(id);
        if(isoutStockOut){
            Result result = new Result();
            result.setMessage(XiaMesssageResource.getMessage("MS000035",null,""));
            result.setStatus(Result.STATUS_ERROR);
            result.setData(null);
            result.setCode(Result.CODE_200);
            return result;
            }else if(isOut) {
            Result result = new Result();
            result.setCode(Result.CODE_200);
            result.setMessage(XiaMesssageResource.getMessage("MS000034",null,""));
            result.setData(null);
            result.setStatus(Result.STATUS_ERROR);
            return result;
        }else {
                outManagementService.rejectedStock(id, dto.getContent());
                return new Result(XiaMesssageResource.getMessage("MS000032", null, ""));
            }
    }

    /**
     * 预定详情列表
     * @return
     */
    @POST
    @Path("detailtable/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findetailtable(@PathParam("id") Long id) {
        List<ReserverDetailDto> reserves = outManagementService.findDetailReserve(id);
        return new Result(reserves);
    }

}
