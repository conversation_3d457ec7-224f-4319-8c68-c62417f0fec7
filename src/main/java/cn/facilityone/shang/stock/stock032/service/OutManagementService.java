package cn.facilityone.shang.stock.stock032.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.stock.stock036.dto.ReserverDetailDto;
import cn.facilityone.shang.stock.stock036.dto.ReverseManagerDto;

import java.util.List;

/**
 * Created by panda.yang on 2016/11/10.
 */
public interface OutManagementService {
    List<ReverseManagerDto> findAllReservesInPage(DataTableRequest request);

    int findAllReservesCount(DataTableRequest request);

    void rejectedStock(Long id, String content);

    List<ReserverDetailDto> findDetailReserve(Long id);

    Long findAllReservesCountByMobile();
}