package cn.facilityone.shang.stock.stock032.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.entity.inventory.InventoryActivity;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.inventory.respository.InventoryActivityRepository;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.stock.stock003.service.InventoryManagementActivityService;
import cn.facilityone.shang.stock.stock036.dto.ReserverDetailDto;
import cn.facilityone.shang.stock.stock036.dto.ReverseManagerDto;
import cn.facilityone.shang.stock.stock036.service.ReserveManagerService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by panda.yang on 2016/11/10.
 */
@Service
public class OutManagementServiceImpl implements OutManagementService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private InventoryActivityRepository inventoManagementActivity;

    @Autowired
    private InventoryManagementActivityService inventoryManagementActivityService;

    @Autowired
    private ReserveManagerService reserveManagerService;

    @Autowired
    private CommonUserService userService;;

    @Override
    public List<ReverseManagerDto> findAllReservesInPage(DataTableRequest request) {
        return reserveManagerService.findAllReservesInPage(request, queryTypesParam(), InventoryManagementActivity.MANAGERS);
    }

    public int findAllReservesCount(DataTableRequest request){
        return reserveManagerService.findAllReservesCount(request, queryTypesParam(), InventoryManagementActivity.MANAGERS);
    }

    public List<InventoryManagementActivity.InventoryActivityType> queryTypesParam(){
        List<InventoryManagementActivity.InventoryActivityType> types = new ArrayList<InventoryManagementActivity.InventoryActivityType>();
        types.add(InventoryManagementActivity.InventoryActivityType.AUDITSUC);
        return types;
    }

    /**
     * 取消出库
     * @param id
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void rejectedStock(Long id, String content) {


        //取消出库
        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityService.
                    handleCancelStockReserve(id, InventoryManagementActivity.InventoryActivityType.CANCEL_OUT);
        //预定记录
        InventoryActivity inventoryActivity  = new InventoryActivity();
        inventoryActivity.setReserveNo(inventoryManagementActivity.getNo());
        inventoryActivity.setActivityType(InventoryActivity.InventoryActivityType.RESERVE);
        inventoryActivity.setStatus(InventoryActivity.InventoryActivityStatus.CANCELOUT);
        inventoryActivity.setComment(content);
        inventoManagementActivity.save(inventoryActivity);
    }

    @Override
    public List<ReserverDetailDto> findDetailReserve(Long id) {
        return reserveManagerService.findDetailReserve(id);
    }

    @Override
    public Long findAllReservesCountByMobile() {
        Long emId = userService.findCurrentEmployeeId();
        List<InventoryManagementActivity.InventoryActivityType> types = queryTypesParam();
        String logintype = "managers";
        StringBuilder sb = new StringBuilder();
        sb.append("select count(ac.id) from InventoryManagementActivity ac " +
                " where ac.deleted = 0 and ac.project=:projectId and ac.type in (:type)  and ac." + logintype + ".id=:supId ");
        sb.append(" and ac.connectNo is null ");
        Object obj = entityManager.createQuery(sb.toString())
                .setParameter("projectId", ProjectContext.getCurrentProject())
                .setParameter("type", types)
                .setParameter("supId", emId)
                .getSingleResult();
        Long total = 0L;
        if (null != obj) {
            total = Long.valueOf(Integer.parseInt(obj.toString()));
        }
        return total;
    }
}
