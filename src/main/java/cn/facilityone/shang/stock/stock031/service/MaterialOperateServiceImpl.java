package cn.facilityone.shang.stock.stock031.service;

import cn.facilityone.shang.asset.asset002.service.ProviderService;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.common.util.ValidateUtil;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.Material;
import cn.facilityone.shang.stock.common.repository.InventoryRepository;
import cn.facilityone.shang.stock.common.staticmetamodel.Inventory_;
import cn.facilityone.shang.stock.stock003.service.MaterialService;
import cn.facilityone.shang.stock.stock031.dto.MaterialDto;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.transfer.core.Importer;
import cn.facilityone.xia.transfer.core.data.DTO;
import cn.facilityone.xia.transfer.core.data.Template;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.*;

/**
 * Created by panda.yang on 2016/11/10.
 */
@Service
public class MaterialOperateServiceImpl implements MaterialOperateService {

    private static final Logger log = LoggerFactory.getLogger(MaterialOperateServiceImpl.class);

    private static final String EXCEL_TYPE = "XLS|XLSX";

    @Autowired
    private UploadFileService uploadFileService;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private MaterialService materialService;

    @Autowired
    @Qualifier("xlsxImporter")
    private Importer importer;

    @Override
    @Transactional
    public Result importData(Long docId, Long warehouseId) {
        List<String> errorM = new LinkedList<String>();
        List<String> warnM = new LinkedList<String>();
        XiaFile xiaFile=uploadFileService.findOne(docId);
        String filePath = uploadFileService.getFilePath(xiaFile);
        Map<String,Object> data = new HashMap<String,Object>();
        boolean validate = this.checkUploadFileData(xiaFile, filePath);
        if (!validate) {
            errorM.add(XiaMesssageResource.getMessage("message.import.new.template"));
        }else{
            //获取数据
            try {
                FileInputStream in = new FileInputStream(new File(filePath));
                DTO dto = importer.importData(in, new Template(3L,importTemplatePath()));
                List<MaterialDto> materialDtos = this.stockImportData(dto, warehouseId, errorM, warnM);
                data.put("materialDtos", materialDtos);
            } catch (FileNotFoundException e) {
                log.error(SystemConst.STR_EMPTY,e);
                errorM.add(XiaMesssageResource.getMessage("page.stock031.data.exception"));
            } catch (Exception e) {
                log.error(SystemConst.STR_EMPTY,e);
                errorM.add(XiaMesssageResource.getMessage("page.stock031.data.exception"));
            }
        }
        data.put("errorM", errorM);
        data.put("warnM", warnM);
        data.put("providers", materialService.findAllProiderName(null));
        return new Result(data);
    }

    public boolean checkUploadFileData(XiaFile file, String path) {
        if (file == null || org.apache.commons.lang.StringUtils.isBlank(file.getName())) {
            log.debug("file is null");
            return false;
        }

        //check file type
        String fileName = file.getName();
        String extension = FilenameUtils.getExtension(fileName);
        if (!EXCEL_TYPE.contains(extension == null ? "" : extension.toUpperCase())) {
            log.debug("file type of file:{} is illegal", fileName);
            return false;
        }

        return checkExcelFile(path);
    }

    /**
     * check whether excel file is matching inventory verification table
     *
     * @param path
     * @return
     */
    private boolean checkExcelFile(String path) {
        if (org.apache.commons.lang.StringUtils.isBlank(path)) {
            log.debug("excel file path:{} is illegal!", path);
            return false;
        }
        try {
            FileInputStream fileInputStream = new FileInputStream(path);
            Workbook wb = WorkbookFactory.create(fileInputStream);
            Sheet sheet = wb.getSheetAt(0);
            //depends on default template header row
            //0 base counting
            final int HEADER_ROW_INDEX = 3;
            Row header = sheet.getRow(HEADER_ROW_INDEX);
            Inventory_.TABLE_IMPORT_MATERIAL_VALIDATION[] tableColumns = Inventory_.TABLE_IMPORT_MATERIAL_VALIDATION.values();
            //column index start from 0
            int m = 0;
            for (int i = header.getFirstCellNum(), j = header.getLastCellNum(); i <= j; i++) {
                Cell cell = header.getCell(i);
                if (cell == null) {
                    break;
                }
                String value = cell.getStringCellValue();
                if (org.apache.commons.lang.StringUtils.isBlank(value)) {
                    break;
                }

                if (m >= tableColumns.length) {
                    break;
                }

                if (m==0 || m==1 || m==5 || m==6 || m==9) {
                    if (!value.equalsIgnoreCase(XiaMesssageResource.getMessage(tableColumns[m++].getProperty())+"（"+XiaMesssageResource.getMessage("page.stock031.required")+"）")) {
                        return false;
                    }
                } else if( m==8 ){
                    if (!value.equalsIgnoreCase(XiaMesssageResource.getMessage(tableColumns[m++].getProperty())+"（"+XiaMesssageResource.getMessage("page.stock031.dollar")+"）"
                            +"（"+XiaMesssageResource.getMessage("page.stock031.required")+"）")) {
                        return false;
                    }
                }else{
                    if (!value.equalsIgnoreCase(XiaMesssageResource.getMessage(tableColumns[m++].getProperty()))) {
                        return false;
                    }
                }
            }
            if (m < tableColumns.length) {
                return false;
            }
        } catch (FileNotFoundException e) {
            log.error("File cannot found in path:{}", path);
            return false;
        } catch (Exception e) {
            log.error("Cannot parse excel file", e);
            return false;
        }
        return true;
    }

    /**
     * 模版地址
     * @return
     */
    public String importTemplatePath() {
        StringBuffer sb = new StringBuffer();
        sb.append("static");
        sb.append(File.separator);
        sb.append("tpl");
        sb.append(File.separator);
        sb.append("stock.xlsx");
        return sb.toString();
    }

    public Map<String, Inventory> getInventoryMap(List<Inventory> inventories){
        Map<String, Inventory> map = new HashMap<>();
        for(Inventory inventory : inventories){
            map.put(inventory.getMaterial().getCode(), inventory);
        }
        return map;
    }

    /**
     * 处理数据
     */
    private List<MaterialDto> stockImportData(DTO dto, Long warehouseId, List<String> errorM, List<String> warnM){
        LinkedHashMap<String, List<Object>> dataList = dto.getDataMaps().get(0);
        int rowdataNum = dataList.get("row0:0").size();
        List<MaterialDto> materialDtos = new ArrayList<>();
        List<Inventory> inventories = inventoryRepository.findMaterialListByWarehouseId(warehouseId);
        Map<String, Inventory> inventoryMap = getInventoryMap(inventories);
        MaterialDto materialDto = null;
        int beforeRowNum = 5;
        for (int j = 0; j < rowdataNum; j++) {
            int rowNum = (j+beforeRowNum);
            try {
                materialDto = new MaterialDto();
                //物资编码
                Object materialCode = getObject(dataList.get("row0:0"), j);
                //物资名称
                Object materialName = getObject(dataList.get("row0:1"), j);
                //展架
                Object displayRack = getObject(dataList.get("row0:2"), j);
                //品牌
                Object brand = getObject(dataList.get("row0:3"), j);
                //型号
                Object model = getObject(dataList.get("row0:4"), j);
                //单位
                Object unit = getObject(dataList.get("row0:5"), j);
                //供应商
                Object providerName = getObject(dataList.get("row0:6"), j);
                //过期时间
                Object dueDate = getObject(dataList.get("row0:7"), j);
                //单价
                Object price = getObject(dataList.get("row0:8"), j);
                //数量
                Object amount = getObject(dataList.get("row0:9"), j);

                if(StringUtils.isEmpty(materialCode)
                        && StringUtils.isEmpty(materialName)
                        && StringUtils.isEmpty(displayRack)
                        && StringUtils.isEmpty(brand)
                        && StringUtils.isEmpty(model)
                        && StringUtils.isEmpty(unit)
                        && StringUtils.isEmpty(providerName)
                        && StringUtils.isEmpty(dueDate)
                        && StringUtils.isEmpty(price)
                        && StringUtils.isEmpty(amount) ){
                    continue;
                }

                String rowStr = XiaMesssageResource.getMessage("page.stock031.tip.line").replace("EEEEE", String.valueOf(rowNum));

                List<String> mesList =  new ArrayList<String>();
                Material material = null;
                //1
                if(!StringUtils.isEmpty(materialCode)){
                    String materialCodeStr = StringUtils.trimWhitespace(materialCode.toString());
                    Inventory inventory = inventoryMap.get(materialCodeStr);
                    if(null != inventory){
                        materialDto.setMaterialCode(materialCodeStr);
                        materialDto.setId(inventory.getId());
                        material = inventory.getMaterial();
                    }else{
                        mesList.add(XiaMesssageResource.getMessage("page.stock031.no.matching.material"));
                    }
                }else{
                    mesList.add(XiaMesssageResource.getMessage("page.stock031.empty.material.code"));
                }

                //2
                if(!StringUtils.isEmpty(materialName)){
                    String materialNameStr = StringUtils.trimWhitespace(materialName.toString());
                    materialDto.setMaterialName(materialNameStr);
                }else{
                    mesList.add(XiaMesssageResource.getMessage("page.stock031.fill.in.material.name"));
                }

                //3
                if(!StringUtils.isEmpty(displayRack)){
                    String displayRackStr = StringUtils.trimWhitespace(displayRack.toString());
                    materialDto.setDisplayRack(displayRackStr==null?"":displayRackStr);
                }
                //4
                if(!StringUtils.isEmpty(brand)){
                    String brandStr = StringUtils.trimWhitespace(brand.toString());
                    materialDto.setBrand(brandStr==null?"":brandStr);
                }

                //5
                if(!StringUtils.isEmpty(model)){
                    String modelStr = StringUtils.trimWhitespace(model.toString());
                    materialDto.setModel(modelStr==null?"":modelStr);
                }

                //6
                if(!StringUtils.isEmpty(unit)){
                    String unitStr = StringUtils.trimWhitespace(unit.toString());
                    materialDto.setUnit(unitStr);
                }else{
                    mesList.add(XiaMesssageResource.getMessage("page.stock031.fill.in.unit"));
                }

                //7
                if(!StringUtils.isEmpty(providerName)){
                    String providerNameStr = StringUtils.trimWhitespace(providerName.toString());
                    materialDto.setProviderName(providerNameStr);
//                    Provider provider = providerService.findOrCreateByName(providerNameStr);
//                    if(null != provider){
//                        materialDto.setProviderId(provider.getId());
//                    }
                }else{
                    mesList.add(XiaMesssageResource.getMessage("page.stock031.fill.in.supplier"));
                }

                //8
                if(!StringUtils.isEmpty(dueDate)){
                    String dueDateStr = StringUtils.trimWhitespace(dueDate.toString());
                    if(ValidateUtil.isDateYYYYMMDD(dueDateStr)){
                        materialDto.setDueDate(dueDateStr);
                    }else{
                        mesList.add(XiaMesssageResource.getMessage("page.stock031.format.incorrect"));
                    }
                }

                //9
                if(!StringUtils.isEmpty(price)){
                    String priceStr = StringUtils.trimWhitespace(price.toString());
                    if(ValidateUtil.isDoubleNumber(priceStr)){
                        Double priceDou = Double.valueOf(priceStr);
                        materialDto.setPrice(String.valueOf(DoubleOperationUtils.formatPointDecimal(priceDou, DoubleOperationUtils.POINT_TWO)));
                    }else{
                        mesList.add(XiaMesssageResource.getMessage("page.stock031.format.price"));
                    }
                }else{
                    mesList.add(XiaMesssageResource.getMessage("page.stock031.empty.unit.price"));
                }

                //10
                if(!StringUtils.isEmpty(amount)){
                    String amountStr = StringUtils.trimWhitespace(amount.toString());
                    if(ValidateUtil.isDoubleNumber(amountStr)){
                        Double priceDou = Double.valueOf(amountStr);
                        materialDto.setAmount(String.valueOf(DoubleOperationUtils.formatPointDecimal(priceDou, DoubleOperationUtils.POINT_TWO)));
                    }else{
                        mesList.add(XiaMesssageResource.getMessage("page.stock031.format.number"));
                    }
                }else{
                    mesList.add(XiaMesssageResource.getMessage("page.stock031.fill.in.quantity"));
                }
                initMaterialmes(material, materialDto);
                materialDtos.add(materialDto);
                if(CollectionUtils.isNotEmpty(mesList)){
                    warnM.add(rowStr + this.formatTipMes(mesList));
                }
            } catch (Exception e) {
                errorM.add(XiaMesssageResource.getMessage("page.stock031.line.data.exception").replace("EEEEE", String.valueOf(rowNum)));
                log.error("",e);
            }
        }
        return materialDtos;
    }

    public void initMaterialmes(Material material, MaterialDto materialDto){
        if(null != material){
            materialDto.setMaterialName(material.getName());
            materialDto.setModel(material.getModel());
            materialDto.setBrand(material.getBrand());
            materialDto.setUnit(material.getUnit());
        }
    }

    public String formatTipMes(List<String> list){
        StringBuilder sb = new StringBuilder();
        for(String str : list){
            if(sb.length() == 0){
               sb.append(str);
            }else{
                sb.append("，"+str);
            }
        }
        return sb.toString();
    }

    private Object getObject(List<Object> oL,int index){
        Object o = new String("");
        if(oL!=null && oL.size() > index ){
            o = oL.get(index);
            if(o==null){
                o = new String("");
            }
        }
        return o;
    }

    @Override
    public List<MaterialDto> exportMaterialListByWarehouseId(MaterialDto dto){
        List<MaterialDto> dtos = new ArrayList<MaterialDto>();
        MaterialDto materialDto = null;
        if(null != dto){
            List<Inventory> inventories = inventoryRepository.findMaterialListByWarehouseId(dto.getWarehouseId());
            for(Inventory inventory : inventories){
                materialDto = new MaterialDto(inventory);
                dtos.add(materialDto);
            }
        }
        return dtos;
    }
}
