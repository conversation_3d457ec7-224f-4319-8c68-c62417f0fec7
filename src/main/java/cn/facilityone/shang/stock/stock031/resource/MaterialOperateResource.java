package cn.facilityone.shang.stock.stock031.resource;

import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.stock.stock031.dto.MaterialDto;
import cn.facilityone.shang.stock.stock031.service.MaterialOperateService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import cn.facilityone.xia.transfer.core.Exporter;
import cn.facilityone.xia.transfer.core.data.DTO;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Path("/stock031")
public class MaterialOperateResource {
    private static final String TEMPLATE_POSITION_PATH = "/business/stockV2/stock031-materialOperate.ftl";

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private MaterialOperateService materialOperateService;

    @Autowired
    @Qualifier("xlsxExporter")
    private Exporter exporter;

    @GET
    @Template(name = TEMPLATE_POSITION_PATH)
    public Map<String, Object> init() {
        Map<String, Object> map = new HashMap<>();
        XiaPrincipal user = (XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal();
        Long userId = user.getId();
        Employee employee = employeeRepository.findOneByUserId(userId);
        if(null != employee){
            map.put("employeeid", String.valueOf(employee.getId()));
            map.put("employeename", employee.getName());
        }
        return map;
    }

    /**
     * 导入
     */
    @POST
    @Path("/import/{docId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result uploadFile(@PathParam("docId") Long docId,MaterialDto materialDto) {
        Long warehouseId = null;
        if(null != materialDto){
            warehouseId = materialDto.getWarehouseId();
        }
        return materialOperateService.importData(docId, warehouseId);
    }

    /**
     * 导出
     * @return
     */
    @POST
    @Path("/export")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response export(MaterialDto materialDto) {
        LinkedHashMap<String, String> title = new LinkedHashMap<String, String>();
        title.put("materialCode", XiaMesssageResource.getMessage("Material.code")+"（"+XiaMesssageResource.getMessage("page.stock031.required")+"）");
        title.put("materialName", XiaMesssageResource.getMessage("InventoryManagementActivity.materialName")
                             +"（"+XiaMesssageResource.getMessage("page.stock031.required")+"）");
        title.put("displayRack", XiaMesssageResource.getMessage("Inventory.displayRack"));
        title.put("brand", XiaMesssageResource.getMessage("Equipment.brand"));
        title.put("model", XiaMesssageResource.getMessage("Equipment.model"));
        title.put("unit", XiaMesssageResource.getMessage("Material.unit")+"（"+XiaMesssageResource.getMessage("page.stock031.required")+"）");
        title.put("providerName", XiaMesssageResource.getMessage("Equipment.provider")+"（"+XiaMesssageResource.getMessage("page.stock031.required")+"）");
        title.put("dueDate", XiaMesssageResource.getMessage("MaterialBatch.dueDate"));
        title.put("price", XiaMesssageResource.getMessage("Inventory.price")+"（"+XiaMesssageResource.getMessage("page.stock031.dollar")+"）"
                             +"（"+XiaMesssageResource.getMessage("page.stock031.required")+"）");
        title.put("amount", XiaMesssageResource.getMessage("page.stock031.inamount")+"（"+XiaMesssageResource.getMessage("page.stock031.required")+"）");

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("typename", XiaMesssageResource.getMessage("page.stock031.material.list"));
        if(null != materialDto){
            map.put("warehousename", materialDto.getWarehouseName());
        }else{
            map.put("warehousename", "");
        }
        map.put("time", DateUtil.formatDateYYYYMMDD(new Date()));
        map.put("tip1", XiaMesssageResource.getMessage("page.stock031.tip1"));
        map.put("tip2", XiaMesssageResource.getMessage("page.stock031.tip2"));
        map.put("tip3", XiaMesssageResource.getMessage("page.stock031.tip3"));
        DTO dto = new DTO(map);
        dto.addTitleAndData(title, materialOperateService.exportMaterialListByWarehouseId(materialDto));
        cn.facilityone.xia.transfer.core.data.Template tpl = new cn.facilityone.xia.transfer.core.data.Template(3L, "static/tpl/stock.xlsx");
        ByteArrayOutputStream out = (ByteArrayOutputStream) exporter.export(dto,tpl);
        return Response.ok( new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.success.export"),
                FileUtil.exportFile(out, FileUtil.FILE_TYPE_EXCELX))).build();
    }

}
