package cn.facilityone.shang.stock.stock031.dto;

import cn.facilityone.shang.entity.inventory.Inventory;

/**
 * Author：panda.yang
 * CreateTime：2016/11/22 21:07
 */
public class MaterialDto {
    //库存id
    private Long id;
    //仓库id
    private Long warehouseId;
    //仓库名称
    private String warehouseName;
    //物资展架
    private String displayRack;
    //物资编码
    private String materialCode;
    //物资名称
    private String materialName;
    //品牌
    private String brand;
    //型号
    private String model;
    //单位
    private String unit;
    //供应商
    private String providerName;
    //过期时间
    private String dueDate;
    //单价
    private String price;
    //数量
    private String amount;
    //供应商id
    private Long providerId;

    public MaterialDto(){}

    public MaterialDto(Inventory inventory) {
        this.materialCode = inventory.getMaterial().getCode();
        this.materialName = inventory.getMaterial().getName();
        this.model = inventory.getMaterial().getModel();
        this.brand = inventory.getMaterial().getBrand();
        this.unit = inventory.getMaterial().getUnit();
        this.displayRack = inventory.getDisplayRack();
    }

    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getDueDate() {
        return dueDate;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }
}
