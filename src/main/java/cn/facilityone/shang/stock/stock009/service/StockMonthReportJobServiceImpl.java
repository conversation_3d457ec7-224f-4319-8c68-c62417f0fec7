package cn.facilityone.shang.stock.stock009.service;

import cn.facilityone.shang.common.component.quartz.core.dto.JobLog;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.inventory.InventoryReport;
import cn.facilityone.shang.stock.common.mapper.InventoryMapper;
import cn.facilityone.shang.stock.common.mapper.MaterialBatchMapper;
import cn.facilityone.shang.stock.common.repository.InventoryReportRepository;
import cn.facilityone.shang.stock.stock009.dto.InventoryWithMaterialBatchDTO;
import cn.facilityone.shang.stock.stock009.dto.StockMaterialBatchDTO;
import cn.facilityone.shang.stock.stock009.dto.StockReportDTO;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by lip.gu on 2018/2/26.
 */
@Service
public class StockMonthReportJobServiceImpl implements StockMonthReportJobService {

    @Autowired
    private InventoryReportRepository inventoryReportRepository;
    @Autowired
    private InventoryMapper inventoryMapper;
    @Autowired
    private MaterialBatchMapper materialBatchMapper;

    @Override
    @XiaTransactional(readOnly = false)
    public void doReportForJob(JobLog jobLog, Date nowDate, Long currentProjectId) {
        if(nowDate == null){
            return;
        }

        Calendar now = Calendar.getInstance();
        now.setTime(nowDate);
        now.add(Calendar.MONTH, -1);

        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH)+1;

        String startDate = year+"-"+month+"-01 00:00:00";
        now.set(Calendar.MONTH, month - 1);
        int day = now.getActualMaximum(Calendar.DAY_OF_MONTH);
        String endDate = year+"-"+month+"-"+day+" 23:59:59";


        List<StockReportDTO> inventoryInfo = inventoryMapper.findInventoryInfo(currentProjectId, endDate);
        if (CollectionUtils.isEmpty(inventoryInfo)) {
            return;
        }

        List<InventoryWithMaterialBatchDTO> inventoryWithMaterialBatchDTOs = materialBatchMapper.findInventoryWithMaterialBatches(currentProjectId);
        Map<Long, List<Long>> inventoryWithMaterialBatchMap = inventoryWithMaterialBatchToMap(inventoryWithMaterialBatchDTOs);
        String materialBatchIds = parseToMaterialBatchIds(inventoryWithMaterialBatchDTOs);
        List<StockMaterialBatchDTO> stockMaterialBatchDTOs = StringUtils.isEmpty(materialBatchIds) || "()".equals(materialBatchIds) ? null : materialBatchMapper.findAllMaterialBatchDTO(materialBatchIds, startDate, endDate);
        Map<Long, StockMaterialBatchDTO> stockMaterialBatchMap = stockMaterialBatchToMap(stockMaterialBatchDTOs);

        List<InventoryReport> reports = inventoryReportRepository.findByYearAndMonthHardly(year, month,currentProjectId);
        Map<Long, InventoryReport> reportMap = listToMap(reports);
        for (StockReportDTO stockReportDTO : inventoryInfo) {
            InventoryReport report = null;
            if (null == reportMap.get(stockReportDTO.getInventoryId())) {
                report = new InventoryReport();
                report.setMonth(month);
                report.setYear(year);
                report.setProject(currentProjectId);
            } else {
                report = reportMap.get(stockReportDTO.getInventoryId());
            }
            report.setInventoryId(stockReportDTO.getInventoryId());
            report.setWarehouseId(stockReportDTO.getWarehouseId());
            //本月结存
            report.setCurBalance(stockReportDTO.getCurBalance());
            //本月入库、领用
            Double entry = 0d;
            Double delivery = 0d;
            List<Long> mbIds = inventoryWithMaterialBatchMap.get(stockReportDTO.getInventoryId());
            if (CollectionUtils.isNotEmpty(mbIds)) {
                for (Long id : mbIds) {
                    StockMaterialBatchDTO stockMaterialBatchDTO = stockMaterialBatchMap.get(id);
                    if (null != stockMaterialBatchDTO) {
                        if (null != stockMaterialBatchDTO.getEntry()) {
                            entry += stockMaterialBatchDTO.getEntry();
                        }
                        if (null != stockMaterialBatchDTO.getDelivery()) {
                            delivery += stockMaterialBatchDTO.getDelivery();
                        }
                    }
                }
            }
            report.setCurEntry(entry);
            report.setCurDelivery(delivery);
            report.setPreBalance(stockReportDTO.getCurBalance() - entry + delivery);
            inventoryReportRepository.save(report);
            inventoryReportRepository.flush();
        }
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void doHistoryReportForJob(JobLog jobLog, Date nowDate, Long currentProjectId) {
        if(nowDate == null){
            return;
        }

        Calendar time = Calendar.getInstance();
        time.setTime(nowDate);
        int year = time.get(Calendar.YEAR);
        int month = time.get(Calendar.MONTH)+1;

        String startDate = year+"-"+month+"-01 00:00:00";
        time.set(Calendar.MONTH, month-1);
        int day = time.getActualMaximum(Calendar.DAY_OF_MONTH);
        String endDate = year+"-"+month+"-"+day+" 23:59:59";

        Calendar now = Calendar.getInstance();
        Date end = DateUtil.buildDateOnLastDayOfMonthForSql(now.getTime());
        String endStr = DateUtil.parseDate(end, DateUtil.FORMAT_PATTERN_DATE);

        List<StockReportDTO> inventoryInfo = inventoryMapper.findInventoryInfo(currentProjectId, endDate);
        if (CollectionUtils.isEmpty(inventoryInfo)) {
            return;
        }

        List<InventoryWithMaterialBatchDTO> inventoryWithMaterialBatchDTOs = materialBatchMapper.findInventoryWithMaterialBatches(currentProjectId);
        Map<Long, List<Long>> inventoryWithMaterialBatchMap = inventoryWithMaterialBatchToMap(inventoryWithMaterialBatchDTOs);
        String materialBatchIds = parseToMaterialBatchIds(inventoryWithMaterialBatchDTOs);
        //输入月份的出入库记录
        List<StockMaterialBatchDTO> stockMaterialBatchDTOs = StringUtils.isEmpty(materialBatchIds) || "()".equals(materialBatchIds) ? null : materialBatchMapper.findAllMaterialBatchDTO(materialBatchIds, startDate.trim(), endDate.trim());
        Map<Long, StockMaterialBatchDTO> stockMaterialBatchMap = stockMaterialBatchToMap(stockMaterialBatchDTOs);
        //输入月份到当前时间的出入库记录
        List<StockMaterialBatchDTO> batchDTOs = StringUtils.isEmpty(materialBatchIds) || "()".equals(materialBatchIds) ? null : materialBatchMapper.findAllMaterialBatchDTO(materialBatchIds, startDate.trim(), endStr.trim());
        Map<Long, StockMaterialBatchDTO> materialBatchMap = stockMaterialBatchToMap(batchDTOs);

        List<InventoryReport> reports = inventoryReportRepository.findByYearAndMonthHardly(year, month,currentProjectId);
        Map<Long, InventoryReport> reportMap = listToMap(reports);
        for (StockReportDTO stockReportDTO : inventoryInfo) {
            InventoryReport report = null;
            if (null == reportMap.get(stockReportDTO.getInventoryId())) {
                report = new InventoryReport();
                report.setMonth(month);
                report.setYear(year);
                report.setProject(currentProjectId);
            } else {
                report = reportMap.get(stockReportDTO.getInventoryId());
            }
            report.setInventoryId(stockReportDTO.getInventoryId());
            report.setWarehouseId(stockReportDTO.getWarehouseId());

            //本月入库、领用
            Double entry = 0d;
            Double delivery = 0d;
            //总入库、领用
            Double entryTotal = 0d;
            Double deliveryTotal = 0d;
            List<Long> mbIds = inventoryWithMaterialBatchMap.get(stockReportDTO.getInventoryId());
            if (CollectionUtils.isNotEmpty(mbIds)) {
                for (Long id : mbIds) {
                    StockMaterialBatchDTO stockMaterialBatchDTO = stockMaterialBatchMap.get(id);
                    if (null != stockMaterialBatchDTO) {
                        if (null != stockMaterialBatchDTO.getEntry()) {
                            entry += stockMaterialBatchDTO.getEntry();
                        }
                        if (null != stockMaterialBatchDTO.getDelivery()) {
                            delivery += stockMaterialBatchDTO.getDelivery();
                        }
                    }
                    StockMaterialBatchDTO dto = materialBatchMap.get(id);
                    if (null != dto) {
                        if (null != dto.getEntry()) {
                            entryTotal += dto.getEntry();
                        }
                        if (null != dto.getDelivery()) {
                            deliveryTotal += dto.getDelivery();
                        }
                    }
                }
            }
            report.setCurEntry(entry);
            report.setCurDelivery(delivery);
            //上月结存
            report.setPreBalance(stockReportDTO.getCurBalance() - entryTotal + deliveryTotal);
            //本月结存
            report.setCurBalance(stockReportDTO.getCurBalance() - entryTotal + deliveryTotal + entry - delivery);
            inventoryReportRepository.save(report);
            inventoryReportRepository.flush();
        }
    }

    String parseToMaterialBatchIds(List<InventoryWithMaterialBatchDTO> dtos) {
        StringBuilder result = new StringBuilder("(");
        for (int i = 0; i < dtos.size(); i++) {
            if (i == dtos.size() - 1) {
                result.append(dtos.get(i).getMaterialBatchIds());
            } else {
                result.append(dtos.get(i).getMaterialBatchIds() + ",");
            }
        }
        result.append(")");
        return result.toString();
    }

    Map<Long, InventoryReport> listToMap(List<InventoryReport> reports) {
        Map<Long, InventoryReport> map = new HashMap<>();
        for (InventoryReport report : reports) {
            map.put(report.getInventoryId(), report);
        }
        return map;
    }

    Map<Long, List<Long>> inventoryWithMaterialBatchToMap(List<InventoryWithMaterialBatchDTO> dtos) {
        Map<Long, List<Long>> map = new HashMap<>();
        for (InventoryWithMaterialBatchDTO dto : dtos) {
            List<Long> list = new ArrayList<>();
            if (StringUtils.isNotEmpty(dto.getMaterialBatchIds())) {
                String[] split = dto.getMaterialBatchIds().split(",");
                for (String s : split) {
                    list.add(Long.valueOf(s));
                }
            }
            map.put(dto.getInventoryId(), list);
        }
        return map;
    }

    Map<Long, StockMaterialBatchDTO> stockMaterialBatchToMap(List<StockMaterialBatchDTO> dtos) {
        Map<Long, StockMaterialBatchDTO> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dtos)) {
            for (StockMaterialBatchDTO dto : dtos) {
                map.put(dto.getMaterialBatchId(), dto);
            }
        }
        return map;
    }
}
