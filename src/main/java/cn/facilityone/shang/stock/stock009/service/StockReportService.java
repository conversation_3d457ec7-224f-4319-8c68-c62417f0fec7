package cn.facilityone.shang.stock.stock009.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.stock.stock009.dto.StockReportSearchRequest;
import cn.facilityone.shang.stock.stock009.dto.YearMonthDTO;

import javax.ws.rs.core.Response;

/**
 * Created by lip.gu on 2018/2/23.
 */
public interface StockReportService {

    YearMonthDTO reportDate();

    DataTableResponse findStockRecordsInPage(StockReportSearchRequest request);

    Response exportStockReport(StockReportSearchRequest request);
}
