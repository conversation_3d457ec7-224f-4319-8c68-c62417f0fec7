package cn.facilityone.shang.stock.stock009.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.stock.stock009.dto.StockReportSearchRequest;
import cn.facilityone.shang.stock.stock009.dto.YearMonthDTO;
import cn.facilityone.shang.stock.stock009.service.StockReportService;
import cn.facilityone.xia.core.common.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by lip.gu on 2018/2/23.
 */
@Path("/stock009")
public class StockReportResource {
    private static final String TEMPLATE_STOCK_REPORT_PATH = "/business/stockV2/stock009-stockReport.ftl";

    @Autowired
    private StockReportService stockReportService;

    @GET
    @Template(name = TEMPLATE_STOCK_REPORT_PATH)
    public Map<String, Object> init() {
        YearMonthDTO yearMonthDTO = stockReportService.reportDate();
        Map<String, Object> result = new HashMap<>();
        result.put("yms", yearMonthDTO);
        return result;
    }

    @POST
    @Path("stock/table")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getReportTable(StockReportSearchRequest request) {
        return stockReportService.findStockRecordsInPage(request);
    }

    @POST
    @Path("stock/export")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response export(String param) {
        StockReportSearchRequest request = null;

        if (StringUtils.isNotBlank(param)) {
            try {
                request = new ObjectMapper().readValue(param, StockReportSearchRequest.class);
            } catch (Exception e) {
                return Response.ok(new Result(Response.Status.BAD_REQUEST.getStatusCode(), "IO流错误!", "")).build();
            }
        }

        return stockReportService.exportStockReport(request);
    }
}
