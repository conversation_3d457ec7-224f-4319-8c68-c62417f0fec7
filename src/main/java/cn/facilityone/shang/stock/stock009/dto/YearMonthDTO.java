package cn.facilityone.shang.stock.stock009.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by lip.gu on 2018/2/23.
 */
public class YearMonthDTO {
    private int firstYear;
    private List<Integer> years;
    private int nowYear;

    private List<Integer> months;
    private List<Integer> firstMonths;
    private List<Integer> nowMonths;

    private int nowMonth;

    public YearMonthDTO(){

    }
    public YearMonthDTO(int fyear,int fmonth,int lyear,int lmonth){
        this.nowYear = lyear;
        this.nowMonth = lmonth;
        this.firstYear = fyear;
        years = new ArrayList<>();
        for(int y=fyear;y<=lyear;y++){
            years.add(y);
        }
        months = new ArrayList<>();
        for(int m=1;m<=12;m++){
            months.add(m);
        }
        firstMonths = new ArrayList<>();
        for(int m=fmonth;m<=12;m++){
            firstMonths.add(m);
        }
        nowMonths = new ArrayList<>();
        lmonth = lmonth>12?12:lmonth;
        for(int m=1;m<=lmonth;m++){
            nowMonths.add(m);
        }
    }
    public int getFirstYear() {
        return firstYear;
    }
    public void setFirstYear(int firstYear) {
        this.firstYear = firstYear;
    }
    public List<Integer> getYears() {
        return years;
    }
    public void setYears(List<Integer> years) {
        this.years = years;
    }
    public List<Integer> getMonths() {
        return months;
    }
    public void setMonths(List<Integer> months) {
        this.months = months;
    }
    public List<Integer> getFirstMonths() {
        return firstMonths;
    }
    public void setFirstMonths(List<Integer> firstMonths) {
        this.firstMonths = firstMonths;
    }
    public int getNowYear() {
        return nowYear;
    }
    public void setNowYear(int nowYear) {
        this.nowYear = nowYear;
    }
    public List<Integer> getNowMonths() {
        return nowMonths;
    }
    public void setNowMonths(List<Integer> nowMonths) {
        this.nowMonths = nowMonths;
    }
    public int getNowMonth() {
        return nowMonth;
    }
    public void setNowMonth(int nowMonth) {
        this.nowMonth = nowMonth;
    }
}
