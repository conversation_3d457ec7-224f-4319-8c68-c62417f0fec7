package cn.facilityone.shang.stock.stock009.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.common.util.FileUtil;
import cn.facilityone.shang.projects.pro002.service.ProjectService;
import cn.facilityone.shang.stock.common.mapper.InventoryMapper;
import cn.facilityone.shang.stock.common.mapper.InventoryReportMapper;
import cn.facilityone.shang.stock.common.mapper.WarehouseMapper;
import cn.facilityone.shang.stock.stock009.dto.StockReportDTO;
import cn.facilityone.shang.stock.stock009.dto.StockReportSearchRequest;
import cn.facilityone.shang.stock.stock009.dto.WarehouseWithEmDTO;
import cn.facilityone.shang.stock.stock009.dto.YearMonthDTO;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.transfer.core.Exporter;
import cn.facilityone.xia.transfer.core.data.DTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.ws.rs.core.Response;
import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * Created by lip.gu on 2018/2/23.
 */
@Service
public class StockReportServiceImpl implements StockReportService {

    @Autowired
    private InventoryMapper inventoryMapper;
    @Autowired
    private InventoryReportMapper inventoryReportMapper;
    @Autowired
    @Qualifier("xlsxExporter")
    private Exporter exporter;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private WarehouseMapper warehouseMapper;

    @Override
    public YearMonthDTO reportDate() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        Long projId = ProjectContext.getCurrentProject();
        Date minDate = inventoryMapper.findMinYearMonthReport(projId);
        if(null != minDate){
            Calendar c = Calendar.getInstance();
            c.setTime(minDate);
            year = c.get(Calendar.YEAR);
            month = c.get(Calendar.MONTH) + 1;
        }
        int myear = calendar.get(Calendar.YEAR);
        int mmonth = calendar.get(Calendar.MONTH);
        if (mmonth == 0) {
            myear -= 1;
            mmonth = 12;
        }
        YearMonthDTO dto = new YearMonthDTO(year,month,myear,mmonth);

        return dto;
    }

    @Override
    public DataTableResponse findStockRecordsInPage(StockReportSearchRequest request) {
        PageHelper.startPage(request.getPageNumber(), request.getPageSize());
        Calendar now = Calendar.getInstance();
        now.add(Calendar.MONTH, -1);
        if (null == request.getParam()) {
            StockReportSearchRequest.Data param = new StockReportSearchRequest.Data();
            param.setYear(now.get(Calendar.YEAR));
            param.setMonth(now.get(Calendar.MONTH)+1);
            request.setParam(param);
        }
        List<StockReportDTO> reports = inventoryReportMapper.findInventoryReportByRequest(request, ProjectContext.getCurrentProject());
        for (StockReportDTO dto : reports) {
            dto.setPreBalance(DoubleOperationUtils.changeDecimal(dto.getPreBalance(), 2));
            dto.setCurEntry(DoubleOperationUtils.changeDecimal(dto.getCurEntry(),2));
            dto.setCurDelivery(DoubleOperationUtils.changeDecimal(dto.getCurDelivery(),2));
            dto.setCurBalance(DoubleOperationUtils.changeDecimal(dto.getCurBalance(),2));
        }
        PageInfo info = new PageInfo(reports);
        return new DataTableResponse(reports, (int)info.getTotal(), request.getPageNumber(), request.getDraw());
    }

    @Override
    public Response exportStockReport(StockReportSearchRequest request) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("projectName", projectService.findUserCurrentProjectName());
        map.put("titleName", XiaMesssageResource.getMessage("page.stock009.stock.titleName"));
        Calendar now = Calendar.getInstance();
        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH)+1;
        String warehouseName = "";
        String emName = "";
        if (null != request.getParam()) {
            year = request.getParam().getYear();
            month = request.getParam().getMonth();
            if (null != request.getParam().getWarehouseId()) {
                WarehouseWithEmDTO warehouseWithEm = warehouseMapper.findWarehouseWithEm(request.getParam().getWarehouseId());
                warehouseName = warehouseWithEm.getWarehouseName();
                emName = warehouseWithEm.getEmName();
            }
        }
        if ("".equals(warehouseName)){
            warehouseName = XiaMesssageResource.getMessage("page.stock009.stock.warehouse.all");
        }
        map.put("dateRange", XiaMesssageResource.getMessage("page.stock009.stock.dateRange")+":"+year
                +XiaMesssageResource.getMessage("page.stock009.stock.year")+month+XiaMesssageResource.getMessage("page.stock009.stock.month"));
        map.put("warehouse", XiaMesssageResource.getMessage("page.stock009.stock.warehouse")+":"+warehouseName);
        if (!"".equals(emName)) {
            map.put("admin", XiaMesssageResource.getMessage("page.stock009.stock.admin")+":"+emName);
        } else {
            map.put("admin", emName);
        }
        DTO dto = new DTO(map);

        List<StockReportDTO> dtos = findStockReportDTO(request);
        LinkedHashMap<String, String> title = new LinkedHashMap<>();
        title.put("materialCode", XiaMesssageResource.getMessage("InventoryManagementActivity.materialCode"));
        title.put("materialName", XiaMesssageResource.getMessage("InventoryManagementActivity.materialName"));
        title.put("warehouseName", XiaMesssageResource.getMessage("page.stock009.stock.warehouse"));
        title.put("displayRack", XiaMesssageResource.getMessage("Inventory.displayRack"));
        title.put("brand", XiaMesssageResource.getMessage("Material.brand"));
        title.put("model", XiaMesssageResource.getMessage("Material.model"));
        title.put("unit", XiaMesssageResource.getMessage("Material.unit"));
        title.put("preBalance", translateMonth(month-1) + XiaMesssageResource.getMessage("page.stock009.stock.balance"));
        title.put("curEntry", translateMonth(month) + XiaMesssageResource.getMessage("page.stock009.stock.entry"));
        title.put("curDelivery", translateMonth(month) + XiaMesssageResource.getMessage("page.stock009.stock.delivery"));
        title.put("curBalance", translateMonth(month) + XiaMesssageResource.getMessage("page.stock009.stock.balance"));

        dto.addTitleAndData(title, dtos);
        cn.facilityone.xia.transfer.core.data.Template tpl = new cn.facilityone.xia.transfer.core.data.Template(3L, "static/tpl/stock_report.xlsx");
        ByteArrayOutputStream out = (ByteArrayOutputStream) exporter
                .export(dto, tpl);
        return Response.ok(
                new Result(Response.Status.OK.getStatusCode(), XiaMesssageResource.getMessage("server.result.success.export"),
                        FileUtil.exportFile(out, FileUtil.FILE_TYPE_EXCELX))).build();

    }

    public List<StockReportDTO> findStockReportDTO(StockReportSearchRequest request) {
        List<StockReportDTO> reports = inventoryReportMapper.findInventoryReportByRequest(request, ProjectContext.getCurrentProject());
        Double preBalance = 0d;
        Double curEntry = 0d;
        Double curDelivery = 0d;
        Double curBalance = 0d;
        for (StockReportDTO dto : reports) {
            dto.setPreBalance(DoubleOperationUtils.changeDecimal(dto.getPreBalance(), 2));
            dto.setCurEntry(DoubleOperationUtils.changeDecimal(dto.getCurEntry(),2));
            dto.setCurDelivery(DoubleOperationUtils.changeDecimal(dto.getCurDelivery(),2));
            dto.setCurBalance(DoubleOperationUtils.changeDecimal(dto.getCurBalance(),2));
            preBalance += dto.getPreBalance();
            curEntry += dto.getCurEntry();
            curDelivery += dto.getCurDelivery();
            curBalance += dto.getCurBalance();
        }
        StockReportDTO dto = new StockReportDTO();
        dto.setUnit(XiaMesssageResource.getMessage("page.stock009.stock.total"));
        dto.setPreBalance(DoubleOperationUtils.changeDecimal(preBalance,2));
        dto.setCurEntry(DoubleOperationUtils.changeDecimal(curEntry, 2));
        dto.setCurDelivery(DoubleOperationUtils.changeDecimal(curDelivery, 2));
        dto.setCurBalance(DoubleOperationUtils.changeDecimal(curBalance,2));
        reports.add(dto);
        return reports;
    }

    public String translateMonth(int month) {
        switch (month) {
            case 0:
                return XiaMesssageResource.getMessage("page.stock009.stock.december");
            case 1:
                return XiaMesssageResource.getMessage("page.stock009.stock.january");
            case 2:
                return XiaMesssageResource.getMessage("page.stock009.stock.february");
            case 3:
                return XiaMesssageResource.getMessage("page.stock009.stock.march");
            case 4:
                return XiaMesssageResource.getMessage("page.stock009.stock.april");
            case 5:
                return XiaMesssageResource.getMessage("page.stock009.stock.may");
            case 6:
                return XiaMesssageResource.getMessage("page.stock009.stock.june");
            case 7:
                return XiaMesssageResource.getMessage("page.stock009.stock.july");
            case 8:
                return XiaMesssageResource.getMessage("page.stock009.stock.august");
            case 9:
                return XiaMesssageResource.getMessage("page.stock009.stock.september");
            case 10:
                return XiaMesssageResource.getMessage("page.stock009.stock.october");
            case 11:
                return XiaMesssageResource.getMessage("page.stock009.stock.november");
            case 12:
                return XiaMesssageResource.getMessage("page.stock009.stock.december");
            default:
                return null;
        }
    }
}
