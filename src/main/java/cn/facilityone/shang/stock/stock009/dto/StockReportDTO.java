package cn.facilityone.shang.stock.stock009.dto;

/**
 * Created by lip.gu on 2018/2/24.
 */
public class StockReportDTO {
    private String materialCode;
    private String materialName;
    private String displayRack;
    private String brand;
    private String model;
    private String unit;
    //上月结存
    private Double preBalance;
    //本月入库
    private Double curEntry;
    //本月领用
    private Double curDelivery;
    //本月结存
    private Double curBalance;
    private Long inventoryId;
    private Long warehouseId;
    private Long projectId;
    private String warehouseName;

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getDisplayRack() {
        return displayRack;
    }

    public void setDisplayRack(String displayRack) {
        this.displayRack = displayRack;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getPreBalance() {
        return preBalance;
    }

    public void setPreBalance(Double preBalance) {
        this.preBalance = preBalance;
    }

    public Double getCurEntry() {
        return curEntry;
    }

    public void setCurEntry(Double curEntry) {
        this.curEntry = curEntry;
    }

    public Double getCurDelivery() {
        return curDelivery;
    }

    public void setCurDelivery(Double curDelivery) {
        this.curDelivery = curDelivery;
    }

    public Double getCurBalance() {
        return curBalance;
    }

    public void setCurBalance(Double curBalance) {
        this.curBalance = curBalance;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }
}
