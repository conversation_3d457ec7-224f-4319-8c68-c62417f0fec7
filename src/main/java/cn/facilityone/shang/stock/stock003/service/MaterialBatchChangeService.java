package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.stock.stock002.dto.MoveRecordDTO;
import cn.facilityone.shang.stock.stock002.dto.ReserveActivityDTO;
import cn.facilityone.shang.stock.stock003.dto.StockQueryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by charles.chen on 2015/8/11.
 */
public interface MaterialBatchChangeService {

    MaterialBatchChange create(MaterialBatchChange materialBatchChange);

    /**
     * 根据操作记录类型获得调整类型
     * @param type
     * @return AdjustType
     */
    MaterialBatchChange.AdjustType findAdjustTypeByActivityType(InventoryManagementActivity.InventoryActivityType type);

    /**
     * 根据多条件查询出库批次记录(为退库做准备)
     */
    List<MaterialBatchChange> findMaterialBatchChangeByDtoAndPage(StockQueryRequest searchRequest);

    /**
     * 根据多条件查询库存改动记录
     */
    List<MaterialBatchChange> findMaterialBatchChangeByActivityId(Long id,DataTableRequest request);

    /**
     * 库存的多种记录 入库 出库 预定
     */
    List<MaterialBatchChange> findActivityRecodesByInventoryIdAndType(Long id,InventoryManagementActivity.InventoryActivityType type);

    /**
     * 库存的锁定/有效记录
     */
    List<MaterialBatchChange> findActivityRecodesByInventoryIdAndActivity(Long id,boolean inActivity);

    /**
     * 库存的移库记录
     */
    List<MoveRecordDTO> findActivityRecodesByInventoryId(Long id);

    /**
     * 物资管理中的出库和预定记录
     */
    List<InventoryManagementActivity> findshowingInventoryRecordsByIdAndType(Long id,InventoryManagementActivity.InventoryActivityType type);

    /**
     * 物资管理中的预定记录
     */
    List<ReserveActivityDTO> findReserveRecordsByIdAndType(Long id,InventoryManagementActivity.InventoryActivityType type);
}
