package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatch;
import cn.facilityone.shang.stock.common.repository.InventoryRepository;
import cn.facilityone.shang.stock.common.repository.MaterialBatchRepository;
import cn.facilityone.shang.stock.common.staticmetamodel.Inventory_;
import cn.facilityone.shang.stock.common.staticmetamodel.MaterialBatch_;
import cn.facilityone.shang.stock.stock002.builder.InventoryBuilder;
import cn.facilityone.shang.stock.stock003.builder.MaterialBatchToBuilder;
import cn.facilityone.shang.stock.stock003.dto.MaterialBatchDTO;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by charles.chen on 2015/8/14.
 */
@Service
public class MaterialBatchServiceImpl implements MaterialBatchService {
    @Autowired
    private MaterialBatchRepository materialBatchRepository;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private MaterialBatchToBuilder materialBatchBuilder;
    @Autowired
    private InventoryBuilder inventoryBuilder;

    @Override
    public MaterialBatch create(MaterialBatch materialBatch) {
        return materialBatchRepository.save(materialBatch);
    }

    @Override
    public List<MaterialBatch> findByDto(MaterialBatchDTO dto) {

        return materialBatchRepository.findByInventoryIdAndDueDate(dto.getInventoryId(),dto.getDueDate());
    }

    /**
     *  批次改动
     *  有效数量 = 批次总量 - 锁定数量  当有效数量小与0时，则为0
     * @return
     */
    @Override
    public MaterialBatch quantityChanges(MaterialBatch materialBatch, InventoryManagementActivity.InventoryActivityType type, Double changeNum) {
        int ordinal=type.ordinal();
        //批次总量
        double amount = materialBatch.getAmount();
        //批次有效数量
        double realAmount = materialBatch.getRealNum();
        //锁定数量
        double lockedAmount = materialBatch.getLockNum();

        double changeNumDec = changeNum == null ?0:changeNum;
        switch (ordinal){
            case 0:
                //入库
                amount = DoubleOperationUtils.doubleAdd(amount, changeNumDec, DoubleOperationUtils.POINT_TWO);
                break;
            case 1:
                //出库
                amount = DoubleOperationUtils.doubleSub(amount, changeNumDec, DoubleOperationUtils.POINT_TWO);
                break;
            case 2:
                //预定出库
                amount = DoubleOperationUtils.doubleSub(amount, changeNumDec, DoubleOperationUtils.POINT_TWO);
                break;
            case 3:
                //退库
                amount = DoubleOperationUtils.doubleAdd(amount, changeNumDec, DoubleOperationUtils.POINT_TWO);
                break;
            case 4:
                //预定
                lockedAmount = DoubleOperationUtils.doubleAdd(lockedAmount, changeNumDec, DoubleOperationUtils.POINT_TWO);
                break;
            case 5:
                //取消预定
                lockedAmount = DoubleOperationUtils.doubleSub(lockedAmount, changeNumDec, DoubleOperationUtils.POINT_TWO);
                break;
            case 6:
                //调整
                break;
            case 7:
                //盘点
                break;
            default:
                break;
        }


        //有效数量
        realAmount = DoubleOperationUtils.doubleSub(amount, lockedAmount, DoubleOperationUtils.POINT_TWO);
        materialBatch.setRealNum(realAmount<0?0:realAmount);
        materialBatch.setLockNum(lockedAmount);
        materialBatch.setAmount(amount);

        return materialBatch;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<MaterialBatch> findListByInventoryId(final Long id) {
        List<MaterialBatch> result=materialBatchRepository.findAll(new Specification<MaterialBatch>() {

            @Override
            public Predicate toPredicate(Root<MaterialBatch> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();

                if(id !=null){
                    From<?, ?> inventory = root.join(MaterialBatch_.INVENTORY, JoinType.LEFT);
                    predicatesList.add(cb.equal(inventory.get(Inventory_.ID),id));
                    //库存有效数量>0或者锁定数量>0
                    predicatesList.add(cb.or(cb.gt(root.<Integer>get(MaterialBatch_.REALNUM),0),cb.gt(root.<Integer>get(MaterialBatch_.LOCAKNUM),0)));
                }

                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));

                return query.getRestriction();
            }
        });

        materialBatchBuilder.init(result).addInventory().addProvider();
        List<Inventory> inventoryList = new ArrayList<>();
        for(MaterialBatch materialBatch : result){
            inventoryList.add(materialBatch.getInventory());
        }
        inventoryBuilder.init(inventoryList).addWarehouse().addMaterial();

        return result;
    }


}
