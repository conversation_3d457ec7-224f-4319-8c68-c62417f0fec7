package cn.facilityone.shang.stock.stock003.builder;

import cn.facilityone.shang.entity.asset.Provider;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.MaterialBatch;
import cn.facilityone.shang.stock.common.repository.MaterialBatchRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/8/14.
 */
@Service
@XiaTransactional(readOnly = true)
public class MaterialBatchToBuilder {

    @Autowired
    private MaterialBatchRepository materialBatchRepository;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    private List<MaterialBatch> materialBatchs;

    private List<Long> ids;

    public MaterialBatchToBuilder init(List<MaterialBatch> materialBatchList){
        this.materialBatchs=materialBatchList;
        ids=new ArrayList<>();
        if(materialBatchList!=null && materialBatchList.size()>0){
            for(MaterialBatch materialBatch : materialBatchList){
                this.ids.add(materialBatch.getId());
            }

        }

        return this;
    }

    public MaterialBatchToBuilder addInventory(){
        if(this.ids!=null && this.ids.size()>0){
            List<Inventory> inventorys=materialBatchRepository.findInventoryByIds(this.ids.toArray(new Long[this.ids.size()]));
            Map<Long,Inventory> inventoryMap = new HashMap<>();

            for(Inventory inventory : inventorys){
                inventoryMap.put(inventory.getId(),inventory);
            }
            for(MaterialBatch materialBatch : this.materialBatchs){
                if(materialBatch.getInventory()!=null){
                    materialBatch.setInventory(inventoryMap.get(materialBatch.getInventory().getId()));
                }else{
                    materialBatch.setInventory(null);
                }

            }
        }
        return this;
    }

    public  MaterialBatchToBuilder addProvider(){
        if(this.ids!=null && this.ids.size()>0){
            List<Provider> providerList=materialBatchRepository.findProviderByIds(this.ids.toArray(new Long[this.ids.size()]));
            Map<Long,Provider> longProviderMap =new HashMap<>();
            for(Provider provider : providerList){
                longProviderMap.put(provider.getId(),provider);
            }
            for(MaterialBatch materialBatch : this.materialBatchs){
                if(materialBatch.getProvider()!=null){
                    materialBatch.setProvider(longProviderMap.get(materialBatch.getProvider().getId()));
                }else{
                    materialBatch.setProvider(null);
                }

            }
        }
        return this;
    }

    public  MaterialBatchToBuilder addProviderName(){
        if(this.ids!=null && this.ids.size()>0){
            List<Provider> providerList=materialBatchRepository.findProviderByIds(this.ids.toArray(new Long[this.ids.size()]));
            StringBuffer sb = new StringBuffer();
            sb.append("SELECT mb.materialbatch_id as mbId, v.vendor_name as vName ");
            sb.append("FROM materialbatch mb ");
            sb.append("LEFT JOIN vendor v ON mb.provide_id=v.vendor_id ");
            sb.append("WHERE mb.materialbatch_id IN "+listToString(this.ids)+"");

            Map<Long, String> countEqOrder = (Map<Long, String>) jdbcTemplate.query(sb.toString(), new ResultSetExtractor() {
                @Override
                public Object extractData(ResultSet rs) throws SQLException, DataAccessException {
                    Map<Long, String> countEqOrder = new HashMap<Long, String>();
                    while (rs.next()) {
                            countEqOrder.put(rs.getLong("mbId"), rs.getString("vName"));
                    }
                    return countEqOrder;
                }
            });

            for(MaterialBatch materialBatch : this.materialBatchs){
                if(materialBatch.getProvider()!=null){
                    Provider provider = new Provider();
                    provider.setName(countEqOrder.get(materialBatch.getId()));
                    materialBatch.setProvider(provider);
                }else{
                    materialBatch.setProvider(null);
                }

            }
        }
        return this;
    }

    /**
     * 把List转化成字符串形式
     * @param list
     * @return
     */
    public String listToString(List<Long> list) {
        StringBuilder result = new StringBuilder("(");
        for (int i = 0; i < list.size(); i++) {
            if (i == list.size() - 1) {
                result.append(list.get(i));
            } else {
                result.append(list.get(i) + ",");
            }
        }
        result.append(")");
        return result.toString();
    }

}
