package cn.facilityone.shang.stock.stock003.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org002.service.EmployeeService;
import cn.facilityone.shang.stock.stock002.dto.StockAdjustDTO;
import cn.facilityone.shang.stock.stock002.service.InventoryService;
import cn.facilityone.shang.stock.stock003.dto.MaterialBatchDTO;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;
import cn.facilityone.shang.stock.stock003.dto.StockMessageDto;
import cn.facilityone.shang.stock.stock003.dto.StockQueryRequest;
import cn.facilityone.shang.stock.stock003.service.InventoryManagementActivityService;
import cn.facilityone.shang.stock.stock003.service.MaterialBatchChangeService;
import cn.facilityone.shang.stock.stock003.service.MaterialBatchService;
import cn.facilityone.shang.stock.stock035.service.MaterialReserveService;
import cn.facilityone.shang.system.sys001.service.UserService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存管理
 * Created by charles.chen
 * @since 2015/8/05.
 * @version 1.0
 */

@Path("/stock003")
public class InventoryMgrResource {

    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private MaterialBatchService materialBatchService;
    @Autowired
    private InventoryManagementActivityService inventoryManagementActivityService;
    @Autowired
    private MaterialBatchChangeService materialBatchChangeService;
    @Autowired
    private UserService userService;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private MaterialReserveService materialReserveService;

    private static final String TEMPLATE_User_PATH = "/business/stockV2/stock003-inventoryMgr.ftl";
    private String ms000025;

    @GET
    @Template(name = TEMPLATE_User_PATH)
    public Map<String, Object> init() {
        Map<String, Object> data = new HashMap<String, Object>();

        Long operateUserId =
                ((XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal())
                        .getId();
       String realName =
                    ((XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal())
                            .getRealName();
        data.put("laborerName", realName);
        data.put("laborerId", operateUserId);
        return data;
    }


    /**
     * 根据多条件查询出库批次记录(为退库做准备)
     * @return  List<MaterialBatchChange>
     */
    @POST
    @Path("inventorymanage/records")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findAllInventoryRecords(final StockQueryRequest request) {

        List<MaterialBatchChange> pageInfo=materialBatchChangeService.findMaterialBatchChangeByDtoAndPage(request);

        return new DataTableResponse(pageInfo, pageInfo.size(), 1,
                request.getDraw());
    }


    /**
     * 根据多条件查询库存改动记录
     * @return List<MaterialBatchChange>
     */
    @POST
    @Path("inventorymanage/reserverecords/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findStockReserveRecords(@PathParam("id") Long id,final DataTableRequest request) {

        List<MaterialBatchChange> changeList=materialBatchChangeService.findMaterialBatchChangeByActivityId(id, request);

        return new DataTableResponse(changeList, changeList.size(),1,
                request.getDraw());
    }

    /**
     * 库存入库
     */
    @POST
    @Path("inventorymanage/stockin")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result saveStockIn(StockInDTO dto) {

        String code = inventoryManagementActivityService.handleStockIn(dto);

        return new Result(Result.CODE_200, XiaMesssageResource.getMessage("MS000024", null, ""), new StockMessageDto(code));

    }

    /**
     * 库存出库
     */
    @POST
    @Path("inventorymanage/stockout")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result saveStockOut(StockInDTO dto) {

        if (!inventoryManagementActivityService.checkMaterBatchNumberEnough(dto)) {
            Result result = new Result();
            result.setCode(Result.CODE_200);
            result.setMessage(XiaMesssageResource.getMessage("m.message.can.not.operation.materialBatch",null,""));
            result.setStatus(Result.STATUS_ERROR);
            result.setData(null);
            return result;
        }
        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityService.handleStockOut(dto);

        return new Result(Result.CODE_200, XiaMesssageResource.getMessage("MS000025", null, ""), new StockMessageDto(inventoryManagementActivity.getNo()));

    }

    /**
     * 库存退库
     * @return
     */
    @POST
    @Path("inventorymanage/stockback")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result saveStockBack(StockInDTO dto) {

        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityService.handleStockBack(dto);

        return new Result(Result.CODE_200, XiaMesssageResource.getMessage("MS000026", null, ""), new StockMessageDto(inventoryManagementActivity.getNo()));

    }

    /**
     * 库存预定出库
     */
    @POST
    @Path("inventorymanage/stockreserveout")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result saveStockReserveOut(StockInDTO dto) {
        boolean isoutboundflag = inventoryManagementActivityService.findIsoutbound(dto.getActivityId());
        boolean isoutStockOut = inventoryManagementActivityService.findIsoutStockOut(dto.getActivityId());
        if(isoutboundflag){
            Result result = new Result();
            result.setCode(Result.CODE_200);
            result.setMessage(XiaMesssageResource.getMessage("MS000034",null,""));
            result.setStatus(Result.STATUS_ERROR);
            result.setData(null);
            return result;
        }else if(isoutStockOut) {
            Result result = new Result();
            result.setCode(Result.CODE_200);
            result.setMessage(XiaMesssageResource.getMessage("MS000035",null,""));
            result.setStatus(Result.STATUS_ERROR);
            result.setData(null);
            return result;
        }else {
                InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityService.handleStockReserveOut(dto);
                if (null == inventoryManagementActivity) {
                    Result result = new Result();
                    result.setCode(Result.CODE_200);
                    result.setMessage(XiaMesssageResource.getMessage("m.message.can.not.operation.materialBatch",null,""));
                    result.setStatus(Result.STATUS_ERROR);
                    result.setData(null);
                    return result;
                }
                return new Result(Result.CODE_200, XiaMesssageResource.getMessage(ms000025, null, ""),
                        new StockMessageDto(inventoryManagementActivity.getNo(), inventoryManagementActivity.getConnectNo()));

            }
        }


    /**
     * 库存调整
     */
    @POST
    @Path("inventorymanage/stockadjust")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result saveStockAdjust(StockAdjustDTO dto) {

        inventoryManagementActivityService.handleStockAdjust(dto);

        return new Result(XiaMesssageResource.getMessage("MS000023", null, ""));

    }
    /**
     * 库存取消预定
     */
    @DELETE
    @Path("inventorymanage/stockreservecancel/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result cancelStockReserve(@PathParam("id") Long id) {

        inventoryManagementActivityService.handleCancelStockReserve(id, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE);

        return new Result(XiaMesssageResource.getMessage("MS000028", null, ""));

    }

    /**
     * 库存预定
     */
    @POST
    @Path("inventorymanage/stockreserve")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result saveStockReserve(StockInDTO dto) {

        InventoryManagementActivity inventoryManagementActivity = inventoryManagementActivityService.handleStockReserve(dto);

        // 发送通知
        materialReserveService.sendMessage(inventoryManagementActivity, dto.getSupervisorId());

        return new Result(Result.CODE_200, XiaMesssageResource.getMessage("MS000027", null, ""),
                 new StockMessageDto(inventoryManagementActivity==null?"":inventoryManagementActivity.getNo()));

    }

    /**
     * 获得库存的批次信息
     *
     * @return  List<MaterialBatch>
     */
    @POST
    @Path("materialbatches/date")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public Result getMaterialBatches(MaterialBatchDTO dto) {

        return new Result(materialBatchService.findByDto(dto));
    }


    /**
     * 获得批次的过期时间集合
     *
     * @return Set<Date>
     */
    @GET
    @Path("inventorymanage/inventoryduedates/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findDueDateByInventory(@PathParam("id") Long id) {

        return new Result(inventoryService.findDueDateByInventoryId(id));
    }

}
