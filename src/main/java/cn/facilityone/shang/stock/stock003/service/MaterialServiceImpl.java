package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.asset.asset002.repository.ProviderRepository;
import cn.facilityone.shang.asset.asset002.repository.QRCodeRepository;
import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableConditions;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.configuration.properties.CompanyProperties;
import cn.facilityone.shang.common.repository.AttachmentRepository;
import cn.facilityone.shang.common.repository.PictureRepository;
import cn.facilityone.shang.common.service.QRCodeService;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.entity.asset.Provider;
import cn.facilityone.shang.entity.common.Attachment;
import cn.facilityone.shang.entity.common.Picture;
import cn.facilityone.shang.entity.common.QRCode;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.Material;
import cn.facilityone.shang.entity.inventory.MaterialBatch;
import cn.facilityone.shang.inventory.respository.MaterialRepository;
import cn.facilityone.shang.stock.common.repository.InventoryRepository;
import cn.facilityone.shang.stock.common.repository.MaterialBatchRepository;
import cn.facilityone.shang.stock.stock002.dto.AutoObjectDTO;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Transformer;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Created by charles.chen on 2015/9/2.
 */
@Service
public class MaterialServiceImpl implements MaterialService {
    @Autowired
    private MaterialRepository materialRepository;
    @Autowired
    private ProviderRepository providerRepository;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private UploadFileService uploadFileService;
    @Autowired
    private QRCodeRepository qrCodeRepository;
    @Autowired
    private QRCodeService qrCodeService;
    @Autowired
    private CompanyProperties companyProperties;
    @Autowired
    private MaterialBatchRepository materialBatchRepository;

    private static final String SYS_VERTICAL_LINE="|";

    private static final String STOCK="STOCK";

    private static final String MATERIAL="MATERIAL";

    @Override
    public List<AutoObjectDTO> findAllBrand(DataTableRequest request) {

        String searchText=this.getSearchTextByRequest(request);

        List<String> brands = new ArrayList<>();
        if(!StringUtils.isBlank(searchText)){
          brands = materialRepository.findAllBrandLikeName(dataTableService.buildLikeText(searchText));
        }else{
          brands = materialRepository.findAllBrand();
        }

        List<AutoObjectDTO> dtoList=this.getDtoList(brands);

        return dtoList;
    }

    @Override
    public List<AutoObjectDTO> findAllModel(DataTableRequest request) {

        String searchText=this.getSearchTextByRequest(request);

        List<String> models = new ArrayList<>();

        if(!StringUtils.isBlank(searchText)){
            models = materialRepository.findAllModelLikeName(dataTableService.buildLikeText(searchText));
        }else{
            models = materialRepository.findAllModel();
        }

        List<AutoObjectDTO> dtoList=this.getDtoList(models);

        return dtoList;
    }

    @Override
    public List<AutoObjectDTO> findAllProiderName(DataTableRequest request) {

        String searchText = null;
        if(null != request){
            searchText = this.getSearchTextByRequest(request);
        }

        List<String> providerNames = new ArrayList<>();

        if(!StringUtils.isBlank(searchText)){
            providerNames = providerRepository.findAllProviderNamesLikeName(dataTableService.buildLikeText(searchText));
        }else{
            providerNames = providerRepository.findAllProviderNames();
        }

        List<AutoObjectDTO> dtoList=this.getDtoList(providerNames);

        return dtoList;
    }

    @Override
    public List<Provider> findProiderListByInventoryId(DataTableRequest request){
        List<DataTableConditions> conditionses = request.getConditions();
        Set<Provider> providers = new HashSet<Provider>();
        if(CollectionUtils.isNotEmpty(conditionses)){
            DataTableConditions conditions = conditionses.get(0);
            Long inventoryId = Long.parseLong(conditions.getValues().get(0));
            if (inventoryId!=null){
                List<MaterialBatch> materialBatches = materialBatchRepository.findWithProviderByInventoryId(inventoryId);
                for (MaterialBatch materialBatch : materialBatches) {
                    Provider provider = materialBatch.getProvider();
                    if (null != provider){
                        providers.add(provider);
                    }
                }
            }
        }
        return new ArrayList<>(providers);
    }

    /**
     * Modified by Zack at 2016-8-19
     * Reason: 需要根据项目获取物资列表，不能跨项目。
     * @param request
     * @return
     */
    @Override
    public List<Material> findAllMaterial(DataTableRequest request) {

        String searchText = this.getSearchTextByRequest(request);

        List<Material> materials = new ArrayList<>();

        /**
         * 获取当前项目下所有库存信息中的物资
         * 因为一个库存对应一条物资，所以从库存信息中可以根据项目取到物资
         */
        List<Material> nowMaterials = inventoryRepository.findMaterials();

        if(!StringUtils.isBlank(searchText)){

            materials = materialRepository.findAllLikeName(dataTableService.buildLikeText(searchText));

        }else{
            materials = materialRepository.findAll();
        }

        // 根据条件实际查出的物资信息（当前项目）
        List<Material> selMaterial = new ArrayList<>();
        // 循环判断从material中查出的必须是当前项目的，再放入selMaterial中
        for (Material m : materials) {
            // 这里我重写了Material实体中的equals方法来进行比较
            if (nowMaterials.contains(m)) {
                selMaterial.add(m);
            }
        }

        if(selMaterial==null || selMaterial.size()==0){
            return null;
        }
        List<String> keyList=new ArrayList<>();
        Iterator iterator =selMaterial.iterator();
        while (iterator.hasNext()){
            Material material = (Material)iterator.next();
            String key = this.getPrimarykeyFromMaterial(material);
            if(keyList.contains(key)){
                iterator.remove();
            }else{
                keyList.add(key);
            }
        }

        return selMaterial;
    }

    @Override
    public Material findOrCreateMaterial(HttpServletRequest request,Long warehouseId, Material material, List<Long> picIds, List<Long> docIds, boolean isChange) {
        Material ma=null;
        if(material.getId() !=null){
            Material materials = materialRepository.findOne(material.getId());
            ma=materials;
            ma.setCode(material.getCode());
            ma.setName(material.getName());
            ma.setBrand(material.getBrand());
            ma.setUnit(material.getUnit());
            ma.setModel(material.getModel());
            ma.setPrice(material.getPrice());
            ma.setComment(material.getComment());
            ma=materialRepository.save(ma);

            //绑定图片和附件
            if (picIds!=null&&picIds.size()>0){
                uploadFileService.updateDocument(Material.class.getSimpleName(), ma.getId().toString(), SystemConst.DOC_IMG, picIds);
            }
            if (docIds!=null&&docIds.size()>0){
                uploadFileService.updateDocument(Material.class.getSimpleName(), ma.getId().toString(), SystemConst.DOC_FILE, docIds);
            }

//            if (isChange){
//                QRCode qrCode = qrCodeRepository.findByTableNameAndPKeyId(Material.class.getSimpleName(), material.getId().toString());
//                if (qrCode!=null){
//                    qrCodeRepository.delete(qrCode);
//                }
                //创建二维码
//                ma=createQRPicture(request,warehouseId,ma);
//            }
        }
        if (ma == null) {
            Long oldMaterialId = material.getId();
            material.setId(null);
            ma = materialRepository.save(material);
            //绑定图片和附件
            if (picIds != null && picIds.size() > 0) {
                uploadFileService.updateDocument(Material.class.getSimpleName(), ma.getId().toString(), SystemConst.DOC_IMG, picIds);
            }
            if (docIds != null && docIds.size() > 0) {
                uploadFileService.updateDocument(Material.class.getSimpleName(), ma.getId().toString(), SystemConst.DOC_FILE, docIds);
            }
            //创建二维码
//            ma = createQRPicture(request, warehouseId, ma);
            // 删除旧的物品
            if (oldMaterialId != null) {
                materialRepository.delete(oldMaterialId);
            }

        }
        return ma;
    }

    @Override
    public List<AutoObjectDTO> findBrandByMaterialName(String name) {
        List<String> brands =  materialRepository.findBrandByName(name);
        List<AutoObjectDTO> dtoList=this.getDtoList(brands);
        return dtoList;
    }

    @Override
    public List<AutoObjectDTO> findModelByMaterialName(String name) {
        List<String> models =  materialRepository.findModelByName(name);
        List<AutoObjectDTO> dtoList=this.getDtoList(models);
        return dtoList;
    }

    private List<AutoObjectDTO> getDtoList(List<String> names){
        List<AutoObjectDTO> dtoList = new ArrayList<>();
        for(String name : names){
            if(!StringUtils.isBlank(name)){
                AutoObjectDTO dto = new AutoObjectDTO(name,name);
                dtoList.add(dto);
            }
        }
        return dtoList;

    }

    private String getSearchTextByRequest(DataTableRequest request){

        List<DataTableColumn> tableColumns = request.getColumns();
        String searchText=null;
        if(tableColumns!=null && tableColumns.size()>0){
            DataTableColumn column = tableColumns.get(0);
            searchText=column.getSearchText();
        }

        return searchText;
    }

    private String getPrimarykeyFromMaterial(Material material){
        StringBuffer sb =new StringBuffer();
        sb.append(material.getName()).append(",");
        sb.append(material.getBrand()).append(",");
        sb.append(material.getModel());
        return sb.toString();
    }

    /**
     * 创建物资二维码
     * @param Material
     * @return Picture
     */
    @Override
    public Inventory createQRPicture(HttpServletRequest request,Long warehouseId,Inventory inventory){
        Material material = inventory.getMaterial();
        QRCode qrCode=new QRCode();
        String uuid= UUID.randomUUID().toString().replaceAll("-", "");
        //TODO 应编码
        String materialName = material.getName();
        String code = material.getCode();
        if (material.getName().length()>97) {
            materialName = materialName.substring(0,97);
        }
        if (material.getCode().length()>97) {
            code = code.substring(0,97);
        }
        qrCode.setName(materialName+"("+code+")" + QRCodeService.SUFFIX);
        qrCode.setTableName(Inventory.class.getSimpleName());
        qrCode.setpKeyId(inventory.getId().toString());
        qrCode.setPath("");
        qrCode.setKey(uuid);
        qrCode.setContentType(QRCode.CONTENT_TYPE);
        qrCode=qrCodeRepository.save(qrCode);
        inventory.setQrcode(qrCode);
        //为二维码添加内容
        this.qrCodeGeneration(request, warehouseId, inventory, uuid);
        return inventory;
    }

    /**
     * 生成二维码内容
     * @param
     * @return
     */
    private void qrCodeGeneration(HttpServletRequest request,Long warehouseId,Inventory inventory,String uuid){
        // 调用生成二维码
        String content=this.generateContentByEquipment(inventory,warehouseId,uuid);

        qrCodeService.encodeToQRCode(request, content.toString(), inventory.getQrcode().getId());
    }

    @Override
    public String generateContentByEquipment(Inventory inventory,Long warehouseId,String uuid) {
        //公司名称 UUID MATERIAL 库存id 物资编码
        StringBuffer stringBuffer=new StringBuffer();
        String companyName=companyProperties.getName();
//        String uuid=null;
//        if(equipment.getId()!=null){
//             uuid=qrCodeRepository.findByTableNameAndPkey(Equipment.class.getSimpleName(),equipment.getId().toString());
//        }
//        if(StringUtils.isBlank(uuid)){
//            uuid=UUID.randomUUID().toString().replaceAll("-", "");
//        }
        stringBuffer.append(STOCK);
        stringBuffer.append(SYS_VERTICAL_LINE+MATERIAL);
        stringBuffer.append(SYS_VERTICAL_LINE+warehouseId.toString());
        if(!StringUtils.isBlank(inventory.getMaterial().getCode())){
            stringBuffer.append(SYS_VERTICAL_LINE + inventory.getMaterial().getCode());
        }

        stringBuffer.append(SYS_VERTICAL_LINE+companyName);

        return stringBuffer.toString();
    }
}
