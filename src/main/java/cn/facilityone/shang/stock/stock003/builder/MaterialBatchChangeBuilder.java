package cn.facilityone.shang.stock.stock003.builder;

import cn.facilityone.shang.entity.asset.Provider;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatch;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.stock.common.repository.MaterialBatchChangeRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/8/14.
 */
@Service
public class MaterialBatchChangeBuilder {
    @Autowired
    private MaterialBatchChangeRepository materialBatchChangeRepository;

    private List<MaterialBatchChange> materialBatchChanges;

    private List<Long> ids;

    public MaterialBatchChangeBuilder init(List<MaterialBatchChange> materialBatchChangeList){
        this.materialBatchChanges=materialBatchChangeList;
        this.ids=new ArrayList<>();
        if(this.materialBatchChanges!=null && this.materialBatchChanges.size()>0){
            for(MaterialBatchChange materialBatchChange : this.materialBatchChanges){
                this.ids.add(materialBatchChange.getId());
            }
        }
        return this;
    }

    @XiaTransactional(readOnly = true)
    public MaterialBatchChangeBuilder addActivity(){
        if(this.ids!=null && this.ids.size()>0){
          List<InventoryManagementActivity> activities= materialBatchChangeRepository.findActivitysByIds(this.ids.toArray(new Long[this.ids.size()]));
          Map<Long,InventoryManagementActivity>   activityMap = new HashMap<>();
          for(InventoryManagementActivity activity : activities){
              activityMap.put(activity.getId(),activity);
          }
          for(MaterialBatchChange change : this.materialBatchChanges){
              if(change.getInventoryManagementActivity()!=null){
                  change.setInventoryManagementActivity(activityMap.get(change.getInventoryManagementActivity().getId()));
              }else{
                  change.setInventoryManagementActivity(null);
              }
          }

        }
        return this;
    }

    @XiaTransactional(readOnly = true)
    public MaterialBatchChangeBuilder addMaterialBatch(){
        if(this.ids!=null && this.ids.size()>0){
         List<MaterialBatch> materialBatchList=materialBatchChangeRepository.findMaterialBatchsByIds(this.ids.toArray(new Long[this.ids.size()]));
         Map<Long,MaterialBatch> longMaterialBatchMap =new HashMap<>();
            for(MaterialBatch materialBatch : materialBatchList){
                longMaterialBatchMap.put(materialBatch.getId(),materialBatch);
            }

            for(MaterialBatchChange change : this.materialBatchChanges){
                if(change.getMaterialBatch()!=null){
                    change.setMaterialBatch(longMaterialBatchMap.get(change.getMaterialBatch().getId()));
                }else{
                    change.setMaterialBatch(null);
                }
            }
        }
        return this;
    }

}
