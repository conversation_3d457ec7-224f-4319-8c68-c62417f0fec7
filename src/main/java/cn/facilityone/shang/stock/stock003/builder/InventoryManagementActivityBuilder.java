package cn.facilityone.shang.stock.stock003.builder;

import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.common.repository.InventoryManagementActivityRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/8/14.
 */
@Service
public class InventoryManagementActivityBuilder {
    @Autowired
    private InventoryManagementActivityRepository activityRepository;

    private List<InventoryManagementActivity> activities;

    private List<Long> ids;

    public InventoryManagementActivityBuilder init(List<InventoryManagementActivity> activityList){
        this.activities=activityList;
        this.ids=new ArrayList<>();
        if(this.activities!=null && this.activities.size()>0){
            for(InventoryManagementActivity activity : this.activities){
                this.ids.add(activity.getId());
            }
        }

        return this;
    }
    @XiaTransactional(readOnly = true)
    public InventoryManagementActivityBuilder addLaborer(){
        if(this.ids!=null && this.ids.size()>0){
           List<Employee> employees= activityRepository.findEmployeesByIds(this.ids.toArray(new Long[this.ids.size()]));
           Map<Long,Employee> employeeMap = new HashMap<>();
           for(Employee employee : employees){
               employeeMap.put(employee.getId(),employee);
           }
            for(InventoryManagementActivity activity : this.activities){
                if(activity.getLaborer()!=null){
                    activity.setLaborer(employeeMap.get(activity.getLaborer().getId()));
                }else{
                    activity.setLaborer(null);
                }
            }

        }
        return this;
    }

    @XiaTransactional(readOnly = true)
    public InventoryManagementActivityBuilder addHandles(){
        if(this.ids!=null && this.ids.size()>0){
            List<Employee> employees= activityRepository.findHandelsByIds(this.ids.toArray(new Long[this.ids.size()]));
            Map<Long,Employee> employeeMap = new HashMap<>();
            for(Employee employee : employees){
                employeeMap.put(employee.getId(),employee);
            }
            for(InventoryManagementActivity activity : this.activities){
                if(activity.getHandles()!=null){
                    activity.setHandles(employeeMap.get(activity.getHandles().getId()));
                }else{
                    activity.setHandles(null);
                }
            }

        }
        return this;
    }

    @XiaTransactional(readOnly = true)
    public InventoryManagementActivityBuilder addMaterialBatchChange(){
        if(this.ids!=null && this.ids.size()>0){
            List<MaterialBatchChange> materialBatchChanges= activityRepository.findMaterialBatchChangesByIds(this.ids.toArray(new Long[this.ids.size()]));
            Map<Long,List<MaterialBatchChange>> longChangeMap = new HashMap<>();
            Long activityId = null;
            for(MaterialBatchChange change : materialBatchChanges){
                if (change!=null) {
                    if(change.getInventoryManagementActivity()!=null){
                        activityId = change.getInventoryManagementActivity().getId();
                        if(longChangeMap.get(activityId)!=null){
                            longChangeMap.get(activityId).add(change);
                        }else{
                            List<MaterialBatchChange> changeList = new ArrayList<>();
                            changeList.add(change);
                            longChangeMap.put(activityId,changeList);
                        }
                    }
                }
            }

            for(InventoryManagementActivity activity : this.activities){
                if(activity.getMaterialBatchChanges()!=null ){
                    activity.setMaterialBatchChanges(longChangeMap.get(activity.getId()));
                }

            }
        }
        return this;
    }

}
