package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.asset.asset002.repository.ProviderRepository;
import cn.facilityone.shang.asset.asset002.service.ProviderService;
import cn.facilityone.shang.common.component.message.MessageSenderTool;
import cn.facilityone.shang.common.component.message.template.MessageStockReservedeliveredlTemplate;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.staticmetamodel.MobilePush_;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.asset.Provider;
import cn.facilityone.shang.entity.inventory.*;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.inventory.respository.InventoryActivityRepository;
import cn.facilityone.shang.mobile.v1.stock.view.MInventoryListRequest;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org002.service.EmployeeService;
import cn.facilityone.shang.organize.org003.repository.OrganizationRepository;
import cn.facilityone.shang.stock.common.repository.*;
import cn.facilityone.shang.stock.common.staticmetamodel.InventoryManagementActivity_;
import cn.facilityone.shang.stock.common.util.StockCode;
import cn.facilityone.shang.stock.stock002.dto.StockAdjustDTO;
import cn.facilityone.shang.stock.stock002.dto.StockAdjustDataDTO;
import cn.facilityone.shang.stock.stock002.service.InventoryService;
import cn.facilityone.shang.stock.stock003.dto.ActivityCollectionDTO;
import cn.facilityone.shang.stock.stock003.dto.BatchDto;
import cn.facilityone.shang.stock.stock003.dto.StockDataDTO;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.core.exception.BusinessException;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.message.common.MessageType;
import cn.facilityone.xia.message.entity.MessageTemplate;
import cn.facilityone.xia.message.repository.MessageTemplateRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.persistence.criteria.*;
import java.util.*;

/**
 * Created by charles.chen on 2015/8/11.
 */
@Service
public class InventoryManagementActivityServiceImpl implements InventoryManagementActivityService {
    @Autowired
    private InventoryManagementActivityRepository inventoryManagementActivityRepository;
    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    private MaterialBatchChangeRepository materialBatchChangeRepository;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private MaterialBatchService materialBatchService;
    @Autowired
    private MaterialBatchRepository materialBatchRepository;
    @Autowired
    private ProviderService providerService;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private CommonUserService employeeService;
    @Autowired
    private MaterialBatchChangeService materialBatchChangeService;
    @Autowired
    private StockCode stockCode;
    @Autowired
    private InventoryActivityRepository inventoManagementActivity;
    @Autowired
    private OrganizationRepository organizationRepository;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private MessageSenderTool messageSenderTool;
    @Autowired
    private ProviderRepository providerRepository;
    @Autowired
    private MessageTemplateRepository messageTemplateRepository;
    @Autowired
    private InventoryActivityRepository inventoryActivityRepository;

    @Override
    public InventoryManagementActivity create(InventoryManagementActivity inventoryManagementActivity) {
        return inventoryManagementActivityRepository.save(inventoryManagementActivity);
    }

    @Override
    @XiaTransactional(readOnly = false)
    public String handleStockIn(StockInDTO dto) {
        //入库-行数据
        List<StockDataDTO> stockDataList=dto.getStockDatas();
        //最终保存集合(库存，批次，批次改动记录)
        List<MaterialBatch> materialBatches=new ArrayList<>();
        List<MaterialBatchChange> materialBatchChanges=new ArrayList<>();
        List<Inventory> inventories=new ArrayList<>();

        //根据每一行的入库数据 创建新的批次
        materialBatches = this.getBatchListFromStockDto(dto.getWarehouse().getId(),stockDataList);

        //创建操作记录
        InventoryManagementActivity activity = this.createActivityForStockIn(dto, materialBatches);

        //根据批次 创建 批次改动记录,改动 库存数量记录
        for(MaterialBatch materialBatch : materialBatches){
            //创建批次改动记录
            MaterialBatchChange materialBatchChange = this.createBatchChangeForStockIn(materialBatch,activity);
            materialBatchChanges.add(materialBatchChange);

            //修改库存记录-调整有效数量和平均价
            Inventory inventory = this.createInventoryForStockIn(materialBatch);
            inventories.add(inventory);
        }

        if (inventories.size()>0 && inventories.get(0)!=null) {
            inventoryRepository.saveInBatch(inventories);
        }
        materialBatchChangeRepository.saveInBatch(materialBatchChanges);
        String code = "";
        if(null != activity){
            code = activity.getNo();
        }
        return code;
    }

    private List<MaterialBatch> getBatchListFromStockDto(Long warehouseId,List<StockDataDTO> stockDataList){
        List<MaterialBatch> materialBatches=new ArrayList<>();
        List<Provider> providers = providerRepository.findAll();
        Map<String, Provider> pmap = getProviderMap(providers);
        List<Inventory> inventories = inventoryRepository.findMaterialListByWarehouseId(warehouseId);
        Map<String, Inventory> inventoryMap = getInventoryMap(inventories);
        for(StockDataDTO stockData : stockDataList){
            MaterialBatch materialBatch = this.getMaterialBatchFromDto(warehouseId,stockData,pmap,inventoryMap);
            materialBatches.add(materialBatch);
        }
        return materialBatches;
    }

    public Map<String, Provider> getProviderMap(List<Provider> providers){
        Map<String, Provider> map = new HashMap<>();
        for(Provider provider : providers){
            map.put(provider.getName(), provider);
        }
        return map;
    }

    public Map<String, Inventory> getInventoryMap(List<Inventory> inventories){
        Map<String, Inventory> map = new HashMap<>();
        for(Inventory inventory : inventories){
            map.put(inventory.getMaterial().getCode(), inventory);
        }
        return map;
    }

    private InventoryManagementActivity createActivityForStockIn(StockInDTO dto,List<MaterialBatch> batches){
        InventoryManagementActivity inventoryManagementActivity = this.createActivityByDtoAndType(dto,InventoryManagementActivity.InventoryActivityType.IN);
        inventoryManagementActivity.setMaterialBatchs(batches);
        return this.create(inventoryManagementActivity);
    }

    private Inventory createInventoryForStockIn(MaterialBatch materialBatch){
        Inventory inventory = materialBatch.getInventory();
        //计算库存的平均值
        inventory=inventoryService.calculatedAvg(inventory,materialBatch.getAmount(), materialBatch.getPrice());
        //调整库存数量
        inventory=inventoryService.quantityChanges(inventory,InventoryManagementActivity.InventoryActivityType.IN,materialBatch.getAmount());
        return inventory;
    }

    private MaterialBatchChange createBatchChangeForStockIn(MaterialBatch materialBatch,InventoryManagementActivity activity){
        MaterialBatchChange materialBatchChange = new MaterialBatchChange();
        materialBatchChange.setMaterialBatch(materialBatch);
        materialBatchChange.setChangeNum(materialBatch.getAmount());
        materialBatchChange.setType(materialBatchChangeService.findAdjustTypeByActivityType(InventoryManagementActivity.InventoryActivityType.IN));
        materialBatchChange.setInventoryManagementActivity(activity);
        return materialBatchChange;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity handleStockReserve(StockInDTO dto) {
        ActivityCollectionDTO collectionDTO = this.buildStockReserveDto(dto, "");
        //最终存储集合
        Set<MaterialBatch> materialBatches=collectionDTO.getBatchSets();
        List<MaterialBatchChange> materialBatchChanges=collectionDTO.getChanges();
        List<Inventory> inventorys=collectionDTO.getInventorys();

        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);

        //新建预定操作记录
        InventoryManagementActivity inventoryManagementActivity = this.createActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.RESERVE);
        inventoryManagementActivity.setMaterialBatchs(new ArrayList<MaterialBatch>(materialBatches));
        //绑定工单
        Long woId = dto.getWoId();
        if(null != woId){
            inventoryManagementActivity.setPkeyId(String.valueOf(woId));
            inventoryManagementActivity.setTableName(WorkOrder.class.getSimpleName());
            WorkOrder workOrder = workOrderRepository.findOne(woId);
            if(null != workOrder){
                inventoryManagementActivity.setDescription(XiaMesssageResource.getMessage("page.pm002.connectWoCode")+":"+workOrder.getCode());
            }
        }
        inventoryManagementActivity=this.create(inventoryManagementActivity);
        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(inventoryManagementActivity);
        }
        materialBatchChangeRepository.saveInBatch(materialBatchChanges);

        //预定记录
        InventoryActivity inventoryActivity  = new InventoryActivity();
        inventoryActivity.setReserveNo(inventoryManagementActivity.getNo());
        inventoryActivity.setActivityType(InventoryActivity.InventoryActivityType.RESERVE);
        inventoryActivity.setStatus(InventoryActivity.InventoryActivityStatus.PENDING);
        inventoryActivity.setComment("");
        inventoManagementActivity.save(inventoryActivity);

        return inventoryManagementActivity;

    }

    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity handleStockReserveForPm(StockInDTO dto, Long project) {

        ActivityCollectionDTO collectionDTO = this.buildStockReserveDto(dto, InventoryManagementActivity.INVENTORY_PM);
        //最终存储集合
        Set<MaterialBatch> materialBatches=collectionDTO.getBatchSets();
        List<MaterialBatchChange> materialBatchChanges=collectionDTO.getChanges();
        List<Inventory> inventorys=collectionDTO.getInventorys();
        if(CollectionUtils.isEmpty(materialBatchChanges)){
            return null;
        }
        //保存修改对象
        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);
        //新建预定操作记录
        InventoryManagementActivity inventoryManagementActivity = this.createActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.RESERVE);
        inventoryManagementActivity.setMaterialBatchs(new ArrayList<MaterialBatch>(materialBatches));
        //手动注入项目ID
        inventoryManagementActivity.setProject(project);
        inventoryManagementActivity.setNo(stockCode.getStockCodeWithProjectId(InventoryManagementActivity.InventoryActivityType.RESERVE,project));
        inventoryManagementActivity=this.create(inventoryManagementActivity);
        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(inventoryManagementActivity);
        }
        materialBatchChangeRepository.saveInBatch(materialBatchChanges);

        return inventoryManagementActivity;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity handleStockOut(StockInDTO dto) {
        //最终存储集合(批次，批次改动记录)
        Set<MaterialBatch> materialBatches=new HashSet<>();
        List<MaterialBatchChange> materialBatchChanges=new ArrayList<>();
        List<Inventory> inventorys=new ArrayList<>();
        Map<Long,Inventory> inventoryMap=new HashMap<>();

        //批次MAP
        Map<Long,List<MaterialBatch>> batchMap=new HashMap<>();
        Map<Long,Map<Date,List<MaterialBatch>>> batchDateMap=new HashMap<>();
        Map<Date,List<MaterialBatch>> dataBatch = new HashMap<>();

        List<StockDataDTO> stockDatas=dto.getStockDatas();
        //获得所有的改动库存
        inventorys = this.getInventoryFromStockOutData(stockDatas);

        inventoryMap = this.getInventoryMapFromStockOutData(inventorys);

        //出库记录-对每一个行出库记录进行处理
        for(StockDataDTO data : stockDatas){
            //出库数量
            Double amount=data.getAmount();
            Double outAmount=data.getAmount();
            //批次过期时间
            Date dueDate=data.getDueDate();
            //获得改动库存
            Inventory inventory =inventoryMap.get(data.getInventory().getId());

            //库存变化
            inventory=inventoryService.quantityChanges(inventory,InventoryManagementActivity.InventoryActivityType.OUT,amount);

            //获得改动的需要改动的批次
            List<MaterialBatch> materialBatchList = new LinkedList<>();

            //选择的批次
            List<BatchDto> batchDtos = data.getBatchDtos();
            //选择了批次
            if(CollectionUtils.isNotEmpty(batchDtos)){
                for(BatchDto batchDto : batchDtos){
                    Long batchId = batchDto.getBatchId();
                    if(null != batchId){
                        Double batchAmount=batchDto.getNumber();//选择的批次数量
                        MaterialBatch materialBatch = materialBatchRepository.findOne(batchId);
                        //批次数量改动
                        materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.OUT, batchAmount);
                        materialBatches.add(materialBatch);
                        //新建批次变化记录
                        MaterialBatchChange materialBatchChange = this.buildMaterialBatchChange(materialBatch, InventoryManagementActivity.InventoryActivityType.OUT, batchAmount);
                        materialBatchChanges.add(materialBatchChange);
                    }
                }
            }
// else{
//                 //根据过期时间来获得不同批次
//                //可能出现多种情况，比如一个库存出库多个批次且过期时间不同
//                if(dueDate!=null){
//                    //有选择过期时间
//                    if(batchDateMap.get(inventory.getId())!=null){
//                        dataBatch = batchDateMap.get(inventory.getId());
//                        //出库库存指定过期时间的批次是否存在？？？
//                        if(dataBatch.get(dueDate)!=null){
//                            materialBatchList = dataBatch.get(dueDate);
//                        }else{
//                            materialBatchList=materialBatchRepository.findByInventoryIdAndDueDate(inventory.getId(),dueDate);
//                            Map<Date,List<MaterialBatch>> dateListMap = new HashMap<>();
//                            dateListMap.put(dueDate,materialBatchList);
//                            batchDateMap.put(inventory.getId(),dateListMap);
//                        }
//                    }else{
//                        materialBatchList=materialBatchRepository.findByInventoryIdAndDueDate(inventory.getId(),dueDate);
//                        Map<Date,List<MaterialBatch>> dateListMap = new HashMap<>();
//                        dateListMap.put(dueDate,materialBatchList);
//                        batchDateMap.put(inventory.getId(),dateListMap);
//                    }
//
//                }else{
//                    //先出库过期时间近
//                    //即使批次过期 也可以进行出库
//                    //没有选择过期时间
//                    if(batchMap.get(inventory.getId())!=null){
//                        materialBatchList = batchMap.get(inventory.getId());
//                    }else{
//                        List<MaterialBatch> batchNodate = materialBatchRepository.findByInventoryIdHardly(inventory.getId());
//                        List<MaterialBatch> batchDateAsc = materialBatchRepository.findByInventoryIdAscHardly(inventory.getId());
//                        materialBatchList.addAll(batchDateAsc);
//                        materialBatchList.addAll(batchNodate);
//                        batchMap.put(inventory.getId(),materialBatchList);
//                    }
//                }
//                //根据出库记录对批次进行数量改动 同时创建批次改动记录
//                if(materialBatchList!=null && materialBatchList.size()>0){
//                    for(MaterialBatch materialBatch : materialBatchList){
//
//                        //处理出库数量 >0的记录问题
//                        if(materialBatch.getRealNum() > 0 ){
//                            //批次数量>=出库数量 批次有效数量-出库数量
//                            if(materialBatch.getRealNum() >=outAmount){
//                                materialBatch=materialBatchService.quantityChanges(materialBatch,InventoryManagementActivity.InventoryActivityType.OUT,outAmount);
//                                materialBatches.add(materialBatch);
//                                //批次改动
//                                MaterialBatchChange materialBatchChange = this.buildMaterialBatchChange(materialBatch,InventoryManagementActivity.InventoryActivityType.OUT,outAmount);
//                                materialBatchChanges.add(materialBatchChange);
//                                break;
//                            }else{
//                                //批次数量<出库数量 该批次有效数量变为0 需要借库存中的其他批次
//                                outAmount=outAmount-materialBatch.getRealNum();
//                                //批次改动
//                                MaterialBatchChange materialBatchChange = this.buildMaterialBatchChange(materialBatch,InventoryManagementActivity.InventoryActivityType.OUT,materialBatch.getRealNum());
//                                materialBatchChanges.add(materialBatchChange);
//                                materialBatch=materialBatchService.quantityChanges(materialBatch,InventoryManagementActivity.InventoryActivityType.OUT,materialBatch.getRealNum());
//                                materialBatches.add(materialBatch);
//                            }
//                        }
//                    }
//                }
//            }
        }
        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);
        //创建出库操作记录 注入关联对象数据
        InventoryManagementActivity inventoryManagementActivity = this.createActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.OUT);
        inventoryManagementActivity.setMaterialBatchs(new ArrayList<MaterialBatch>(materialBatches));
        inventoryManagementActivity=this.create(inventoryManagementActivity);
        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(inventoryManagementActivity);
        }
        materialBatchChangeRepository.saveInBatch(materialBatchChanges);
        return inventoryManagementActivity;
    }

    private List<Inventory> getInventoryFromStockOutData(List<StockDataDTO> stockDatas){
        List<Inventory> inventoryList = new ArrayList<>();
        List<Long> inventoryIds = new ArrayList<>();
        for(StockDataDTO dataDTO : stockDatas){
            if(dataDTO.getInventory() != null && dataDTO.getInventory().getId() != null){
                inventoryIds.add(dataDTO.getInventory().getId());
            }
        }
        if(inventoryIds.size() > 0){
             inventoryList = inventoryRepository.findByIds(inventoryIds.toArray(new Long[inventoryIds.size()]));
        }
        return inventoryList;
    }

    private Map<Long,Inventory> getInventoryMapFromStockOutData(List<Inventory> inventoryList){

        Map<Long,Inventory> longInventoryMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(inventoryList)){
            for(Inventory inventory : inventoryList){
                longInventoryMap.put(inventory.getId(),inventory);
            }
        }

        return longInventoryMap;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity handleStockBack(StockInDTO dto) {
        //最终保存集合（库存，批次，批次变化记录，操作记录）
        List<MaterialBatch> materialBatches=new ArrayList<>();
        //新建批次变化
        List<MaterialBatchChange> materialBatchChanges=new ArrayList<>();
        //原始批次变化-用于记录每个批次变化的退库数量
        List<MaterialBatchChange> materialOrigins=new ArrayList<>();
        List<Inventory> inventorys=new ArrayList<>();

        Map<Long,Inventory> inventoryMap=new HashMap<>();
        //退库数据
        List<StockDataDTO> stockDatas=dto.getStockDatas();

        for(StockDataDTO data : stockDatas){
            //选择的批次
            List<BatchDto> batchDtos = data.getBatchDtos();
//            if(null != batchDtos){
                //对选择的批次信息进行操作
                if(CollectionUtils.isNotEmpty(batchDtos)){
                    for(BatchDto batchDto : batchDtos){
                        Long batchChangeId = batchDto.getBatchId();
                        if(null != batchChangeId){
                            Double amount=batchDto.getNumber();//页面输入的退库数量
                            MaterialBatchChange materialBatchChange = materialBatchChangeRepository.findOne(batchChangeId);

                            MaterialBatch materialBatch = materialBatchChange.getMaterialBatch();
                            //批次数量改动
                            materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.BACK,amount);

                            materialBatches.add(materialBatch);

                            if(materialBatch.getInventory()!=null){
                                Inventory inventory = materialBatch.getInventory();
                                //库存数量改动
                                inventory=inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.BACK,amount);
                                inventorys.add(inventory);
                            }

                            //创建新的批次改动记录
                            MaterialBatchChange mChange = this.buildMaterialBatchChange(materialBatch,InventoryManagementActivity.InventoryActivityType.BACK,amount);
                            materialBatchChanges.add(mChange);

                            //修改原先改动记录的退库数量
                            Double backnum = materialBatchChange.getBackNum();
                            Double outNum = materialBatchChange.getChangeNum();

                            if (backnum + amount > outNum) {
                                String provider = materialBatch.getProvider().getName();
                                String inDate = "";
                                if (materialBatch.getCreatedDate() != null) {
                                    inDate = DateUtil.formatDateTimeNotContainSS(materialBatch.getCreatedDate());
                                }

                                String msg = XiaMesssageResource.getMessage("message.stock.error.back-num");
                                if (StringUtils.isNotEmpty(msg)) {
                                    msg = msg.replace("{provider}", provider).replace("{inDate}", inDate);
                                }
                                throw new BusinessException(msg);
                            }

                            materialBatchChange.setBackNum((backnum==null?0d:backnum)+amount);
                            materialOrigins.add(materialBatchChange);
                        }
                    }
                }
//            }else{
//                //批次改动
//                MaterialBatch materialBatchIn = materialBatchRepository.findMaterialBatchByIdFetchInventory(data.getMaterialBatchId());
//                //获得改动库存 退库可能会对同一个库存的不同批次同时进行退库操作
//                Inventory inventory =materialBatchIn.getInventory();
//                if(inventoryMap.containsKey(inventory.getId())){
//                    inventory=inventoryMap.get(inventory.getId());
//                }else{
//                    inventoryMap.put(inventory.getId(),inventory);
//                }
//
//                //库存数量改动
//                inventory=inventoryService.quantityChanges(inventory,InventoryManagementActivity.InventoryActivityType.BACK, data.getAmount());
//                inventorys.add(inventory);
//
//                //修改原先改动记录的退库数量
//                List<Long> materialBatchChangeIds = data.getMaterialBatchChangeIds();
//                if(materialBatchChangeIds.size()==1){
//                    MaterialBatchChange originChange=materialBatchChangeRepository.findOne(materialBatchChangeIds.get(0));
//                    Double backnum = originChange.getBackNum();
//                    originChange.setBackNum((backnum==null?0d:backnum)+data.getAmount());
//                    materialOrigins.add(originChange);
//
//                    //批次改动
//                    MaterialBatch materialBatch = originChange.getMaterialBatch();
//                    materialBatch=materialBatchService.quantityChanges(materialBatch,InventoryManagementActivity.InventoryActivityType.BACK,data.getAmount());
//                    materialBatches.add(materialBatch);
//
//                    //创建批次改动记录
//                    MaterialBatchChange materialBatchChange = this.buildMaterialBatchChange(materialBatch,InventoryManagementActivity.InventoryActivityType.BACK,data.getAmount());
//                    materialBatchChanges.add(materialBatchChange);
//                }else{
//                    List<MaterialBatchChange> changes = materialBatchChangeRepository.findBymaterialBatchChangeRepositoryIds(buildMaterialBatchChangeRepositoryId(materialBatchChangeIds));
//                    double backtotal = 0;
//                    Double tamount = data.getAmount();
//                    Double amount = tamount;
//                    for(MaterialBatchChange change : changes){
//                        Double backnum = change.getBackNum();
//                        Double changenum = change.getChangeNum();
//                        backnum = backnum==null?0d:backnum;
//                        //真实可以退订的数量
//                        Double realNum = changenum-backnum;
//                        Double realBackNum = 0d;
//                        if(realNum>amount){
//                            realBackNum = amount;
//                            backtotal += amount;
//                            change.setBackNum(backnum+amount);
//                        }else{
//                            if(backtotal>=tamount){
//                                break;
//                            }else{
//                                change.setBackNum(backnum+realNum);
//                                backtotal += realNum;
//                                amount = amount - realNum;
//                                realBackNum = realNum;
//                            }
//                        }
//                        materialOrigins.add(change);
//
//                         //批次改动
//                        MaterialBatch materialBatch = change.getMaterialBatch();
//                        materialBatch=materialBatchService.quantityChanges(materialBatch,InventoryManagementActivity.InventoryActivityType.BACK,realBackNum);
//                        materialBatches.add(materialBatch);
//
//                        //创建批次改动记录
//                        MaterialBatchChange materialBatchChange = this.buildMaterialBatchChange(materialBatch,InventoryManagementActivity.InventoryActivityType.BACK,realBackNum);
//                        materialBatchChanges.add(materialBatchChange);
//                    }
//                }
//            }
        }
        //存储数量改动记录
        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);

        //根据ID获得出库记录
        InventoryManagementActivity outActivity=inventoryManagementActivityRepository.findByIdLazyLoad(dto.getActivityId());
        //新建退库操作记录
        InventoryManagementActivity inventoryManagementActivity = this.createActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.BACK);
        inventoryManagementActivity.setMaterialBatchs(onlyLastMaterialBatchChange(materialBatches));
        inventoryManagementActivity.setConnectNo(outActivity.getNo());
        inventoryManagementActivity.setDescription(this.buildoutDescription("", outActivity.getNo()));
        inventoryManagementActivity=this.create(inventoryManagementActivity);

        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(inventoryManagementActivity);
        }
        materialBatchChangeRepository.saveInBatch(materialBatchChanges);
        materialBatchChangeRepository.saveInBatch(materialOrigins);
        return inventoryManagementActivity;
    }

    public Long[] buildMaterialBatchChangeRepositoryId(List<Long> ids){
        int len = ids.size();
        Long[] arr = new Long[len];
        for(int i=0;i<len;i++){
            arr[i]=ids.get(i);
        }
        return arr;
    }

    private List<MaterialBatch> onlyLastMaterialBatchChange(List<MaterialBatch> mbL){
        List<MaterialBatch> results = new ArrayList<MaterialBatch>();
        Map<Long,MaterialBatch> mbM = new HashMap<Long,MaterialBatch>();
        for(MaterialBatch mb:mbL){
            mbM.put(mb.getId(), mb);
        }
        Set<Long> keys = mbM.keySet();
        for(Long key:keys){
            results.add(mbM.get(key));
        }
        return results;
    }
    
    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity handleCancelStockReserve(Long id, InventoryManagementActivity.InventoryActivityType type) {

        //检查预订单状态
        InventoryManagementActivity outActivity = inventoryManagementActivityRepository.findByIdLazyLoad(id);
        if (null != outActivity) {
            if (type != null && type.equals(InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE)) {
                if (!outActivity.getType().equals(InventoryManagementActivity.InventoryActivityType.RESERVE)) {
                    throw new BusinessException(XiaMesssageResource.getMessage("message.stock.error.list-status"));
                }
            } else if (type != null && type.equals(InventoryManagementActivity.InventoryActivityType.CANCEL_OUT)) {
                if (!outActivity.getType().equals(InventoryManagementActivity.InventoryActivityType.AUDITSUC)) {
                    throw new BusinessException(XiaMesssageResource.getMessage("message.stock.error.list-status"));
                }
            }
        }

        //获得取消预定的记录
        ActivityCollectionDTO collectionDTO = this.buildStockCancelReserveDto(id, type);
        if(collectionDTO == null){
            return null;
        }
        //最终存储集合
//        List<MaterialBatch> materialBatchs = collectionDTO.getBatches();
        Set<Inventory> inventorys = collectionDTO.getInventorySet();
//        List<MaterialBatchChange> changeList = collectionDTO.getChangeList();
//        List<MaterialBatchChange> changes = collectionDTO.getChanges();
        InventoryManagementActivity activity = collectionDTO.getActivity();

        inventoryRepository.saveInBatch(inventorys);
//        materialBatchRepository.saveInBatch(materialBatchs);
        //新建取消预定的操作记录
//        InventoryManagementActivity cancelActivity = this.createCancelReserveActivity(activity, type);
//        activity.setConnectNo(cancelActivity.getNo());
//        activity.setWorkOrder(null);
//        activity.setPkeyId(null);
//        activity.setTableName(null);
        activity.setType(type);
//        cancelActivity.setMaterialBatchs(materialBatchs);
//        //取消预定的备注信息
//        cancelActivity.setDescription(this.buildDescription("",activity.getNo()));
        inventoryManagementActivityRepository.save(activity);
//        cancelActivity=inventoryManagementActivityRepository.save(cancelActivity);
//        materialBatchChangeRepository.saveInBatch(changes);
        
//        for(MaterialBatchChange change : changeList){
//            change.setInventoryManagementActivity(activity);
//        }
//        materialBatchChangeRepository.saveInBatch(changeList);
        return activity;

    }

    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity handleStockReserveOut(StockInDTO dto) {
        //执行操作前验证批次数量是否足够
        if (!checkMaterBatchNumberEnough(dto)) {
            return null;
        }
        //保存集合
        Set<MaterialBatch> materialBatches=new HashSet<>();
//        Set<Long> materialBatchIds=new HashSet<>();
        List<MaterialBatchChange> materialBatchChanges=new ArrayList<>();
        List<Inventory> inventorys=new ArrayList<>();
        //根据ID获得出库记录
        InventoryManagementActivity outActivity=inventoryManagementActivityRepository.findByIdLazyLoad(dto.getActivityId());
        //获得批次改动记录
        List<MaterialBatchChange> materialBatchChangeList =materialBatchChangeRepository.findByActivityId(dto.getActivityId());
//        Map<Long,MaterialBatchChange> changeMap = this.convertMbChangeListToMap(materialBatchChangeList);
//        for(MaterialBatchChange change : materialBatchChangeList){
//            if(change.getMaterialBatch()!=null){
//                materialBatches.add(change.getMaterialBatch());
//                materialBatchIds.add(change.getMaterialBatch().getId());
//            }
//        }
//        Map<Long,MaterialBatch> materialBatchMap=this.convertMaterialBatchListToMap(new ArrayList<MaterialBatch>(materialBatches));
//        inventorys = materialBatchRepository.findInventoryByIds(materialBatchIds.toArray(new Long[materialBatchIds.size()]));
//        Map<Long,Inventory> longInventoryMap = this.convertInventoryListToMap(inventorys);
        //先对批次进行解锁操作 再进行出库操作
        for(MaterialBatchChange change : materialBatchChangeList){
            Double amount = change.getChangeNum();
            MaterialBatch materialBatch = change.getMaterialBatch();
            if(materialBatch!=null){
//                materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,amount);
//                materialBatches.add(materialBatch);
                Inventory inventory = materialBatch.getInventory();
                if(inventory!=null){
                    inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,amount);
                }
                //设置批次锁定状态为失效
                change.setLockActivity(Boolean.FALSE);
            }
        }
        //删除预定预定的批次
//        materialBatchChangeRepository.deleteInBatch(materialBatchChangeList);

        //实际改动数据
        List<StockDataDTO> stockDatas=dto.getStockDatas();
        //根据每一行的数据进行出库操作
        for(StockDataDTO data : stockDatas){
            //选择的批次信息
            List<BatchDto> batchDtos = data.getBatchDtos();
//            if(CollectionUtils.isEmpty(batchDtos)){
//                Double amount=data.getAmount();//页面输入的实际出库数量
//                Long changId=data.getMaterialBatchChangeId();//改动批次的主键
//                MaterialBatchChange materialBatchChange = changeMap.get(changId);
//                MaterialBatch materialBatch = materialBatchMap.get(materialBatchChange.getMaterialBatch().getId());
//                //批次数量改动
//                materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,amount);
//
//                if(materialBatch.getInventory()!=null){
//                    Inventory inventory = longInventoryMap.get(materialBatch.getInventory().getId());
//                    //库存数量改动
//                    inventory=inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,amount);
//                }
//
//                //新建批次变化记录
//                MaterialBatchChange mChange = new MaterialBatchChange();
//                mChange.setType(MaterialBatchChange.AdjustType.DOWN);
//                mChange.setChangeNum(amount);
//                mChange.setMaterialBatch(materialBatch);
//                materialBatchChanges.add(mChange);
//            }else{
                //对选择的批次信息进行操作
                if(CollectionUtils.isNotEmpty(batchDtos)){
                    for(BatchDto batchDto : batchDtos){
                        Long batchId = batchDto.getBatchId();
                        if(null != batchId){
                            Double amount=batchDto.getNumber();//页面输入的实际出库数量
                            MaterialBatch materialBatch = materialBatchRepository.findOne(batchId);
                            //批次数量改动
                            materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,amount);

                            materialBatches.add(materialBatch);

                            if(materialBatch.getInventory()!=null){
                                Inventory inventory = materialBatch.getInventory();
                                //库存数量改动
                                inventory=inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,amount);
                                inventorys.add(inventory);
                            }

                            //新建批次变化记录
                            MaterialBatchChange mChange = new MaterialBatchChange();
                            mChange.setType(MaterialBatchChange.AdjustType.DOWN);
                            mChange.setChangeNum(amount);
                            mChange.setMaterialBatch(materialBatch);
                            materialBatchChanges.add(mChange);
                        }
                    }
                }
//            }
        }
        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);

        //创建预定出库操作的记录 同时关联对应的预定记录NO
        InventoryManagementActivity inventoryManagementActivity = this.createActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT);
        inventoryManagementActivity.setConnectNo(outActivity.getNo());
        //备注中添加关联code
        String desc = inventoryManagementActivity.getDescription();
        if(null == desc){
            inventoryManagementActivity.setDescription(this.buildDescription("",outActivity.getNo()));
        }else{
            inventoryManagementActivity.setDescription(this.buildDescription(inventoryManagementActivity.getDescription(),outActivity.getNo()));
        }
        outActivity.setConnectNo(inventoryManagementActivity.getNo());
        //备注中添加关联code
        String outdesc = outActivity.getDescription();
        if(null == outdesc){
            outActivity.setDescription(this.buildoutDescription("",inventoryManagementActivity.getNo()));
        }else{
            outActivity.setDescription(this.buildoutDescription(outActivity.getDescription(),inventoryManagementActivity.getNo()));
        }
        inventoryManagementActivity.setMaterialBatchs(new ArrayList<MaterialBatch>(materialBatches));
        inventoryManagementActivity=this.create(inventoryManagementActivity);

        //预定记录
        InventoryActivity inventoryActivity  = new InventoryActivity();
        inventoryActivity.setReserveNo(outActivity.getNo());
        inventoryActivity.setActivityType(InventoryActivity.InventoryActivityType.RESERVE);
        inventoryActivity.setStatus(InventoryActivity.InventoryActivityStatus.HASLIB);
        inventoryActivity.setComment("");
        inventoManagementActivity.save(inventoryActivity);
        this.create(outActivity);
        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(inventoryManagementActivity);
        }

        materialBatchChangeRepository.saveInBatch(materialBatchChangeList);
        materialBatchChangeRepository.saveInBatch(materialBatchChanges);
        this.sendmessage(inventoryManagementActivity,dto,false);
        return inventoryManagementActivity;
    }

    public Map<Long, List<MaterialBatchChange>> convertMbChangeListToMapActivity(List<MaterialBatchChange> materialBatchChangesActivity){
        Map<Long, List<MaterialBatchChange>> changeMap = new HashMap<Long ,List<MaterialBatchChange>>();
        for(MaterialBatchChange change : materialBatchChangesActivity){
             Inventory inventory = change.getMaterialBatch().getInventory();
             Long id = inventory.getId();
             if(changeMap.containsKey(id)){
                 List<MaterialBatchChange> changes = changeMap.get(id);
                 changes.add(change);
                 changeMap.put(id, changes);
             }else{
                 List<MaterialBatchChange> changes = new ArrayList<MaterialBatchChange>();
                 changes.add(change);
                 changeMap.put(id, changes);
             }
        }
        return changeMap;
    }

    @XiaTransactional(readOnly = false)
    public void handleStockReserveOut2(StockInDTO dto) {
        //保存集合
        Set<MaterialBatch> materialBatches=new HashSet<>();
        Set<Long> materialBatchIds=new HashSet<>();
        List<MaterialBatchChange> materialBatchChanges=new ArrayList<>();
        List<Inventory> inventorys=new ArrayList<>();
        //根据ID获得出库记录
        InventoryManagementActivity outActivity=inventoryManagementActivityRepository.findByIdLazyLoad(dto.getActivityId());
        //获得批次改动记录
        List<MaterialBatchChange> materialBatchChangeList =materialBatchChangeRepository.findByActivityId(dto.getActivityId());
        Map<Long,MaterialBatchChange> changeMap = this.convertMbChangeListToMap(materialBatchChangeList);
        for(MaterialBatchChange change : materialBatchChangeList){
            if(change.getMaterialBatch()!=null){
                materialBatches.add(change.getMaterialBatch());
                materialBatchIds.add(change.getMaterialBatch().getId());
            }
        }
        Map<Long,MaterialBatch> materialBatchMap=this.convertMaterialBatchListToMap(new ArrayList<MaterialBatch>(materialBatches));
        inventorys = materialBatchRepository.findInventoryByIds(materialBatchIds.toArray(new Long[materialBatchIds.size()]));
        Map<Long,Inventory> longInventoryMap = this.convertInventoryListToMap(inventorys);
        //先对批次进行解锁操作 再进行出库操作
        for(MaterialBatchChange change : materialBatchChangeList){
            Double amount = change.getChangeNum();
            if(change.getMaterialBatch()!=null){
                MaterialBatch materialBatch = change.getMaterialBatch();
                materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,amount);
                if(materialBatch.getInventory()!=null){
                    Inventory inventory = longInventoryMap.get(materialBatch.getInventory().getId());
                    inventory=inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,amount);
                }
                //设置批次锁定状态为失效
                change.setLockActivity(Boolean.FALSE);
            }
        }
        //实际改动数据
        List<StockDataDTO> stockDatas=dto.getStockDatas();
        //根据每一行的数据进行出库操作
        for(StockDataDTO data : stockDatas){
            Double amount=data.getAmount();//页面输入的实际出库数量
            Long changId=data.getMaterialBatchChangeId();//改动批次的主键
            MaterialBatchChange materialBatchChange = changeMap.get(changId);
            MaterialBatch materialBatch = materialBatchMap.get(materialBatchChange.getMaterialBatch().getId());
            //批次数量改动
            materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,amount);

            if(materialBatch.getInventory()!=null){
                Inventory inventory = longInventoryMap.get(materialBatch.getInventory().getId());
                //库存数量改动
                inventory=inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,amount);
            }

            //新建批次变化记录
            MaterialBatchChange mChange = new MaterialBatchChange();
            mChange.setType(MaterialBatchChange.AdjustType.DOWN);
            mChange.setChangeNum(amount);
            mChange.setMaterialBatch(materialBatch);
            materialBatchChanges.add(mChange);

        }
        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);

        //创建预定出库操作的记录 同时关联对应的预定记录NO
        InventoryManagementActivity inventoryManagementActivity = this.createActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT);
        inventoryManagementActivity.setConnectNo(outActivity.getNo());
        //备注中添加关联code
        inventoryManagementActivity.setDescription(this.buildDescription(inventoryManagementActivity.getDescription(), outActivity.getNo()));
        outActivity.setConnectNo(inventoryManagementActivity.getNo());
        //备注中添加关联code
        outActivity.setDescription(this.buildDescription(outActivity.getDescription(),inventoryManagementActivity.getNo()));
        inventoryManagementActivity.setMaterialBatchs(new ArrayList<MaterialBatch>(materialBatches));
        inventoryManagementActivity=this.create(inventoryManagementActivity);

        this.create(outActivity);
        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(inventoryManagementActivity);
        }

        materialBatchChangeRepository.saveInBatch(materialBatchChangeList);
        materialBatchChangeRepository.saveInBatch(materialBatchChanges);

    }

    private String buildDescription(String desc,String code){

        StringBuffer stringBuffer = new StringBuffer(desc);
        stringBuffer.append(" ").append(XiaMesssageResource.getMessage("page.stock003.stockBack.connectReserveNo")).append(" : ").append(code).append("。");
        return stringBuffer.toString();
    }

    private String buildoutDescription(String desc,String code){

        StringBuffer stringBuffer = new StringBuffer(desc);
        stringBuffer.append(" ").append(XiaMesssageResource.getMessage("page.stock003.stockBack.connectoutNo")).append(" : ").append(code).append("。");
        return stringBuffer.toString();
    }

    @Override
    @XiaTransactional(readOnly =false)
    public void handleStockAdjust(StockAdjustDTO dto) {
        if(dto.getDataList()==null || dto.getDataList().size()==0){
            return;
        }
        //批次变化集合
        List<MaterialBatchChange> changeList = new ArrayList<>();
        //根据ID获得需要改动的库存
        Inventory inventory = inventoryRepository.findWithMaterialAndWarehouseById(dto.getInventoryId());
        //创建调整操作记录
        InventoryManagementActivity.InventoryActivityType activityType=InventoryManagementActivity.InventoryActivityType.ADJUST;
        //库存数量变化=有效量+锁定量-实际数量
        Double readTotalNum=inventory.getAmount()+inventory.getLockAmount();
        Double totalChange=readTotalNum-dto.getTotalActualAmount();
        //实际数量>账面数量则记为入库，反之则记为出库
        InventoryManagementActivity.InventoryActivityType inventoryType = this.getActivityTypeByOriginAmountAndTargetAmount(readTotalNum,dto.getTotalActualAmount());

//        InventoryManagementActivity.InventoryActivityType inventoryType=InventoryManagementActivity.InventoryActivityType.IN;
//        if(readTotalNum>dto.getTotalActualAmount()){
//            inventoryType=InventoryManagementActivity.InventoryActivityType.OUT;
//        }
        //库存变化--需要考虑实际数量>?有效数量 若> 同时需要减少锁定量
        if(inventoryType==InventoryManagementActivity.InventoryActivityType.IN){
            inventory = inventoryService.quantityChanges(inventory, inventoryType, Math.abs(totalChange));
        }else{
            //出库情况:需要考虑出库数量是否>有效数量
            if(Math.abs(totalChange)>inventory.getAmount()){
                //改动量>有效量，需要先减去所有有效量，再减去锁定量
                Double reduceAmount=Math.abs(totalChange)-inventory.getAmount();
                inventory = inventoryService.quantityChanges(inventory, inventoryType, inventory.getAmount());
                inventory.setLockAmount(inventory.getLockAmount()-reduceAmount);
            }else{
                inventory = inventoryService.quantityChanges(inventory, inventoryType, Math.abs(totalChange));
            }
        }
        inventoryRepository.save(inventory);

        List<StockAdjustDataDTO> dataList = dto.getDataList();
        List<Long> materialBatchIds = new ArrayList<>();
        for(StockAdjustDataDTO data : dataList){
            materialBatchIds.add(data.getMaterilBatchId());
        }
        //变化的批次
        List<MaterialBatch> materialBatchList = materialBatchRepository.findByIds(materialBatchIds.toArray(new Long[materialBatchIds.size()]));
        Map<Long,MaterialBatch> longBatchMap = this.convertMaterialBatchListToMap(materialBatchList);
        for(StockAdjustDataDTO data : dataList){
            MaterialBatch materialBatch = longBatchMap.get(data.getMaterilBatchId());
            //批次账面数量=有效数量+锁定数量
            Double batchTotalRealNum=materialBatch.getRealNum()+materialBatch.getLockNum();
            //批次变动量=实际数量-批次账面数量 -1 = 0 - 1
            Double changeNum=data.getActualNum()-batchTotalRealNum;
            //实际数量<账面数量则记为入库，反之则记为出库 默认为入库
            InventoryManagementActivity.InventoryActivityType materialBatchType = this.getActivityTypeByOriginAmountAndTargetAmount(batchTotalRealNum,data.getActualNum());
//            if(changeNum<0){
//                materialBatchType= InventoryManagementActivity.InventoryActivityType.OUT;
//            }
            //数量改动参照批次修改--根据操作类型判断
            if(materialBatchType==InventoryManagementActivity.InventoryActivityType.IN){
                materialBatch=materialBatchService.quantityChanges(materialBatch,materialBatchType,Math.abs(changeNum));
            }else{
                if(Math.abs(changeNum)>materialBatch.getRealNum()){
                    Double reduceAmount=Math.abs(changeNum)-materialBatch.getRealNum();
                    materialBatch=materialBatchService.quantityChanges(materialBatch,materialBatchType,materialBatch.getRealNum());
                    materialBatch.setLockNum(materialBatch.getLockNum()-reduceAmount);
                }else{
                    materialBatch=materialBatchService.quantityChanges(materialBatch,materialBatchType,Math.abs(changeNum));
                }
            }
            //新建批次变动记录
           MaterialBatchChange change = this.buildMaterialBatchChange(materialBatch, materialBatchType, Math.abs(changeNum));
           changeList.add(change);
        }

        materialBatchRepository.saveInBatch(materialBatchList);
        //新建调整操作记录
        InventoryManagementActivity activity = this.createForAdjust(inventory,totalChange);
        activity.setMaterialBatchs(materialBatchList);
        activity=inventoryManagementActivityRepository.save(activity);

        for(MaterialBatchChange change : changeList){
            change.setInventoryManagementActivity(activity);
        }

       materialBatchChangeRepository.saveInBatch(changeList);

    }

    private InventoryManagementActivity.InventoryActivityType getActivityTypeByOriginAmountAndTargetAmount(Double originAmount,Double targetAmount){
        InventoryManagementActivity.InventoryActivityType type = InventoryManagementActivity.InventoryActivityType.IN;
        if(originAmount > targetAmount){
            type = InventoryManagementActivity.InventoryActivityType.OUT;
        }
        return type;
    }

    @Override
    public InventoryManagementActivity createActivityByDtoAndType(StockInDTO dto,InventoryManagementActivity.InventoryActivityType type){
        InventoryManagementActivity inventoryManagementActivity = new InventoryManagementActivity();
        if(dto.getAmount()==0){
            dto.setAmount(this.getTotalAmountFromDto(dto));
        }
        BeanUtils.copyProperties(dto, inventoryManagementActivity);
//        if(!StringUtils.isBlank(inventoryManagementActivity.getDescription())){
//            inventoryManagementActivity.setDescription(XiaMesssageResource.getMessage("page.pm002.connectWoCode")+":"+inventoryManagementActivity.getDescription());
//        }
        inventoryManagementActivity.setWarehouse(warehouseRepository.findOne(dto.getWarehouse().getId()));
        //针对自动生成工单的BUG
//        if(dto.getLaborer()!=null){
        Employee loginEm = employeeService.findLoginEmployee();
        inventoryManagementActivity.setLaborer(loginEm);
//            if(type==InventoryManagementActivity.InventoryActivityType.IN){
//                inventoryManagementActivity.setLaborer(employeeRepository.findOneByUserId(dto.getLaborer().getId()));
//            }else{
//                inventoryManagementActivity.setLaborer(employeeRepository.findOne(dto.getLaborer().getId()));
//            }
//        }
        //仓库管理员
        if(null != dto.getManagers()){
            inventoryManagementActivity.setManagers(employeeRepository.findOne(dto.getManagers().getId()));
        }else{
            inventoryManagementActivity.setManagers(loginEm);
        }

        //经手人/领用人/退库人/移库人
        if(null != dto.getHandles()){
            Employee handles = employeeRepository.findOne(dto.getHandles().getId());
            inventoryManagementActivity.setHandles(handles);
        }

        //主管
        if(null != dto.getSupervisorId()){
            inventoryManagementActivity.setSupervisor(employeeRepository.findOne(dto.getSupervisorId()));
        }

        //部门
        if(null != dto.getOrganizationId()){
            inventoryManagementActivity.setOrganization(organizationRepository.findOne(dto.getOrganizationId()));
        }

        inventoryManagementActivity.setNo(stockCode.getStockCodeWithProjectId(type, ProjectContext.getCurrentProject()));
        inventoryManagementActivity.setType(type);

        return inventoryManagementActivity;
    }

    /**
     * 物资预定  不改变批次的锁定数量，只改变批次change
     *  panda.yang
     */
    @Override
    public ActivityCollectionDTO buildStockReserveDto(StockInDTO dto, String type){
        //存储集合返回DTO对象
        ActivityCollectionDTO result = new ActivityCollectionDTO();
        //最终存储集合--注入到返回对象中
        Set<MaterialBatch> materialBatches=new HashSet<>();
        List<MaterialBatchChange> materialBatchChanges=new ArrayList<>();
        List<Inventory> inventorys=new ArrayList<>();
        result.setBatchSets(materialBatches);
        result.setChanges(materialBatchChanges);
        result.setInventorys(inventorys);

        Map<Long,Inventory> inventoryMap = new HashMap<>();
        //批次MAP
        Map<Long,List<MaterialBatch>> longBatchMap =new HashMap<>();
        Map<Long,Map<Date,List<MaterialBatch>>> longMapHashMap =new HashMap<>();
        Map<Date,List<MaterialBatch>> dateListMap =new HashMap<>();

        List<StockDataDTO> stockDatas=dto.getStockDatas();
        if(stockDatas==null || stockDatas.size()==0){
            return null;
        }
        for(StockDataDTO stockData : stockDatas){
            Inventory in = inventoryRepository.findOne(stockData.getInventory().getId());
            //库存数量改动
            Double amount=stockData.getAmount();
            if (in != null) {
                if(in.getAmount() < amount){
                    throw new BusinessException(XiaMesssageResource.getMessage("m.message.can.not.operation.materialBatch"));
                }
            }
            //根据库存ID获得对应的库存 可能会对同一个库存的多个批次同时进行改动 需要对inventorys集合进行去重处理
            Inventory inventory =null;
            if(inventoryMap.containsKey(stockData.getInventory().getId())){
                inventory=inventoryMap.get(stockData.getInventory().getId());
            }else{
                inventory=inventoryRepository.findOne(stockData.getInventory().getId());
                if(null != inventory){
                    inventoryMap.put(inventory.getId(),inventory);
                    inventorys.add(inventory);
                }
            }

            //PM生成的预订单
            if(InventoryManagementActivity.INVENTORY_PM.equals(type)){
                if(null != inventory){
                    //PM工单生成的预订单，库存不足时也可以生成
                    inventory=inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.RESERVE, amount);

                    /**
                     * 只保留一条预定 批次change,不改变批次的锁定
                     * panda
                     */
                    List<MaterialBatch>  materialBatchList = materialBatchRepository.findWithProviderByInventoryId(inventory.getId());
                    if(CollectionUtils.isNotEmpty(materialBatchList)){
                        MaterialBatch materialBatch = materialBatchList.get(0);
                        materialBatches.add(materialBatch);
                        //批次改动记录
                        MaterialBatchChange materialBatchChange = new MaterialBatchChange();
                        materialBatchChange.setChangeNum(amount);
                        materialBatchChange.setMaterialBatch(materialBatch);
                        //锁定
                        materialBatchChange.setType(MaterialBatchChange.AdjustType.LOCK);
                        materialBatchChanges.add(materialBatchChange);
                    }
                }
            }else{
                //先判断库存是否存在有效数量 若已经没有有效数量可用，则不用进行计算
                if(inventory.getAmount() > 0){
                    //库存数量改动总量改动量由库存实际量决定
                    Double changeAmount = inventory.getAmount()>amount?amount:inventory.getAmount();
                    inventory=inventoryService.quantityChanges(inventory,InventoryManagementActivity.InventoryActivityType.RESERVE,changeAmount);
                    /**
                     * 只保留一条预定 批次change,不改变批次的锁定
                     * panda
                     */
                    List<MaterialBatch>  materialBatchList = materialBatchRepository.findWithProviderByInventoryId(inventory.getId());
                    if(CollectionUtils.isNotEmpty(materialBatchList)){
                        MaterialBatch materialBatch = materialBatchList.get(0);
                        materialBatches.add(materialBatch);
                        //批次改动记录
                        MaterialBatchChange materialBatchChange = new MaterialBatchChange();
                        materialBatchChange.setChangeNum(amount);
                        materialBatchChange.setMaterialBatch(materialBatch);
                        //锁定
                        materialBatchChange.setType(MaterialBatchChange.AdjustType.LOCK);
                        materialBatchChanges.add(materialBatchChange);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public ActivityCollectionDTO buildStockCancelReserveDto(Long id, InventoryManagementActivity.InventoryActivityType type) {

        ActivityCollectionDTO result = new ActivityCollectionDTO();
        //存储集合 注入到返回对象中
        List<MaterialBatch> materialBatchs = new ArrayList<>();
//        List<Inventory> inventorys = new ArrayList<>();
        Set<Inventory> inventorySet = new HashSet<>();
//        List<MaterialBatchChange> changeList = new LinkedList<>();
//        result.setChangeList(changeList);
//        result.setInventorys(inventorys);
        result.setBatches(materialBatchs);
        result.setInventorySet(inventorySet);

        //根据ID获得预定记录
        InventoryManagementActivity activity = inventoryManagementActivityRepository.findByIdLazyLoad(id);
        if(activity==null){
            return null;
        }
        result.setActivity(activity);
        //获得预定记录关联的批次改动记录
        List<MaterialBatchChange> changes = materialBatchChangeRepository.findByActivityId(activity.getId());
//        result.setChanges(changes);
        //蝴蝶预定记录关联的库存记录
//        List<Inventory> inventoryList = inventoryRepository.findByActivityId(activity.getId());
//        Map<Long,Inventory> inventoryMap = this.convertInventoryListToMap(inventoryList);
        //批次集合
//        Map<Long,MaterialBatch> longBatchMap = new HashMap<>();
        MaterialBatch materialBatch =null;
        //对批次改动记录进行操作
        for(MaterialBatchChange materialBatchChange : changes){
            //设置批次改动的锁定状态为FALSE
            materialBatchChange.setLockActivity(Boolean.FALSE);
            //复制一份batchChange给取消预定的记录
//            MaterialBatchChange copyChange = this.copyBatchChange(materialBatchChange);
//            changeList.add(copyChange);
            //批次数量改动
//            if(longBatchMap.get(materialBatchChange.getMaterialBatch().getId())!=null){
//                materialBatch=longBatchMap.get(materialBatchChange.getMaterialBatch().getId());
//            }else{
//                materialBatch=materialBatchChange.getMaterialBatch();
//                longBatchMap.put(materialBatch.getId(),materialBatch);
//                materialBatchs.add(materialBatch);
//            }
//            materialBatch=materialBatchService.quantityChanges(materialBatch,InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,materialBatchChange.getChangeNum());
            //对应库存的数量改动
            materialBatch=materialBatchChange.getMaterialBatch();
            Inventory inventory = materialBatch.getInventory();
            if(inventory != null){
                inventory = inventoryService.quantityChanges(inventory, type, materialBatchChange.getChangeNum());
//                    inventorys.add(inventory);
                inventorySet.add(inventory);
            }
        }
        return result;
    }

    @Override
    public Page<InventoryManagementActivity> findReservationListInPage(final MInventoryListRequest request) {
        Pageable page = request.getPage() == null ? new PageRequest(0, 10) : new PageRequest(request.getPage().getPageNumber(), request.getPage().getPageSize());

        Page<InventoryManagementActivity> activityPage = inventoryManagementActivityRepository.findAll(new XiaSpecification<InventoryManagementActivity>() {
            @Override
            public Root<InventoryManagementActivity> toRoot(Root<InventoryManagementActivity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                root.fetch(InventoryManagementActivity_.WORK_ORDER, JoinType.LEFT);
                root.fetch(InventoryManagementActivity_.WAREHOUSE, JoinType.LEFT);
                root.fetch(InventoryManagementActivity_.LABORER, JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<InventoryManagementActivity> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicates = new ArrayList<Predicate>();

                switch (request.getQueryType()) {
                    case MInventoryListRequest.TYPE_HISTORY_OF_VALIDATION:
                        Date start = null;
                        Date end = null;
                        if(null != request.getTimeStart()){
                            start = new Date(request.getTimeStart());
                        }
                        if(null != request.getTimeEnd()){
                            end = new Date(request.getTimeEnd());
                        }
                        predicates.add(buildPredicateForValidationHistory(request.getUserId(), cb, root, start, end));
                        break;
                    case MInventoryListRequest.TYPE_RESERVED:
                        Date start1 = null;
                        Date end1 = null;
                        if(null != request.getTimeStart()){
                            start1 = new Date(request.getTimeStart());
                        }
                        if(null != request.getTimeEnd()){
                            end1 = new Date(request.getTimeEnd());
                        }
                        predicates.add(buildPredicateForReservedList(request.getUserId(), cb, root,start1, end1));
                        break;
                    case MInventoryListRequest.TYPE_WAIT_VALIDATING:
                        predicates.add(buildPredicateForWaitValidationReservation(request.getUserId(), cb, root));
                        break;
                    default:
                        break;
                }

                query.where(predicates.toArray(new Predicate[predicates.size()]));
                query.distinct(true);
                query.orderBy(cb.desc(root.get(InventoryManagementActivity_.CREATED_DATE)));
                return query.getRestriction();
            }

        }, page);

        return activityPage;
    }

    private Predicate buildPredicateForWaitValidationReservation(Long userId, CriteriaBuilder cb, Root<InventoryManagementActivity> root) {
        Employee employee = employeeRepository.findOne(userId);
        List<Warehouse> warehouses = warehouseRepository.findByAdminId(employee.getId());
        Predicate warehousePredicate;
        if (CollectionUtils.isNotEmpty(warehouses)) {
            warehousePredicate = root.get(InventoryManagementActivity_.WAREHOUSE).in(warehouses);
        } else {
            warehousePredicate = cb.isNull(root.get(InventoryManagementActivity_.WAREHOUSE));
        }
        Predicate workOrderIsNull = cb.isNull(root.get(InventoryManagementActivity_.WORK_ORDER));
        Predicate status = cb.isNull(root.get(InventoryManagementActivity_.CONNECT_NO));
      //  Predicate time = cb.between(root.<Date>get(InventoryManagementActivity_.CREATED_DATE), start, end);
        Predicate type = cb.equal(root.get(InventoryManagementActivity_.TYPE), InventoryManagementActivity.InventoryActivityType.RESERVE);

        return cb.and(warehousePredicate, status, type, workOrderIsNull);
    }

    /**
     * find inventory activity record for user validation history
     * <p/>
     * 1. user should be admin of warehouse
     * <p/>
     * 2. user should be verifier of activity
     *
     * @param userId
     * @param cb
     * @param root
     * @return
     */
    private Predicate buildPredicateForValidationHistory(Long userId, CriteriaBuilder cb, Root<InventoryManagementActivity> root,Date start,Date end) {
        Assert.notNull(userId);
        List<Warehouse> warehouses = warehouseRepository.findByAdminId(userId);
        Predicate warehousePredicate;
        if (CollectionUtils.isNotEmpty(warehouses)) {
            warehousePredicate = root.get(InventoryManagementActivity_.WAREHOUSE).in(warehouses);
        } else {
            warehousePredicate = cb.isNull(root.get(InventoryManagementActivity_.WAREHOUSE));
        }
//        Predicate verifier = cb.equal(root.get(InventoryManagementActivity_.VERIFIER_USER_ID), userId);
        Predicate status = cb.isNotNull(root.get(InventoryManagementActivity_.CONNECT_NO));
        Predicate time = cb.between(root.<Date>get(InventoryManagementActivity_.CREATED_DATE), start, end);
        Predicate type = cb.equal(root.get(InventoryManagementActivity_.TYPE), InventoryManagementActivity.InventoryActivityType.RESERVE);

        return cb.and(warehousePredicate, time,status,type);
    }

    private Predicate buildPredicateForReservedList(Long userId, CriteriaBuilder cb, Root<InventoryManagementActivity> root,Date start,Date end) {
        Assert.notNull(userId);
        Employee employee = employeeRepository.findOne(userId);
        Predicate operator = cb.equal(root.get(InventoryManagementActivity_.LABORER), employee);
        Predicate time = cb.between(root.<Date>get(InventoryManagementActivity_.CREATED_DATE), start, end);
        Predicate type = cb.equal(root.get(InventoryManagementActivity_.TYPE), InventoryManagementActivity.InventoryActivityType.RESERVE);
        return cb.and(operator,time,type);
    }


    private MaterialBatchChange getChangByInventoryAndDate(List<MaterialBatchChange> changeList,Long inventoryId,Date dueDate){

        for(MaterialBatchChange mbc : changeList)
            if(mbc.getMaterialBatch()!=null){
                MaterialBatch materialBatch = mbc.getMaterialBatch();
                if(materialBatch.getInventory()!=null){
                    if(dueDate==null){
                        if(materialBatch.getInventory().getId().equals(inventoryId)){
                            return mbc;
                        }
                    }else{
                        if(materialBatch.getInventory().getId().equals(inventoryId) && materialBatch.getDueDate().getTime()==dueDate.getTime()){
                            return mbc;
                        }
                    }
                }
        }
        return null;
    }

    private InventoryManagementActivity createForAdjust(Inventory inventory,Double totalChange){

        InventoryManagementActivity.InventoryActivityType activityType=InventoryManagementActivity.InventoryActivityType.ADJUST;
        Long operateUserId =
                ((XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal())
                        .getId();
        InventoryManagementActivity activity = new InventoryManagementActivity();
        activity.setWarehouse(inventory.getWarehouse());
        activity.setOperateDate(new Date());
        activity.setLaborer(employeeRepository.findOneByUserId(operateUserId));
        activity.setAmount(Math.abs(totalChange));
        activity.setNo(stockCode.getStockCodeWithProjectId(activityType, ProjectContext.getCurrentProject()));
        activity.setType(activityType);

        return activity;
    }

    private Map<Long,MaterialBatch> convertMaterialBatchListToMap(List<MaterialBatch> batchList){
        Map<Long,MaterialBatch> changeMap = new HashMap<>();
        for(MaterialBatch batch : batchList){
            changeMap.put(batch.getId(),batch);
        }
        return changeMap;
    }

    private Map<Long,MaterialBatchChange> convertMbChangeListToMap(List<MaterialBatchChange> changeList){
        Map<Long,MaterialBatchChange> changeMap = new HashMap<>();
        for(MaterialBatchChange change : changeList){
            changeMap.put(change.getId(),change);
        }
        return changeMap;
    }

    private Map<Long,Inventory> convertInventoryListToMap(List<Inventory> inventorys){
        Map<Long,Inventory> inventoryMap = new HashMap<>();
        for(Inventory inventory : inventorys){
            inventoryMap.put(inventory.getId(),inventory);
        }

        return inventoryMap;
    }

    private InventoryManagementActivity createCancelReserveActivity(InventoryManagementActivity reserveActivity, InventoryManagementActivity.InventoryActivityType type){
        InventoryManagementActivity activity = new InventoryManagementActivity();
        activity.setType(type);
        //activity.setNo(stockCode.getStockCode(InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE));
        activity.setNo(stockCode.getStockCodeWithProjectId(type,ProjectContext.getCurrentProject()));
        activity.setProject(ProjectContext.getCurrentProject());
        activity.setAmount(reserveActivity.getAmount());
        activity.setConnectNo(reserveActivity.getNo());
        activity.setDescription(reserveActivity.getDescription());
        activity.setOperateDate(reserveActivity.getOperateDate());
        activity.setWarehouse(reserveActivity.getWarehouse());
        activity.setTableName(reserveActivity.getTableName());
        activity.setPkeyId(reserveActivity.getPkeyId());
//        activity.setWorkOrder(reserveActivity.getWorkOrder());
        activity.setLaborer(reserveActivity.getLaborer());
        return activity;

    }

    private Double getTotalAmountFromDto(StockInDTO dto){
        Double amount = 0d;
        List<StockDataDTO> datas = dto.getStockDatas();
        for(StockDataDTO data : datas){
            amount+=data.getAmount();
        }
        return amount;
    }

    private MaterialBatchChange buildMaterialBatchChange(MaterialBatch materialBatch,InventoryManagementActivity.InventoryActivityType type,Double changeNum){
        MaterialBatchChange materialBatchChange = new MaterialBatchChange();
        materialBatchChange.setMaterialBatch(materialBatch);
        materialBatchChange.setChangeNum(changeNum);
        materialBatchChange.setType(materialBatchChangeService.findAdjustTypeByActivityType(type));

        return materialBatchChange;
    }

    private MaterialBatch getMaterialBatchFromDto(Long warehouseId,StockDataDTO dto,Map<String, Provider> pmap,Map<String, Inventory> inventoryMap){

        MaterialBatch materialBatch = new MaterialBatch();

        BeanUtils.copyProperties(dto, materialBatch);

        materialBatch.setAmount(dto.getAmount());

        materialBatch.setRealNum(dto.getAmount());

        materialBatch.setPrice(dto.getPrice());

        Inventory inventory = inventoryMap.get(dto.getMaterialCode());

        materialBatch.setInventory(inventory);

        Provider provider= pmap.get(dto.getProvider().getName());
        if(null==provider){
            provider=new Provider();
            provider.setName(dto.getProvider().getName());
            provider=providerRepository.save(provider);
            pmap.put(provider.getName(),provider);
        }
        materialBatch.setProvider(provider);

        return materialBatchService.create(materialBatch);
    }

    private MaterialBatchChange copyBatchChange(MaterialBatchChange change){

        MaterialBatchChange batchChange = new MaterialBatchChange();
        batchChange.setLockActivity(change.isLockActivity());
        batchChange.setMaterialBatch(change.getMaterialBatch());
        batchChange.setChangeNum(change.getChangeNum());
        batchChange.setType(change.getType());

        return batchChange;
    }

    @Override
    public void sendmessage(InventoryManagementActivity inventoryManagementActivity, StockInDTO dto,Boolean isMoblie) {
        List<StockDataDTO> StockData = dto.getStockDatas();
        //物料名字(如有多个取第一个)
        String materialName = StockData.get(0).getMaterialName();
        //数量
        Long AllmaterialCount = 0L;
        if(isMoblie){
            int  size = dto.getStockDatas().size();
            AllmaterialCount = new Double(dto.getStockDatas().get(size-1).getAmount()).longValue();
        }else{
            for(int i= 0 ; i<StockData.size();i++){
                AllmaterialCount+=new Double(StockData.get(i).getAmount()).longValue();
            }
        }

        InventoryManagementActivity inventoryManagementActivitys = inventoryManagementActivityRepository.findOne(inventoryManagementActivity.getId());

        //预订人
        List<InventoryManagementActivity> inventoryManagementActivity1 = inventoryManagementActivityRepository.findReserverByConnectNo(inventoryManagementActivitys.getConnectNo());
        Employee handles = inventoryManagementActivity1.get(0).getHandles();
        List<Employee> person = new ArrayList<>();
        person.add(handles);

        // 发送消息
        Map<String,Map<String,Object>> typeData = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        params.put(MobilePush_.TYPE, MobilePush_.STOCK);
        params.put(MobilePush_.PROJECT_ID, ProjectContext.getCurrentProject());
        params.put(MobilePush_.RESERVATION_ID, inventoryManagementActivitys.getId());
        typeData.put(MessageType.MPUSH, params);

        String types = MessageType.buildTypes(MessageType.EMAIL,MessageType.MPUSH,MessageType.SITE);
        //校验是否设置消息模板
        String newType = checkTypes(types,MessageStockReservedeliveredlTemplate.CODE_STOCK_MATERIALS_DELIVERED,inventoryManagementActivity.getProject());
        if(StringUtils.isNotEmpty(newType)){
            messageSenderTool.send(MessageStockReservedeliveredlTemplate.CODE_STOCK_MATERIALS_DELIVERED,
                    MessageStockReservedeliveredlTemplate.buildData(materialName, AllmaterialCount),
                    typeData, person, inventoryManagementActivity.getProject(), newType);
        }

    }

    @Override
    public Boolean findIsoutbound(Long activityId) {
        boolean flag = false;
        InventoryManagementActivity outActivity=inventoryManagementActivityRepository.findByIdLazyLoad(activityId);
        if (null!=outActivity) {
            String connectNo = "";
            connectNo =outActivity.getConnectNo();
            List<InventoryManagementActivity> outActivity_ = new ArrayList<>();
            if(null!=connectNo){
                outActivity_ = inventoryManagementActivityRepository.findReserverByConnectNo(connectNo);
                if(outActivity_.size()>0){
                    flag=true;
                }
            }
        }

        return flag;
    }

    @Override
    public Boolean findIsoutStockOut(Long activityId) {
        boolean flag = false;
        InventoryManagementActivity outActivity=inventoryManagementActivityRepository.findByIdLazyLoad(activityId);
        if (null!=outActivity) {
            if(outActivity.getType().equals(InventoryManagementActivity.InventoryActivityType.CANCEL_OUT)){
                flag=true;
            }

        }


        return flag;
    }


    private String checkTypes(String types, String code, Long project) {
        String types_ = "";
        String[] str = types.split(SystemConst.STR_COMMA);
        for(String type : str){
            if(StringUtils.isNotEmpty(type)){
                MessageTemplate messageTemplate = messageTemplateRepository.findByTypeAndCodeHardly(type,code,project);
                if(messageTemplate != null){
                    types_ += type + SystemConst.STR_COMMA;
                }
            }
        }
        return types_;
    }

    @Override
    public Boolean checkMaterBatchNumberEnough(StockInDTO dto) {
        Boolean enough = true;
        List<StockDataDTO> stockDatas = dto.getStockDatas();
        for (StockDataDTO data : stockDatas) {
            List<BatchDto> batchDtos = data.getBatchDtos();
            if (CollectionUtils.isNotEmpty(batchDtos)) {
                for (BatchDto batchDto : batchDtos) {
                    Long batchId = batchDto.getBatchId();
                    if (null != batchId) {
                        //页面输入的实际出库数量
                        Double amount = batchDto.getNumber();
                        MaterialBatch materialBatch = materialBatchRepository.findOne(batchId);
                        if (materialBatch.getAmount() < amount) {
                            enough = false;
                            break;
                        }
                    }
                }
            }
        }
        return enough;
    }
}
