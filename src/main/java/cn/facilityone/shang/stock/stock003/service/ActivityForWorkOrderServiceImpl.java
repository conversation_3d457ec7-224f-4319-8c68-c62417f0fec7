package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.entity.inventory.*;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.stock.common.repository.*;
import cn.facilityone.shang.stock.stock002.service.InventoryService;
import cn.facilityone.shang.stock.stock003.builder.InventoryManagementActivityBuilder;
import cn.facilityone.shang.stock.stock003.builder.MaterialBatchToBuilder;
import cn.facilityone.shang.stock.stock003.dto.ActivityCollectionDTO;
import cn.facilityone.shang.stock.stock003.dto.StockDataDTO;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;
import cn.facilityone.shang.stock.stock003.dto.StockReserveUpdateDTO;
import cn.facilityone.shang.workorder.common.repository.WorkOrderMaterialRepository;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by charles.chen on 2015/9/29.
 */
@Service
public class ActivityForWorkOrderServiceImpl implements ActivityForWorkOrderService {
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private InventoryManagementActivityRepository activityRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private MaterialBatchChangeRepository changeRepository;
    @Autowired
    private WorkOrderMaterialRepository workOrderMaterialRepository;
    @Autowired
    private MaterialBatchRepository materialBatchRepository;
    @Autowired
    private InventoryManagementActivityService activityService;
    @Autowired
    private MaterialBatchChangeService changeService;
    @Autowired
    private MaterialBatchService materialBatchService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private InventoryManagementActivityBuilder activityBuilder;
    @Autowired
    private MaterialBatchToBuilder materialBatchToBuilder;



    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity handleStockCancelOutForWorkOrder(Long woId) {

        //后的关联操作记录
        List<Long> connectActivityIds = workOrderMaterialRepository.findActivitysByWoId(woId);

        if(connectActivityIds ==null || connectActivityIds.size()==0){
            return null;
        }

        //取消预定
        InventoryManagementActivity activity = activityService.handleCancelStockReserve(connectActivityIds.get(0), InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE);

        return activity;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity handleStockReserveForWorkOrder(StockInDTO dto, Long woId) {
        InventoryManagementActivity activity =null;
        if(dto.getOriginActivityId()!=null){
            if(CollectionUtils.isEmpty(dto.getStockDatas())){
                //完全取消预定
                activityService.handleCancelStockReserve(dto.getOriginActivityId(), InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE);
            }else{
                //编辑预定操作
                //先取消原先的预定信息 对预定库存,批次进行回库操作
                activity =this.cancelStockReserveActivity(dto.getOriginActivityId());
                //删除原先预定记录关联的库存变化记录信息
                this.deleteConnectRecordForActivity(dto.getOriginActivityId());
            }

        }
         //重新关联预定信息
        activity = this.handleStockReserveForWorkOrder(activity,dto);

        if(activity ==null){
            //删除全部物料
            return null;
        }else{
            //对物料进行修改
//            activity.setWorkOrder(workOrderRepository.findOne(woId));
            if(null != woId){
                activity.setPkeyId(String.valueOf(woId));
                activity.setTableName(WorkOrder.class.getSimpleName());
            }
            return activity;
        }
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void handleReserveOutForCompleteWorkOrder(Long woId, Employee employee) {
        List<InventoryManagementActivity> activities = activityRepository.findByWorkOrderIdHardly(woId, WorkOrder.class.getSimpleName());
        if(CollectionUtils.isEmpty(activities)){
            return;
        }
        //获得工单号
        String woCode = workOrderRepository.findCodeById(woId);
        //获得该工单关联的预定记录
        InventoryManagementActivity activity = activities.get(0);
        //最终保存集合 --使用Map解决库存和批次的重复使用问题
        Set<MaterialBatch> materialBatches=new HashSet<>();
        Map<Long,MaterialBatch> longBatchMap = new HashMap<>();
        Set<Long> materialBatchIds=new HashSet<>();
        List<MaterialBatchChange> materialBatchChanges=new ArrayList<>();
        List<Inventory> inventorys=new ArrayList<>();

        //获得预定记录关联的批次变化记录集合
        List<MaterialBatchChange> materialBatchChangeList =changeRepository.findByActivityId(activity.getId());
        for(MaterialBatchChange change : materialBatchChangeList){
            if(change.getMaterialBatch()!=null){
                materialBatches.add(change.getMaterialBatch());
                longBatchMap.put(change.getMaterialBatch().getId(), change.getMaterialBatch());
                materialBatchIds.add(change.getMaterialBatch().getId());
            }
        }

        //获得库存集合
        inventorys = materialBatchRepository.findInventoryByIds(materialBatchIds.toArray(new Long[materialBatchIds.size()]));
        Map<Long,Inventory> longInventoryMap = this.convertInventoryListToMap(inventorys);

        //先进行解锁操作-再进行出库操作
        for(MaterialBatchChange change : materialBatchChangeList){
            Double amount = change.getChangeNum();
            if(change.getMaterialBatch()!=null){
                MaterialBatch materialBatch = longBatchMap.get((change.getMaterialBatch().getId()));
                //先解锁 再出库
                materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,amount);
                materialBatch=materialBatchService.quantityChanges(materialBatch, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,amount);

                if(materialBatch.getInventory()!=null){
                    Inventory inventory = longInventoryMap.get(materialBatch.getInventory().getId());
                    //先解锁-再出库
                    inventory=inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,amount);
                    inventory=inventoryService.quantityChanges(inventory, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,amount);

                }
                //原始批次改动记录设置预定失效
                change.setLockActivity(Boolean.FALSE);
                //新的批次改动记录
                MaterialBatchChange mChange = new MaterialBatchChange();
                mChange.setType(MaterialBatchChange.AdjustType.DOWN);
                mChange.setChangeNum(amount);
                mChange.setMaterialBatch(materialBatch);
                materialBatchChanges.add(mChange);
            }
        }

        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);

        //创建预定出库操作的记录 同时关联对应的预定记录NO
        //为新建预定出库记录,创建dto
        StockInDTO dto =new StockInDTO();
        dto.setLaborer(employee);
        dto.setWarehouse(activity.getWarehouse());
        dto.setAmount(activity.getAmount());
        InventoryManagementActivity inventoryManagementActivity = activityService.createActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT);
        inventoryManagementActivity.setConnectNo(activity.getNo());
        inventoryManagementActivity.setOperateDate(new Date());
        //添加关联code
        inventoryManagementActivity.setDescription(this.buildDescriptionForWoActivity(inventoryManagementActivity.getDescription(), activity.getNo(), woCode));
        activity.setConnectNo(inventoryManagementActivity.getNo());
        //添加关联code
        activity.setDescription(this.buildDescriptionForWoActivity(activity.getDescription(),inventoryManagementActivity.getNo(),woCode));
        inventoryManagementActivity.setMaterialBatchs(new ArrayList<MaterialBatch>(materialBatches));
//        inventoryManagementActivity.setWorkOrder(activity.getWorkOrder());
        inventoryManagementActivity.setPkeyId(activity.getPkeyId());
        inventoryManagementActivity.setTableName(activity.getTableName());

        inventoryManagementActivity=activityService.create(inventoryManagementActivity);
        activityService.create(activity);
        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(inventoryManagementActivity);
        }

        changeRepository.saveInBatch(materialBatchChangeList);
        changeRepository.saveInBatch(materialBatchChanges);

    }

    @Override
    @XiaTransactional(readOnly = false)
    public void updateStockReserveRecord(List<StockReserveUpdateDTO> datas) {
        Set<Long> activityIds = new HashSet<>();
        for(StockReserveUpdateDTO data : datas){
            activityIds.add(data.getActivityId());
        }
        if(activityIds==null || activityIds.size()==0){
            return;
        }
        List<InventoryManagementActivity> activitys = activityRepository.findByIds(activityIds.toArray(new Long[activityIds.size()]));
        activityBuilder.init(activitys).addMaterialBatchChange();
        //关联
        Map<Long,List<MaterialBatchChange>> lChangeMap = new HashMap<>();
        Map<Long,List<MaterialBatch>> lbatchMap = new HashMap<>();
        for(InventoryManagementActivity activity : activitys){
            lChangeMap.put(activity.getId(),activity.getMaterialBatchChanges());
            lbatchMap.put(activity.getId(),activityRepository.findMaterialBatchsById(activity.getId()));

        }
        //批次改动集合 批次集合
        Set<MaterialBatchChange> changeSet = new HashSet<>();
        Set<MaterialBatch> batchSet = new HashSet<>();
        for(List<MaterialBatchChange> lcs : lChangeMap.values()){
            changeSet.addAll(lcs);
        }
        for(List<MaterialBatch> mb : lbatchMap.values()){
            batchSet.addAll(mb);
        }
        //批次集合
        Map<Long,MaterialBatch> longMaterialBatchMap = this.convertMaterialBatchListToMap(new ArrayList<MaterialBatch>(batchSet));

        materialBatchToBuilder.init(new ArrayList<MaterialBatch>(batchSet)).addInventory();
        //库存集合
        List<Inventory> inventoryList = new ArrayList<>();
        for(MaterialBatch mb : batchSet){
            inventoryList.add(mb.getInventory());
        }
        Map<Long,Inventory> longInventoryMap = this.convertInventoryListToMap(inventoryList);


        for(StockReserveUpdateDTO dto : datas){

            Long aid = dto.getActivityId();//当前activityId
            Double amount=dto.getAmount();//该批次的现有预定数量
            List<MaterialBatchChange> changeList= lChangeMap.get(aid);
            MaterialBatchChange change = this.getChangByInventoryAndDate(changeList,dto.getInventoryId(),dto.getDueDate());

            if(change!=null){
                Double originAmount=change.getChangeNum();//批次原有的改动数量
                if(change.getMaterialBatch()!=null){

                    MaterialBatch materialBatch = longMaterialBatchMap.get(change.getMaterialBatch().getId());
                    Inventory inventory =longInventoryMap.get(materialBatch.getInventory().getId());
                    //根据数量对批次，库存进行操作
                    /**
                     * 若amount==0 执行取消预定操作  取消量=originAmount
                     * 若amount>originAmount  执行预定操作 预定量=若amount-originAmount
                     * 若amount<originAmount 执行取消预定操作 取消量=originAmount-amount
                     */
                    if(amount!=originAmount){
                        if(amount==0){
                            change.setLockActivity(Boolean.FALSE);
                            inventory=inventoryService.quantityChanges(inventory,InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,originAmount);
                            materialBatch=materialBatchService.quantityChanges(materialBatch,InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,originAmount);

                        }else{
                            if(amount > originAmount){
                                inventory=inventoryService.quantityChanges(inventory,InventoryManagementActivity.InventoryActivityType.RESERVE,amount-originAmount);
                                materialBatch=materialBatchService.quantityChanges(materialBatch,InventoryManagementActivity.InventoryActivityType.RESERVE,amount-originAmount);

                            }else{
                                inventory=inventoryService.quantityChanges(inventory,InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,originAmount-amount);
                                materialBatch=materialBatchService.quantityChanges(materialBatch,InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE,originAmount-amount);
                            }
                        }
                    }
                }
            }
        }

        inventoryRepository.saveInBatch(inventoryList);
        materialBatchRepository.saveInBatch(batchSet);
        changeRepository.saveInBatch(changeSet);
    }

    private String buildDescriptionForWoActivity(String desc,String code,String woCode){
        StringBuffer stringBuffer = new StringBuffer();
        if(!StringUtils.isBlank(desc)){
            stringBuffer.append(desc).append(" ");
        }
        stringBuffer.append(XiaMesssageResource.getMessage("page.pm002.connectWoCode")).append(":").append(woCode).append("。");
        stringBuffer.append(XiaMesssageResource.getMessage("page.stock003.stockBack.connectReserveNo")).append(":").append(code).append("。");
        return stringBuffer.toString();
    }

    private Map<Long,MaterialBatch> convertMaterialBatchListToMap(List<MaterialBatch> batchList){
        Map<Long,MaterialBatch> changeMap = new HashMap<>();
        for(MaterialBatch batch : batchList){
            changeMap.put(batch.getId(),batch);
        }
        return changeMap;
    }

    private InventoryManagementActivity handleStockReserveForWorkOrder(InventoryManagementActivity activity,StockInDTO dto){
        //计算操作总量
        if(null == dto.getAmount() || dto.getAmount() == 0){
            dto.setAmount(this.getTotalAmountFromDto(dto));
        }
        //最终存储集合
        ActivityCollectionDTO collectionDTO = activityService.buildStockReserveDto(dto, "");

        if(collectionDTO==null){
            return null;
        }

        Set<MaterialBatch> materialBatches=collectionDTO.getBatchSets();
        List<MaterialBatchChange> materialBatchChanges=collectionDTO.getChanges();
        List<Inventory> inventorys=collectionDTO.getInventorys();

        inventoryRepository.saveInBatch(inventorys);
        materialBatchRepository.saveInBatch(materialBatches);

        Warehouse warehouse = warehouseRepository.findOne(dto.getWarehouse().getId());
        if(null != warehouse && null != warehouse.getEmployees()){
            dto.setManagers(warehouse.getEmployees().get(0));
        }

        //关联操作记录
        if(activity==null){
            dto.setHandles(dto.getLaborer());
            dto.setOperateDate(new Date());
            activity = activityService.createActivityByDtoAndType(dto, InventoryManagementActivity.InventoryActivityType.RESERVE);
            //activity=activityRepository.save(activity);
        }else{
            //关联activity
            BeanUtils.copyProperties(dto, activity);
            Employee employee = employeeRepository.findOne(dto.getLaborer().getId());
            activity.setLaborer(employee);
            activity.setHandles(employee);
            activity.setOperateDate(new Date());
            activity.setWarehouse(warehouse);
            activity.setManagers(dto.getManagers());
        }
        activity.setMaterialBatchs(new ArrayList<MaterialBatch>(materialBatches));
        activity = activityRepository.save(activity);

        for(MaterialBatchChange mChange : materialBatchChanges){
            mChange.setInventoryManagementActivity(activity);
        }

        changeRepository.saveInBatch(materialBatchChanges);

        return activityRepository.save(activity);
    }

    private MaterialBatchChange getChangByInventoryAndDate(List<MaterialBatchChange> changeList,Long inventoryId,Date dueDate){

        for(MaterialBatchChange mbc : changeList)
            if(mbc.getMaterialBatch()!=null){
                MaterialBatch materialBatch = mbc.getMaterialBatch();
                if(materialBatch.getInventory()!=null){
                    if(dueDate==null){
                        if(materialBatch.getInventory().getId().equals(inventoryId)){
                            return mbc;
                        }
                    }else{
                        if(materialBatch.getInventory().getId().equals(inventoryId) && materialBatch.getDueDate().getTime()==dueDate.getTime()){
                            return mbc;
                        }
                    }
                }
            }
        return null;
    }

    private Double getTotalAmountFromDto(StockInDTO dto){
        Double amount = 0d;
        List<StockDataDTO> datas = dto.getStockDatas();
        for(StockDataDTO data : datas){
            amount+=data.getAmount();
        }
        return amount;
    }

    private InventoryManagementActivity cancelStockReserveActivity(Long activityId){

        ActivityCollectionDTO collectionDTO = activityService.buildStockCancelReserveDto(activityId, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE);
        if(collectionDTO == null){
            return null;
        }
        InventoryManagementActivity activity = collectionDTO.getActivity();
        //存储集合
//        List<MaterialBatch> materialBatchs = collectionDTO.getBatches();
        Set<Inventory> inventorys = collectionDTO.getInventorySet();

        //先取消原先的预定信息，取消实体关联
        inventoryRepository.saveInBatch(inventorys);
//        materialBatchRepository.saveInBatch(materialBatchs);
        activity.setMaterialBatchChanges(null);
        activity.setWarehouse(null);
        activity.setMaterialBatchs(null);
        activity.setLaborer(null);
//        activity.setWorkOrder(null);
        activity.setPkeyId(null);
        activity.setTableName(null);
        activity.setOperateDate(null);
        return  activityRepository.save(activity);
    }

    private void deleteConnectRecordForActivity(Long activityId){

        changeRepository.deleteByActivityId(activityId);

    }

    private Map<Long,Inventory> convertInventoryListToMap(List<Inventory> inventorys){
        Map<Long,Inventory> inventoryMap = new HashMap<>();
        for(Inventory inventory : inventorys){
            inventoryMap.put(inventory.getId(),inventory);
        }

        return inventoryMap;
    }
}
