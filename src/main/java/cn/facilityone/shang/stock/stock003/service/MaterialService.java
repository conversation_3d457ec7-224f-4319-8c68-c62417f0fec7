package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.entity.asset.Provider;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.Material;
import cn.facilityone.shang.stock.stock002.dto.AutoObjectDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Created by charles.chen on 2015/9/2.
 */
public interface MaterialService {


    /**
     * 获得物资的品牌对象集合
     */
    List<AutoObjectDTO> findAllBrand(DataTableRequest request);

    /**
     * 获得物资的型号对象集合
     */
    List<AutoObjectDTO> findAllModel(DataTableRequest request);

    /**
     * 获得物资的型号对象集合
     */
    List<AutoObjectDTO> findAllProiderName(DataTableRequest request);

    /**
     * 获得物资
     */
    List<Material> findAllMaterial(DataTableRequest request);

    Material findOrCreateMaterial(HttpServletRequest request, Long warehouseId, Material material, List<Long> picIds, List<Long> docIds,boolean isChange);

    Inventory createQRPicture(HttpServletRequest request,Long inventoryId,Inventory inventory);

    String generateContentByEquipment(Inventory inventory,Long inventoryId,String uuid);

    /**
     * 根据物资名称来获取牌子
     */
    List<AutoObjectDTO> findBrandByMaterialName(String name);

    /**
     * 根据物资名称来获取型号
     */
    List<AutoObjectDTO> findModelByMaterialName(String name);

    /**
     * 获取对应库存的供应商
     */
    List<Provider> findProiderListByInventoryId(DataTableRequest request);
}
