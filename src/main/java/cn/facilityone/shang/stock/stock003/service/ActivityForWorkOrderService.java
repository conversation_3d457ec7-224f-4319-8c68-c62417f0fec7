package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;
import cn.facilityone.shang.stock.stock003.dto.StockReserveUpdateDTO;

import java.util.List;

/**
 * Created by charles.chen on 2015/9/29.
 */
public interface ActivityForWorkOrderService {

    /**
     * 工单处于终止状态时，进行取消预定操作
     */
    InventoryManagementActivity handleStockCancelOutForWorkOrder(Long woId);

    /**
     * 为工单添加/编辑物料
     */
    InventoryManagementActivity handleStockReserveForWorkOrder(StockInDTO dto, Long woId);

    /**
     * 工单完成时的预定出库
     */
    void handleReserveOutForCompleteWorkOrder(Long woId,Employee employee);

    /**
     * 修改预定工单信息
     */
    public void updateStockReserveRecord(List<StockReserveUpdateDTO> datas);
}
