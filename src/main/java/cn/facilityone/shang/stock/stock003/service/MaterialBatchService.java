package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.MaterialBatch;
import cn.facilityone.shang.stock.stock003.dto.MaterialBatchDTO;

import java.util.List;

/**
 * Created by charles.chen on 2015/8/14.
 */
public interface MaterialBatchService {

    MaterialBatch create(MaterialBatch materialBatch);

    /**
     * 获得库存的批次信息
     */
    List<MaterialBatch> findByDto(MaterialBatchDTO dto);

    /**
     * 显示库存中的批次信息 不支持排序和搜索
     */
    List<MaterialBatch> findListByInventoryId(Long id);

    /**
     * 修改批次的数量和锁定数量
     * @param materialBatch,type,changeNum
     * @return MaterialBatch
     */
    MaterialBatch quantityChanges(MaterialBatch materialBatch,InventoryManagementActivity.InventoryActivityType type,Double changeNum);



}
