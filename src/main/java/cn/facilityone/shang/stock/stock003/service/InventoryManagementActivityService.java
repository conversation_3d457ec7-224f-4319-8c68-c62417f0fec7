package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.mobile.v1.stock.view.MInventoryListRequest;
import cn.facilityone.shang.stock.stock002.dto.StockAdjustDTO;
import cn.facilityone.shang.stock.stock003.dto.ActivityCollectionDTO;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;
import org.springframework.data.domain.Page;


/**
 * Created by charles.chen on 2015/8/11.
 */
public  interface InventoryManagementActivityService {


    InventoryManagementActivity create(InventoryManagementActivity inventoryManagementActivity);

    /**
     * 执行库存入库操作
     */
    String handleStockIn(StockInDTO dto);

    /**
     * 执行库存预定操作
     */
    InventoryManagementActivity handleStockReserve(StockInDTO dto);

    /**
     * 自动生成工单 执行库存预定操作
     */
    InventoryManagementActivity handleStockReserveForPm(StockInDTO dto,Long project);

    /**
     * 执行库存出库操作
     */
    InventoryManagementActivity handleStockOut(StockInDTO dto);

    /**
     * 执行库存退库操作
     */
    InventoryManagementActivity handleStockBack(StockInDTO dto);


    /**
     * 执行预定出库操作
     */
    InventoryManagementActivity handleStockReserveOut(StockInDTO dto);

    /**
     * 执行预定出库操作
     */
    void handleStockReserveOut2(StockInDTO dto);
    /**
     * 执行库存调整操作
     */
    void handleStockAdjust(StockAdjustDTO dto);

    /**
     * 执行取消预定操作
     */
    InventoryManagementActivity handleCancelStockReserve(Long id, InventoryManagementActivity.InventoryActivityType type);



    /**
     * 根据DTO和操作类型创建操作记录
     */
    InventoryManagementActivity createActivityByDtoAndType(StockInDTO dto,InventoryManagementActivity.InventoryActivityType type);

    /**
     * 获得库存预定数据集合
     */
    ActivityCollectionDTO buildStockReserveDto(StockInDTO dto, String type);

    /**
     * 获得库存取消预定数据集合
     */
    ActivityCollectionDTO buildStockCancelReserveDto(Long id, InventoryManagementActivity.InventoryActivityType type);

    Page<InventoryManagementActivity> findReservationListInPage(MInventoryListRequest request);

    void sendmessage(InventoryManagementActivity inventoryManagementActivity, StockInDTO dto,Boolean IsMobile);

    /**
     * 判断是否已经被出库
     * @param activityId
     * @return
     */
    Boolean findIsoutbound(Long activityId);

    /**
     * 判断是否已经被取消出库
     * @param activityId
     * @return
     */
    Boolean findIsoutStockOut(Long activityId);


    Boolean checkMaterBatchNumberEnough(StockInDTO dto);

}
