package cn.facilityone.shang.stock.stock003.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.common.util.DoubleOperationUtils;
import cn.facilityone.shang.entity.inventory.*;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.stock.common.repository.InventoryManagementActivityRepository;
import cn.facilityone.shang.stock.common.repository.InventoryRepository;
import cn.facilityone.shang.stock.common.repository.MaterialBatchChangeRepository;
import cn.facilityone.shang.stock.common.staticmetamodel.*;
import cn.facilityone.shang.stock.stock002.builder.InventoryBuilder;
import cn.facilityone.shang.stock.stock002.dto.MoveRecordDTO;
import cn.facilityone.shang.stock.stock002.dto.ReserveActivityDTO;
import cn.facilityone.shang.stock.stock003.builder.InventoryManagementActivityBuilder;
import cn.facilityone.shang.stock.stock003.builder.MaterialBatchChangeBuilder;
import cn.facilityone.shang.stock.stock003.builder.MaterialBatchToBuilder;
import cn.facilityone.shang.stock.stock003.dto.StockBackSearchRequestDTO;
import cn.facilityone.shang.stock.stock003.dto.StockQueryRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort.Order;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import java.util.*;

/**
 * Created by charles.chen on 2015/8/11.
 */
@Service
public class MaterialBatchChangeServiceImpl implements MaterialBatchChangeService {
    @Autowired
    private MaterialBatchChangeRepository materialBatchChangeRepository;
    @Autowired
    private InventoryBuilder inventoryBuilder;
    @Autowired
    private InventoryManagementActivityBuilder inventoryManagementActivityBuilder;
    @Autowired
    private MaterialBatchToBuilder materialBatchBuilder;
    @Autowired
    private MaterialBatchChangeBuilder materialBatchChangeBuilder;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private InventoryManagementActivityRepository activityRepository;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private InventoryManagementActivityRepository inventoryManagementActivityRepository;

    @Override
    public MaterialBatchChange create(MaterialBatchChange materialBatchChange) {
        return materialBatchChangeRepository.save(materialBatchChange);
    }

    @Override
    public MaterialBatchChange.AdjustType findAdjustTypeByActivityType(InventoryManagementActivity.InventoryActivityType type) {

        InventoryManagementActivity.InventoryActivityType downTypes[]={
                InventoryManagementActivity.InventoryActivityType.OUT,
                InventoryManagementActivity.InventoryActivityType.RESERVE_OUT
        };

        InventoryManagementActivity.InventoryActivityType upTypes[]={
                InventoryManagementActivity.InventoryActivityType.IN,
                InventoryManagementActivity.InventoryActivityType.BACK
        };

        InventoryManagementActivity.InventoryActivityType lockTypes[]={InventoryManagementActivity.InventoryActivityType.RESERVE};

        if(Arrays.asList(upTypes).contains(type)){

            return MaterialBatchChange.AdjustType.UP;

        }else if(Arrays.asList(downTypes).contains(type)){

            return MaterialBatchChange.AdjustType.DOWN;

        }else if(Arrays.asList(lockTypes).contains(type)){

            return MaterialBatchChange.AdjustType.LOCK;
        }

        return MaterialBatchChange.AdjustType.UP;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<MaterialBatchChange> findMaterialBatchChangeByDtoAndPage(final StockQueryRequest searchRequest) {

        searchRequest.setSort(dataTableService.buildColumnSort(searchRequest.getColumns()));

        Page<MaterialBatchChange> result=materialBatchChangeRepository.findAll(new XiaSpecification<MaterialBatchChange>() {
            @Override
            public Root<MaterialBatchChange> toRoot(Root<MaterialBatchChange> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                root.fetch(MaterialBatchChange_.MATERIALBATCH, JoinType.LEFT);
                root.fetch(MaterialBatchChange_.ACTIVITY, JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<MaterialBatchChange> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                //列搜索 columns
                List<Predicate> cpreL  = dataTableService.buildConditionsSearch(searchRequest.getConditions(), root, cb);
                //List<Predicate> preL = dataTableService.buildColumnSearchAllOr(searchRequest.getColumns(), root, cb);
                predicatesList.addAll(cpreL);
                //predicatesList.addAll(preL);

                //只显示出库数据
                InventoryManagementActivity.InventoryActivityType[] types={InventoryManagementActivity.InventoryActivityType.OUT,InventoryManagementActivity.InventoryActivityType.RESERVE_OUT};
                From<?, ?> inventoryActivity = root.join(MaterialBatchChange_.ACTIVITY, JoinType.LEFT);
                predicatesList.add(inventoryActivity.get(InventoryManagementActivity_.TYPE).in(types));
                //列搜索
                List<DataTableColumn> dataTableColumnList=searchRequest.getColumns();
                //搜索
                if(dataTableColumnList!=null && dataTableColumnList.size()>0){
                    List<Predicate> lps =
                            dataTableService.buildColumnSearch(dataTableColumnList, root, cb);
                    predicatesList.addAll(lps);
                    //SORT 排序
                }


                StockBackSearchRequestDTO searchDto=searchRequest.getSdto();
                if(searchDto!=null){
                    if(searchDto.getBeginDate()!=null && searchDto.getEndDate()!=null){
                        //From<?, ?> inventoryActivity = root.join("inventoryManagementActivity", JoinType.LEFT);
                        Date startDate =
                                DateUtil.buildDateOnFirstSecond(searchDto.getBeginDate());
                        Date endDate =
                                DateUtil.buildDateOnLastSecond(searchDto.getEndDate());
                        predicatesList.add(cb.between(
                                inventoryActivity.<Date>get(InventoryManagementActivity_.OPERATE_DATE), startDate, endDate));
                    }

                    if(!StringUtils.isBlank(searchDto.getNo())){
                        predicatesList.add(cb.equal(inventoryActivity.get(InventoryManagementActivity_.NO),searchDto.getNo()));
                    }
                    if(searchDto.getLaborer()!=null){
                        From<?, ?> employee = inventoryActivity.join(InventoryManagementActivity_.LABORER, JoinType.LEFT);
                        predicatesList.add(cb.equal(employee.get(InventoryManagementActivity_.ID),searchDto.getLaborer().getId()));
                    }
                    if(searchDto.getWarehouse()!=null){
                        From<?, ?> warehouse = inventoryActivity.join(InventoryManagementActivity_.WAREHOUSE, JoinType.LEFT);
                        predicatesList.add(cb.equal(warehouse.get(InventoryManagementActivity_.ID),searchDto.getWarehouse().getId()));

                    }
                }else{
                    predicatesList.add(cb.isNull(root.get(MaterialBatchChange_.ID)));
                }
                query.orderBy(cb.desc(root.get(MaterialBatchChange_.ID)));
                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                return query.getRestriction();
            }
        },searchRequest);

        //load lazy datas @ManyToMant columns
        List<InventoryManagementActivity> activities = new ArrayList<>();
        materialBatchChangeBuilder.init( result.getContent()).addActivity();
        List<MaterialBatch> materialBatches = new ArrayList<>();
        for(MaterialBatchChange materialBatchChange : result){
            if(materialBatchChange.getMaterialBatch()!=null){
                materialBatches.add(materialBatchChange.getMaterialBatch());
                activities.add(materialBatchChange.getInventoryManagementActivity());
            }
        }
        inventoryManagementActivityBuilder.init(activities).addLaborer();
        materialBatchBuilder.init(materialBatches).addInventory();
        List<Inventory> inventorys = new ArrayList<>();
        for(MaterialBatch materialBatch : materialBatches){
          if(materialBatch.getInventory()!=null){
              inventorys.add(materialBatch.getInventory());
          }
            materialBatch.setMaterialBatchChanges(null);
        }
        for(Inventory inventory : inventorys){
            inventory.setMaterialBatchs(null);
        }
        inventoryBuilder.init(inventorys).addMaterial();

        return result.getContent();

    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<MaterialBatchChange> findMaterialBatchChangeByActivityId(final Long id,final DataTableRequest request) {
        request.setSort(dataTableService.buildColumnSort(request.getColumns()));

        Page<MaterialBatchChange> result=materialBatchChangeRepository.findAll(new XiaSpecification<MaterialBatchChange>() {
            @Override
            public Root<MaterialBatchChange> toRoot(Root<MaterialBatchChange> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                root.fetch(MaterialBatchChange_.MATERIALBATCH, JoinType.LEFT);
                root.fetch(MaterialBatchChange_.ACTIVITY, JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<MaterialBatchChange> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                //列搜索 condition
                List<Predicate> cpreL  = dataTableService.buildConditionsSearch(request.getConditions(), root, cb);
                predicatesList.addAll(cpreL);
                //列搜索
                List<DataTableColumn> dataTableColumnList=request.getColumns();
                //搜索
                if(dataTableColumnList!=null && dataTableColumnList.size()>0){
                    List<Predicate> lps =
                            dataTableService.buildColumnSearch(dataTableColumnList, root, cb);
                    predicatesList.addAll(lps);
                //SORT 排序
                }

                //只显示预定的数据
                InventoryManagementActivity.InventoryActivityType[] types={InventoryManagementActivity.InventoryActivityType.OUT,InventoryManagementActivity.InventoryActivityType.RESERVE};
                From<?, ?> inventoryActivity = root.join(MaterialBatchChange_.ACTIVITY, JoinType.LEFT);
                predicatesList.add(inventoryActivity.get(InventoryManagementActivity_.TYPE).in(types));
                if(id !=null){
                    predicatesList.add(cb.equal(inventoryActivity.get(MaterialBatchChange_.ID),id));
                }
                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                return query.getRestriction();
            }
        },request);

        //build懒加载数据
        List<InventoryManagementActivity> activities = new ArrayList<>();
        materialBatchChangeBuilder.init( result.getContent()).addActivity();
        List<MaterialBatch> materialBatches = new ArrayList<>();
        for(MaterialBatchChange materialBatchChange : result){
            if(materialBatchChange.getMaterialBatch()!=null){
                materialBatches.add(materialBatchChange.getMaterialBatch());
                activities.add(materialBatchChange.getInventoryManagementActivity());
            }
        }
        inventoryManagementActivityBuilder.init(activities).addLaborer();
        materialBatchBuilder.init(materialBatches).addInventory();
        List<Inventory> inventorys = new ArrayList<>();
        for(MaterialBatch materialBatch : materialBatches){
            if(materialBatch.getInventory()!=null){
                inventorys.add(materialBatch.getInventory());
            }
            materialBatch.setMaterialBatchChanges(null);
        }
        for(Inventory inventory : inventorys){
            inventory.setMaterialBatchs(null);
        }
        inventoryBuilder.init(inventorys).addMaterial().addWarehouse();

        return result.getContent();
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<MaterialBatchChange> findActivityRecodesByInventoryIdAndType(final Long id,final InventoryManagementActivity.InventoryActivityType type) {
        List<MaterialBatchChange> result=materialBatchChangeRepository.findAll(new Specification<MaterialBatchChange>() {

            @Override
            public Predicate toPredicate(Root<MaterialBatchChange> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                //只显示需要的类型
                From<?, ?> inventoryActivity = root.join(MaterialBatchChange_.ACTIVITY, JoinType.LEFT);
                //出库记录包括正常出库和预定出库
                if(type.ordinal()==1){
                    InventoryManagementActivity.InventoryActivityType[] types={InventoryManagementActivity.InventoryActivityType.RESERVE_OUT,InventoryManagementActivity.InventoryActivityType.OUT};
                    predicatesList.add(inventoryActivity.get(InventoryManagementActivity_.TYPE).in(types));
                }else{
                    predicatesList.add(cb.equal(inventoryActivity.get(InventoryManagementActivity_.TYPE),type));
                }
                if(id !=null){
                    From<?, ?> materialBatch = root.join(MaterialBatchChange_.MATERIALBATCH, JoinType.LEFT);
                    From<?, ?> inventory = materialBatch.join(MaterialBatch_.INVENTORY, JoinType.LEFT);
                    predicatesList.add(cb.equal(inventory.get(MaterialBatchChange_.ID),id));
                }
                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                query.orderBy(cb.desc(inventoryActivity.get("operateDate")), cb.desc(inventoryActivity.get("createdDate")));
                return query.getRestriction();
            }
        });

        //load lazy datas
        materialBatchChangeBuilder.init( result).addActivity().addMaterialBatch();

        List<InventoryManagementActivity> activitys = new ArrayList<>();
        List<MaterialBatch> materialBatches = new ArrayList<>();

        for(MaterialBatchChange materialBatchChange : result){
            materialBatchChange.setChangeNum(DoubleOperationUtils.changeDecimal(materialBatchChange.getChangeNum(), 2));
            if(materialBatchChange.getMaterialBatch()!=null){
                double price = materialBatchChange.getMaterialBatch().getPrice();
                materialBatchChange.getMaterialBatch().setPrice(DoubleOperationUtils.changeDecimal(price,2));
                materialBatches.add(materialBatchChange.getMaterialBatch());
                activitys.add(materialBatchChange.getInventoryManagementActivity());
            }
        }
        inventoryManagementActivityBuilder.init(activitys).addLaborer();
        materialBatchBuilder.init(materialBatches).addInventory().addProvider();

        return result;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<MaterialBatchChange> findActivityRecodesByInventoryIdAndActivity(final Long id,final boolean inActivity) {
        List<MaterialBatchChange> result=materialBatchChangeRepository.findAll(new Specification<MaterialBatchChange>() {

            @Override
            public Predicate toPredicate(Root<MaterialBatchChange> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();

                if(id !=null){
                    From<?, ?> materialBatch = root.join(MaterialBatchChange_.MATERIALBATCH, JoinType.LEFT);
                    From<?, ?> inventory = materialBatch.join(MaterialBatch_.INVENTORY, JoinType.LEFT);
                    predicatesList.add(cb.equal(inventory.get(Inventory_.ID),id));
                }
                From<?, ?> inventoryActivity = root.join(MaterialBatchChange_.ACTIVITY, JoinType.LEFT);
                From<?, ?> materialBatch = root.join(MaterialBatchChange_.MATERIALBATCH, JoinType.LEFT);

                if(inActivity){
                    //有效
                    InventoryManagementActivity.InventoryActivityType[] types={InventoryManagementActivity.InventoryActivityType.IN,InventoryManagementActivity.InventoryActivityType.MOVEIN};
                    predicatesList.add(inventoryActivity.get(InventoryManagementActivity_.TYPE).in(types));
//                    predicatesList.add(cb.equal(inventoryActivity.get(InventoryManagementActivity_.TYPE), InventoryManagementActivity.InventoryActivityType.IN));
//                    predicatesList.add(cb.gt(materialBatch.<Integer>get(MaterialBatchChange_.REALNUM),0));
                }else{
                    //预定(无效)
//                    predicatesList.add(cb.equal(inventoryActivity.get(InventoryManagementActivity_.TYPE),InventoryManagementActivity.InventoryActivityType.RESERVE));
//                    predicatesList.add(cb.gt(materialBatch.<Integer>get(MaterialBatchChange_.LOCAKNUM),0));
//                   predicatesList.add(cb.isTrue(root.<Boolean>get(MaterialBatchChange_.ISLOCKACTIVITY)));
                    //移库记录
                    InventoryManagementActivity.InventoryActivityType[] types={InventoryManagementActivity.InventoryActivityType.MOVEIN,InventoryManagementActivity.InventoryActivityType.MOVEOUT};
                    predicatesList.add(inventoryActivity.get(InventoryManagementActivity_.TYPE).in(types));
                }
                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                query.orderBy(cb.desc(inventoryActivity.get("operateDate")), cb.desc(inventoryActivity.get("createdDate")));
                return query.getRestriction();
            }
        });

        //load lazy datas
        materialBatchChangeBuilder.init(result).addActivity().addMaterialBatch();

        List<InventoryManagementActivity> activitys = new ArrayList<>();
        List<MaterialBatch> materialBatches = new ArrayList<>();

        for(MaterialBatchChange materialBatchChange : result){
            materialBatchChange.setChangeNum(DoubleOperationUtils.changeDecimal(materialBatchChange.getChangeNum(), 2));
            if(materialBatchChange.getMaterialBatch()!=null){
                double amount = materialBatchChange.getMaterialBatch().getAmount();
                double price = materialBatchChange.getMaterialBatch().getPrice();
                materialBatchChange.getMaterialBatch().setAmount(DoubleOperationUtils.changeDecimal(amount,2));
                materialBatchChange.getMaterialBatch().setPrice(DoubleOperationUtils.changeDecimal(price,2));
                materialBatches.add(materialBatchChange.getMaterialBatch());
                activitys.add(materialBatchChange.getInventoryManagementActivity());
            }
        }
        inventoryManagementActivityBuilder.init(activitys).addLaborer();
        materialBatchBuilder.init(materialBatches).addInventory().addProvider();

        return result;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<MoveRecordDTO> findActivityRecodesByInventoryId(Long id){
        List<MoveRecordDTO> moveRecordDTOList = new ArrayList<>();

        List<InventoryManagementActivity> imaInList = this.findshowingInventoryRecordsByIdAndType(id, InventoryManagementActivity.InventoryActivityType.MOVEIN);
        List<InventoryManagementActivity> imaOutList = this.findshowingInventoryRecordsByIdAndType(id, InventoryManagementActivity.InventoryActivityType.MOVEOUT);

        for (InventoryManagementActivity ima : imaInList){
            MoveRecordDTO moveRecordDTO = new MoveRecordDTO();
            List<InventoryManagementActivity> imas = inventoryManagementActivityRepository.findByNO(ima.getConnectNo());
            moveRecordDTO.setNo(ima.getNo());
            moveRecordDTO.setWarehourseName(imas.get(0).getWarehouse().getName());
            moveRecordDTO.setTargetWarehourseName(ima.getWarehouse().getName());
            moveRecordDTO.setAmount(DoubleOperationUtils.changeDecimal(ima.getAmount(),2));
            if (null!=ima.getOperateDate()){
                moveRecordDTO.setOperateDate(ima.getOperateDate());
            }
            if (null!=ima.getHandles()){
                moveRecordDTO.setHandlesName(ima.getHandles().getName());
            }
            if (null!=ima.getDescription()){
                moveRecordDTO.setDescription(ima.getDescription());
            }
            if (null!=ima.getCreatedDate()){
                moveRecordDTO.setCreatedDate(ima.getCreatedDate());
            }
            moveRecordDTO.setId(id);
            moveRecordDTOList.add(moveRecordDTO);
        }
        for (InventoryManagementActivity ima : imaOutList){
            MoveRecordDTO moveRecordDTO = new MoveRecordDTO();
            List<InventoryManagementActivity> imas = inventoryManagementActivityRepository.findByNO(ima.getConnectNo());
            moveRecordDTO.setNo(ima.getNo());
            moveRecordDTO.setWarehourseName(ima.getWarehouse().getName());
            moveRecordDTO.setTargetWarehourseName(imas.get(0).getWarehouse().getName());
            moveRecordDTO.setAmount(DoubleOperationUtils.changeDecimal(ima.getAmount(), 2));
            if (null!=ima.getOperateDate()){
                moveRecordDTO.setOperateDate(ima.getOperateDate());
            }
            if (null!=ima.getHandles()){
                moveRecordDTO.setHandlesName(ima.getHandles().getName());
            }
            if (null!=ima.getDescription()){
                moveRecordDTO.setDescription(ima.getDescription());
            }
            if (null!=ima.getCreatedDate()){
                moveRecordDTO.setCreatedDate(ima.getCreatedDate());
            }
            moveRecordDTO.setId(id);
            moveRecordDTOList.add(moveRecordDTO);
        }
        Collections.sort(moveRecordDTOList, new Comparator<MoveRecordDTO>() {
            @Override
            public int compare(MoveRecordDTO m1, MoveRecordDTO m2) {
                if (m2.getOperateDate().getTime()==m1.getOperateDate().getTime()) {
                    return ((int) m2.getCreatedDate().getTime())-((int) m1.getCreatedDate().getTime());
                }
                return ((int) m2.getOperateDate().getTime())-((int) m1.getOperateDate().getTime());
            }
        });
        return moveRecordDTOList;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<InventoryManagementActivity> findshowingInventoryRecordsByIdAndType(final Long id,final InventoryManagementActivity.InventoryActivityType type) {

        List<InventoryManagementActivity> result = activityRepository.findAll(new XiaSpecification<InventoryManagementActivity>() {
            @Override
            public Root<InventoryManagementActivity> toRoot(Root<InventoryManagementActivity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {

                return root;
            }

            @Override
            public Predicate toPredicate(Root<InventoryManagementActivity> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                //根据activity的type 显示数据
                if (type == InventoryManagementActivity.InventoryActivityType.OUT) {
                    InventoryManagementActivity.InventoryActivityType[] types = {InventoryManagementActivity.InventoryActivityType.OUT, InventoryManagementActivity.InventoryActivityType.RESERVE_OUT};
                    predicatesList.add(root.get(InventoryManagementActivity_.TYPE).in(types));

                } else if (type == InventoryManagementActivity.InventoryActivityType.RESERVE) {
                    InventoryManagementActivity.InventoryActivityType[] types = {InventoryManagementActivity.InventoryActivityType.RESERVE, InventoryManagementActivity.InventoryActivityType.AUDITSUC, InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE, InventoryManagementActivity.InventoryActivityType.REJECTED, InventoryManagementActivity.InventoryActivityType.CANCEL_OUT,};
                    predicatesList.add(root.get(InventoryManagementActivity_.TYPE).in(types));
                } else {
                    predicatesList.add(cb.equal(root.get(InventoryManagementActivity_.TYPE), type));
                }

                if (id != null) {
                    From<?, ?> materialBatch = root.join(InventoryManagementActivity_.MATERIAL_BATCH, JoinType.LEFT);
                    From<?, ?> inventory = materialBatch.join(MaterialBatch_.INVENTORY, JoinType.LEFT);
                    predicatesList.add(cb.equal(inventory.get(Inventory_.ID), id));
                }
                query.distinct(Boolean.TRUE);
                // 排序
                query.orderBy(cb.desc(root.get("operateDate")), cb.desc(root.get("createdDate")));
                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                return query.getRestriction();
            }
        });
        if (type== InventoryManagementActivity.InventoryActivityType.MOVEIN||type== InventoryManagementActivity.InventoryActivityType.MOVEOUT){
            inventoryManagementActivityBuilder.init(result).addLaborer();
        } else {
            inventoryManagementActivityBuilder.init(result).addLaborer().addMaterialBatchChange().addHandles();
        }

        //对集合进行处理 将amount重新赋值
        List<Long> ids = new ArrayList<>();
        for(InventoryManagementActivity activity : result) {
            ids.add(activity.getId());
        }
        if(ids!=null && ids.size()>0){
            List<MaterialBatchChange> changeResult = this.findChangeListByActivityIdsAndInventoryId(id,ids);
            materialBatchChangeBuilder.init(changeResult).addActivity();
            Map<Long,Double> longAmountMap = this.convertChangesToMapByActivity(changeResult);

            //对集合进行处理 将amount重新赋值
            for(InventoryManagementActivity activity : result){
                if(longAmountMap.get(activity.getId())!=null){
                    double amount = longAmountMap.get(activity.getId());
                    activity.setAmount(DoubleOperationUtils.changeDecimal(amount,2));
                }
                activity.setMaterialBatchChanges(null);
            }
        }

        return result;

    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<ReserveActivityDTO> findReserveRecordsByIdAndType(final Long id,final InventoryManagementActivity.InventoryActivityType type) {
        List<InventoryManagementActivity> activityList = this.findshowingInventoryRecordsByIdAndType(id, type);
        List<ReserveActivityDTO> result = new ArrayList<>();
        for(InventoryManagementActivity activity : activityList){
            ReserveActivityDTO dto = new ReserveActivityDTO();
            BeanUtils.copyProperties(activity,dto);
            if (activity.getHandles()==null){
                Employee employee = new Employee();
                employee.setName("");
                dto.setHandles(employee);
            }
            if (activity.getLaborer()==null) {
                Employee employee = new Employee();
                employee.setName("");
                dto.setLaborer(employee);
            }
            dto=this.getStatusByConnectNo(dto);
            result.add(dto);
        }

        return result;
    }

    private ReserveActivityDTO getStatusByConnectNo(ReserveActivityDTO dto){
//        if(!StringUtils.isBlank(dto.getConnectNo())){
//            if(dto.getConnectNo().contains(InventoryManagementActivity.InventoryActivityType.RESERVE_OUT.getAbbreviate())){
//                dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.OUT);
//            }else{
//                dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.CANCEL);
//            }
//
//        }else{
//            dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.RESERVE);
//        }
        if (dto.getType().equals(InventoryManagementActivity.InventoryActivityType.RESERVE)) {
            dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.RESERVE);
        } else if (dto.getType().equals(InventoryManagementActivity.InventoryActivityType.AUDITSUC)) {
            if (StringUtils.isBlank(dto.getConnectNo())) {
                dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.RESERVE_OUT);
            } else {
                dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.OUT);
            }
        } else if (dto.getType().equals(InventoryManagementActivity.InventoryActivityType.CANCEL_RESERVE)) {
            dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.CANCEL_RESERVE);
        } else if (dto.getType().equals(InventoryManagementActivity.InventoryActivityType.REJECTED)) {
            dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.REJECTED);
        } else if (dto.getType().equals(InventoryManagementActivity.InventoryActivityType.CANCEL_OUT)) {
            dto.setStatus(ReserveActivityDTO.InventoryActivityStatus.CANCEL_OUT);
        }
        return dto;
    }


    /**
     * 根据库存ID和记录IDs 获得所有的批次改动记录
     * @param  Long id List<Long> activityIds
     * @return List<MaterialBatchChange>
     */
    private List<MaterialBatchChange> findChangeListByActivityIdsAndInventoryId(final Long id,final List<Long> activityIds){
        List<MaterialBatchChange> changeResult=materialBatchChangeRepository.findAll(new Specification<MaterialBatchChange>() {

            @Override
            public Predicate toPredicate(Root<MaterialBatchChange> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<Predicate>();
                //只显示预定的数据
                From<?, ?> inventoryActivity = root.join(MaterialBatchChange_.ACTIVITY, JoinType.LEFT);
                predicatesList.add(inventoryActivity.get(MaterialBatchChange_.ID).in(activityIds));

                if(id !=null){
                    From<?, ?> materialBatch = root.join(MaterialBatchChange_.MATERIALBATCH, JoinType.LEFT);
                    From<?, ?> inventory = materialBatch.join(MaterialBatch_.INVENTORY, JoinType.LEFT);
                    predicatesList.add(cb.equal(inventory.get(MaterialBatchChange_.ID),id));
                }
                query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                return query.getRestriction();
            }
        });
        return changeResult;
    }

    private Map<Long,Double> convertChangesToMapByActivity(List<MaterialBatchChange> changeList){
        Map<Long,Double> longAmountMap = new HashMap<>();
        for(MaterialBatchChange change : changeList){
            if(change.getInventoryManagementActivity()!=null){
                Long activityId = change.getInventoryManagementActivity().getId();
              if(longAmountMap.get(activityId)!=null){
                  longAmountMap.put(activityId,longAmountMap.get(activityId)+change.getChangeNum());
              }else{
                  longAmountMap.put(activityId,change.getChangeNum());
              }
            }
        }

        return longAmountMap;
    }

}
