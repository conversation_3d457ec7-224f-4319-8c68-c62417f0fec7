package cn.facilityone.shang.ai.service.impl;

import cn.facilityone.shang.ai.dto.AiSimpleProjectDTO;
import cn.facilityone.shang.ai.dto.AiSimpleUserDTO;
import cn.facilityone.shang.ai.properties.AiProperties;
import cn.facilityone.shang.ai.service.AiService;
import cn.facilityone.shang.projects.pro002.service.ProjectService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.security.model.User;
import cn.facilityone.xia.security.service.SecurityService;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:21
 * @Version 1.0
 */
@Service
public class AiServiceImpl implements AiService {

    @Autowired
    private AiProperties aiProperties;
    @Autowired
    private SecurityService securityService;
    @Autowired
    private ProjectService projectService;

    private static final Logger log = LoggerFactory.getLogger(AiServiceImpl.class);


    @Override
    public Result getUserInfo(HttpServletRequest request, String username) {
        if (!checkToken(request)) {
            return new Result(Result.CODE_400, "认证失败", null);
        }
        if (StrUtil.isBlank(username)) {
            return new Result(Result.CODE_400, "用户名不能为空", null);
        }

        User user = securityService.getUserByUsername(username);
        if (user == null) {
            return new Result(Result.CODE_400, "用户不存在", null);
        }

        AiSimpleUserDTO u = new AiSimpleUserDTO();
        u.setUserName(user.getUserName());
        u.setRealName(user.getRealName());
        u.setPhone(user.getPhone());
        u.setId(user.getId());
        u.setUserId(user.getId());

        // 获取用户授权的项目
        String[] projIds = user.getProject().split(SystemConst.STR_COMMA);
        Set<Long> projectIds = new HashSet<Long>();
        for (String projId : projIds) {
            if (StringUtils.isNotBlank(projId)) {
                projectIds.add(Long.valueOf(projId));
            }
        }
        List<AiSimpleProjectDTO> projectDTOList = projectService.findProjects(projectIds).values().stream()
                .filter(p -> !p.isDeleted())
                .map(p -> {
                    AiSimpleProjectDTO dto = new AiSimpleProjectDTO();
                    dto.setId(p.getId());
                    dto.setName(p.getName());
                    return dto;
                }).collect(Collectors.toList());
        u.setProjects(projectDTOList);

        return Result.data(u);
    }

    @Override
    public Result getProject(HttpServletRequest request, Long projectId) {
        if (!checkToken(request)) {
            return new Result(400, "认证失败", null);
        }

        List<AiSimpleProjectDTO> projectDTOList;
        Set<Long> allProjects = new HashSet<>();
        if (projectId != null && projectId > 0) {
            allProjects.add(projectId);
        } else {
            // 获取所有项目
            Long[] all = projectService.findAll();
            allProjects = new HashSet<>(Arrays.asList(all));
        }
        projectDTOList = projectService.findProjects(allProjects).values().stream()
                .map(p -> {
                    AiSimpleProjectDTO dto = new AiSimpleProjectDTO();
                    dto.setId(p.getId());
                    dto.setName(p.getName());
                    return dto;
                }).collect(Collectors.toList());
        return Result.data(projectDTOList);
    }

    private boolean checkToken(HttpServletRequest request) {
        if (!aiProperties.getEnabled() || StrUtil.isBlank(aiProperties.getBearerToken())) {
            return false;
        }
        String authorization = request.getHeader("Authorization");
        if (StrUtil.isBlank(authorization)) {
            return false;
        }
        if (authorization.length() < 8 || !authorization.startsWith("Bearer ")) {
            return false;
        }
        authorization = authorization.substring(7);
        String bearerTokenStr = aiProperties.getBearerToken();
        String[] split = bearerTokenStr.split(",");
        for (String s : split) {
            if (StrUtil.isNotBlank(s) && Objects.equals(authorization, s)) {
                return true;
            }
        }
        return false;
    }

}
