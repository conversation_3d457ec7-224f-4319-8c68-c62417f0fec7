package cn.facilityone.shang.preventive.pm002.dto;

import cn.facilityone.shang.entity.organize.Employee;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/6/22.
 */
public class MessageSendDTO implements Serializable {
    private List<Employee> employeeList;

    private String msgType;

    private String msgCode;

    private Map data;

    private Long pmId;

    private Long workOrderId;

    private Long todoId;
    private Long projectId;

    public Long getTodoId() {
        return todoId;
    }

    public void setTodoId(Long todoId) {
        this.todoId = todoId;
    }

    public Long getPmId() {
        return pmId;
    }

    public void setPmId(Long pmId) {
        this.pmId = pmId;
    }

    public Long getWorkOrderId() {
        return workOrderId;
    }

    public void setWorkOrderId(Long workOrderId) {
        this.workOrderId = workOrderId;
    }

    public MessageSendDTO(){

    }
    public MessageSendDTO(List<Employee> employeeList,String msgType,String msgCode,Map data){
        this.employeeList=employeeList;
        this.msgType=msgType;
        this.msgCode=msgCode;
        this.data=data;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public List<Employee> getEmployeeList() {
        return employeeList;
    }

    public void setEmployeeList(List<Employee> employeeList) {
        this.employeeList = employeeList;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getMsgCode() {
        return msgCode;
    }

    public void setMsgCode(String msgCode) {
        this.msgCode = msgCode;
    }

    public Map getData() {
        return data;
    }

    public void setData(Map data) {
        this.data = data;
    }
}
