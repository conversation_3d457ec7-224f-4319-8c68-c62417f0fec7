package cn.facilityone.shang.preventive.pm002.respository;

import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by charles.chen on 2015/5/21.
 */
public interface PMDateTodoRepository extends XiaRepository<PMDateTodo, Long> {

    @Deprecated
    List<PMDateTodo> findByGenStatusAndPm(boolean status,PreventiveMaintenance pm);

    @Deprecated
    List<PMDateTodo> findByDateTodoAndGenStatusAndPm(Date datetodo,boolean status,PreventiveMaintenance pm);

    @Deprecated
    List<PMDateTodo> findByDateTodoInAndGenStatusAndPm(Date[] datetodo,boolean status,PreventiveMaintenance pm);
    
    List<PMDateTodo> findByDateTodoInAndPm(Date[] datetodo,PreventiveMaintenance pm);

    @Query("select pd from #{#entityName} pd where pd.dateTodo in(?1) and pd.pm.id=?2 and pd.project =?3")
    List<PMDateTodo> findByDateTodoInAndPmListHardly(List<Date> datetodo,Long id,Long proId);

    @Deprecated
    List<PMDateTodo> findByPm(PreventiveMaintenance pm);

    @Query("select pd from #{#entityName} pd where pd.pm.id=?1 and pd.dateTodo=?2")
    List<PMDateTodo> findByPmIdAndTodoDate(Long pmId,Date dateTodo);

    @Query("SELECT count (pd.id) FROM #{#entityName} pd WHERE pd.pm.id =?1")
    Integer findCountPmTodoByPmId(Long id);

    @Query("SELECT count (pd.id) FROM #{#entityName} pd WHERE pd.pm.id =?1 and pd.project= ?2")
    Integer findCountPmTodoByPmIdHardly(Long id,Long proId);

    @Query("SELECT count (pd.id) FROM #{#entityName} pd WHERE pd.pm.id =?1 and pd.dateTodo >=?2")
    Integer findCountPmTodoByPmIdAndTodoDate(Long id,Date date);

    @Query("SELECT count (pd.id) FROM #{#entityName} pd WHERE pd.pm.id =?1 and pd.dateTodo >=?2 and pd.project= ?3 ")
    Integer findCountPmTodoByPmIdAndTodoDateHardly(Long id,Date date,Long proId);

    @Modifying
    @Query("update #{#entityName} todo set todo.genRemindNotice=0 where todo.pm.id=?1 and todo.dateTodo>?2")
    void updateTodoByRemind(Long pmtId,Date date);

    @Deprecated
    List<PMDateTodo> findBy(PreventiveMaintenance pm);


//    @Query("SELECT  pd FROM #{#entityName} pd LEFT JOIN FETCH pd.pm  WHERE pd.genStatus=?1 AND pd.genRemindNotice=?2 and pd.workOrders.status in (?3)")
//    List<PMDateTodo> findByGenStatusAndGenRemindNotice(boolean genstatus,boolean genremind,WorkOrder.WorkOrderStatus[] status);

//    @Query("SELECT new cn.facilityone.shang.preventive.pm001.dto.WoAndPmDateTodoEntityDTO(pd as pmn) FROM #{#entityName} pd WHERE pd.genStatus = ?1 AND pd.genRemindNotice = ?2 AND pd.workOrders.status = ?3 OR pd.workOrders.status = ?4")
//    List<WoAndPmDateTodoEntityDTO> findDtoByGenStatusAndGenRemindNotice(boolean genstatus,boolean genremind,WorkOrder.WorkOrderStatus statuCreate,WorkOrder.WorkOrderStatus statuDispatcher);


//    @Query("SELECT new cn.facilityone.shang.preventive.pm001.dto.WoAndPmDateTodoEntityDTO(pd as pmn,pd.workorders as workorder) FROM #{#entityName} pd WHERE pd.genStatus=?1 AND pd.genRemindNotice=?2 AND pd.workOrders.status=?3 OR pd.workOrders.status=?4")
//    List<WoAndPmDateTodoEntityDTO> findDtoByGenStatusAndGenRemindNotice(boolean genstatus,boolean genremind,WorkOrder.WorkOrderStatus statuCreate,WorkOrder.WorkOrderStatus statuDispatcher);

    @Deprecated
    @Modifying
    @Query("delete from #{#entityName} pd where pd.genStatus='0' AND pd.workOrders is null  AND  pd.pm.id=?1")
    void deleteByPmId(Long pmtId);

    @Query("select distinct pd from #{#entityName} pd LEFT JOIN FETCH pd.pm pm LEFT JOIN fetch  pm.equipments  LEFT JOIN  pm.pmTools   LEFT JOIN  fetch pm.estimatedWorkingTime wt left join pm.pmMaterials where pd.id=?1")
    PMDateTodo findTodoAndLazyPm(Long id);

    @Modifying
    @Query("delete from #{#entityName} pd where pd.genStatus=0  AND  pd.pm.id=?1")
    int deleteByPmIdAndWoIsNotNull(Long id);

    @Query("select  pd.workOrders from #{#entityName} pd where pd.id = ?1 ")
    List<WorkOrder> findWorkOrderById(Long id);

    @Query("select pd from #{#entityName} pd LEFT JOIN FETCH pd.workOrders wo where pd.id in (?1) ")
    List<PMDateTodo> findTodoAndWorkOrderByIds(Long[] ids);

    @Query("select todo.dateTodo from #{#entityName} todo  where  todo.pm.id=?1  and todo.dateTodo >?2 ORDER BY todo.dateTodo ASC")
    List<Date> findDateTodoByPmIdAndDate(Long pmId,Date date);

    @Query("select pd from #{#entityName} pd LEFT JOIN FETCH pd.workOrders wo where pd.id =?1 ")
    PMDateTodo findTodoAndWorkOrderById(Long id);

    @Modifying
    @Query("delete from #{#entityName} pd where pd.pm.id = ?1 and pd.genStatus=0")
    void deleteTodoHasNoWorkOrderByPmId(Long id);

    @Modifying
    @Query("update #{#entityName} todo set todo.activated=0 where todo.id = ?1")
    void updateTodoActivityById(Long id);

    @Query("select pd.dateTodo from #{#entityName} pd  where pm.id =?1 order by pd.dateTodo desc")
    List<Date> findDateByPmId(Long pmId);

    @Modifying
    @Query("update #{#entityName} todo set todo.genRemindNotice = 0 where todo.id in (?1)")
    void updateTodoSetWoAcvitityFalse(Long[] ids);

    @Query("select pd from #{#entityName} pd left join fetch pd.pm pm left join fetch pm.period left join fetch pm.estimatedWorkingTime  where pd.genStatus=0 and pd.genRemindNotice=0 and pm.id in (?1) and pd.dateTodo >=?2 and pd.dateTodo<?3")
    List<PMDateTodo> findByPmIdsAndNoGenAndNoGenRemindNotice(Set<Long> pmIds,Date start,Date end);

    @Query("select pd from #{#entityName} pd where pd.dateTodo >= ?1 and pd.dateTodo <= ?2 and pd.project = ?3")
    List<PMDateTodo> findTodoByDate(Date start,Date end, Long proId);

    @Query(value = "SELECT ptd.date_todo FROM pm_date_todo  ptd WHERE ptd.pm_id =?1 AND ptd.date_todo > ?2 and ptd.proj_id= ?3 ORDER BY ptd.date_todo ASC  LIMIT 1",nativeQuery =true)
    Date  findDateBYPmIdAndDate(Long pmId,Date date,Long proId);

    @Query("SELECT count (pd.id) from #{#entityName} pd where pd.dateTodo >= ?1 and pd.dateTodo <= ?2 and pd.project in ?3")
    Integer findTodoCountByDateHardly(Date start, Date end, List<Long> proList);

    @Query("SELECT count (pd.id) from #{#entityName} pd where pd.dateTodo >= ?1 and pd.dateTodo <= ?2 and pd.project = ?3")
    Integer findTodoCountByDateByAndProjectHardly(Date start, Date end, Long proId);

    /**
     * job使用
     * @param start
     * @param end
     * @param proId
     * @param id
     * @return
     */
//    @Query("SELECT pd from #{#entityName} pd LEFT JOIN fetch  pd.pm pm LEFT JOIN  fetch pm.equipments   LEFT JOIN  fetch pm.estimatedWorkingTime wt  where pd.dateTodo >= ?1 and pd.dateTodo <= ?2 and pd.project = ?3 and pd.genStatus =false and pd.pm.id=?4")
    @Query("SELECT distinct pd from #{#entityName} pd LEFT JOIN fetch  pd.pm pm   LEFT JOIN fetch  pm.equipments  LEFT JOIN  pm.pmTools   LEFT JOIN  fetch pm.estimatedWorkingTime wt left join pm.pmMaterials   where pd.dateTodo >= ?1 and pd.dateTodo <= ?2 and pd.project = ?3 and pd.genStatus =false and pd.pm.id=?4")
    List<PMDateTodo> findPmDateToForGenerateWorkOrderJobHardly(Date start, Date end, Long proId,Long id);

    @Query("select pd from #{#entityName} pd where pd.dateTodo >= ?1 and pd.dateTodo <= ?2 and pd.project = ?3")
    List<PMDateTodo> findTodoByDateHardly(Date time, Date time1, Long proId);

    /**
     * job使用
     * @param id
     * @param date
     * @return
     */
    @Query("SELECT pd FROM #{#entityName} pd WHERE pd.pm.id =?1 and pd.dateTodo >=?2")
    List<PMDateTodo> findPmTodoByPmIdAndTodoDate(Long id,Date date);
}
