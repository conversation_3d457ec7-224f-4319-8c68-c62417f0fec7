package cn.facilityone.shang.preventive.pm002.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableConditions;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.component.message.MessageSenderTool;
import cn.facilityone.shang.common.component.message.template.MessageWorkOrderProcessNoticeTemplate;
import cn.facilityone.shang.common.component.ztree.ZTreeNode;
import cn.facilityone.shang.common.repository.WorkOrderEquipmentRepository;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.common.staticmetamodel.MobilePush_;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.common.Notice;
import cn.facilityone.shang.entity.common.Notifier;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.organize.*;
import cn.facilityone.shang.entity.preventive.*;
import cn.facilityone.shang.entity.workorder.ApprovalTemplate;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.organize.org004.repository.BuildingRepository;
import cn.facilityone.shang.organize.org004.repository.FloorRepository;
import cn.facilityone.shang.organize.org004.repository.RoomRepository;
import cn.facilityone.shang.organize.org004.repository.SiteRepository;
import cn.facilityone.shang.preventive.common.util.PeriodDateUtil;
import cn.facilityone.shang.preventive.pm001.dto.PMMaterialDTO;
import cn.facilityone.shang.preventive.pm001.dto.PmShowDetailDTO;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeActivityRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import cn.facilityone.shang.preventive.pm001.service.PreventiveMaintenanceService;
import cn.facilityone.shang.preventive.pm001.staticmetamodel.PM_;
import cn.facilityone.shang.preventive.pm002.dto.*;
import cn.facilityone.shang.preventive.pm002.respository.PMDateTodoRepository;
import cn.facilityone.shang.preventive.pm002.staticmetamodel.PMDateTodo_;
import cn.facilityone.shang.report.report005.dto.PMtodoQueryRequest;
import cn.facilityone.shang.report.report005.dto.PMtooQueryDto;
import cn.facilityone.shang.stock.stock002.builder.InventoryBuilder;
import cn.facilityone.shang.workorder.common.constant.WorkOrderConstant;
import cn.facilityone.shang.workorder.common.repository.WorkOrderMaterialRepository;
import cn.facilityone.shang.workorder.common.service.WorkOrderService;
import cn.facilityone.shang.workorder.common.service.WorkOrderTaskService;
import cn.facilityone.shang.workorder.common.staticmetamodel.WorkOrder_;
import cn.facilityone.shang.workorder.wo001.dto.WorkOrderManagementRequest;
import cn.facilityone.shang.workorder.wo001.service.WorkOrderSocketService;
import cn.facilityone.shang.workorder.wo004.dto.WorkOrderApprovalRequestDTO;
import cn.facilityone.shang.workorder.wo004.repository.ApprovalTemplateRepository;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.message.common.MessageType;
import cn.facilityone.xia.message.service.MessageMobilePushSender;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import java.util.*;

/**
 * Created by charles.chen on 2015/5/25.
 */
@Service
public class PMDateTodoServiceImpl implements PMDateTodoService {
    @Autowired
    private PMDateTodoRepository pmDateTodoRepository;
    @Autowired
    private PreventiveMaintenanceRepository preventiveMaintenanceRepository;
    @Autowired
    private PMNoticeActivityRepository pmNoticeActivityRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private PreventiveMaintenanceService preventiveMaintenanceService;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private ApprovalTemplateRepository approvalTemplateRepository;
    @Autowired
    private WorkOrderEquipmentRepository workOrderEquipmentRepository;
    @Autowired
    private WorkOrderMaterialRepository workOrderMaterialRepository;
    @Autowired
    private SiteRepository siteRepository;
    @Autowired
    private BuildingRepository buildingRepository;
    @Autowired
    private FloorRepository floorRepository;
    @Autowired
    private RoomRepository roomRepository;
    @Autowired
    private WorkOrderTaskService workOrderTaskService;
    @Autowired
    private PMNoticeRepository pmNoticeRepository;
    @Autowired
    private PMDateTodoJobService pmDateTodoJobService;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private InventoryBuilder inventoryBuilder;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private MessageSenderTool messageSenderTool;
    @Autowired
    private WorkOrderService workOrderService;


    @Override
    public List<PMDateTodo> generateTodoList(PreventiveMaintenance pm) {

        Date start = pm.getDateFristTodo();
        start = DateUtil.buildDateOnFirstSecond(start);
        Date loopcontrol = new Date();
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(start);
        Date now = DateUtil.buildDateOnFirstSecond(new Date());
        Calendar nowCal = Calendar.getInstance();
        nowCal.setTime(now);

        long maintenanceTimeFrom = pm.getPeriod().getMaintenanceTimeFrom();
        long maintenanceTimeTo = pm.getPeriod().getMaintenanceTimeTo();
        int nowMonth = nowCal.get(Calendar.MONTH);

        int count=1;
        if(now.compareTo(start)>0){
            //首次维护时间在以前,如果当前月份不在有效月份内，找到下一次有效时间
            count = PeriodDateUtil.getCountForStartToToday(new Date(),pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue());
            do{
                loopcontrol = PeriodDateUtil.buildNextTime(start, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), (count));
                startCal.setTime(loopcontrol);
                count++;
            }while (!PeriodDateUtil.monthPeriodJudge(maintenanceTimeFrom,maintenanceTimeTo,startCal.get(Calendar.MONTH)+1));
            //将下次开始时间赋值给start
            start = loopcontrol;
        }

        List<PMDateTodo> pmDateTodoGroup = new ArrayList<PMDateTodo>();
        if (now.compareTo(start) != 0) {
            PMDateTodo firstPmtodo = new PMDateTodo();
            firstPmtodo.setDateTodo(start);
            firstPmtodo.setPm(pm);
            firstPmtodo.setGenRemindNotice(Boolean.FALSE);
            firstPmtodo.setGenStatus(Boolean.FALSE);
            pmDateTodoGroup.add(firstPmtodo);
        }

        Calendar nextCal = Calendar.getInstance();
        //如果下次维护不在月份内，不产生todo
        //默认生成条数
        for (int i = 0; i < PMDateTodo_.TODO_AUTO_GE_NUM; i++) {
            nextCal.setTime(PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), count+i));
            //如果下次维护不在月份，则不生成todo
            if(PeriodDateUtil.monthPeriodJudge(maintenanceTimeFrom,maintenanceTimeTo,nextCal.get(Calendar.MONTH)+1)) {
                if (!pm.getFixedPeriod() || pmDateTodoGroup.size() == 0) {
                    PMDateTodo pmtodo = new PMDateTodo();
                    pmtodo.setDateTodo(PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), count+i));
                    pmtodo.setPm(pm);
                    pmtodo.setGenRemindNotice(Boolean.FALSE);
                    pmtodo.setGenStatus(Boolean.FALSE);
                    pmDateTodoGroup.add(pmtodo);
                }
            }
        }
        List<Date> DateTodos = pmDateTodoRepository.findDateByPmId(pm.getId());
        if(DateTodos.size()>0){
            for(Date date : DateTodos) {
                for(int length = 0; length<pmDateTodoGroup.size(); length++){
                    if(pmDateTodoGroup.get(length).getDateTodo().equals(date)) {
                        pmDateTodoGroup.remove(length);
                    }
                }
            }
        }

        if(pmDateTodoGroup.size()>0) {
            pmDateTodoGroup = pmDateTodoRepository.save(pmDateTodoGroup);
        }

        return pmDateTodoGroup;
    }

    @Override
    public void deleteTodoListNoGenerateWO(PreventiveMaintenance pm) {
        //删除未通知的关联提醒通知记录，删除计划 删除未生成工单的计划
        //List<PMDateTodo> pmDateTodos=pmDateTodoRepository.findByGenStatusAndPm(Boolean.FALSE,preventiveMaintenanceRepository.findOne(pm.getId()));
        pmNoticeActivityRepository.deleteByPmIdAndWoIsNullAndUnNotify(pm.getId());
        pmDateTodoRepository.deleteByPmIdAndWoIsNotNull(pm.getId());

    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<PmDateTodoCalendarDTO> calculateCalerdarListByDate(final CalendarRequestDTO request) {
        List<PmDateTodoCalendarDTO> results = new ArrayList<>();
        List<PMDateTodo> pdts = new ArrayList<>();
        List<Long> pmIds = new ArrayList<>();
        List<PreventiveMaintenance> pms = this.findAllPmsInDate(request);
        Map<Long, PreventiveMaintenance> pmMaps = new HashMap<>();
        if (pms != null && pms.size() > 0) {
            //赋值
            for (PreventiveMaintenance pm : pms) {
                pmIds.add(pm.getId());
                pmMaps.put(pm.getId(), pm);
            }

            final List<Long> pmIdss = pmIds;
            //查出数据库中所有存在的todo信息
//            List<PMDateTodo> todoExits = this.findAllTodosByPmsAndInDate(request, pmIds.toArray(new Long[pmIds.size()]));
//            Map<Long, Map<Long, PMDateTodo>> pmAndLongtodoDateTodoMaps = this.parePmDateTodosToLongAndDateForManual(todoExits);

            List<PMDateTodo> todoExits = pmDateTodoRepository.findAll(new XiaSpecification<PMDateTodo>() {

                @Override
                public Root<PMDateTodo> toRoot(Root<PMDateTodo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
//                    root.fetch(PMDateTodo_.PM, JoinType.LEFT);
                    Fetch<?, ?> wos = root.fetch(PMDateTodo_.WORKORDERS, JoinType.LEFT);//考虑去重
                    wos.fetch(WorkOrder_.ESTIMATED_WORKING_TIME, JoinType.LEFT);
                    wos.fetch(WorkOrder_.ACTUAL_WORKING_TIME, JoinType.LEFT);
//                wos.fetch(WorkOrder_.INVENTORY_ACTIVITY, JoinType.LEFT);
                    return root;
                }

                @Override
                public Predicate toPredicate(Root<PMDateTodo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                    List<Predicate> predicatesList = new ArrayList<Predicate>();

                    //date
                    Date startDate =
                            DateUtil.buildDateOnFirstSecond(request.getStartDate());
                    Date endDate =
                            DateUtil.buildDateOnLastSecond(request.getEndDate());
                    predicatesList.add(cb.between(
                            root.<Date>get("dateTodo"), startDate, endDate));

                    //Pms
                    From<?, ?> pm = (From<?, ?>) root.fetch(PMDateTodo_.PM, JoinType.LEFT);
                    predicatesList.add(pm.get(PMDateTodo_.PM_ID).in(pmIdss));

                    query.distinct(true);
                    query.where(predicatesList.toArray(new Predicate[predicatesList
                            .size()]));
                    return query.getRestriction();
                }
            });
            Map<Long, Map<Long, PMDateTodo>> pmAndLongtodoDateTodoMaps = new HashMap<>();
            for (PMDateTodo todo : todoExits) {
                if (pmAndLongtodoDateTodoMaps.containsKey(todo.getPm().getId())) {
                    pmAndLongtodoDateTodoMaps.get(todo.getPm().getId()).put(todo.getDateTodo().getTime(), todo);
                } else {
                    Map<Long, PMDateTodo> data = new HashMap<>();
                    data.put(todo.getDateTodo().getTime(), todo);
                    pmAndLongtodoDateTodoMaps.put(todo.getPm().getId(), data);
                }

            }

            //查找PM在时间段内所有的todo 若已经生成记录，则使用数据库中的时间，若没有 则使用虚拟数据
            for (Long pmId : pmIds) {
                PreventiveMaintenance pm = pmMaps.get(pmId);
                Map<Long, PMDateTodo> dateMaps = pmAndLongtodoDateTodoMaps.get(pmId);
                pdts = this.findPmDateTodosByPmAndInDate(pm, dateMaps, request, pdts);
            }

            //虚拟数据
            for (PMDateTodo todo : pdts) {
                if (!todo.getPm().getFixedPeriod()) {
                    PmDateTodoCalendarDTO dto = this.parsePmDateTodoToDto(todo);
                    results.add(dto);
                }
            }
            //实体数据
            for (PMDateTodo todo : todoExits) {
                if (todo.isActivated()) {
                    PmDateTodoCalendarDTO dto = this.parsePmDateTodoToDto(todo);
                    results.add(dto);
                }
            }

        }

        //查出数据库中计划性维护设置已经删除但是todo已经生成工单的记录
        List<PMDateTodo> todoHasFinishes = this.findAllTodoHaveWOrkOrderAndNoPm(request);

        /* 已经生成工单但是设置删除的计划 额外添加进去日历中 */
        for (PMDateTodo todo : todoHasFinishes) {
            PmDateTodoCalendarDTO dto = this.parsePmDateTodoToDto(todo);
            results.add(dto);
        }
        return results;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public boolean generateTodoManual(PMDateTodo pmDateTodo) {
        List<PMDateTodo> todos = pmDateTodoRepository.findByPmIdAndTodoDate(pmDateTodo
                .getPm().getId(), pmDateTodo.getDateTodo());
        if (todos.size() > 0) {
            for (PMDateTodo todo : todos) {
                todo.setActivated(Boolean.TRUE);
            }
            pmDateTodoRepository.saveInBatch(todos);
            return true;
        }
        pmDateTodo.setPm(preventiveMaintenanceRepository.findOne(pmDateTodo
                .getPm().getId()));
        pmDateTodo.setGenStatus(Boolean.FALSE);
        pmDateTodo.setGenRemindNotice(Boolean.FALSE);
        pmDateTodoRepository.save(pmDateTodo);
        return true;
    }

    @Override
    public List<ZTreeNode> findSpaceTree() {
        //根据SortId 降序
        Sort sort = new Sort(new Sort.Order(Sort.Direction.ASC, "sort"),
                new Sort.Order(Sort.Direction.ASC, "id"));
        List<Site> sites = siteRepository.findAll(sort);
        Collections.sort(sites, new Comparator<Site>() {
            public int compare(Site org1, Site org2) {
                return org1.getSort() - org2.getSort();
            }
        });
        Sort sortBuilding = new Sort(new Sort.Order(Sort.Direction.ASC, "sort"),
                new Sort.Order(Sort.Direction.ASC, "id"));
        List<Building> buildings = buildingRepository.findAll(sort);
        Collections.sort(buildings, new Comparator<Building>() {
            public int compare(Building org1, Building org2) {
                return org1.getSort() - org2.getSort();
            }
        });
        Sort sortFloor = new Sort(new Sort.Order(Sort.Direction.ASC, "sort"),
                new Sort.Order(Sort.Direction.ASC, "id"));
        List<Floor> floors = floorRepository.findAll(sort);
        Collections.sort(floors, new Comparator<Floor>() {
            public int compare(Floor org1, Floor org2) {
                return org1.getSort() - org2.getSort();
            }
        });
        Sort sortRoom = new Sort(new Sort.Order(Sort.Direction.ASC, "sort"),
                new Sort.Order(Sort.Direction.ASC, "id"));
        List<Room> rooms = roomRepository.findAll(sort);
        Collections.sort(rooms, new Comparator<Room>() {
            public int compare(Room org1, Room org2) {
                return org1.getSort() - org2.getSort();
            }
        });
        List<ZTreeNode> spaceVo = new ArrayList<ZTreeNode>();
        spaceVo = this.buildSpaceWithSite(spaceVo, sites);
        spaceVo = this.buildSpaceWithBuilding(spaceVo, buildings);
        spaceVo = this.buildSpaceWithFloor(spaceVo, floors);
        spaceVo = this.buildSpaceWithRoom(spaceVo, rooms);

        return spaceVo;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public PmBaseInfoDTO findPmBaseByTodoId(Long pmId, Long todoId, Date tododate) {

        PmBaseInfoDTO result = new PmBaseInfoDTO();
        PreventiveMaintenance pm = preventiveMaintenanceRepository.findOneHardly(pmId);
        result = this.parsePmToDto(result, pm);
        //date_todo 关联工单信息
        if (todoId != null) {
            List<WorkOrder> workOrders = pmDateTodoRepository.findWorkOrderById(todoId);
            if (workOrders != null && workOrders.size() > 0) {
                for (WorkOrder workOrder : workOrders) {
                    workOrder.setPmDateTodo(null);
                    workOrder.setPreventiveMaintenance(null);
                }
                result.setWorkOrders(workOrders);
            }
            result.setTodoId(todoId);
        }

        List<PMMaterial> pmMaterials = preventiveMaintenanceRepository.findPMMaterialsById(pmId);
        List<Inventory> inventoryList = new ArrayList<>();
        for (PMMaterial pmMaterial : pmMaterials) {
            if (pmMaterial != null && pmMaterial.getInventory() != null) {
                inventoryList.add(pmMaterial.getInventory());
            }
        }
        if (inventoryList.size() > 0) {
            inventoryBuilder.init(inventoryList).addMaterial().addWarehouse();
        }

        if (pmMaterials != null && pmMaterials.size() > 0) {
            for (PMMaterial pmMaterial : pmMaterials) {
                if (pmMaterial == null) {
                } else {
                    List<PMMaterialDTO> pmMaterialDTOs = this.parsePMMaterial(pmMaterials);
                    result.setPmMaterialDTOList(pmMaterialDTOs);
                }
            }
        }

        return result;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public PmAndWoDetailDTO findPmAndWoByPmIdAndDate(Long pmId, Long todoId, Long woid, Long todoTime) {
        PmAndWoDetailDTO resultDto = new PmAndWoDetailDTO();
        PmShowDetailDTO psdto = preventiveMaintenanceService.findPmDtoById(pmId);
        BeanUtils.copyProperties(psdto, resultDto);
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(todoTime);
        Date todoDate = calendar.getTime();
        if (todoId != null) {
            PMDateTodo todo = pmDateTodoRepository.findOne(todoId);
            resultDto.setTodo(todo);
            WorkOrder workOrder = workOrderRepository.findOne(woid);
            resultDto.setWorkOrder(workOrder);

        }
        //提醒通知信息
        List<PMNotice> pmNotices = pmNoticeRepository.findByPmId(pmId);
        resultDto = this.parsePmNoticesToDto(resultDto, pmNotices, todoDate);
        return resultDto;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void deleteById(Long id) {
        //删除关联的工作提醒activity
        PMDateTodo todo = pmDateTodoRepository.findTodoAndLazyPm(id);
        Calendar cal = Calendar.getInstance();
        PreventiveMaintenance pm = todo.getPm();
        List<Date> aheadDate = new ArrayList<>();
        Integer ahead = pm.getAhead();
        List<Integer> noticeAheadDays = pmNoticeRepository.findTotalAheadDaysByPmIdAndType(pm.getId(), PMNotice.PMNoticeType.REMIND, ahead);
        for (Integer day : noticeAheadDays) {
            cal.setTime(todo.getDateTodo());
            cal.add(Calendar.DAY_OF_YEAR, -day);
            aheadDate.add(cal.getTime());
        }
        if (aheadDate.size() > 0) {
            pmNoticeActivityRepository.deleteByPmIdAndNoticeDate(pm.getId(), aheadDate.toArray(new Date[aheadDate.size()]));
        }
        //删除计划 =修改计划的活动状态 设置为失效
        pmDateTodoRepository.updateTodoActivityById(id);

    }

    @Override
    @XiaTransactional(readOnly = false)
    public void deleteByRequest(PmDateToDoRequestDTO requestDTO) {
        if (requestDTO.getPmId() == null || requestDTO.getSelectDate() == null) {
            return;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(requestDTO.getSelectDate());
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date currentDate = calendar.getTime();
        List<PMDateTodo> todos = this.generateTodoByPmIdAndDate(requestDTO.getPmId(), currentDate);
        for (PMDateTodo todo : todos) {
            if (todo.getDateTodo().compareTo(currentDate) == 0) {
                todo.setActivated(Boolean.FALSE);
            }
        }
        pmDateTodoRepository.saveInBatch(todos);
    }

    @Override
    @XiaTransactional(readOnly = false)
    public List<WorkOrder> generateWoById(Long id) {
        PMDateTodo todo = pmDateTodoRepository.findTodoAndLazyPm(id);
        List<WorkOrder> workOrders = pmDateTodoJobService.generateWorkOrderByToDoDate(todo);
        List<Long> todoIds = new ArrayList<>();
        if (workOrders != null && workOrders.size() > 0) {
            workOrders = pmDateTodoJobService.connectWorkOrder(workOrders);
            todoIds.add(id);
            pmDateTodoRepository.updateTodoSetWoAcvitityFalse(todoIds.toArray(new Long[todoIds.size()]));
            pmNoticeActivityRepository.deleteByTodoIdsIn(todoIds.toArray(new Long[todoIds.size()]));
        }
        // 发送socket提示刷新
//        if (null != workOrders && workOrders.size() > 0) {
//            workOrderSocketService.newWorkOrderSocket(workOrders.size(), workOrders);
//        }
        return workOrders;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public List<WorkOrder> generateWoByRequest(PmDateToDoRequestDTO requestDTO) {
        if (requestDTO.getPmId() == null || requestDTO.getSelectDate() == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(requestDTO.getSelectDate());
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date currentDate = calendar.getTime();
        List<WorkOrder> workOrders = new ArrayList<>();
        List<PMDateTodo> todos = this.generateTodoByPmIdAndDate(requestDTO.getPmId(), currentDate);
        pmDateTodoRepository.saveInBatch(todos);
        List<Long> todoIds = new ArrayList<>();
        for (PMDateTodo todo : todos) {
            if (todo.getDateTodo().compareTo(currentDate) == 0) {
                workOrders = pmDateTodoJobService.generateWorkOrderByToDoDate(todo);
                if (workOrders != null && workOrders.size() > 0) {
                    todoIds.add(todo.getId());
                    workOrders = pmDateTodoJobService.connectWorkOrder(workOrders);
                }
            }

        }
        if (CollectionUtils.isNotEmpty(todoIds)) {
            pmDateTodoRepository.updateTodoSetWoAcvitityFalse(todoIds.toArray(new Long[todoIds.size()]));
            pmNoticeActivityRepository.deleteByTodoIdsIn(todoIds.toArray(new Long[todoIds.size()]));
        }
        // 发送socket提示刷新
//        if (null != workOrders && workOrders.size() > 0) {
//            workOrderSocketService.newWorkOrderSocket(workOrders.size(), workOrders);
//        }
        return workOrders;
    }

    @Override
    public Page<WorkOrder> findWorkOrderByReqyest(final DataTableRequest request, final Pageable pageable) {

        Page<WorkOrder> result = workOrderRepository.findAll(new Specification<WorkOrder>() {
            @Override
            public Predicate toPredicate(Root<WorkOrder> root, CriteriaQuery<?> criteriaQuery,
                                         CriteriaBuilder cb) {
                List<Predicate> predicatesList = new ArrayList<>();

                List<DataTableConditions> conditions = request.getConditions();
                List<Predicate> cpreL = dataTableService.buildConditionsSearch(request.getConditions(), root, cb);
                predicatesList.addAll(cpreL);
                List<DataTableColumn> dataTableColumns = request.getColumns();
                for (DataTableColumn column : dataTableColumns) {
                    if (!StringUtils.isBlank(column.getSearchText())) {
                        if ("code".equals(column.getName())) {
                            predicatesList.add(cb.like(root.<String>get("code"), dataTableService.buildLikeText(column.getSearchText())));
                        }
                        if ("status".equals(column.getName())) {

                            Class<? extends Enum> enumClass = WorkOrder.WorkOrderStatus.class;
                            if ("STOP".equals(column.getSearchText())) {
                                Expression expressionStop = cb.equal(root.<Integer>get("status"), Enum.valueOf(enumClass, "STOP"));
                                Expression expressionStop_N = cb.equal(root.<Integer>get("status"), Enum.valueOf(enumClass, "STOP_N"));
                                predicatesList.add(cb.or(expressionStop, expressionStop_N));
                            } else {
                                predicatesList.add(cb.equal(root.<Integer>get("status"), Enum.valueOf(enumClass, column.getSearchText())));
                            }
                        }
                    }
                }
                criteriaQuery.distinct(true);
                criteriaQuery.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                criteriaQuery.orderBy(cb.desc(root.<Date>get(WorkOrderConstant.CREATED_DATE)));
                return criteriaQuery.getRestriction();
            }
        }, pageable);
        for (WorkOrder workOrder : result.getContent()) {
            if (WorkOrder.WorkOrderStatus.STOP_N.equals(workOrder.getStatus())) {
                workOrder.setStatus(WorkOrder.WorkOrderStatus.STOP);
            }
        }
        return result;
    }

    /**
     * request a approval for work order and send message to approver
     *
     * @param workOrderList
     * @param pm
     */
    @Override
    public void handleApprovalForPPM(List<WorkOrder> workOrderList, PreventiveMaintenance pm) {
        PMNotice approvalNotice = pmNoticeRepository.findApprovalNoticeByPmId(pm.getId());
        if (approvalNotice == null || approvalNotice.getNotice() == null) {
            return;
        }
        WorkOrderManagementRequest request = new WorkOrderManagementRequest();
        WorkOrderApprovalRequestDTO dto = new WorkOrderApprovalRequestDTO();
        dto.setTemplateId(getPpmApprovalTemplateId());
        Notice notice = approvalNotice.getNotice();
        List<Notifier> notifiers = notice.getNotifiers();
        List<Long> employeeIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(notifiers)) {
            for (Notifier notifier : notifiers) {
                employeeIds.add(notifier.getInformedId());
            }
            dto.setApproverIds(employeeIds);
        }
        request.setApprovalRequest(dto);
        Employee user = commonUserService.findProjectsLoginEmployee();
        
        String code = MessageWorkOrderProcessNoticeTemplate.CODE_WORKORDER_PROCESS_NOTICE;
        
        for (WorkOrder workOrder : workOrderList) {
            workOrderTaskService.requestApproval(workOrder, request, user);

            if (CollectionUtils.isNotEmpty(employeeIds) && StringUtils.isNotEmpty(notice.getMsgType())) {
                List<Employee> employees = employeeRepository.findAllWithUser(employeeIds);
                Map<String,Map<String,Object>> typeData = new HashMap<>();
                Map<String, Object> params = new HashMap<>();
                params.put(MobilePush_.TYPE, MobilePush_.WORK_ORDER);
                params.put(MobilePush_.WO_ID, workOrder.getId());
                params.put(MobilePush_.WO_STATUS, workOrder.getStatus().ordinal());
                params.put(MobilePush_.PROJECT_ID, workOrder.getProject());
                typeData.put(MessageType.MPUSH,params);
                
                messageSenderTool.send(code, MessageWorkOrderProcessNoticeTemplate.buildData(workOrder.getCode(), "ppm-approval", workOrder.getDescription())
                        , typeData, employees, workOrder.getProject(), notice.getMsgType());
                
            }
        }

    }

    @Override
    @XiaTransactional(readOnly = true)
    public List<PMtooQueryDto> queryPmTodos(PMtodoQueryRequest request) {
        List<PMtooQueryDto> dataRes=new ArrayList<>();
        List<PmDateTodoCalendarDTO> calDtos=this.calculateCalerdarListByRequest(request);
        if(calDtos!=null && calDtos.size()>0){
            //SORT 排序
            Collections.sort(calDtos,new Comparator<PmDateTodoCalendarDTO>(){
                public int compare(PmDateTodoCalendarDTO arg0, PmDateTodoCalendarDTO arg1) {
                    return arg0.getStartDate().compareTo(arg1.getStartDate());
                }
            });

            List<Long> todoIds=new ArrayList<>();
            List<PMDateTodo> todos=new ArrayList<>();
            for(PmDateTodoCalendarDTO dto : calDtos){
                if(dto.getTodoId()!=null){
                    todoIds.add(dto.getTodoId());
                }
            }
            Map<Long,List<Long>> longListMap=new HashMap<>();
            if(todoIds!=null && todoIds.size()>0){
                List<WorkOrder> workOrders=workOrderRepository.findByTodoIdsIn(todoIds.toArray(new Long[todoIds.size()]));
                //PmDateTodoId:List<WorkOrderId>
                longListMap=this.convertWorkOrderListToMap(workOrders);
            }

            Map<Long,PMDateTodo> todoMaps =new HashMap<>();
            if(todoIds.size()>0){
                todos=pmDateTodoRepository.findTodoAndWorkOrderByIds(todoIds.toArray(new Long[todoIds.size()]));
                todoMaps=this.parsePmDateTodosToMap(todos);
            }

            Map<Long,List<WorkOrder.WorkOrderStatus>> statusM = workOrderService.findStatusByDateToDoId(todoIds);

            //拼装数据
            for(PmDateTodoCalendarDTO dto : calDtos){
                PMtooQueryDto data=new PMtooQueryDto();
                data.setPmId(dto.getPmId());
                data.setPmName(dto.getTitle());
                Calendar cal=Calendar.getInstance();
                cal.setTime(dto.getStartDate());
                data.setDateTodo(cal.getTimeInMillis());
                if(dto.getTodoId()!=null){
                    data.setPmtodoId(dto.getTodoId());
                    PMDateTodo todo=todoMaps.get(dto.getTodoId());
                    data.setGenStatus(todo.getGenStatus());
                    data.setWoIds(longListMap.get(dto.getTodoId()));
                }
                data.setStatus(this.judgeStatus(todoMaps.get(dto.getTodoId()),statusM.get(dto.getTodoId())));
                dataRes.add(data);
            }
        }

        return dataRes;
    }

    /**
     * init and get template id for ppm approval
     *
     * @return
     */
    private Long getPpmApprovalTemplateId() {
        ApprovalTemplate template = approvalTemplateRepository.findByNameHardly(PM_.APPROVAL_TEMPLATE_NAME);
        if (template == null) {
            template = new ApprovalTemplate();
            template.setDescription("This is only for ppm approval and should be deleted once created!");
            template.setProject(ProjectContext.getCurrentProject());
            template.setName(PM_.APPROVAL_TEMPLATE_NAME);
            template.setType(ApprovalTemplate.ApprovalType.ONE);
            template.setDeleted(true);
            template = approvalTemplateRepository.save(template);
        }
        return template.getId();
    }

    private List<PMDateTodo> generateTodoByPmIdAndDate(Long pmId, Date date) {
        List<PMDateTodo> todos = new ArrayList<>();
        List<Date> dates = pmDateTodoRepository.findDateByPmId(pmId);
        PreventiveMaintenance pm = preventiveMaintenanceRepository.findOneHardly(pmId);
        Date lastDate = dates.get(0);
        Integer phaseDifference = PeriodDateUtil.getCountForStartToToday(date, lastDate, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue());
        lastDate = PeriodDateUtil.buildNextTime(lastDate, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(),1);
        Map<Date, PMDateTodo> maps = pmDateTodoJobService.generateTodoByDateAndTimes(pm, lastDate, phaseDifference);
        List<PMDateTodo> pmDateTodoList = new ArrayList<>(maps.values());
        todos = pmDateTodoList;
        return todos;
    }

    private PmAndWoDetailDTO parsePmNoticesToDto(PmAndWoDetailDTO pdto, List<PMNotice> pmNotices, Date todoDate) {
        List<PmNoticeRemindDateDTO> pmNoticeRemindDateDTOs = new ArrayList<>();
        for (PMNotice pmNotice : pmNotices) {
            if (pdto.getWorkOrder() != null) {
                if (PMNotice.PMNoticeType.RESPONSE.equals(pmNotice.getType())) {
                    List<Date> unResponseDate = pmNoticeActivityRepository.findWoNoticeActivity(pdto.getPm().getId(), pmNotice.getId());
                    if (unResponseDate != null && unResponseDate.size() > 0) {
                        pdto.setUnResponseDate(unResponseDate.get(0));
                    }


                } else if (PMNotice.PMNoticeType.COMPLETE.equals(pmNotice.getType())) {
                    List<Date> unCompleteDate = pmNoticeActivityRepository.findWoNoticeActivity(pdto.getPm().getId(), pmNotice.getId());
                    if (unCompleteDate != null && unCompleteDate.size() > 0) {
                        pdto.setUnCompleteDate(unCompleteDate.get(0));
                    }
                }
                if (PMNotice.PMNoticeType.REMIND.equals(pmNotice.getType())) {
                    PmNoticeRemindDateDTO dto = new PmNoticeRemindDateDTO();
                    List<Date> remindDate = pmNoticeActivityRepository.findWoNoticeActivity(pdto.getPm().getId(), pmNotice.getId());
                    dto.setDay(pmNotice.getAhead());
                    if (remindDate != null && remindDate.size() > 0) {
                        dto.setNoticeDate(remindDate.get(0));
                    }
                    pmNoticeRemindDateDTOs.add(dto);
                }
            }
        }
        pdto.setRemindNotices(pmNoticeRemindDateDTOs);
        return pdto;
    }

    private Map<Long, List<PMNoticeActivity>> parsePMNoticeActivityToMap(List<PMNoticeActivity> pmNoticeActivities) {
        Map<Long, List<PMNoticeActivity>> maps = new HashMap<>();
        for (PMNoticeActivity pmNoticeActivity : pmNoticeActivities) {
            if (maps.containsKey(pmNoticeActivity.getPmNotice().getId())) {
                maps.get(pmNoticeActivity.getPmNotice().getId()).add(pmNoticeActivity);
            } else {
                List<PMNoticeActivity> pns = new ArrayList<>();
                pns.add(pmNoticeActivity);
                maps.put(pmNoticeActivity.getPmNotice().getId(), pns);
            }
        }
        return maps;
    }


    private PmBaseInfoDTO parsePmToDto(PmBaseInfoDTO dto, PreventiveMaintenance pm) {
        String periodName = pm.getPeriod().getValue() + XiaMesssageResource.getMessage(pm.getPeriod().getType().toString());
        dto.setPeriodName(periodName);
        dto.setPmId(pm.getId());
        dto.setPmName(pm.getName());
        dto.setInfluence(pm.getInfluence());
        dto.setPriorityName(XiaMesssageResource.getMessage(pm.getPriority().getClass().getSimpleName() + SystemConst.STR_DOT + pm.getPriority().toString()));
        //dto.setWorkTeamName(pm.getWorkteam().getName());
        return dto;
    }

    private List<PMMaterialDTO> parsePMMaterial(List<PMMaterial> pmMaterials) {
        List<PMMaterialDTO> dtos = new ArrayList<>();
        for (PMMaterial pmMaterial : pmMaterials) {
            PMMaterialDTO ptdo = new PMMaterialDTO();
            if (pmMaterial.getInventory() != null && pmMaterial.getInventory().getMaterial() != null) {
                ptdo.setName(pmMaterial.getInventory().getMaterial().getName());
                ptdo.setBrand(pmMaterial.getInventory().getMaterial().getBrand());
                ptdo.setModel(pmMaterial.getInventory().getMaterial().getModel());
                ptdo.setUnit(pmMaterial.getInventory().getMaterial().getUnit());
            }

            ptdo.setComment(pmMaterial.getComment());
            ptdo.setId(pmMaterial.getId());
            ptdo.setQuantity(pmMaterial.getAmount());
            dtos.add(ptdo);
        }
        return dtos;
    }

    private List<ZTreeNode> buildSpaceWithSite(List<ZTreeNode> zTreeNodes, List<Site> sites) {
        for (Site site : sites) {
            ZTreeNode treeSite;
            //如果没有父级元素则设置父级为0 树形默认0为根节点
            treeSite = new ZTreeNode(site.getId(), site.getName(),
                    (long) 0);
            zTreeNodes.add(treeSite);
        }
        return zTreeNodes;
    }

    private List<ZTreeNode> buildSpaceWithBuilding(List<ZTreeNode> zTreeNodes, List<Building> buildings) {
        for (Building building : buildings) {
            ZTreeNode treeSiteBuilding;
            //如果没有父级元素则设置父级为0 树形默认0为根节点
            treeSiteBuilding = new ZTreeNode(building.getId(), building.getName(),
                    building.getSite().getId());
            zTreeNodes.add(treeSiteBuilding);
        }

        return zTreeNodes;
    }

    private List<ZTreeNode> buildSpaceWithFloor(List<ZTreeNode> zTreeNodes, List<Floor> floors) {
        for (Floor floor : floors) {
            ZTreeNode treeFloor;
            //如果没有父级元素则设置父级为0 树形默认0为根节点
            treeFloor = new ZTreeNode(floor.getId(), floor.getName(),
                    floor.getBuilding().getId());
            zTreeNodes.add(treeFloor);
        }
        return zTreeNodes;
    }

    private List<ZTreeNode> buildSpaceWithRoom(List<ZTreeNode> zTreeNodes, List<Room> rooms) {
        for (Room room : rooms) {
            ZTreeNode treeRoom;
            //如果没有父级元素则设置父级为0 树形默认0为根节点
            treeRoom = new ZTreeNode(room.getId(), room.getName(),
                    room.getFloor().getId());
            zTreeNodes.add(treeRoom);
        }
        return zTreeNodes;
    }


    private List<PMDateTodo> comparePmDateTodos(List<PMDateTodo> pdts, Map<Long, Map<Date, WorkOrder>> pmAndtodoDateMaps) {
        if (pmAndtodoDateMaps.size() == 0) {
            return pdts;
        }

        for (PMDateTodo todo : pdts) {
            Map<Date, WorkOrder> dwMap = pmAndtodoDateMaps.get(todo.getPm().getId());
            if (dwMap != null && dwMap.size() > 0 && dwMap.containsKey(todo.getDateTodo())) {
                //todo.setWorkOrder(dwMap.get(todo.getDateTodo()));
            }

        }

        return pdts;

    }

    private PmDateTodoCalendarDTO parsePmDateTodoToDto(PMDateTodo todo) {

        PmDateTodoCalendarDTO pcd = new PmDateTodoCalendarDTO();
        pcd.setTodoId(todo.getId());
        pcd.setPmId(todo.getPm().getId());
        pcd.setEndDate(todo.getDateTodo());
        pcd.setStartDate(todo.getDateTodo());
        pcd.setTitle(todo.getPm().getName());
        //状态
        pcd.setWoStatus(this.judgeTodoStatusByWorkOrder(todo));
        return pcd;
    }

    private String judgeTodoStatusByWorkOrder(PMDateTodo todo) {
        if (todo.getWorkOrders() != null) {
            //未开始-状态集
            Set<WorkOrder.WorkOrderStatus> unBegin = new HashSet<>();
            //处理中-状态集
            Set<WorkOrder.WorkOrderStatus> process = new HashSet<>();
            //已完成,已验证,已存档-状态集
            Set<WorkOrder.WorkOrderStatus> complete = new HashSet<>();
            List<WorkOrder> workOrders = todo.getWorkOrders();
            for (WorkOrder workOrder : workOrders) {
                if(workOrder.getStatus() == WorkOrder.WorkOrderStatus.CREATE
                        ||workOrder.getStatus() == WorkOrder.WorkOrderStatus.DISPATCHE) {
                    unBegin.add(workOrder.getStatus());
                }
                if(workOrder.getStatus() == WorkOrder.WorkOrderStatus.PROCESS
                        || workOrder.getStatus() == WorkOrder.WorkOrderStatus.APPROVE
                        || workOrder.getStatus() == WorkOrder.WorkOrderStatus.STOP
                        || workOrder.getStatus() == WorkOrder.WorkOrderStatus.STOP_N) {
                    process.add(workOrder.getStatus());
                }
                if(workOrder.getStatus() == WorkOrder.WorkOrderStatus.FINISH
                        || workOrder.getStatus() == WorkOrder.WorkOrderStatus.TERMINATE
                        || workOrder.getStatus() == WorkOrder.WorkOrderStatus.VALIDATATION
                        || workOrder.getStatus() == WorkOrder.WorkOrderStatus.CLOSE) {
                    complete.add(workOrder.getStatus());
                }
            }
            Boolean hasUnBegin = CollectionUtils.isNotEmpty(unBegin);
            Boolean hasProcess = CollectionUtils.isNotEmpty(process);
            Boolean hasComplete = CollectionUtils.isNotEmpty(complete);
            if (hasUnBegin || hasProcess || hasComplete) {
                if (hasUnBegin && !hasProcess && !hasComplete) {
                    return PMDateTodo_.TODO_STATUS_UNBEGIN;
                } else if (hasComplete && !hasUnBegin && !hasProcess) {
                    return PMDateTodo_.TODO_STATUS_COMPLETE;
                } else {
                    return PMDateTodo_.TODO_STATUS_PROCESS;
                }
            }
        }
        return PMDateTodo_.TODO_STATUS_UNBEGIN;
    }


    private List<PreventiveMaintenance> findAllPmsInDate(final CalendarRequestDTO request) {

        List<PreventiveMaintenance> pms = preventiveMaintenanceRepository.findAll(new XiaSpecification<PreventiveMaintenance>() {
            @Override
            public Predicate toPredicate(Root<PreventiveMaintenance> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicatesList = new ArrayList<Predicate>();
                List<Predicate> predicatesForOr = new ArrayList<Predicate>();

                //cb.or(predicatesForOr.toArray(new Predicate[predicatesForOr.size()]));

                //合并关联，避免分类+设备查询时，产生多余的  left outer join pm_eq
                boolean searchEqu = ( request.getEquId() != null );
                boolean searchEquSys = ( request.getEquSysId() != null && request.getEquSysId().length > 0 );

                if ( searchEqu || searchEquSys ) {
                    From<?, ?> equForm = root.join("equipments", JoinType.LEFT);

                    if( searchEqu ){
                        predicatesList.add(cb.equal(equForm.get("id"), request.getEquId()));
                    }

                    if( searchEquSys ){
                        From<?, ?> equSys = equForm.join("equipmentSystem", JoinType.LEFT);
                        predicatesList.add(equSys.get("id").in(request.getEquSysId()));
                    }
                }

                boolean searchSite = ( request.getSiteId() != null && request.getSiteId().length > 0);
                boolean searchBuilding = ( request.getBuildingId() != null && request.getBuildingId().length > 0);
                boolean searchFloor = ( request.getFloorId() != null && request.getFloorId().length > 0 );
                boolean searchRoom = (request.getRoomId() != null && request.getRoomId().length > 0);

                if ( searchSite || searchBuilding || searchFloor || searchRoom ){

                    From<?, ?> spaceForm = root.join("spaces", JoinType.LEFT);

                    if(searchSite){
                        From<?, ?> siteForm = spaceForm.join("site", JoinType.LEFT);
                        predicatesForOr.add(cb.or(siteForm.get("id").in(request.getSiteId())));
                    }

                    if(searchBuilding){
                        From<?, ?> buildingForm = spaceForm.join("building", JoinType.LEFT);
                        predicatesForOr.add(cb.or(buildingForm.get("id").in(request.getBuildingId())));
                    }

                    if(searchFloor){
                        From<?, ?> floorForm = spaceForm.join("floor", JoinType.LEFT);
                        predicatesForOr.add(cb.or(floorForm.get("id").in(request.getFloorId())));
                    }

                    if(searchRoom){
                        From<?, ?> roomForm = spaceForm.join("room", JoinType.LEFT);
                        predicatesForOr.add(cb.or(roomForm.get("id").in(request.getRoomId())));
                    }
                }

                predicatesList.add(cb.and(predicatesForOr.toArray(new Predicate[predicatesForOr.size()])));

                query.distinct(Boolean.TRUE);
                query.where(predicatesList.toArray(new Predicate[predicatesList
                        .size()]));
                return query.getRestriction();
            }

            @Override
            public Root<PreventiveMaintenance> toRoot(Root<PreventiveMaintenance> root,
                                                      CriteriaQuery<?> query, CriteriaBuilder cb) {
                root.fetch(PM_.PERIOD, JoinType.LEFT);
                root.fetch(PM_.ESTIMATED_WORKING_TIME, JoinType.LEFT);
                root.fetch(PM_.WORKORDER_PROCESS, JoinType.LEFT);
                return null;
            }
        });

        return pms;
    }

    private List<PMDateTodo> findAllTodosByPmsAndInDate(final CalendarRequestDTO request, final Long[] pmIds) {

        List<PMDateTodo> pms = pmDateTodoRepository.findAll(new XiaSpecification<PMDateTodo>() {

            @Override
            public Root<PMDateTodo> toRoot(Root<PMDateTodo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                root.fetch(PMDateTodo_.PM, JoinType.LEFT);
                Fetch<?, ?> wos = root.fetch(PMDateTodo_.WORKORDERS, JoinType.LEFT);//考虑去重
                wos.fetch(WorkOrder_.ESTIMATED_WORKING_TIME, JoinType.LEFT);
                wos.fetch(WorkOrder_.ACTUAL_WORKING_TIME, JoinType.LEFT);
//                wos.fetch(WorkOrder_.INVENTORY_ACTIVITY, JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<PMDateTodo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicatesList = new ArrayList<Predicate>();

                //date
                Date startDate =
                        DateUtil.buildDateOnFirstSecond(request.getStartDate());
                Date endDate =
                        DateUtil.buildDateOnLastSecond(request.getEndDate());
                predicatesList.add(cb.between(
                        root.<Date>get("dateTodo"), startDate, endDate));

                //Pms
                From<?, ?> pm = root.join(PMDateTodo_.PM, JoinType.LEFT);
                predicatesList.add(pm.get(PMDateTodo_.PM_ID).in(pmIds));

                query.distinct(true);
                query.where(predicatesList.toArray(new Predicate[predicatesList
                        .size()]));
                return query.getRestriction();
            }
        });

        return pms;
    }

    private List<PMDateTodo> findAllTodoHaveWOrkOrderAndNoPm(final CalendarRequestDTO request) {

        List<PMDateTodo> pms = pmDateTodoRepository.findAll(new XiaSpecification<PMDateTodo>() {

            @Override
            public Root<PMDateTodo> toRoot(Root<PMDateTodo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                //一对多关系 需要考虑去重
                Fetch<?, ?> woroot = root.fetch("workOrders", JoinType.LEFT);
//                woroot.fetch(WorkOrder_.INVENTORY_ACTIVITY, JoinType.LEFT);
                woroot.fetch(WorkOrder_.ACTUAL_WORKING_TIME, JoinType.LEFT);
                woroot.fetch(WorkOrder_.ESTIMATED_WORKING_TIME, JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<PMDateTodo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicatesList = new ArrayList<Predicate>();

                //date
                Date startDate =
                        DateUtil.buildDateOnFirstSecond(request.getStartDate());
                Date endDate =
                        DateUtil.buildDateOnLastSecond(request.getEndDate());
                predicatesList.add(cb.between(
                        root.<Date>get("dateTodo"), startDate, endDate));

                //Pms
//                From<?, ?> pm = root.join("pm", JoinType.LEFT);
                From<?, ?> pm = (From<?, ?>) root.fetch(PMDateTodo_.PM, JoinType.LEFT);
                predicatesList.add(cb.equal(pm.<Boolean>get("deleted"), Boolean.TRUE));
                //多项目支持
                predicatesList.add(cb.equal(pm.<Long>get("project"), ProjectContext.getCurrentProject()));

                query.distinct(true);
                query.where(predicatesList.toArray(new Predicate[predicatesList
                        .size()]));
                return query.getRestriction();
            }
        });

        return pms;
    }

    private Map<Long, Map<Date, PMDateTodo>> parePmDateTodosForManual(List<PMDateTodo> todos) {
        Map<Long, Map<Date, PMDateTodo>> resultMap = new HashMap<>();
        for (PMDateTodo todo : todos) {
            if (resultMap.containsKey(todo.getPm().getId())) {
                resultMap.get(todo.getPm().getId()).put(todo.getDateTodo(), todo);
            } else {
                Map<Date, PMDateTodo> data = new HashMap<>();
                data.put(todo.getDateTodo(), todo);
                resultMap.put(todo.getPm().getId(), data);
            }

        }
        return resultMap;
    }

    private Map<Long, Map<Long, PMDateTodo>> parePmDateTodosToLongAndDateForManual(List<PMDateTodo> todos) {
        Map<Long, Map<Long, PMDateTodo>> resultMap = new HashMap<>();
        for (PMDateTodo todo : todos) {
            if (resultMap.containsKey(todo.getPm().getId())) {
                resultMap.get(todo.getPm().getId()).put(todo.getDateTodo().getTime(), todo);
            } else {
                Map<Long, PMDateTodo> data = new HashMap<>();
                data.put(todo.getDateTodo().getTime(), todo);
                resultMap.put(todo.getPm().getId(), data);
            }

        }
        return resultMap;
    }

    private Map<Long, Map<Date, WorkOrder>> parePmDateTodos(List<PMDateTodo> todos) {
        Map<Long, Map<Date, WorkOrder>> resultMap = new HashMap<>();
        for (PMDateTodo todo : todos) {
//            if(resultMap.containsKey(todo.getPm().getId())){
//                resultMap.get(todo.getPm().getId()).put(todo.getDateTodo(), todo.getWorkOrder());
//            }else{
//                Map<Date,WorkOrder> data=new HashMap<>();
//                data.put(todo.getDateTodo(), todo.getWorkOrder());
//                resultMap.put(todo.getPm().getId(),data);
//            }

        }
        return resultMap;
    }

    private List<PMDateTodo> findPmDateTodosByPmAndInDate(PreventiveMaintenance pm, Map<Long, PMDateTodo> pmMap, CalendarRequestDTO request, List<PMDateTodo> pdts) {
        //设置开始时间 变量~
        Calendar cal = Calendar.getInstance();
        cal.setTime(pm.getDateFristTodo()); //PM 首次维护日期
        //cal.setTimeZone(TimeZone.getTimeZone("GMT+8"));

        //查询开始时间
        Calendar startcal = Calendar.getInstance();
        startcal.setTime(request.getStartDate());
        startcal.set(Calendar.DAY_OF_MONTH, startcal.get(Calendar.DAY_OF_MONTH) - 1);
        Integer phaseDifference = PeriodDateUtil.getCountForStartToToday(DateUtil.buildDateOnLastSecond(new Date()), pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue());
        cal.setTime(PeriodDateUtil.buildNextTime(cal.getTime(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), phaseDifference));

        //查询结束时间
        Calendar endcal = Calendar.getInstance();
        endcal.setTime(request.getEndDate());
        endcal.set(Calendar.DAY_OF_MONTH, endcal.get(Calendar.DAY_OF_MONTH) + 1);

        //首次维护时间
        Date first = DateUtil.buildDateOnFirstSecond(pm.getDateFristTodo());
        Calendar cfirst = Calendar.getInstance();
        cfirst.setTime(first);

        //有月份控制得到开始月份和结束月份
        long mtFrom = 0;
        long mtTo = 0;
        if(null != pm.getPeriod().getMaintenanceTimeFrom() && null != pm.getPeriod().getMaintenanceTimeTo()) {
            mtFrom = pm.getPeriod().getMaintenanceTimeFrom();
            mtTo = pm.getPeriod().getMaintenanceTimeTo();
        }

        while (cal.before(endcal)) {
            //如果日历时间不在月份范围内，进入下一次周期判断
            if( !PeriodDateUtil.monthPeriodJudge(mtFrom,mtTo,cal.get(Calendar.MONTH)+1)){
                phaseDifference++;
                cal.setTime(PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), phaseDifference));
                continue;
            }
            phaseDifference++;
            if (cal.compareTo(cfirst) >= 0) {
                //计划时间在时间范围内 减少返回数据量
                if (cal.after(startcal)) {
                    cal.set(Calendar.HOUR, 0);
                    cal.set(Calendar.MINUTE, 0);
                    cal.set(Calendar.SECOND, 0);
                    PMDateTodo pdt = new PMDateTodo(pm,
                            cal.getTime());
                    Date dddd = cal.getTime();
                    // 统一集合 最后判断哪些是已经存在的 存在的toto取出工单计划 判断时间是否已经存在数据库中
                    if (pmMap == null) {
                        pdts.add(pdt);
                    } else if (!pmMap.containsKey(cal.getTime().getTime())) {
                        pdts.add(pdt);
                    }
                }
            }
            cal.setTime(PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), phaseDifference));
        }

        return pdts;
    }

    private List<PmDateTodoCalendarDTO> calculateCalerdarListByRequest(PMtodoQueryRequest request){

        Calendar calStart=Calendar.getInstance();
        calStart.setTimeInMillis(request.getStartTime());
        Calendar calEnd=Calendar.getInstance();
        calEnd.setTimeInMillis(request.getEndTime());
        CalendarRequestDTO calRequest =new CalendarRequestDTO();
        calRequest.setStartDate(calStart.getTime());
        calRequest.setEndDate(calEnd.getTime());

        List<PmDateTodoCalendarDTO> dtos = this.calculateCalerdarListByDate(calRequest);
        //过滤多余的
        List<PmDateTodoCalendarDTO> results = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dtos)){
            for(PmDateTodoCalendarDTO dto :dtos){
                if(dto.getStartDate().compareTo(calStart.getTime()) >=0 && dto.getStartDate().compareTo(calEnd.getTime()) <=0){
                    results.add(dto);
                }
            }
        }
        return results;
    }

    private Map<Long,List<Long>> convertWorkOrderListToMap(List<WorkOrder> workOrders){
        Map<Long,List<Long>> maps=new HashMap<>();
        for(WorkOrder workOrder : workOrders){
            if(workOrder.getPmDateTodo()!=null){
                if(maps.containsKey(workOrder.getPmDateTodo().getId())){
                    maps.get(workOrder.getPmDateTodo().getId()).add(workOrder.getId());
                }else{
                    List<Long> woIds=new ArrayList<>();
                    woIds.add(workOrder.getId());
                    maps.put(workOrder.getPmDateTodo().getId(),woIds);
                }
            }
        }
        return maps;
    }

    private Map<Long,PMDateTodo> parsePmDateTodosToMap(List<PMDateTodo> todos){
        Map<Long,PMDateTodo> maps=new HashMap<>();
        for(PMDateTodo todo : todos){
            maps.put(todo.getId(),todo);
        }

        return maps;
    }

    /**
     * 判断PmdateTodo的当前状态
     * 1未开始，2处理中，3已结束，4遗漏
     */
    public Integer judgeStatus(PMDateTodo todo,List<WorkOrder.WorkOrderStatus> workOrderStatuss){

        //未开始-状态集
        Set<WorkOrder.WorkOrderStatus> unBegin=new HashSet<>();
        unBegin.add(WorkOrder.WorkOrderStatus.CREATE);
        unBegin.add(WorkOrder.WorkOrderStatus.DISPATCHE);
        //已经完成-状态集
        Set<WorkOrder.WorkOrderStatus> complete=new HashSet<>();
        complete.add(WorkOrder.WorkOrderStatus.TERMINATE);
        complete.add(WorkOrder.WorkOrderStatus.FINISH);
        complete.add(WorkOrder.WorkOrderStatus.VALIDATATION);
        complete.add(WorkOrder.WorkOrderStatus.CLOSE);
        Integer status=1;
        if(todo==null){
            //未处理
            status=1;
        }else{
            if(workOrderStatuss!=null && workOrderStatuss.size()>0){
                //实际状态集
                Set<WorkOrder.WorkOrderStatus> statusSet=new HashSet<>();
                for(WorkOrder.WorkOrderStatus state : workOrderStatuss){
                    statusSet.add(state);
                }
                if(statusSet.size()<=2){
                    if(unBegin.containsAll(statusSet)){
                        //计划生成的工单处于已创建和发布中
                        if(new Date().compareTo(todo.getDateTodo())>0){
                            status=4;
                        }else{
                            //未开始
                            status=1;
                        }
                    }else if(complete.containsAll(statusSet)){
                        //计划已经完成
                        status=3;
                    }else  if(statusSet.contains(WorkOrder.WorkOrderStatus.CREATE) || statusSet.contains(WorkOrder.WorkOrderStatus.DISPATCHE)){
                        if(new Date().compareTo(todo.getDateTodo())>0){
                            status=4;
                        }else{
                            status=2;
                        }
                    } else{
                        status=2;
                    }
                }else {
                    if(complete.containsAll(statusSet)){
                        //计划已经完成
                        status=3;
                    }else{
                        if(statusSet.contains(WorkOrder.WorkOrderStatus.CREATE) || statusSet.contains(WorkOrder.WorkOrderStatus.DISPATCHE)){
                            if(new Date().compareTo(todo.getDateTodo())>0){
                                status=4;
                            }else{
                                status=2;
                            }
                        }else {
                            if(new Date().compareTo(todo.getDateTodo())>0){
                                status=4;
                            }else{
                                //处理中
                                status=2;
                            }
                        }

                    }

                }
            }else{
                if(new Date().compareTo(todo.getDateTodo())>0){
                    status=4;
                }else{
                    //计划未生成工单
                    status=1;
                }
            }
        }

        return status;
    }
}
