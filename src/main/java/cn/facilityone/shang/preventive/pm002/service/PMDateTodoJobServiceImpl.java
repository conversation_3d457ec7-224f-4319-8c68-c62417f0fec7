package cn.facilityone.shang.preventive.pm002.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import cn.facilityone.shang.asset.asset002.repository.EquipmentRepository;
import cn.facilityone.shang.common.component.quartz.core.dto.JobLog;
import cn.facilityone.shang.common.util.LocationUtil;
import cn.facilityone.shang.entity.inventory.MaterialBatch;
import cn.facilityone.shang.entity.preventive.*;
import cn.facilityone.shang.entity.workorder.*;
import cn.facilityone.shang.preventive.pm001.respository.*;
import cn.facilityone.shang.workorder.common.repository.WorkOrderSpaceRepository;
import cn.facilityone.shang.workorder.common.service.WorkOrderService;
import cn.facilityone.shang.workorder.common.service.WorkOrderTaskService;
import cn.facilityone.shang.workorder.wo001.service.WorkOrderSocketService;
import cn.facilityone.shang.workorder.wo002.service.WorkOrderSearchServiceImpl;
import cn.facilityone.shang.workorder.wo003.service.WorkOrderProcessService;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.facilityone.shang.asset.asset002.repository.TimeCountRepository;
import cn.facilityone.shang.common.repository.WorkOrderEquipmentRepository;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.common.util.WorkOrderCode;
import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.common.TimeCount;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.organize.org002.repository.UserRepository;
import cn.facilityone.shang.preventive.common.util.PeriodDateUtil;
import cn.facilityone.shang.preventive.pm001.builder.PreventiveMaintenanceBuilder;
import cn.facilityone.shang.preventive.pm001.service.PMMaterialService;
import cn.facilityone.shang.preventive.pm001.service.PreventiveMaintenanceService;
import cn.facilityone.shang.preventive.pm002.respository.PMDateTodoRepository;
import cn.facilityone.shang.preventive.pm002.staticmetamodel.PMDateTodo_;
import cn.facilityone.shang.stock.common.repository.InventoryManagementActivityRepository;
import cn.facilityone.shang.workorder.common.repository.WorkOrderMaterialRepository;
import cn.facilityone.shang.workorder.common.repository.WorkOrderToolRepository;
import cn.facilityone.xia.core.common.SystemConst;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.springframework.transaction.annotation.Propagation;

/**
 * Created by charles.chen on 2015/6/10.
 */
@Service
public class PMDateTodoJobServiceImpl implements  PMDateTodoJobService {

    @Autowired
    private PMDateTodoRepository pmDateTodoRepository;
    @Autowired
    private PMStepRepository pmStepRepository;
    @Autowired
    private InventoryManagementActivityRepository activityRepository;
    @Autowired
    private PMNoticeActivityRepository noticeActivityRepository;
    @Autowired
    private PreventiveMaintenanceRepository preventiveMaintenanceRepository;
    @Autowired
    private PreventiveMaintenanceService preventiveMaintenanceService;
    @Autowired
    private PMMaterialService pmMaterialService;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private TimeCountRepository timeCountRepository;
    @Autowired
    private WorkOrderEquipmentRepository workOrderEquipmentRepository;
    @Autowired
    private WorkOrderMaterialRepository workOrderMaterialRepository;
    @Autowired
    private WorkOrderToolRepository workOrderToolRepository;
    @Autowired
    private WorkOrderCode workOrderCode;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PreventiveMaintenanceBuilder preventiveMaintenanceBuilder;

    @Autowired
    private WorkOrderSpaceRepository workOrderSpaceRepository;
    @Autowired
    private WorkOrderTaskService workOrderTaskService;
    @Autowired
    private WorkOrderProcessService workOrderProcessService;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private PMDateTodoService pmDateTodoService;
//    @Autowired
//    private EquipmentRepository equipmentRepository;
    @Autowired
    private PMMaterialRepository pmMaterialRepository;
    @Autowired
    private PMToolRepository pmToolRepository;


    private static final Logger logger = LoggerFactory.getLogger(PMDateTodoJobServiceImpl.class);
    //数据库中有效todo默认数量
    private static final int defaultMaxCount=50;

    private static final int periodCount=1;

    private static final int workOrderEstimatedTime=8;

    /**
     * 自动生成TODO JobLog jobLog
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void generateTodoByJob() {

        // 所有生成计划
        List<PreventiveMaintenance> pmL = preventiveMaintenanceRepository.findAllForJob();
        if (pmL != null && pmL.size() > 0) {
            for (PreventiveMaintenance pm : pmL) {
                // 固定周期的不生成
                if (pm.getFixedPeriod()) {
                    continue;
                }
                Date now = new Date();
                //jobLog.info(DateUtil.nowDateTime()+":PmId is:"+pm.getId());
                //从首次维护到今天一共经过多少周期 次数
                int periodValue = pm.getPeriod().getValue().intValue();
                Integer phaseDifference = 0;
                if(periodValue > 0){
                    phaseDifference=PeriodDateUtil.getCountForStartToToday(new Date(),pm.getDateFristTodo(), pm.getPeriod().getType(), periodValue);
                }
                //有效计划数量
                Integer count=pmDateTodoRepository.findCountPmTodoByPmIdAndTodoDate(pm.getId(), new Date());
                //计划总数
                Integer totalCount=pmDateTodoRepository.findCountPmTodoByPmId(pm.getId());
                //jobLog.info(DateUtil.nowDateTime()+":PmDateTodo is:"+count);
                /**
                 * 计算出todo生成的起始时间
                 */
                now=PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(),
                        phaseDifference);
                //todo总数小于100 从今天算起生成100条，总数>100且总数>有效条数 从最大日期起添加缺失的条数 总条数>100条
                // (今天=最后一次todo时间)
                if(totalCount<defaultMaxCount){
                    now=PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(),
                            phaseDifference);
                }else if(totalCount > count){
                    now=PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(),
                            totalCount);
                }

                now = DateUtil.buildDateOnFirstSecond(now);

                List<Date> todoDates=new ArrayList<>();

                //生成的TODO计划时间和TODO
                Map<Date,PMDateTodo> maps=new HashMap<>();

                //defaultCount(最大有效TODO数量)-count：需要生成TODO的次数
                int generateCount = defaultMaxCount-count;
                maps=this.generateTodoByDateAndTimes(pm,now,generateCount);

                todoDates=new ArrayList<>(maps.keySet());

                if(todoDates.size()>0){
                    //过滤已经生成的TODO
                    maps= this.removeTodosHaveExit(pm,maps, todoDates);

                    //从map中取出在数据库中未生成的TODO
                    List<PMDateTodo> pmDateTodoList=new ArrayList<>( maps.values());

                    if(pmDateTodoList!=null && pmDateTodoList.size()>0){
                        //jobLog.info(DateUtil.nowDateTime()+":Finally GenerateTodoSize is:"+pmDateTodoList.size());

                        //多项目支持
                        for(PMDateTodo pmdt:pmDateTodoList){
                            pmdt.setProject(pm.getProject());
                        }
                        pmDateTodoRepository.saveInBatch(pmDateTodoList);
                    }
                }
            }
        }
    }

    @Override
    public void generateTodoByJobV2() {
        // 所有生成计划
        List<PreventiveMaintenance> pmL = preventiveMaintenanceRepository.findAllForJob();
        if (pmL != null && pmL.size() > 0) {
            for (PreventiveMaintenance pm : pmL) {
                // 固定周期的不生成
                if (pm.getFixedPeriod()) {
                    continue;
                }
                //从首次维护到今天一共经过多少周期 次数
                int periodValue = pm.getPeriod().getValue().intValue();
                Integer phaseDifference = 0;
                if(periodValue > 0){
                    phaseDifference=PeriodDateUtil.getCountForStartToToday(new Date(),pm.getDateFristTodo(), pm.getPeriod().getType(), periodValue);
                }
                //有效计划
                List <PMDateTodo> pmDateToDos=pmDateTodoRepository.findPmTodoByPmIdAndTodoDate(pm.getId(), new Date());
                //查询数据中的有效todo , 以当前时间向后生成默认todo条数 ，数据库中存在的不生成 .
                List <PMDateTodo> pmDateTodoList = generateTodo(pmDateToDos,phaseDifference,defaultMaxCount,pm);

                if(CollectionUtils.isNotEmpty(pmDateTodoList)){
                    //多项目支持
                    for(PMDateTodo pmdt:pmDateTodoList){
                        pmdt.setProject(pm.getProject());
                    }
                    pmDateTodoRepository.save(pmDateTodoList);
                }
            }
        }

    }

    /**
     * 验证通过或者直接存档工单后生成下一个todo计划 -- 滑动周期专属
     * @param pm
     */
    @Override
    public void generateTodoAfterWorkorder(PreventiveMaintenance pm) {
        if (null == pm) {
            return;
        }
        if (pm.getFixedPeriod()) {
            // 下一次todo就是这次以后推算一个周期
            Date nextTime = PeriodDateUtil.buildNextTime(new Date(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), 1);
            nextTime = DateUtil.buildDateOnFirstSecond(nextTime);
            // 如果下次生成todo日期在设定的首次维护日期之前，那么在首次维护日期之后的一个周期生成下一次todo
            if (nextTime.before(pm.getDateFristTodo())) {
                nextTime = PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), 1);
            }
            Map<Date, PMDateTodo> maps = this.generateTodoByDateAndTimes(pm, nextTime, 1);
            List<Date> todoDates = new ArrayList<>(maps.keySet());
            if (todoDates.size() > 0) {
                //过滤已经生成的TODO
                //maps = this.removeTodosHaveExit(pm, maps, todoDates);
                //从map中取出在数据库中未生成的TODO
                List<PMDateTodo> pmDateTodoList = new ArrayList<>(maps.values());
                if (pmDateTodoList != null && pmDateTodoList.size() > 0) {
                    //多项目支持
                    for (PMDateTodo pmdt : pmDateTodoList) {
                        pmdt.setProject(pm.getProject());
                    }
                    pmDateTodoRepository.saveInBatch(pmDateTodoList);
                }
            }
        }
    }

    @Override
    @XiaTransactional(readOnly = false)
    public List<WorkOrder> gengerateWorkOrder(){
        List<WorkOrder> workOrders=new ArrayList<>();
        //the pm for AutoGenerateWorkOrder
        List<PreventiveMaintenance> pmL = preventiveMaintenanceRepository.findByAutoTrueAndProjectIsNotNull();
        // 查找所有计划
        if (pmL != null && pmL.size() > 0) {
            for (final PreventiveMaintenance pm : pmL) {
                //筛选出符合条件，需要生成工单的TODO
                List<PMDateTodo> _pmdtodo =this.findPmDateToForGenerateWorkOrder(pm);
                // 生成工单
                if (_pmdtodo != null && _pmdtodo.size() > 0) {
                    List<Long> todoIds = new ArrayList<>();
                    for (PMDateTodo pmdtodo : _pmdtodo) {
                        List<WorkOrder> workOrderList=this.generateWorkOrderByToDoDate(pmdtodo);
                        if(workOrderList!=null && workOrderList.size()>0){
                            todoIds.add(pmdtodo.getId());
                            //关联工单信息
                            workOrderList=this.connectWorkOrder(workOrderList);
                            workOrders.addAll(workOrderList);
                        }
                    }
                    //删除该计划未开始的提醒
                    this.deleteTodoActivity(todoIds);
                }
            }
        }
        // 发送socket提示刷新
//        if (null != workOrders && workOrders.size() > 0) {
//            workOrderSocketService.newWorkOrderSocket(workOrders.size(), workOrders);
//        }
        return workOrders;
    }

    private void deleteTodoActivity(List<Long> todoIds){
        if(CollectionUtils.isEmpty(todoIds)){
            return;
        }
        pmDateTodoRepository.updateTodoSetWoAcvitityFalse(todoIds.toArray(new Long[todoIds.size()]));
        noticeActivityRepository.deleteByTodoIdsIn(todoIds.toArray(new Long[todoIds.size()]));
    }



    /**
     * 根据todo生成工单
     */
    @Override
    @XiaTransactional(readOnly = false)
    public  List<WorkOrder> generateWorkOrderByToDoDate(PMDateTodo pmtodo) {
        List<WorkOrder> workOrders=new ArrayList<>();
        if (pmtodo != null) {
            if(!pmtodo.isActivated()){
                return workOrders;
            }
            // 登陆用户信息
            String operateUserName =
                    ((XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal())
                            .getName();
            if(StringUtils.isBlank(operateUserName)){
                operateUserName = XiaMesssageResource.getMessage("page.common.system");
            }
            // 创建工单-根据todo中的PM信息
            if (pmtodo.getPm() != null) {
                PreventiveMaintenance pm =preventiveMaintenanceRepository.findOne(pmtodo.getPm().getId());
//                if(CollectionUtils.isEmpty(pm.getSpaces())){
//                    return workOrders;
//                }

                List<PMSpace> pmSpaces = preventiveMaintenanceRepository.findPMSpacesByIdHardly(pm.getId());
//                PMSpace pMSpace = preventiveMaintenanceService.findOneSpace(pm.getSpaces());
                List<PMStep> pmSteps=pmStepRepository.findLazyByPmIdAndWoIsNull(pm.getId());
                Map<Long,WorkOrder> longWorkOrderMap=new HashMap<>();
                Map<Long,InventoryManagementActivity> longActivity = new HashMap<>();
                //为每个步骤绑定工单
                for(PMStep pmStep : pmSteps){
                    if(pmStep.getWorkTeam()!=null && longWorkOrderMap.get(pmStep.getWorkTeam().getId())==null){
                        //创建工单
                        WorkOrder wo=this.createWo(pmStep, operateUserName, pmtodo,pmSpaces);
                        wo.setDescription(pmStep.getPm().getName());
                        //关联预定记录
                        if(pm.getPmMaterials()!=null &&pm.getPmMaterials().size()>0){
                            //根据工作组来对物资进行分组 分配给各自工作组所处的工单
                            List<PMMaterial> pmMaterials = this.choosePmmaterialsByWorkTeamId(pm.getPmMaterials(),pmStep.getWorkTeam().getId());
                            if(pmMaterials!=null && pmMaterials.size()>0){
                                InventoryManagementActivity  reserveActivity = pmMaterialService.createActivityByPmm(pmMaterials);
                                //添加备注信息=工单号
                                if(reserveActivity!=null){
                                    String desc = XiaMesssageResource.getMessage("page.pm002.connectWoCode")+":"+wo.getCode();
                                    reserveActivity.setDescription(desc);
//                                        reserveActivity.setWorkOrder(wo);
                                    reserveActivity.setPkeyId(wo.getId()+"");
                                    reserveActivity.setTableName(WorkOrder.class.getSimpleName());
                                    activityRepository.save(reserveActivity);
                                    longActivity.put(wo.getId(),reserveActivity);
                                }
                            }
                        }

                        workOrders.add(wo);
                        longWorkOrderMap.put(pmStep.getWorkTeam().getId(),wo);
                    }
                }
                List<PMTool> pmTooles=pm.getPmTools();
                pm.setPmTools(pmTooles);
                //重新绑定工单和计划性维护关联字段
                this.bindWorkOrderAndPm(pm,workOrders,longWorkOrderMap,longActivity);

            }
            // 修改todo的工单生成状态
            pmtodo.setGenStatus(Boolean.TRUE);
            pmDateTodoRepository.save(pmtodo);
        }
        return workOrders;
    }

    /**
     * 线程job 根据todo生成工单
     */
    @Override
    @XiaTransactional(readOnly = false)
    public   List<WorkOrder> generateWorkOrderByToDoDateJob(PMDateTodo pmtodo) {
        List<WorkOrder> workOrders=new ArrayList<>();
        if (pmtodo != null) {
            if(!pmtodo.isActivated()){
                return workOrders;
            }
            // 登陆用户信息
            String operateUserName =
                    ((XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal())
                            .getName();
            if(StringUtils.isBlank(operateUserName)){
                operateUserName = XiaMesssageResource.getMessage("page.common.system");
            }
            // 创建工单-根据todo中的PM信息
            if (pmtodo.getPm() != null) {
                PreventiveMaintenance pm = pmtodo.getPm();
//                if(CollectionUtils.isEmpty(pm.getSpaces())){
//                    return workOrders;
//                }

                List<PMSpace> pmSpaces = preventiveMaintenanceRepository.findPMSpacesByIdHardly(pm.getId());
//                PMSpace pMSpace = preventiveMaintenanceService.findOneSpace(pm.getSpaces());
                List<PMStep> pmSteps=pmStepRepository.findLazyByPmIdAndWoIsNull(pm.getId());
                Map<Long,WorkOrder> longWorkOrderMap=new HashMap<>();
                Map<Long,InventoryManagementActivity> longActivity = new HashMap<>();
                //为每个步骤绑定工单
                for(PMStep pmStep : pmSteps){
                    if(pmStep.getWorkTeam()!=null && longWorkOrderMap.get(pmStep.getWorkTeam().getId())==null){
                        //创建工单
                        WorkOrder wo=this.createWo(pmStep, operateUserName, pmtodo,pmSpaces);
                        wo.setDescription(pmStep.getPm().getName());
                        //关联预定记录
                        if(pm.getPmMaterials()!=null &&pm.getPmMaterials().size()>0){
                            //根据工作组来对物资进行分组 分配给各自工作组所处的工单
                            List<PMMaterial> pmMaterials = this.choosePmmaterialsByWorkTeamId(pm.getPmMaterials(),pmStep.getWorkTeam().getId());
                            if(pmMaterials!=null && pmMaterials.size()>0){
                                InventoryManagementActivity  reserveActivity = pmMaterialService.createActivityByPmm(pmMaterials);
                                //添加备注信息=工单号
                                if(reserveActivity!=null){
                                    String desc = XiaMesssageResource.getMessage("page.pm002.connectWoCode")+":"+wo.getCode();
                                    reserveActivity.setDescription(desc);
//                                        reserveActivity.setWorkOrder(wo);
                                    reserveActivity.setPkeyId(wo.getId()+"");
                                    reserveActivity.setTableName(WorkOrder.class.getSimpleName());
                                    activityRepository.save(reserveActivity);
                                    longActivity.put(wo.getId(),reserveActivity);
                                }
                            }
                        }

                        workOrders.add(wo);
                        longWorkOrderMap.put(pmStep.getWorkTeam().getId(),wo);
                    }
                }
                //重新绑定工单和计划性维护关联字段
                this.bindWorkOrderAndPm(pm,workOrders,longWorkOrderMap,longActivity);

            }
            // 修改todo的工单生成状态
            pmtodo.setGenStatus(Boolean.TRUE);
            pmDateTodoRepository.save(pmtodo);
        }
        return workOrders;
    }
    @Override
    public Map<Date,PMDateTodo> generateTodoByDateAndTimes(PreventiveMaintenance pm,Date nowDate,int genCount){
        Map<Date,PMDateTodo> maps=new HashMap<>();
        for(int i=0;i<genCount;i++){
            Date todoDate =
                    PeriodDateUtil.buildNextTime(nowDate, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(),
                            i);
            if(null != pm.getPeriod().getMaintenanceTimeFrom() && null != pm.getPeriod().getMaintenanceTimeTo()) {
                long maintenanceTimeFrom = pm.getPeriod().getMaintenanceTimeFrom();
                long maintenanceTimeTo = pm.getPeriod().getMaintenanceTimeTo();
                Calendar todoMonthCal = Calendar.getInstance();
                todoMonthCal.setTime(todoDate);
                long currentMonth = todoMonthCal.get(Calendar.MONTH) + 1;
                //如果循环的计划时间不在维护周期的月份范围内，增加一次循环，本次循环作废
                if(!PeriodDateUtil.monthPeriodJudge(maintenanceTimeFrom,maintenanceTimeTo,currentMonth)) {
                    genCount++;
                    continue;
                }
            }
            PMDateTodo pmtodo = new PMDateTodo();
            pmtodo.setDateTodo(todoDate);
            pmtodo.setPm(pm);
            pmtodo.setActivated(Boolean.TRUE);
            pmtodo.setGenRemindNotice(Boolean.FALSE);
            pmtodo.setGenStatus(Boolean.FALSE);
            maps.put(pmtodo.getDateTodo(),pmtodo);
        }
        return maps;
    }

    /**
     * JOB
     * @param pm
     * @param nowDate
     * @param genCount
     * @return
     */
    @Override
    public  Map<Date,PMDateTodo> generateTodoByDateAndTimesJob(PreventiveMaintenance pm,Date nowDate,int genCount){
        Map <Date,PMDateTodo> maps=new HashMap<>();
        for(int i=0;i<genCount;i++){
            Date todoDate =
                    PeriodDateUtil.buildNextTime(nowDate, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(),
                            i);
            if(null != pm.getPeriod().getMaintenanceTimeFrom() && null != pm.getPeriod().getMaintenanceTimeTo()) {
                long maintenanceTimeFrom = pm.getPeriod().getMaintenanceTimeFrom();
                long maintenanceTimeTo = pm.getPeriod().getMaintenanceTimeTo();
                Calendar todoMonthCal = Calendar.getInstance();
                todoMonthCal.setTime(todoDate);
                long currentMonth = todoMonthCal.get(Calendar.MONTH) + 1;
                //如果循环的计划时间不在维护周期的月份范围内，增加一次循环，本次循环作废
                if(!PeriodDateUtil.monthPeriodJudge(maintenanceTimeFrom,maintenanceTimeTo,currentMonth)) {
                    genCount++;
                    continue;
                }
            }
            PMDateTodo pmtodo = new PMDateTodo();
            pmtodo.setDateTodo(todoDate);
            pmtodo.setPm(pm);
            pmtodo.setActivated(Boolean.TRUE);
            pmtodo.setGenRemindNotice(Boolean.FALSE);
            pmtodo.setGenStatus(Boolean.FALSE);
            maps.put(pmtodo.getDateTodo(),pmtodo);
        }
        return maps;
    }

    private List<PMMaterial> choosePmmaterialsByWorkTeamId(List<PMMaterial> pmms,Long id){
        List<PMMaterial> result = new ArrayList<>();
        for(PMMaterial pmm : pmms){
            if(pmm.getWorkTeam()!=null && pmm.getWorkTeam().getId().equals(id)){
                result.add(pmm);
            }
        }
        return result;
    }


    /**
     * Job使用 筛选TODO表，删除已经生成的todo
     *
     */
    private  Map<Date,PMDateTodo> removeTodosHaveExitJob(PreventiveMaintenance pm, Map<Date,PMDateTodo> map,List<Date> todoDates){
        List<PMDateTodo> _pmTdL = pmDateTodoRepository.findByDateTodoInAndPmListHardly(todoDates, pm.getId(),pm.getProject());

        //根据todo时间删除已经存在的todo记录
        for(PMDateTodo pmDateTodo : _pmTdL){
            map.remove(pmDateTodo.getDateTodo());
        }
        return map;
    }

    /**
     * 筛选TODO表，删除已经生成的todo
     *
     */
    private Map<Date,PMDateTodo> removeTodosHaveExit(PreventiveMaintenance pm,Map<Date,PMDateTodo> map,List<Date> todoDates){
        List<PMDateTodo> _pmTdL = pmDateTodoRepository.findByDateTodoInAndPm(todoDates.toArray(new Date[todoDates.size()]), pm);

        //根据todo时间删除已经存在的todo记录
        for(PMDateTodo pmDateTodo : _pmTdL){
            map.remove(pmDateTodo.getDateTodo());
        }
        return map;
    }

    private List<PMDateTodo> findPmDateToForGenerateWorkOrder(final PreventiveMaintenance pm){
        Date now = new Date();
        now = DateUtil.buildDateOnFirstSecond(now);
        //起始日期
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(now);
        startCal.add(Calendar.DAY_OF_YEAR, -pm.getAhead());
        //结束日期
        Calendar end = Calendar.getInstance();
        end.setTime(now);
        end.add(Calendar.DAY_OF_YEAR, pm.getAhead());

        final Date _end = end.getTime();
        final Date _start = startCal.getTime();

        List<PMDateTodo> _pmdtodo =
                pmDateTodoRepository.findAll(new XiaSpecification<PMDateTodo>() {

                    @Override
                    public Root<PMDateTodo> toRoot(Root<PMDateTodo> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
                        root.fetch("pm", JoinType.LEFT);
                        return root;
                    }
                    @Override
                    public Predicate toPredicate(Root<PMDateTodo> root,
                                                 CriteriaQuery<?> query, CriteriaBuilder cb) {
                        List<Predicate> predicatesList = new ArrayList<Predicate>();
                        // 条件-未生成工单，计划未开始
                        predicatesList.add(cb.equal(root.get(PMDateTodo_.GENSTATUS),
                                Boolean.FALSE));

                        predicatesList.add(cb.lessThanOrEqualTo(
                                root.<Date>get(PMDateTodo_.DATE_TODO), _end));
                        predicatesList.add(cb.greaterThanOrEqualTo(
                                root.<Date>get(PMDateTodo_.DATE_TODO), _start));

                        //多项目支持
                        //predicatesList.add(cb.isNotNull(root.get(PMDateTodo_.PROJECT)));

                        From<?, ?> pmRoot = root.join(PMDateTodo_.PM, JoinType.LEFT);
                        predicatesList.add(cb.equal(pmRoot.get(PMDateTodo_.PM_ID), pm.getId()));
                        query.where(predicatesList.toArray(new Predicate[predicatesList
                                .size()]));
                        return query.getRestriction();
                    }
                });
        return _pmdtodo;
    }

    /**
     * job使用
     * @param pm
     * @return
     */
    private   List<PMDateTodo>  findPmDateToForGenerateWorkOrderJob(PreventiveMaintenance pm){
        Date now = new Date();
        now = DateUtil.buildDateOnFirstSecondJob(now);
        //起始日期
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(now);
        startCal.add(Calendar.DAY_OF_YEAR, -pm.getAhead());
        //结束日期
        Calendar end = Calendar.getInstance();
        end.setTime(now);
        end.add(Calendar.DAY_OF_YEAR, pm.getAhead());

        final Date _end = end.getTime();
        final Date _start = startCal.getTime();
        List<PMDateTodo> _pmdtodo =pmDateTodoRepository.findPmDateToForGenerateWorkOrderJobHardly(_start,_end,pm.getProject(),pm.getId());
        return _pmdtodo;
    }
    @Override
    public   List<WorkOrder> connectWorkOrder(List<WorkOrder> workOrders){
        String codeString=this.findAllWoCodeString(workOrders);
        for(WorkOrder workOrder : workOrders){
            workOrder.setRelatedWorkOrders(codeString);
        }

        return workOrders;
    }

    /**
     * ppm生成工单线程JOB使用
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void ppmWorkOrdersGeneratePlan(PreventiveMaintenance pm, JobLog jobLog) {
        pm=preventiveMaintenanceRepository.findOne(pm.getId());
        jobLog.info("start");
        // 固定周期的不生成
        if (!pm.getFixedPeriod()) {
        Date now;
        //jobLog.info(DateUtil.nowDateTime()+":PmId is:"+pm.getId());
        //从首次维护到今天一共经过多少周期 次数
        int periodValue = pm.getPeriod().getValue().intValue();
        Integer phaseDifference = 0;
        if(periodValue > 0){
            phaseDifference= PeriodDateUtil.getCountForStartToToday(new Date(),pm.getDateFristTodo(), pm.getPeriod().getType(), periodValue);
        }
        //有效计划
        List <PMDateTodo> pmDateToDos=pmDateTodoRepository.findPmTodoByPmIdAndTodoDate(pm.getId(), new Date());

        /**
         * 计算出todo生成的起始时间
         */
//        now=PeriodDateUtil.buildNextTime(pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(),
//                phaseDifference);
//        now =DateUtil.buildDateOnFirstSecondJob(now);
        //查询数据中的有效todo , 以当前时间向后生成默认todo条数 ，数据库中存在的不生成 .
        List<PMDateTodo> pmDateTodoList = this.generateTodo(pmDateToDos,phaseDifference,defaultMaxCount,pm);

        if(CollectionUtils.isNotEmpty(pmDateTodoList)){
            //多项目支持
            for(PMDateTodo pmdt:pmDateTodoList){
                pmdt.setProject(pm.getProject());
            }
            pmDateTodoRepository.save(pmDateTodoList);
        }
        }

        //如果此pm不要自动创建工单则return 结束
        if(!pm.isAuto()){
            jobLog.info("end");
            return;
        }
        //筛选出符合条件，需要生成工单的TODO
        List<PMDateTodo> _pmdtodo =this.findPmDateToForGenerateWorkOrderJob(pm);
        // 生成工单
        if (_pmdtodo != null && _pmdtodo.size() > 0) {
            //工单
            List<WorkOrder> workOrders=new ArrayList<>();
            List<Long> todoIds = new ArrayList<>();
            for (PMDateTodo pmdtodo : _pmdtodo) {
                List<WorkOrder> workOrderList=this.generateWorkOrderByToDoDateJob(pmdtodo);
                if(workOrderList!=null && workOrderList.size()>0){
                    todoIds.add(pmdtodo.getId());
                    //关联工单信息
                    workOrderList=this.connectWorkOrder(workOrderList);
                    workOrders.addAll(workOrderList);
                }
            }
            //删除该计划未开始的提醒
            this.deleteTodoActivity(todoIds);
            //启动流程
            for (WorkOrder workOrder : workOrders) {
                List<WorkOrder> workOrderList = new ArrayList<>();
                boolean needApproval = false;
                PreventiveMaintenance newpm = workOrder.getPreventiveMaintenance();
                if (WorkOrder.WorkOrderType.PPM.equals(workOrder.getType())) {
                    if (newpm.getNeedApproval() != null && newpm.getNeedApproval()) {
                        needApproval = true;
                    }
                }
                if (needApproval) {
                    //启动流程
                    workOrder = workOrderProcessService.startWorkProcess(workOrder);
                    //完成工单
                    workOrder = workOrderTaskService.completeCreateJob(workOrder);
                    workOrderList.add(workOrder);
                    pmDateTodoService.handleApprovalForPPM(workOrderList, newpm);
                } else {
                    //启动流程
                    workOrder = workOrderProcessService.startWorkProcess(workOrder);
                    //完成工单
                    workOrder = workOrderTaskService.completeCreateJob(workOrder);
                    //自动派工
                    workOrderService.autoDispatchJob(workOrder.getId(),pm.getProject());
                }
            }
        }

        jobLog.info("end");
    }

    private List<PMDateTodo> generateTodo(List<PMDateTodo> pmDateTodos, int phaseDifference, int defaultMaxCount, PreventiveMaintenance pm) {
        Map <Date,PMDateTodo> pmDateTodoMap=new HashMap<>();
        List<PMDateTodo> pmDateTodoList=new ArrayList<>();

        Date firstDate= DateUtil.buildDateOnFirstSecondJob(pm.getDateFristTodo());

        if(CollectionUtils.isNotEmpty(pmDateTodos)){
            for(PMDateTodo pmDateTodo:pmDateTodos){
                pmDateTodoMap.put(pmDateTodo.getDateTodo(),pmDateTodo);
            }
        }
        for(int i=0;i<defaultMaxCount;i++){
            Date todoDate =
                    PeriodDateUtil.buildNextTime(firstDate, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(),
                            phaseDifference+i);
            if(null != pm.getPeriod().getMaintenanceTimeFrom() && null != pm.getPeriod().getMaintenanceTimeTo()) {
                long maintenanceTimeFrom = pm.getPeriod().getMaintenanceTimeFrom();
                long maintenanceTimeTo = pm.getPeriod().getMaintenanceTimeTo();
                Calendar todoMonthCal = Calendar.getInstance();
                todoMonthCal.setTime(todoDate);
                long currentMonth = todoMonthCal.get(Calendar.MONTH) + 1;
                //如果循环的计划时间不在维护周期的月份范围内，增加一次循环，本次循环作废
                if(!PeriodDateUtil.monthPeriodJudge(maintenanceTimeFrom,maintenanceTimeTo,currentMonth)) {
                    defaultMaxCount++;
                    continue;
                }
            }
            if(!pmDateTodoMap.containsKey(todoDate)){
                PMDateTodo pmTodo = new PMDateTodo();
                pmTodo.setDateTodo(todoDate);
                pmTodo.setPm(pm);
                pmTodo.setActivated(Boolean.TRUE);
                pmTodo.setGenRemindNotice(Boolean.FALSE);
                pmTodo.setGenStatus(Boolean.FALSE);
                pmDateTodoList.add(pmTodo);
            }
        }
        return pmDateTodoList;

    }
    
    
    private String findAllWoCodeString(List<WorkOrder> workOrders){
        StringBuffer stringBuffer=new StringBuffer();
        for(WorkOrder workOrder : workOrders){
            stringBuffer.append(workOrder.getCode()+ SystemConst.STR_COMMA);
        }

        return stringBuffer.toString().substring(0,(stringBuffer.toString().length()-1));
    }

    /**
     * 创建工单
     * @return
     */
    private WorkOrder createWo(PMStep pmStep, String sysUsrName,
                               PMDateTodo pmtodo,List<PMSpace> pmSpaceList) {
        WorkOrder wo = new WorkOrder();
        WorkOrderSpace wspace = null;
        PreventiveMaintenance pm=pmtodo.getPm();
        List<WorkOrderEquipment> wqList = new ArrayList<>();
        wo.setRequestName(sysUsrName);
        wo.setCreatedBy(sysUsrName);
        wo.setSource(WorkOrder.SourceType.PPM);

        //多项目支持
        wo.setProject(pmtodo.getProject());

        if(pm.getEstimatedWorkingTime()!=null){
            TimeCount esWorkingTime=new TimeCount();
            Integer ordinal=pm.getEstimatedWorkingTime().getUnit().ordinal();
            TimeUnit[] tus=TimeUnit.class.getEnumConstants();
            esWorkingTime.setUnit(tus[ordinal]);
            esWorkingTime.setValue(pm.getEstimatedWorkingTime().getValue());
            esWorkingTime=timeCountRepository.save(esWorkingTime);
            wo.setEstimatedWorkingTime(esWorkingTime);
        }
        //预估开始时间
        Date EstimatedArrivalDateTime=this.buildEstimatedArrivalDateTime(pmtodo.getDateTodo());
        wo.setEstimatedArrivalDateTime(EstimatedArrivalDateTime);

        //预估完成时间=预估开始时间+工作时长
        if(pm.getEstimatedWorkingTime().getValue()!=null){
            Date estimatedCompleteDate=PeriodDateUtil.buildCompleteWorkTime(EstimatedArrivalDateTime,pm.getEstimatedWorkingTime().getUnit(),pm.getEstimatedWorkingTime().getValue().intValue(),1);
            wo.setEstimatedCompletionDateTime(estimatedCompleteDate);
        }
        wo.setPmDateTodo(pmtodo);
        wo.setPreventiveMaintenance(pm);
        wo.setWorkTeam(pmStep.getWorkTeam());
        wo.setWorkOrderProcess(pmStep.getWorkOrderProcess());
        wo.setType(WorkOrder.WorkOrderType.PPM);
        wo.setCode(workOrderCode.getCode(WorkOrder.WorkOrderType.PPM, wo.getProject()));
        wo.setStatus(WorkOrder.WorkOrderStatus.CREATE);
//        wo.setSite(ps.getSite());
//        wo.setBuilding(ps.getBuilding());
//        wo.setFloor(ps.getFloor());
//        wo.setRoom(ps.getRoom());
        //联系电话
        Long operateUserId =
                ((XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal())
                        .getId();
        if(operateUserId!=null){
            wo.setContactPhone(userRepository.findPhonebyId(operateUserId));
        }
//        if (ps.getSite() == null && ps.getBuilding() == null && ps.getFloor() == null
//                && ps.getRoom() == null) {
//            wo.setDescription("没有选择正确的空间");
//        }
        wo.setDescription(pm.getInfluence());
        wo.setStatus(WorkOrder.WorkOrderStatus.CREATE);
        wo.setWorkOrderEquipments(wqList);
        wo = workOrderRepository.save(wo);

        StringBuilder sb = new StringBuilder();
        for(PMSpace pmSpace :pmSpaceList){
            if(pmSpace != null ){
                wspace=new WorkOrderSpace();
                if(pmSpace.getBuilding()!=null){
                    wspace.setBuilding(pmSpace.getBuilding());
                }
                if(pmSpace.getFloor()!=null){
                    wspace.setFloor(pmSpace.getFloor());
                }
                if(pmSpace.getRoom()!=null){
                    wspace.setRoom(pmSpace.getRoom());
                }
                String location = LocationUtil.buildString(wspace.getBuilding(), wspace.getFloor(), wspace.getRoom());
                if(sb.length() == 0){
                    sb.append(location);
                }else{
                    sb.append(WorkOrderSearchServiceImpl.SPACE_SPLIT_STR);
                    sb.append(location);
                }
                wspace.setPreventiveMaintenance(pm);
                workOrderSpaceRepository.save(wspace);
                wspace.setWorkOrder(wo);
            }

        }
        wo.setLocation(sb.toString());
        return wo;
    }

    /**
     *为工单绑定关联计划性维护的关联实体
     */
    private void bindWorkOrderAndPm(PreventiveMaintenance pm,List<WorkOrder> workOrders,Map<Long,WorkOrder> maps,Map<Long,InventoryManagementActivity> longActivity){

        List<PreventiveMaintenance> pms=new ArrayList<>();
        pm = preventiveMaintenanceRepository.findOne(pm.getId());
        pms.add(pm);
        List<Long> ids = new ArrayList<>();


        preventiveMaintenanceBuilder.init(pms,ids);
        if(CollectionUtils.isNotEmpty(ids)){
            ids.add(-1L);
        }
        List<PMMaterial> pmMaterials = pmMaterialRepository.findByPmIds(ids.toArray(new Long[ids.size()]));
        List<PMStep>  pmSteps=pmStepRepository.findByPmIds(ids.toArray(new Long[ids.size()]));
        List<PMTool>  tools=pmToolRepository.findByPmIds(ids.toArray(new Long[ids.size()]));

        preventiveMaintenanceBuilder.addMaterials(pms,pmMaterials);
        preventiveMaintenanceBuilder.addSteps(pms,pmSteps);
        preventiveMaintenanceBuilder.addTools(pms,tools);


        /**
         * !!!为一个批次的工单创建一个批次的步骤!!!
         * @return List<WorkOrder>
         */
        this.bindStepsForWo(maps, pm);

        //为每个工单关联实体
        for(WorkOrder wo : workOrders){
            //设备
            wo=this.bindEquipmentsForWo(wo,pm);
            //物料
            this.bindMaterialsForWo(wo,pm,longActivity);
            //工具
            pm.setPmTools(pm.getPmTools());
            List<PMTool> pmTooles=pm.getPmTools();
            this.bindToolsForWo(wo,pm);
        }
    }

    /**
     *生成预估都打时间
     */
    private Date buildEstimatedArrivalDateTime(Date date){
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR,workOrderEstimatedTime);
        return calendar.getTime();
    }

    /**
     * 为工单绑定设备
     */
    private WorkOrder bindEquipmentsForWo(WorkOrder wo,PreventiveMaintenance pm){
        List<WorkOrderEquipment> wqList = new ArrayList<>();
        //设备
        for (Equipment equipment : pm.getEquipments()) {
            WorkOrderEquipment we = new WorkOrderEquipment();
            we.setWorkOrder(wo);
            we.setEquipment(equipment);
            we = workOrderEquipmentRepository.save(we);
            wqList.add(we);
        }
        wo.setWorkOrderEquipments(wqList);
        wo = workOrderRepository.save(wo);

        return wo;
    }

    /**
     * 为工单绑定步骤
     */
    private void  bindStepsForWo(Map<Long,WorkOrder> maps,PreventiveMaintenance pm){
        List<PMStep> stepList=new ArrayList<>();
        List<PMStep> steps=pm.getPmSteps();

        if(steps!=null  && steps.size()>0){
            for(PMStep step : steps){
                PMStep pmStep=new PMStep();
                pmStep.setPm(pm);
                pmStep.setComment(step.getComment());
                pmStep.setWorkOrderProcess(step.getWorkOrderProcess());
                pmStep.setWorkTeam(step.getWorkTeam());
                pmStep.setSort(step.getSort());
                pmStep.setStep(step.getStep());
                pmStep.setFinished(Boolean.FALSE);
                pmStep.setWorkOrder(maps.get(step.getWorkTeam().getId()));
                stepList.add(pmStep);
            }
            pmStepRepository.saveInBatch(stepList);
        }
    }

    /**
     * 为工单绑定物料 每个工作组对应其相关的物料
     */
    private void  bindMaterialsForWo(WorkOrder wo,PreventiveMaintenance pm,Map<Long,InventoryManagementActivity> longActivity){
        List<PMMaterial> mas = pm.getPmMaterials();
        List<WorkOrderMaterial> workOrderMaterials=new ArrayList<>();
        if(longActivity==null|| longActivity.size()==0){
            return;
        }else{
            if(mas!=null && mas.size()>0 && longActivity.get(wo.getId())!=null){
                List<MaterialBatch> materialBatches = longActivity.get(wo.getId()).getMaterialBatchs();
                Map<Long,Double> inventoryIdBatchMap = new HashMap<>();
                for(MaterialBatch batch : materialBatches){
                    if(batch.getInventory()!=null){
                        if(inventoryIdBatchMap.containsKey(batch.getInventory().getId())){
                            inventoryIdBatchMap.put(batch.getInventory().getId(),inventoryIdBatchMap.get(batch.getInventory().getId())+batch.getAmount());
                        }else{
                            inventoryIdBatchMap.put(batch.getInventory().getId(),batch.getAmount());
                        }
                    }
                }
                for(PMMaterial pmm : mas){
                    if(wo.getWorkTeam().getId()==pmm.getWorkTeam().getId()){
                        WorkOrderMaterial wm=new WorkOrderMaterial();
                        wm.setInventory(pmm.getInventory());
                        wm.setWorkOrder(wo);
                        wm.setComment(pmm.getComment());
                        wm.setMaterials(pmm.getMaterial());
                        wm.setAmount(pmm.getAmount());
//                    wm.setAmount(inventoryIdBatchMap.get(pmm.getInventory().getId()));
                        if(longActivity.get(wo.getId())!=null){
                            wm.setActivity(longActivity.get(wo.getId()));
                        }
                        workOrderMaterials.add(wm);
                    }
                }
                workOrderMaterialRepository.saveInBatch(workOrderMaterials);
            }
        }}

    /**
     * 为工单绑定工具
     */
    private void  bindToolsForWo(WorkOrder wo,PreventiveMaintenance pm){
        List<WorkOrderTool> woTools=new ArrayList<>();
        pm= preventiveMaintenanceRepository.findOne(pm.getId());
        List<PMTool> pmTooles=pm.getPmTools();
        if(pmTooles!=null && pmTooles.size()>0){
            for(PMTool pmTool : pmTooles){
                WorkOrderTool wTool=new WorkOrderTool();
                wTool.setName(pmTool.getName());
                wTool.setWorkOrder(wo);
                wTool.setAmount(pmTool.getAmount());
                wTool.setComment(pmTool.getComment());
                wTool.setModel(pmTool.getModel());
                wTool.setUnit(pmTool.getUnit());
                woTools.add(wTool);

            }
            workOrderToolRepository.saveInBatch(woTools);
        }
    }

}
