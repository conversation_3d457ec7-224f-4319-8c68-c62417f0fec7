package cn.facilityone.shang.preventive.pm002.dto;

import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.preventive.pm001.dto.PmShowDetailDTO;

import java.util.Date;
import java.util.List;

/**
 * Created by charles.chen on 2015/6/16.
 */
public class PmAndWoDetailDTO extends PmShowDetailDTO  {
    //额外添加内容
    private WorkOrder workOrder;

    private PMDateTodo todo;

    //未及时响应提醒时间
    private Date unResponseDate;
    //未及时完成提醒时间
    private Date unCompleteDate;
    //工作提醒时间
    private List<PmNoticeRemindDateDTO> remindNotices;

    public PMDateTodo getTodo() {
        return todo;
    }

    public void setTodo(PMDateTodo todo) {
        this.todo = todo;
    }

    public Date getUnResponseDate() {
        return unResponseDate;
    }

    public void setUnResponseDate(Date unResponseDate) {
        this.unResponseDate = unResponseDate;
    }

    public Date getUnCompleteDate() {
        return unCompleteDate;
    }

    public void setUnCompleteDate(Date unCompleteDate) {
        this.unCompleteDate = unCompleteDate;
    }

    public List<PmNoticeRemindDateDTO> getRemindNotices() {
        return remindNotices;
    }

    public void setRemindNotices(List<PmNoticeRemindDateDTO> remindNotices) {
        this.remindNotices = remindNotices;
    }

    public WorkOrder getWorkOrder() {
        return workOrder;
    }

    public void setWorkOrder(WorkOrder workOrder) {
        this.workOrder = workOrder;
    }
}
