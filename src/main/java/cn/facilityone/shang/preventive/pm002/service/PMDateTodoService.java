package cn.facilityone.shang.preventive.pm002.service;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.ztree.ZTreeNode;
import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.preventive.pm002.dto.*;
import cn.facilityone.shang.report.report005.dto.PMtodoQueryRequest;
import cn.facilityone.shang.report.report005.dto.PMtooQueryDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

/**
 * Created by charles.chen on 2015/5/25.
 */
public interface PMDateTodoService {


    /**
     * 生成TODO记录
     * @param PreventiveMaintenance
     * @return List<PMDateTodo>
     */
    List<PMDateTodo>  generateTodoList(PreventiveMaintenance pm);

    /**
     * 删除所有未生成工单的TODO记录
     * @param PreventiveMaintenance
     *
     */
    void deleteTodoListNoGenerateWO(PreventiveMaintenance pm);

    /**
     * 维保日历中显示数据
     * @param request
     * @return List<PmDateTodoCalendarDTO>
     */
    List<PmDateTodoCalendarDTO> calculateCalerdarListByDate(CalendarRequestDTO request);

    /**
     * 手动生成TODO记录
     * @param pmDateTodo
     * @return
     */
    boolean generateTodoManual(PMDateTodo pmDateTodo);

    /**
     * 获得空间的树形数据
     * @param
     * @return List<ZTreeNode>
     */
    List<ZTreeNode> findSpaceTree();

    /**
     * 获得TODO的基本信息
     * @param pmId tododate
     * @return PmBaseInfoDTO
     */
    PmBaseInfoDTO findPmBaseByTodoId(Long pmId,Long todoId,Date tododate);

    /**
     * 获得TODO以及其关联工单，PM的详细信息
     * @param pmId todoTime
     * @return PmAndWoDetailDTO
     */
    PmAndWoDetailDTO findPmAndWoByPmIdAndDate(Long pmId,Long todoId,Long woid,Long todoTime);

    /**
     * 删除TODO记录
     * @param id
     * @return
     */
    void deleteById(Long id);

    void deleteByRequest(PmDateToDoRequestDTO requestDTO);

    /**
     * 手动生成TODO的工单
     * @param id
     * @return
     */
    List<WorkOrder> generateWoById(Long id);

    List<WorkOrder> generateWoByRequest(PmDateToDoRequestDTO requestDTO);

    Page<WorkOrder> findWorkOrderByReqyest(DataTableRequest request,Pageable pageable);

    void handleApprovalForPPM(List<WorkOrder> workOrderList, PreventiveMaintenance pm);

    /**
     * 按日期查PM分类详情
     * @param request
     * @return
     */
    List<PMtooQueryDto> queryPmTodos(PMtodoQueryRequest request);
}
