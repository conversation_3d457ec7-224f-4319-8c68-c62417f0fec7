package cn.facilityone.shang.preventive.pm002.service;

import cn.facilityone.shang.entity.workorder.WorkOrder;

import java.util.Date;

/**
 * Created by charles.chen on 2015/7/25.
 */
public interface PmNoticeActivityService {
    /**
     * 自动生成TODO关联工单的未及时响应提醒和未及时完成提醒 JobLog jobLog
     */
    void generatePmNoticeActivityForWoForJob();

    /**
     * 自动生成已经生成工单记录TODO的工作提醒
     */
    void generatePmNoticeActivityForRemindHaveWoForJob();

    /**
     * 自动生成未生成工单记录TODO的工作提醒
     */
    void generatePmNoticeActivityForRemindHaveNoWoForJob(Date nowDate);

    /**
     * 自动生成未生成工单记录TODO的工作提醒
     */
    void updateRemindNoticeForUpdateWorkOrderTime(WorkOrder workOrder);
}
