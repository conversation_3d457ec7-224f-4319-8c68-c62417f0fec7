package cn.facilityone.shang.preventive.pm002.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.service.CommonUserService;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import cn.facilityone.shang.preventive.pm002.dto.*;
import cn.facilityone.shang.preventive.pm002.service.PMDateTodoService;
import cn.facilityone.shang.workorder.common.service.WorkOrderService;
import cn.facilityone.shang.workorder.common.service.WorkOrderTaskService;
import cn.facilityone.shang.workorder.wo003.service.WorkOrderProcessService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.message.common.MessageType;
import org.apache.commons.collections.CollectionUtils;
import org.glassfish.jersey.server.mvc.Template;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.*;

@Path("/pm002")
public class PMDateTodoResource {
    private static final Logger LOGGER = LoggerFactory.getLogger(PMDateTodoResource.class);
    private static final String TEMPLATE_PM_PATH = "/business/preventive/pm002-pm-todo.ftl";
    @Autowired
    private PMDateTodoService pmDateTodoService;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private WorkOrderProcessService workOrderProcessService;
    @Autowired
    private WorkOrderTaskService workOrderTaskService;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private PreventiveMaintenanceRepository preventiveMaintenanceRepository;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private WorkOrderRepository workOrderRepository;

    @GET
    @Template(name = TEMPLATE_PM_PATH)
    public Map<String, Object> init() {
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("noticetypes", MessageType.findAll());
        return data;
    }

    /**
     * 根据日历起始/结束时间获得计划数据
     */
    @POST
    @Path("pmdatetodos/calendar")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getCalendarData(CalendarRequestDTO crdto) {
        List<PmDateTodoCalendarDTO> dtos = pmDateTodoService.calculateCalerdarListByDate(crdto);
        return new Result(dtos);
    }

    /**
     * 手动添加维保计划
     */
    @POST
    @Path("pmdatetodos/generatetodo")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result addPMDateTodo(PMDateTodo pMDateTodo) {
        boolean result = pmDateTodoService.generateTodoManual(pMDateTodo);
        if (result) {
            return new Result(XiaMesssageResource.getMessage("server.result.success.add", null, "计划"));
        } else {
            return new Result(XiaMesssageResource.getMessage("server.result.error.add.existed", null, "计划"));

        }
    }

    /**
     * 手动添加维保计划的工单
     * 注意工单生成的流程！！
     */
    @POST
    @Path("pmdatetodos/generateWo")
    @Produces(MediaType.APPLICATION_JSON)
    public Result generateWo(PmDateToDoRequestDTO requestDTO) {
        try {
            List<WorkOrder> workOrders = new ArrayList<>();
            if (requestDTO.getTodoId() != null) {
                workOrders = pmDateTodoService.generateWoById(requestDTO.getTodoId());
            } else {
                workOrders = pmDateTodoService.generateWoByRequest(requestDTO);
            }

            //PPM手动生成工单时，request_id 当前登录员工，
            Long em = commonUserService.findCurrentEmployeeId();
            if(CollectionUtils.isNotEmpty(workOrders)){
                if (em != null) {
                    for (WorkOrder workOrder : workOrders) {
                        workOrder.setRequestId(em);
                        workOrder.setCreateById(em);
                        workOrderRepository.save(workOrder);

                    }
                }

            }

            Long pmId = requestDTO.getPmId();
            PreventiveMaintenance pm = preventiveMaintenanceRepository.findOneHardly(pmId);
            if (pm.getNeedApproval() == null || !pm.getNeedApproval()) {
                for (WorkOrder workOrder : workOrders) {
                    //启动流程
                    workOrder = workOrderProcessService.startWorkProcess(workOrder);
                    //完成工单
                    workOrder = workOrderTaskService.completeCreate(workOrder);
                    //自动派工
                    workOrderService.autoDispatch(workOrder.getId());
                }
            } else {
                List<WorkOrder> workOrderList = new ArrayList<>();
                for (WorkOrder workOrder : workOrders) {
                    //启动流程
                    workOrder = workOrderProcessService.startWorkProcess(workOrder);
                    //完成工单
                    workOrder = workOrderTaskService.completeCreate(workOrder);
                    workOrderList.add(workOrder);
                }

                pmDateTodoService.handleApprovalForPPM(workOrderList, pm);
            }

            return new Result(XiaMesssageResource.getMessage("server.result.success.add", null, XiaMesssageResource.getMessage("MS000033")));
        } catch (Exception e) {
            LOGGER.error("something went wrong", e);
        }
        return null;

    }

    /**
     * 获得空间树
     */
    @GET
    @Path("pmdatetodos/pmspacetree")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getOrganizationTree() {
        return new Result().data(pmDateTodoService.findSpaceTree());
    }

    /**
     * 显示计划详情(大致详情)
     */
    @GET
    @Path("pmdatetodos/pmshow/{pmid}/todoId/{todoid}/date/{tododate}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getOrganizationTree(@PathParam("pmid") Long pmid, @PathParam("todoid") Long todoid, @PathParam("tododate") Long tododate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(tododate);
        PmBaseInfoDTO pmBaseInfoDTO = pmDateTodoService.findPmBaseByTodoId(pmid, todoid, calendar.getTime());
        return new Result(pmBaseInfoDTO);
    }

    /**
     * 显示计划详情（具体）
     */
    @GET
    @Path("pmdatetodos/pmdetail/{pmid}/todoid/{todoid}/woid/{woid}/date/{tododate}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getPmDetial(@PathParam("pmid") Long pmid, @PathParam("todoid") Long todoid, @PathParam("woid") Long woid, @PathParam("tododate") Long tododate) {
        PmAndWoDetailDTO pmAndWo = pmDateTodoService.findPmAndWoByPmIdAndDate(pmid, todoid, woid, tododate);
        return new Result(pmAndWo);
    }

    /**
     * 根据ID删除计划性维护
     *
     * @param id
     */
    @DELETE
    @Path("pmdatetodos/")
    @Produces(MediaType.APPLICATION_JSON)
    public Result deletePmTodo(PmDateToDoRequestDTO requestDTO) {
        if (requestDTO.getTodoId() != null) {
            pmDateTodoService.deleteById(requestDTO.getTodoId());
        } else {
            pmDateTodoService.deleteByRequest(requestDTO);
        }
        return new Result(XiaMesssageResource.getMessage("server.result.success.delete", null, ""));
    }


    /**
     * 分页显示维修工单
     *
     * @return DataTableResponse
     */
    @POST
    @Path("pmdatetodo/workorders")
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWarrantyWoPage(final DataTableRequest request) {
        Pageable pageable =
                new DataTableRequest(request.getPageNumber(), request.getOffset(), request.getPageSize(),
                        dataTableService.buildColumnSort(request.getColumns()),
                        request.getDraw());
        Page<WorkOrder> result = pmDateTodoService.findWorkOrderByReqyest(request, pageable);
        int count = 0;
        if (result != null) {
            count = (int) result.getTotalElements();
        }
        return new DataTableResponse(result.getContent(), count, request.getPageNumber(), request.getDraw());
    }
}
