package cn.facilityone.shang.preventive.pm002.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by charles.chen on 2015/6/11.
 */
public class CalendarRequestDTO implements Serializable {

    private Date startDate;

    private Date endDate;

    private Long[] siteId;

    private Long[] buildingId;

    private Long[] floorId;

    private Long[] roomId;

    private Long[] equSysId;

    private Long equId;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long[] getSiteId() {
        return siteId;
    }

    public void setSiteId(Long[] siteId) {
        this.siteId = siteId;
    }

    public Long[] getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(Long[] buildingId) {
        this.buildingId = buildingId;
    }

    public Long[] getFloorId() {
        return floorId;
    }

    public void setFloorId(Long[] floorId) {
        this.floorId = floorId;
    }

    public Long[] getRoomId() {
        return roomId;
    }

    public void setRoomId(Long[] roomId) {
        this.roomId = roomId;
    }

    public Long[] getEquSysId() {
        return equSysId;
    }

    public void setEquSysId(Long[] equSysId) {
        this.equSysId = equSysId;
    }

    public Long getEquId() {
        return equId;
    }

    public void setEquId(Long equId) {
        this.equId = equId;
    }
}
