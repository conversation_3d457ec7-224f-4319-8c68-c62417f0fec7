package cn.facilityone.shang.preventive.pm002.service;

import cn.facilityone.shang.common.component.quartz.core.dto.JobLog;
import cn.facilityone.shang.entity.common.Notice;
import cn.facilityone.shang.entity.inventory.Warehouse;
import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PMNoticeActivity;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/6/10.
 */
public interface PMDateTodoJobService {


    /**
     * 自动生成TODO记录 JobLog jobLog
     */
    void generateTodoByJob();

    void generateTodoByJobV2();

    void generateTodoAfterWorkorder(PreventiveMaintenance pm);


    /**
     * generate List<WorkOrder> By Pmtodo
     */
    List<WorkOrder> generateWorkOrderByToDoDate(PMDateTodo pmtodo);


    /**
     * job使用
     */
    List<WorkOrder> generateWorkOrderByToDoDateJob(PMDateTodo pmtodo);


    /**
     * generateWorkOrder for job
     * @return List<WorkOrder>
     */

    List<WorkOrder> gengerateWorkOrder();

    Map<Date,PMDateTodo> generateTodoByDateAndTimes(PreventiveMaintenance pm,Date nowDate,int genCount);

    /**
     * job使用
     * @param pm
     * @param nowDate
     * @param genCount
     * @return
     */
    Map<Date,PMDateTodo> generateTodoByDateAndTimesJob(PreventiveMaintenance pm,Date nowDate,int genCount);

    List<WorkOrder> connectWorkOrder(List<WorkOrder> workOrders);

    /**
     * job使用
     * @param pm
     * @param jobLog
     */
    void ppmWorkOrdersGeneratePlan(PreventiveMaintenance pm, JobLog jobLog);
}
