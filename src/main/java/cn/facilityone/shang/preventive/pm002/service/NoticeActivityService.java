package cn.facilityone.shang.preventive.pm002.service;

import cn.facilityone.shang.common.component.quartz.core.dto.JobLog;
import cn.facilityone.shang.entity.preventive.PMNoticeActivity;

import java.util.List;

/**
 * Created by charles.chen on 2015/7/2.
 */
public interface NoticeActivityService {
    /**
     * 对TODO关联工单进行未及时响应提醒和未及时完成提醒JobLog jobLog
     */
    void noticeUnResponseAndCompleteWo(JobLog jobLog);

    /**
     * 对TODO进行工作提醒 JobLog jobLog
     */
    void noticeRemind(JobLog jobLog);

    /**
     * 对提醒通知进行推送
     */
    void handleNotice(List<PMNoticeActivity> pmNoticeActivities);
}
