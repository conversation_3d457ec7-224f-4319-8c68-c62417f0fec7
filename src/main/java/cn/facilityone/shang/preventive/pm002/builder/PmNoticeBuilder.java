package cn.facilityone.shang.preventive.pm002.builder;

import cn.facilityone.shang.common.repository.NoticeRepository;
import cn.facilityone.shang.entity.common.Notice;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/6/19.
 */
@Service
@XiaTransactional(readOnly = true)
public class PmNoticeBuilder {
    @Autowired
    private PMNoticeRepository pmNoticeRepository;
    @Autowired
    private NoticeRepository noticeRepository;


    private List<PMNotice> pmNotices;

    private List<Long> ids;


    public PmNoticeBuilder init(List<PMNotice> notices){
        this.pmNotices = notices;
        this.ids = new ArrayList<Long>();
        if(this.pmNotices!=null && this.pmNotices.size()>0){
            for(PMNotice notice: this.pmNotices){
                this.ids.add(notice.getId()) ;
            }
        }
        return this;
    }


    public PmNoticeBuilder  addNotice(){
        if(this.ids!=null && this.ids.size()>0){
            List<Notice> notices = pmNoticeRepository.findNoticesInPmIdsHardly(this.ids.toArray(new Long[this.ids.size()]));
            Map<Long,Notice> noticeMap = new HashMap<Long,Notice>();
            for(Notice notice:notices){
                noticeMap.put(notice.getId(), notice);
            }
            for(int i=0;i<pmNotices.size();i++){
                if(pmNotices.get(i).getNotice() == null){
                    pmNotices.get(i).setNotice(null);
                }else{
                    pmNotices.get(i).setNotice(noticeMap.get(pmNotices.get(i).getNotice().getId()));
                }
            }
        }
        return this;
    }




}
