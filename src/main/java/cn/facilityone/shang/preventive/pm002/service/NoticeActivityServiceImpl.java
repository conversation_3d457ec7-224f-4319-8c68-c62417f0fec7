package cn.facilityone.shang.preventive.pm002.service;

import cn.facilityone.shang.common.component.message.MessageSenderTool;
import cn.facilityone.shang.common.component.message.template.MessagePmRemindTemplate;
import cn.facilityone.shang.common.component.message.template.MessagePmUnCompleteTemplate;
import cn.facilityone.shang.common.component.message.template.MessagePmUnResponseTemplate;
import cn.facilityone.shang.common.component.quartz.core.dto.JobLog;
import cn.facilityone.shang.common.component.quartz.job.preventive.GeneratePmTodoAndWorkOrderDayTimeJobServiceImpl;
import cn.facilityone.shang.common.service.NoticeService;
import cn.facilityone.shang.common.staticmetamodel.MobilePush_;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.common.Notice;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PMNoticeActivity;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderActivity;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeActivityRepository;
import cn.facilityone.shang.preventive.pm002.builder.PmNoticeBuilder;
import cn.facilityone.shang.preventive.pm002.dto.MessageSendDTO;
import cn.facilityone.shang.workorder.common.repository.WorkOrderActivityRepository;
import cn.facilityone.xia.message.common.MessageType;
import cn.facilityone.xia.message.service.MessageMobilePushSender;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import cn.facilityone.xia.persistence.repository.XiaSpecification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import java.util.*;

/**
 * Created by charles.chen on 2015/7/2.
 */
@Service
public class NoticeActivityServiceImpl implements NoticeActivityService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NoticeActivityServiceImpl.class);
    @Autowired
    private PMNoticeActivityRepository pmNoticeActivityRepository;
    @Autowired
    private MessageSenderTool messageSenderTool;
    @Autowired
    private PmNoticeBuilder pmNoticeBuilder;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private WorkOrderActivityRepository workOrderActivityRepository;

    @Override
    @XiaTransactional(readOnly = false)
    public void noticeUnResponseAndCompleteWo(JobLog jobLog) {

        //所有有提醒通知=未提醒+提醒类型（完成+响应）+已经生成工单+未通知
        List<PMNoticeActivity> pmNoticeActivitySend = new ArrayList<>();
        List<PMNoticeActivity> pmNoticeActivityGroup = pmNoticeActivityRepository.findAll(new XiaSpecification<PMNoticeActivity>() {
            @Override
            public Root<PMNoticeActivity> toRoot(Root<PMNoticeActivity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                root.fetch("pmNotice", JoinType.LEFT);
                root.fetch("pmDateTodo", JoinType.LEFT);
                Fetch<?, ?> pm = root.fetch("pm", JoinType.LEFT);
                pm.fetch("period", JoinType.LEFT);
                pm.fetch("estimatedWorkingTime", JoinType.LEFT);
                Fetch<?, ?> work = root.fetch("workOrder", JoinType.LEFT);
//                work.fetch("inventoryActivity", JoinType.LEFT);
                work.fetch("estimatedWorkingTime", JoinType.LEFT);
                work.fetch("actualWorkingTime", JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<PMNoticeActivity> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicatesList = new ArrayList<Predicate>();

                predicatesList.add(cb.isNotNull(root.get("workOrder")));

                predicatesList.add(cb.isNull(root.get("notifyDateTime")));

                From<?, ?> pmnotice = root.join("pmNotice", JoinType.LEFT);
                predicatesList.add(cb.or(cb.equal(pmnotice.get("type"),
                        PMNotice.PMNoticeType.COMPLETE), cb.equal(pmnotice.get("type"),
                        PMNotice.PMNoticeType.RESPONSE)));

                predicatesList.add(cb.equal(root.get("isNotify"), Boolean.FALSE));

                query.where(predicatesList.toArray(new Predicate[predicatesList
                        .size()]));
                return query.getRestriction();
            }
        });
        //  this.handleNotice(pmNoticeActivityGroup);
        Date now = new Date();
        Calendar nowCal = Calendar.getInstance();
        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();
        nowCal.setTime(now);
        //未过期项目
        List<PMNoticeActivity> overduePMNoticeActivity = new ArrayList<>();
        List<Long> ids = Arrays.asList(ProjectContext.getProjects());
        for (PMNoticeActivity pmNoticeActivity:pmNoticeActivityGroup){
            if(!ids.contains(pmNoticeActivity.getPmDateTodo().getProject())){
                overduePMNoticeActivity.add(pmNoticeActivity);
            }
        }
        //移除已过期项目的工单流程通知
        pmNoticeActivityGroup.removeAll(overduePMNoticeActivity);
        for (PMNoticeActivity activity : pmNoticeActivityGroup) {
            if (activity.getWorkOrder().getEstimatedArrivalDateTime() == null || activity.getWorkOrder().getEstimatedCompletionDateTime() == null) {
            } else {
                startCal.setTime(activity.getWorkOrder().getEstimatedArrivalDateTime());
                endCal.setTime(activity.getWorkOrder().getEstimatedCompletionDateTime());
//                //未及时响应
//                if(PMNotice.PMNoticeType.RESPONSE.equals(activity.getPmNotice().getType())){
//                    if(nowCal.after(startCal)){
//                        if(WorkOrder.WorkOrderStatus.CREATE.equals(activity.getWorkOrder().getStatus()) ||
//                                WorkOrder.WorkOrderStatus.DISPATCHE.equals(activity.getWorkOrder().getStatus())){
//                            //未及时响应
//                            pmNoticeActivitySend.add(activity);
//                        }
//                    }
//                }
                //未及时完成
                if (PMNotice.PMNoticeType.COMPLETE.equals(activity.getPmNotice().getType())) {
                    if (nowCal.after(endCal)) {
                        if (WorkOrder.WorkOrderStatus.CREATE.equals(activity.getWorkOrder().getStatus()) ||
                                WorkOrder.WorkOrderStatus.DISPATCHE.equals(activity.getWorkOrder().getStatus()) ||
                                WorkOrder.WorkOrderStatus.PROCESS.equals(activity.getWorkOrder().getStatus()) ||
                                WorkOrder.WorkOrderStatus.STOP.equals(activity.getWorkOrder().getStatus())
                                ) {
                            //未及时完成
                            pmNoticeActivitySend.add(activity);
                        }
                    }
                }

            }
        }
        if (pmNoticeActivityGroup != null && pmNoticeActivityGroup.size() > 0) {
            this.handleNotice(pmNoticeActivitySend);
        }
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void noticeRemind(JobLog jobLog) {

        //提醒时间在今天之内的时间
        final Date startDate = DateUtil.buildDateOnFirstSecond(new Date());
        final Date endDate = DateUtil.buildDateOnLastSecond(new Date());

        //所有符合条件的提醒通知-（通知类型=工作提醒）+（通知时间=今天0点-明天23:59:59点）+尚未通知
        List<PMNoticeActivity> pmNoticeActivityGroup = pmNoticeActivityRepository.findAll(new XiaSpecification<PMNoticeActivity>() {
            @Override
            public Root<PMNoticeActivity> toRoot(Root<PMNoticeActivity> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                root.fetch("pmNotice", JoinType.LEFT);
                root.fetch("pmDateTodo", JoinType.LEFT);
                Fetch<?, ?> pm = root.fetch("pm", JoinType.LEFT);
                pm.fetch("period", JoinType.LEFT);
                pm.fetch("estimatedWorkingTime", JoinType.LEFT);
                Fetch<?, ?> work = root.fetch("workOrder", JoinType.LEFT);
//                work.fetch("inventoryActivity", JoinType.LEFT);
                work.fetch("estimatedWorkingTime", JoinType.LEFT);
                work.fetch("actualWorkingTime", JoinType.LEFT);
                return root;
            }

            @Override
            public Predicate toPredicate(Root<PMNoticeActivity> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

                List<Predicate> predicatesList = new ArrayList<Predicate>();


                predicatesList.add(cb.between(
                        root.<Date>get("notifyDateTime"), startDate, endDate));
                From<?, ?> pmnotice = root.join("pmNotice", JoinType.LEFT);
                predicatesList.add(cb.equal(pmnotice.get("type"),
                        PMNotice.PMNoticeType.REMIND));

                predicatesList.add(cb.equal(root.<Boolean>get("isNotify"),
                        Boolean.FALSE));

                query.where(predicatesList.toArray(new Predicate[predicatesList
                        .size()]));
                return query.getRestriction();
            }
        });
        //未过期项目
        List<PMNoticeActivity> overduePMNoticeActivity = new ArrayList<>();
        List<Long> ids = Arrays.asList(ProjectContext.getProjects());
        for (PMNoticeActivity pmNoticeActivity:pmNoticeActivityGroup){
            //todo 是否会报懒加载异常
            if(!ids.contains(pmNoticeActivity.getPmDateTodo().getProject())){
                overduePMNoticeActivity.add(pmNoticeActivity);
            }
        }
        //移除已过期项目的工单流程通知
        pmNoticeActivityGroup.removeAll(overduePMNoticeActivity);
        if (pmNoticeActivityGroup != null && pmNoticeActivityGroup.size() > 0) {
            this.handleNotice(pmNoticeActivityGroup);
        }

    }

    @Override
    @XiaTransactional(readOnly = false)
    public void handleNotice(List<PMNoticeActivity> pmNoticeActivities) {

        List<PMNotice> pmNotices = new ArrayList<>();
        for (PMNoticeActivity pmNoticeActivity : pmNoticeActivities) {
            pmNotices.add(pmNoticeActivity.getPmNotice());
        }

        pmNoticeBuilder.init(pmNotices).addNotice();

        List<WorkOrderActivity> activities = new ArrayList<>();
        //发送数据
        for (PMNoticeActivity pmNoticeActivity : pmNoticeActivities) {
            try {
            Notice notice = pmNoticeActivity.getPmNotice().getNotice();
            List<Employee> senders = noticeService.findEmployeesByNotice(notice);
            String msgType = notice.getMsgType();
            String msgCode = "";
            Map data = null;
            boolean isComplete = false;
            if (PMNotice.PMNoticeType.RESPONSE.equals(pmNoticeActivity.getPmNotice().getType())) {
                //未及时响应提醒
                msgCode = MessagePmUnResponseTemplate.CODE_PM_UNRESPONSE;
                data = MessagePmUnResponseTemplate.buildData(pmNoticeActivity.getPm().getName(), pmNoticeActivity.getWorkOrder().getEstimatedArrivalDateTime(), pmNoticeActivity.getWorkOrder().getCode());
                //senders=noticeService.findEmployeesByNotice(notice);
                pmNoticeActivity.setNotifyDateTime(new Date());
            } else if (PMNotice.PMNoticeType.COMPLETE.equals(pmNoticeActivity.getPmNotice().getType())) {
                //未及时完成提醒
                msgCode = MessagePmUnCompleteTemplate.CODE_PM_UNCOMPLETE;
                data = MessagePmUnCompleteTemplate.buildData(pmNoticeActivity.getPm().getName(), pmNoticeActivity.getWorkOrder().getEstimatedArrivalDateTime(), pmNoticeActivity.getWorkOrder().getCode());
                //senders=noticeService.findEmployeesByNotice(notice);
                pmNoticeActivity.setNotifyDateTime(new Date());
                isComplete = true;
            } else if (PMNotice.PMNoticeType.REMIND.equals(pmNoticeActivity.getPmNotice().getType())) {
                //工作提醒
                msgCode = MessagePmRemindTemplate.CODE_PM_REMIND;
                String woCode = "";
                if (pmNoticeActivity.getWorkOrder() != null) {
                    woCode = pmNoticeActivity.getWorkOrder().getCode();
                }
                data = MessagePmRemindTemplate.buildData(pmNoticeActivity.getPm().getName(), pmNoticeActivity.getPmDateTodo().getDateTodo(), pmNoticeActivity.getPmNotice().getAhead(), woCode);
                //senders=noticeService.findEmployeesByNotice(notice);

            }
            MessageSendDTO sendDto = new MessageSendDTO(senders, msgType, msgCode, data);
            if (pmNoticeActivity.getPm() != null) {
                sendDto.setPmId(pmNoticeActivity.getPm().getId());
                sendDto.setProjectId(pmNoticeActivity.getPm().getProject());
            }
            if (pmNoticeActivity.getWorkOrder() != null) {
                sendDto.setWorkOrderId(pmNoticeActivity.getWorkOrder().getId());
                sendDto.setProjectId(pmNoticeActivity.getWorkOrder().getProject());

                //add work order activity for completion notice
                if (isComplete) {
                    WorkOrder workOrder = pmNoticeActivity.getWorkOrder();
                    WorkOrderActivity activity = new WorkOrderActivity();
                    activity.setWorkOrder(workOrder);
                    activity.setActivityType(WorkOrderActivity.ActivityType.ESCALATION);
                    activity.setDescription("完成升级");
                    activities.add(activity);
                    workOrderActivityRepository.save(activity);
                }

            }
            if (pmNoticeActivity.getPmDateTodo() != null) {
                sendDto.setTodoId(pmNoticeActivity.getPmDateTodo().getId());
                sendDto.setProjectId(pmNoticeActivity.getPmDateTodo().getProject());
            }

            this.sendMessageTool(sendDto);
            pmNoticeActivity.setIsNotify(Boolean.TRUE);
            pmNoticeActivityRepository.save(pmNoticeActivity);

        }catch (Exception e){
                LOGGER.error("handleNotice error pmNoticeActivityId={},{}",pmNoticeActivity.getId(),e);
            }
        }
        //修改状态
//        workOrderActivityRepository.save(activities);
//        pmNoticeActivityRepository.saveInBatch(pmNoticeActivities);
    }

    private void sendMessageTool(MessageSendDTO sendDTO) {

        if (sendDTO.getEmployeeList().size() > 0) {
            Map<String,Map<String,Object>> typeData = new HashMap<>();
            Map<String, Object> params = new HashMap<>();
            params.put(MobilePush_.TYPE, MobilePush_.PM);
            params.put(MobilePush_.PM_ID, sendDTO.getPmId());
            params.put(MobilePush_.WO_ID, sendDTO.getWorkOrderId());
            params.put(MobilePush_.TODO_ID, sendDTO.getTodoId());
            params.put(MobilePush_.PROJECT_ID, sendDTO.getProjectId());
            typeData.put(MessageType.MPUSH, params);
            
            messageSenderTool.send(sendDTO.getMsgCode(), sendDTO.getData(), typeData, 
                    sendDTO.getEmployeeList(), sendDTO.getProjectId(), sendDTO.getMsgType());
        }

    }
}
