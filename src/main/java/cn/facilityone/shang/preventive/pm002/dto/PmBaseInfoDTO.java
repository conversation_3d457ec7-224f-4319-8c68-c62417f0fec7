package cn.facilityone.shang.preventive.pm002.dto;

import cn.facilityone.shang.entity.common.Period;
import cn.facilityone.shang.entity.inventory.Warehouse;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.preventive.pm001.dto.PMMaterialDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Created by charles.chen on 2015/6/16.
 */
public class PmBaseInfoDTO implements Serializable {

    private Long pmId;

    private Long todoId;

    //private Long woId;

    private String pmName;

    private String influence;

    private String workTeamName;

    private String priorityName;

    private List<PMMaterialDTO> pmMaterialDTOList;

    //private String woCode;

    private PreventiveMaintenance.PMPriority priority;

    private String periodName;

    private Period period;

    private List<WorkOrder> workOrders;

//    public PmBaseInfoDTO(){
//
//    }
//
//    public PmBaseInfoDTO(Long pmId,String pmName,String influence,PreventiveMaintenance.PMPriority priority,String workTeamName,Period period){
//        this.pmId=pmId;
//        this.pmName=pmName;
//        this.influence=influence;
//        this.priority=priority;
//        this.workTeamName=workTeamName;
//        this.period=period;
//    }
//
//    public PmBaseInfoDTO(Long todoId,Long woId,String woCode){
//        this.todoId=todoId;
//        this.woId=woId;
//        this.woCode=woCode;
//    }

    public List<WorkOrder> getWorkOrders() {
        return workOrders;
    }

    public void setWorkOrders(List<WorkOrder> workOrders) {
        this.workOrders = workOrders;
    }

    public PreventiveMaintenance.PMPriority getPriority() {
        return priority;
    }

    public void setPriority(PreventiveMaintenance.PMPriority priority) {
        this.priority = priority;
    }

    public Long getPmId() {
        return pmId;
    }

    public void setPmId(Long pmId) {
        this.pmId = pmId;
    }

    public Long getTodoId() {
        return todoId;
    }

    public void setTodoId(Long todoId) {
        this.todoId = todoId;
    }
//
//    public Long getWoId() {
//        return woId;
//    }
//
//    public void setWoId(Long woId) {
//        this.woId = woId;
//    }

    public String getPmName() {
        return pmName;
    }

    public void setPmName(String pmName) {
        this.pmName = pmName;
    }

    public String getInfluence() {
        return influence;
    }

    public void setInfluence(String influence) {
        this.influence = influence;
    }

    public String getWorkTeamName() {
        return workTeamName;
    }

    public void setWorkTeamName(String workTeamName) {
        this.workTeamName = workTeamName;
    }

    public String getPriorityName() {
        return priorityName;
    }

    public void setPriorityName(String priorityName) {
        this.priorityName = priorityName;
    }

    public List<PMMaterialDTO> getPmMaterialDTOList() {
        return pmMaterialDTOList;
    }

    public void setPmMaterialDTOList(List<PMMaterialDTO> pmMaterialDTOList) {
        this.pmMaterialDTOList = pmMaterialDTOList;
    }

//    public String getWoCode() {
//        return woCode;
//    }
//
//    public void setWoCode(String woCode) {
//        this.woCode = woCode;
//    }

    public String getPeriodName() {
        return periodName;
    }

    public void setPeriodName(String periodName) {
        this.periodName = periodName;
    }

    public Period getPeriod() {
        return period;
    }

    public void setPeriod(Period period) {
        this.period = period;
    }
}
