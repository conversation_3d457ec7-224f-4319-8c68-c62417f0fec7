package cn.facilityone.shang.preventive.pm002.service;

import cn.facilityone.shang.common.component.quartz.job.preventive.GeneratePmTodoAndWorkOrderDayTimeJobServiceImpl;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PMNoticeActivity;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.preventive.pm001.dto.*;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeActivityRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import cn.facilityone.shang.preventive.pm002.respository.PMDateTodoRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by charles.chen on 2015/7/25.
 */
@Service
public class PmNoticeActivityServiceImpl implements PmNoticeActivityService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PmNoticeActivityService.class);
    @Autowired
    private PMDateTodoRepository pmDateTodoRepository;
    @Autowired
    private PreventiveMaintenanceRepository preventiveMaintenanceRepository;
    @Autowired
    private PMNoticeActivityRepository pmNoticeActivityRepository;
    @Autowired
    private PMNoticeRepository pmNoticeRepository;
    @Autowired
    private WorkOrderRepository workOrderRepository;

    private static final int aheadDayForGenerageActivity=7;

    /**
     * 自动为已创建工单的TODO生成未及时响应提醒通知和未及时完成提醒通知
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void generatePmNoticeActivityForWoForJob() {

        //工单状态-已创建/已经发布/进行中/暂停/暂停-不工作
        WorkOrder.WorkOrderStatus[] status={WorkOrder.WorkOrderStatus.CREATE,WorkOrder.WorkOrderStatus.DISPATCHE,
                WorkOrder.WorkOrderStatus.PROCESS,WorkOrder.WorkOrderStatus.STOP,WorkOrder.WorkOrderStatus.STOP_N};

        //最终存储集合
        List<PMNoticeActivity> pmNoticeActivities=new ArrayList<>();
        //通知集合
        List<PMNotice> pmNotices=new ArrayList<>();
        //由PM创建的工单集合--获得PM工单 且工单处于未开始状态 懒加载
        List<WorkOrder> workOrders=workOrderRepository.findLazyPmByStatusForJob(status);
        //未过期项目
        List<WorkOrder> overdueWorkorder = new ArrayList<>();
        List<Long> ids = Arrays.asList(ProjectContext.getProjects());
        for (WorkOrder workOrder:workOrders){
            if(!ids.contains(workOrder.getProject())){
                overdueWorkorder.add(workOrder);
            }
        }
        //移除已过期工单
        workOrders.removeAll(overdueWorkorder);

        if(workOrders.size()==0){
            return ;
        }

        //Map<woId：PmDateTodo>
        Map<Long,PMDateTodo> woTodoMaps = parserWoIdAndPmDateTodo(workOrders);

        //PM集合
        Set<Long> pms=new HashSet<>();
        for(WorkOrder workOrder : workOrders){
            pms.add(workOrder.getPreventiveMaintenance().getId());
        }

        //未及时响应的提醒
        //List<PMNotice> unResponseNotice=pmNoticeRepository.findByPmInAndType(pms.toArray(new PreventiveMaintenance[pms.size()]),PMNotice.PMNoticeType.RESPONSE);
        //Map<pmId：PMNotice>
        //Map<Long,PMNotice> unResponseMaps=this.noticeListToMap(unResponseNotice);
        //未完成响应的提醒
        List<PMNotice> unCompleteNotice = pmNoticeRepository.findByPmInAndType(pms, PMNotice.PMNoticeType.COMPLETE);
        //Map<pmId：PMNotice>
        Map<Long,PMNotice> unCompleteMaps = noticeListToMap(unCompleteNotice);

        //为每个工单创建提醒--根据工单的当前状态 判断是创建未完成提醒？还是未响应提醒+未完成提醒？
        for(WorkOrder workOrder : workOrders){
            try {
            //工单状态处于未开始-需要创建未响应提醒+未完成提醒
            if(WorkOrder.WorkOrderStatus.CREATE.equals(workOrder.getStatus()) || WorkOrder.WorkOrderStatus.DISPATCHE.equals(workOrder.getStatus())){
                // 未及时响应提醒通知
                /*if(unResponseMaps.get(workOrder.getPreventiveMaintenance().getId())!=null){
                    PMNoticeActivity pmNoticeActivity=this.createActivityForWorkOrder(workOrder,woTodoMaps,unResponseMaps);
                    pmNoticeActivities.add(pmNoticeActivity);
                    pmNotices.add(unResponseMaps.get(workOrder.getPreventiveMaintenance().getId()));
                }*/
                //未及时完成提醒通知
                if(unCompleteMaps.get(workOrder.getPreventiveMaintenance().getId())!=null){
                    PMNoticeActivity pmNoticeActivity = createActivityForWorkOrder(workOrder,woTodoMaps,unCompleteMaps);
                    pmNoticeActivities.add(pmNoticeActivity);
                    pmNotices.add(unCompleteMaps.get(workOrder.getPreventiveMaintenance().getId()));
                }
            }else{
                //工单状态=进行中- 需要生成未及时完成提醒通知
                if(unCompleteMaps.get(workOrder.getPreventiveMaintenance().getId())!=null){
                    PMNoticeActivity pmNoticeActivity = createActivityForWorkOrder(workOrder,woTodoMaps,unCompleteMaps);
                    pmNoticeActivities.add(pmNoticeActivity);
                    pmNotices.add(unCompleteMaps.get(workOrder.getPreventiveMaintenance().getId()));

                }
            }
            }catch (Exception e){
                LOGGER.error("work notice error ,{}",e.getMessage());
            }

        }//循环结束
        //生成activity与数据库中的activity进行对比
        if( pmNotices.size()>0){
            List<WoAndPmNoticeDTO>  woAndPmNoticeDTOs=pmNoticeActivityRepository.findWorkOrderAndPmNoticeByNotices(pmNotices.toArray(new PMNotice[pmNotices.size()]));
            //woId:List<PmnId>
            Map<Long,List<Long>> wpMaps = parseWPdtoToMap(woAndPmNoticeDTOs);
            Iterator<PMNoticeActivity> iter=pmNoticeActivities.iterator();
            while(iter.hasNext()){
                try {
                PMNoticeActivity pmn = iter.next();
                if(wpMaps.containsKey(pmn.getWorkOrder().getId())){
                    if(wpMaps.get(pmn.getWorkOrder().getId()).contains(pmn.getPmNotice().getId())){
                        //过滤已经在数据库中存在的元素
                        iter.remove();
                    }
                }
                }catch (Exception e){
                    LOGGER.error("activity create error,{}",e.getMessage());
                }
            }
        }
        //最终保存
        if(pmNoticeActivities.size()>0){
            pmNoticeActivityRepository.saveInBatch(pmNoticeActivities);
        }
    }

    private PMNoticeActivity createActivityForWorkOrder(WorkOrder workOrder,Map<Long,PMDateTodo> woTodoMaps,Map<Long,PMNotice> noticeMaps){
        PMNoticeActivity pmNoticeActivity=new PMNoticeActivity();
        pmNoticeActivity.setIsNotify(Boolean.FALSE);
        pmNoticeActivity.setPm(workOrder.getPreventiveMaintenance());
        pmNoticeActivity.setWorkOrder(workOrder);
        pmNoticeActivity.setPmDateTodo(woTodoMaps.get(workOrder.getId()));
        pmNoticeActivity.setPmNotice(noticeMaps.get(workOrder.getPreventiveMaintenance().getId()));
        return pmNoticeActivity;
    }

    /**
     * 为未生成工作提醒但已生成工单的TODO生成工作提醒JobLog jobLog
     */
    @Override
    @XiaTransactional(readOnly = false)
    public void generatePmNoticeActivityForRemindHaveWoForJob() {
        //需要创建的提醒通知
        List<PMNoticeActivity> pmNoticeActivities=new ArrayList<>();
        //已生成工单-工单状态=未开始=已创建+已经发布
        WorkOrder.WorkOrderStatus[] status={WorkOrder.WorkOrderStatus.CREATE,WorkOrder.WorkOrderStatus.DISPATCHE};
        
        Set<PMDateTodo> pmDateTodosForWo=new HashSet<>();

        //所有符合条件的(计划+工单)：已经生成工单+未生成工作提醒+工单状态未未开始
        List<WorkOrder> workOrderList=workOrderRepository.findByStatusAndTodoHasGenerateWoAndNoRemind(status);
        //未过期项目
        List<WorkOrder> overdueWorkorder = new ArrayList<>();
        List<Long> ids = Arrays.asList(ProjectContext.getProjects());
        for (WorkOrder workOrder:workOrderList){
            if(!ids.contains(workOrder.getProject())){
                overdueWorkorder.add(workOrder);
            }
        }
        //移除已过期工单
        workOrderList.removeAll(overdueWorkorder);
        if(CollectionUtils.isEmpty(workOrderList)){
            return;
        }
        
        List<Long> pmIds=new ArrayList<>();
        List<Long> pmTodoIds = new ArrayList<>();
        for(WorkOrder workOrder : workOrderList){
            try {
                pmIds.add(workOrder.getPreventiveMaintenance().getId());
                pmTodoIds.add(workOrder.getPmDateTodo().getId());
            }catch (Exception e){
                LOGGER.error("workOrder error, {}",e.getMessage());
            }
        }

        //获得每个PM 以及它所对应的多个工作提醒
        //PmId:List<PMNotice>
        Map<Long, List<PMNotice>> pmAndNoticeMaps = findPmNotice(PMNotice.PMNoticeType.REMIND, pmIds);

        //提醒通知中所有已经存在的(pmnotice 以及他所对应的多个计划通知时间)
        //PmNoticeId:List<Date>
        Map<Long,List<Date>> pmAndPmNoticeByPmMaps = findPmDateTodoByPmDateTodoIds(pmTodoIds);
        
        //为每一个工单创建多个工作提醒
        for(WorkOrder workOrder : workOrderList){
            try {
            List<PMNotice> pmNotices = pmAndNoticeMaps.get(workOrder.getPreventiveMaintenance().getId());
            
            if(!CollectionUtils.isEmpty(pmNotices)){
                List<PMNoticeActivity> pmNoticeActivityList = createNoticeActivityByWorkOrder(workOrder,pmNotices,pmAndPmNoticeByPmMaps);

                //保存数据 修改todo状态-genRemindNotice=true
                if(!CollectionUtils.isEmpty(pmNoticeActivityList)){
                    pmNoticeActivityRepository.saveInBatch(pmNoticeActivityList);
                }
//                pmNoticeActivities.addAll(pmNoticeActivityList);
            }

            PMDateTodo todo=workOrder.getPmDateTodo();
            todo.setGenRemindNotice(Boolean.TRUE);
            pmDateTodosForWo.add(todo);
            pmDateTodoRepository.save(todo);
        }catch (Exception e){
            LOGGER.error("generate PmNoticeActivity For RemindHave Wo  error，workOrderId=",workOrder.getId(),e);
            }
        }

        //保存数据 修改todo状态-genRemindNotice=true
//        if(!CollectionUtils.isEmpty(pmNoticeActivities)){
//            pmNoticeActivityRepository.saveInBatch(pmNoticeActivities);
//        }
//        pmDateTodoRepository.saveInBatch(pmDateTodosForWo);

    }

    private List<PMNoticeActivity> createNoticeActivityByWorkOrder(WorkOrder workOrder,List<PMNotice> pmNotices,Map<Long,List<Date>> longListMap){
        List<PMNoticeActivity> activities=new ArrayList<>();
        //每个提醒都是一个工作提醒 先判断这条提醒和对应的时间已经在pmNoticeActivity中
        for(PMNotice pmn : pmNotices){
            if(workOrder.getEstimatedArrivalDateTime()!=null){
              //根据工单预估开始时间和提前时间 算出通知中的提醒时间
                Date aheadDate = buildAheadDAate(workOrder.getEstimatedArrivalDateTime(),pmn.getAhead());
                //从已有的activity中根据 查找时间，判断该提醒是否已经存在
                List<Date> dates=longListMap.get(pmn.getId());
                if(CollectionUtils.isEmpty(dates) || !dates.contains(aheadDate)){
                    PMNoticeActivity pmNoticeActivity=new PMNoticeActivity();
                    pmNoticeActivity.setPmDateTodo(workOrder.getPmDateTodo());
                    pmNoticeActivity.setPm(workOrder.getPreventiveMaintenance());
                    pmNoticeActivity.setPmNotice(pmn);
                    pmNoticeActivity.setNotifyDateTime(aheadDate);
                    pmNoticeActivity.setWorkOrder(workOrder);
                    pmNoticeActivity.setIsNotify(Boolean.FALSE);
                    activities.add(pmNoticeActivity);
                }
            }
        }//for over
        return activities;
    }


    /**
     * 为未生成工作提醒且未生成工单的TODO生成工作提醒
     */
    @Override
    @XiaTransactional(readOnly = false)
    public  void generatePmNoticeActivityForRemindHaveNoWoForJob(Date nowDate){
        //需要创建的提醒通知集合
        List<PMNoticeActivity> pmNoticeActivities=new ArrayList<>();
        //待修改的todo集合
        Set<PMDateTodo> pmDateTodos=new HashSet<>();

        //过滤时间段
        List<PmDateAheadDTO> pmDateAheadDTOs = screeningQualifyingTodoDate(nowDate);

        if(CollectionUtils.isEmpty(pmDateAheadDTOs)){
            return;
        }

        List<Long> pmDateTodoIds=new ArrayList<>();
        for(PmDateAheadDTO dto : pmDateAheadDTOs){
            if (dto.getPmDateTodo()==null){
                continue;
            }
            pmDateTodoIds.add(dto.getPmDateTodo().getId());
        }

        //提醒通知中所有已经存在的(PmNotice 以及他所对应的多个计划通知时间)
        Map<Long,List<Date>> pmnIdAndNoticeDateByPmMaps = findPmDateTodoByPmDateTodoIds(pmDateTodoIds);

        for(PmDateAheadDTO pmDateAheadDTO : pmDateAheadDTOs){
            try {
            //根据todo_date 和提前提醒时间和工作提醒的提前天数 算出通知中的提醒时间
            Date aheadDate = buildAheadDAate(pmDateAheadDTO.getTodoDate(),pmDateAheadDTO.getTodoAhead());
            
            //从已有的activity中根据 查找时间，判断该提醒是否已经存在
            List<Date> dates = pmnIdAndNoticeDateByPmMaps.get(pmDateAheadDTO.getPmNotice().getId());
            
            if(CollectionUtils.isEmpty(dates) || !dates.contains(aheadDate)){
                
                PMNoticeActivity pmNoticeActivity=new PMNoticeActivity();
                pmNoticeActivity.setPm(pmDateAheadDTO.getPm());
                pmNoticeActivity.setPmNotice(pmDateAheadDTO.getPmNotice());
                pmNoticeActivity.setNotifyDateTime(aheadDate);
                pmNoticeActivity.setIsNotify(Boolean.FALSE);
                pmNoticeActivity.setPmDateTodo(pmDateAheadDTO.getPmDateTodo());
                pmNoticeActivities.add(pmNoticeActivity);
            }

            //修改todo状态
            pmDateAheadDTO.getPmDateTodo().setGenRemindNotice(Boolean.TRUE);
            pmDateTodos.add(pmDateAheadDTO.getPmDateTodo());
        }catch (Exception e){
                LOGGER.error(e.getMessage());
            }
        }

        //保存数据 修改todo状态
        pmNoticeActivityRepository.saveInBatch(pmNoticeActivities);
        pmDateTodoRepository.saveInBatch(pmDateTodos);
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void updateRemindNoticeForUpdateWorkOrderTime(WorkOrder workOrder) {
        List<PMNoticeActivity> pmNoticeActivityList=pmNoticeActivityRepository.findRemindNoticeActivityByWoId(workOrder.getId());
        if(pmNoticeActivityList != null && pmNoticeActivityList.size()>0){
            for(PMNoticeActivity pmNoticeActivity : pmNoticeActivityList){
                pmNoticeActivity.setNotifyDateTime(workOrder.getEstimatedArrivalDateTime());
            }
            pmNoticeActivityRepository.saveInBatch(pmNoticeActivityList);
        }

    }

    private Map<Long,PreventiveMaintenance>  findPmByIds(List<Long> pmIds){
        Map<Long,PreventiveMaintenance> result=new HashMap<>();
        
        if(!CollectionUtils.isEmpty(pmIds)){
            List<PreventiveMaintenance> pmList = preventiveMaintenanceRepository.findByIdIn(pmIds.toArray(new Long[pmIds.size()]));

            for(PreventiveMaintenance pm : pmList){
                result.put(pm.getId(),pm);
            }
        }
        return result;
    }

    private Map<Long,PMDateTodo>  parserWoIdAndPmDateTodo(List<WorkOrder> workOrders){
        Map<Long,PMDateTodo> result=new HashMap<>();
        PMDateTodo todo=null;
        for(WorkOrder workOrder : workOrders){
            result.put(workOrder.getId(),workOrder.getPmDateTodo());
        }

        return result;
    }

    private Map<Long,List<Date>> findPmDateTodoByPmDateTodoIds(List<Long> pmDateTodoIds){
        Map<Long,List<Date>> maps=new HashMap<>();
        if(CollectionUtils.isEmpty(pmDateTodoIds)){
            return maps;
        }
        List<PMNoticeActivity> dtos= pmNoticeActivityRepository.findByPmDateTodoIds(pmDateTodoIds);
        //未过期项目
        List<PMNoticeActivity> overduePMNoticeActivity = new ArrayList<>();
        List<Long> ids = Arrays.asList(ProjectContext.getProjects());
        for (PMNoticeActivity pmNoticeActivity:dtos){
            //todo 是否会报懒加载异常
            if(!ids.contains(pmNoticeActivity.getPmDateTodo().getProject())){
                overduePMNoticeActivity.add(pmNoticeActivity);
            }
        }
        //移除已过期项目的工单流程通知
        dtos.removeAll(overduePMNoticeActivity);
        for(PMNoticeActivity dto : dtos){
            try {
                if (maps.containsKey(dto.getPmDateTodo().getId())) {
                    maps.get(dto.getPmDateTodo().getId()).add(dto.getNotifyDateTime());
                } else {
                    List<Date> pns = new ArrayList<>();
                    pns.add(dto.getNotifyDateTime());
                    maps.put(dto.getPmDateTodo().getId(), pns);
                }
            }catch (Exception e){
                LOGGER.error("PMNoticeActivity error ,{}",e.getMessage());
            }
        }
        return maps;
    }

    private Date buildAheadDAate(Date currentDate,int aheadDay){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.DAY_OF_YEAR,-aheadDay);
        return calendar.getTime();
    }

    private Map<Long,List<PMNotice>> findPmNotice(PMNotice.PMNoticeType type,List<Long> pmIds){
        Map<Long,List<PMNotice>> maps=new HashMap<>();
        if(CollectionUtils.isEmpty(pmIds)){
            return maps;
        }
        List<PMNotice> pmAndNoticeAll = pmNoticeRepository.findByPmIdsInAndType(pmIds, type);

        for(PMNotice dto : pmAndNoticeAll){
            try {
                if(maps.containsKey(dto.getPm().getId())){
                maps.get(dto.getPm().getId()).add(dto);
            }else{
                List<PMNotice> pns=new ArrayList<>();
                pns.add(dto);
                maps.put(dto.getPm().getId(),pns);
            }
        }catch (Exception e){
                LOGGER.error("PMNotice error,PMNoticeId {}",dto.getId());
            }
        }
        return maps;
    }

    private Map<Long,PMNotice> noticeListToMap(List<PMNotice> pmNotices){
        Map<Long,PMNotice> maps=new HashMap<>();
        for(PMNotice pmNotice : pmNotices){
            try {
                maps.put(pmNotice.getPm().getId(),pmNotice);
            }catch (Exception e){
                LOGGER.error("pmNotice error ,{}",e.getMessage());
            }
        }

        return maps;
    }

    /**
     * 筛选符合条件的日期
     *   ( todo_date ) -  工单生成的提前天数  - max(工作提醒的提前天数) >= now
     */
    private List<PmDateAheadDTO> screeningQualifyingTodoDate(Date nowDate){

        //Activity =  ( todo_date ) -  工单生成的提前天数  - max(工作提醒的提前天数) >= now 查询合适的TODO
        List<PMNotice> pmnL = pmNoticeRepository.findByType(PMNotice.PMNoticeType.REMIND);
        if(CollectionUtils.isEmpty(pmnL)){
            return null;
        }
        Map<Long,List<PMNotice>> pmnM = new HashMap<>();
        int maxTotalAhead = 0;
        Map<Long,Integer> maxAheadM = new HashMap<>();
        for(PMNotice pmn:pmnL){
         try {
            if(pmnM.containsKey(pmn.getPm().getId())){
                pmnM.get(pmn.getPm().getId()).add(pmn);
            }else{
                List<PMNotice> _pmnL = new ArrayList<>();
                _pmnL.add(pmn);
                pmnM.put(pmn.getPm().getId(), _pmnL);
            }
            //最大值
            int tempAhead = 0;
            tempAhead = pmn.getAhead() + pmn.getPm().getAhead();
            if( maxTotalAhead < tempAhead){
                maxTotalAhead = tempAhead;
            }
            
            if(maxAheadM.containsKey(pmn.getPm().getId())){
                int _tempAhead = maxAheadM.get(pmn.getPm().getId());
                if(_tempAhead < pmn.getAhead()){
                    maxAheadM.put(pmn.getPm().getId(), pmn.getAhead());
                }
            }else{
                maxAheadM.put(pmn.getPm().getId(), pmn.getAhead());
            }
         }catch (Exception e){
             LOGGER.error("PmnoteiceId {}",pmn.getId());
         }
        }
        
        //TODO activated = true
        maxTotalAhead = maxTotalAhead + aheadDayForGenerageActivity;//再提前7天
        //取出时间区间内的todo，生成notice activity
        Date start = DateUtil.buildDateOnFirstSecond(nowDate);
        Date end = DateUtils.addDays(start, maxTotalAhead);
        List<PMDateTodo> pmDateTodoL = pmDateTodoRepository.findByPmIdsAndNoGenAndNoGenRemindNotice(pmnM.keySet(),start,end);
        if(CollectionUtils.isEmpty(pmDateTodoL)){
            return null;
        }
        List<PmDateAheadDTO> pmDateAheadDTOs = new ArrayList<>();
        for(PMDateTodo pmDateTodo:pmDateTodoL){
            try {
            if(pmnM.containsKey(pmDateTodo.getPm().getId())){
                List<PMNotice> _pmnL = pmnM.get(pmDateTodo.getPm().getId());
                for(PMNotice pmn:_pmnL){
                    PmDateAheadDTO dto = new PmDateAheadDTO(pmDateTodo.getPm().getId(),pmDateTodo,pmn,pmDateTodo.getDateTodo(),pmn.getAhead(),pmDateTodo.getPm().getAhead());
                    dto.setPm(pmDateTodo.getPm());
                    
                    if(maxAheadM.containsKey(dto.getPmId())){
                        Integer sum = maxAheadM.get(dto.getPmId())+dto.getTotalAhead();
                        dto.setTotalAhead(sum);
                    }
                    
                    pmDateAheadDTOs.add(dto);
                }
            }
        }catch (Exception e){

            }
        }

        Iterator<PmDateAheadDTO> iterator=pmDateAheadDTOs.iterator();
        while(iterator.hasNext()){
            PmDateAheadDTO dto=iterator.next();
            Calendar calendar=Calendar.getInstance();
            calendar.setTime(dto.getTodoDate());
            calendar.add(Calendar.DAY_OF_YEAR, -dto.getTotalAhead());
            calendar.add(Calendar.DAY_OF_YEAR, -aheadDayForGenerageActivity);//再提前7天
            Date checkDate=calendar.getTime();
            // ( todo_date ) -  工单生成的提前天数  - max(工作提醒的提前天数) < now
            if(nowDate.compareTo(checkDate)<0){
                iterator.remove();
            }
        }
        return pmDateAheadDTOs;
    }

    private Map<Long,List<Long>> parseWPdtoToMap(List<WoAndPmNoticeDTO> woAndPmNoticeDTOses){
        Map<Long,List<Long>> maps=new HashMap<>();
        for(WoAndPmNoticeDTO dto : woAndPmNoticeDTOses){
            if(maps.containsKey(dto.getWoId())){
                maps.get(dto.getWoId()).add(dto.getPmnId());
            }else{
                List<Long> ls=new ArrayList<>();
                ls.add(dto.getPmnId());
                maps.put(dto.getWoId(), ls);
            }
        }
        return maps;
    }
}
