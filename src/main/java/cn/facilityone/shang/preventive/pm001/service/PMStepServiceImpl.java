package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.common.repository.PictureRepository;
import cn.facilityone.shang.common.repository.WorkOrderRepository;
import cn.facilityone.shang.common.repository.XiaFileRepository;
import cn.facilityone.shang.entity.common.Picture;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderProcess;
import cn.facilityone.shang.organize.org001.repository.WorkTeamRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMStepRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import cn.facilityone.shang.workorder.common.dto.PMStepDTO;
import cn.facilityone.shang.workorder.wo003.repository.WorkOrderProcessRepository;
import cn.facilityone.xia.core.common.SystemConst;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/6/3.
 */
@Service
public class PMStepServiceImpl implements PMStepService {
    @Autowired
    private PreventiveMaintenanceRepository pmRepository;
    @Autowired
    private PMStepRepository pmStepRepository;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private WorkTeamRepository workTeamRepository;
    @Autowired
    private PreventiveMaintenanceService preventiveMaintenanceService;
    @Autowired
    private PictureRepository pictureRepository;
    @Autowired
    private WorkOrderProcessRepository workOrderProcessRepository;

    @Override
    public void createForPm(PreventiveMaintenance pm, List<PMStep> pmSteps) {
        for (PMStep pmStep : pmSteps) {
            pmStep.setPm(pm);
            if (pmStep.getWorkTeam() != null && pmStep.getWorkTeam().getId() != null) {
                pmStep.setWorkTeam(workTeamRepository.findOne(pmStep.getWorkTeam().getId()));
            }
            pmStepRepository.save(pmStep);
        }
    }

    @Override
    public List<PMStep> createSteps(PreventiveMaintenance pm, List<PMStep> pmSteps) {
        List<PMStep> steps = new ArrayList<>();
        if (pmSteps != null && pmSteps.size() > 0) {

            Map<Long, WorkOrderProcess> maps = new HashMap<>();

            steps = this.instancePmsteps(pm, pmSteps);

            for (PMStep pmStep : pmSteps) {

                if (pmStep.getWorkTeam() != null) {

                    if (maps.get(pmStep.getWorkTeam().getId()) == null) {
                        WorkOrderProcess wop = preventiveMaintenanceService.createWopByPmSteps(pm, pmStep);
                        pmStep.setWorkOrderProcess(wop);
                        maps.put(pmStep.getWorkTeam().getId(), wop);
                    } else {
                        pmStep.setWorkOrderProcess(maps.get(pmStep.getWorkTeam().getId()));
                    }
                }
            }
            pmStepRepository.saveInBatch(steps);

        }
        return steps;
    }

    @Override
    public void updateSteps(PreventiveMaintenance pm, List<PMStep> pmSteps) {

        List<PMStep> stepsInPm = pmStepRepository.findByPmId(pm.getId());

        //原先未绑定步骤
        if (stepsInPm == null || stepsInPm.size() == 0) {
            if (pmSteps != null && pmSteps.size() > 0) {
                //调用创建步骤的方法
                this.createSteps(pm, pmSteps);
            }
        } else {
            //等待修改保存的步骤
            List<PMStep> pmStepsForUpdate = new ArrayList<>();
            //原先已经绑定步骤
            Map<Long, WorkOrderProcess> workTeamIdAndWopMap = this.convertPmStepToWorkteamAndWop(stepsInPm);


            for(WorkOrderProcess workOrderProcess:workTeamIdAndWopMap.values()){
                workOrderProcess.setPassAndClose(pm.getArchive());
                workOrderProcessRepository.save(workOrderProcess);
            }




            //区分有无主键-是否新建的步骤
            List<PMStep> newSteps = this.findIsNewPmSteps(pmSteps, true);
            List<PMStep> hasIdSteps = this.findIsNewPmSteps(pmSteps, false);

            //原先已经绑定步骤 需要判断删除添加和工作组是否修改
            List<Long> intanceIds = new ArrayList<>();
            for (PMStep pmStep : stepsInPm) {
                intanceIds.add(pmStep.getId());
            }
            //没有主键 即为新添加的步骤 获得添加的步骤--直接创建步骤
            if (newSteps != null && newSteps.size() > 0) {
                List<PMStep> appendSteps = this.appenNewPmStep(pm, newSteps, workTeamIdAndWopMap);
                pmStepsForUpdate.addAll(appendSteps);
            }

            //有主键 和原有步骤对比 是否有删除项目--直接删除步骤
            if (hasIdSteps != null && hasIdSteps.size() > 0) {
                Map<Long, PMStep> hasIdMaps = this.convertPmStepToMap(hasIdSteps);
                Map<Long, PMStep> mapsInPm = this.convertPmStepToMap(stepsInPm);

                List<Long> hasIds = new ArrayList<>();
                for (PMStep pmStep : hasIdSteps) {
                    hasIds.add(pmStep.getId());
                }
                //删除多余的步骤
                this.deleteForUpdate(intanceIds, hasIds);

                for (Long id : hasIds) {
                    //待修改的步骤
                    PMStep stepOrigin = hasIdMaps.get(id);
                    PMStep stepInPm = mapsInPm.get(id);

                    //基本数据修改
                    stepInPm.setSort(stepOrigin.getSort());
                    stepInPm.setStep(stepOrigin.getStep());
                    //判断工作组是否修改
                    boolean workTeamHasChanged = this.workTeamHasChanged(stepOrigin, stepInPm);
                    if (workTeamHasChanged) {
                        stepInPm = this.updateForChangeWorkTeam(pm, stepOrigin, stepInPm, workTeamIdAndWopMap);
                    }
                    pmStepsForUpdate.add(stepInPm);
                }
                pmStepRepository.saveInBatch(pmStepsForUpdate);
            }
        }
    }

    private PMStep updateForChangeWorkTeam(PreventiveMaintenance pm, PMStep origin, PMStep instance, Map<Long, WorkOrderProcess> maps) {
        if (origin.getWorkTeam() == null) {
            instance.setWorkTeam(null);
            instance.setWorkOrderProcess(null);
        } else {
            if (maps.get(origin.getWorkTeam().getId()) != null) {
                instance.setWorkTeam(workTeamRepository.findOne(origin.getWorkTeam().getId()));
                instance.setWorkOrderProcess(maps.get(origin.getWorkTeam().getId()));
            } else {
                instance.setWorkTeam(workTeamRepository.findOne(origin.getWorkTeam().getId()));
                WorkOrderProcess wop = preventiveMaintenanceService.createWopByPmSteps(pm, instance);
                instance.setWorkOrderProcess(wop);
            }
        }

        return instance;
    }

    private void deleteForUpdate(List<Long> intanceIds, List<Long> hasIds) {

        intanceIds.removeAll(hasIds);
        if (intanceIds.size() > 0) {
            //删除步骤
            pmStepRepository.deleteByIdIn(intanceIds.toArray(new Long[intanceIds.size()]));
        }
    }

    private List<PMStep> appenNewPmStep(PreventiveMaintenance pm, List<PMStep> pmSteps, Map<Long, WorkOrderProcess> maps) {
        List<PMStep> steps = new ArrayList<>();
        pmSteps = this.instancePmsteps(pm, pmSteps);
        for (PMStep pmStep : pmSteps) {
            if (pmStep.getWorkTeam() != null) {
                if (maps.get(pmStep.getWorkTeam().getId()) == null) {
                    WorkOrderProcess wop = preventiveMaintenanceService.createWopByPmSteps(pm, pmStep);
                    pmStep.setWorkOrderProcess(wop);
                    maps.put(pmStep.getWorkTeam().getId(), wop);
                } else {
                    pmStep.setWorkOrderProcess(maps.get(pmStep.getWorkTeam().getId()));
                }
            }
            steps.add(pmStep);
        }
        return steps;
    }

    private List<PMStep> findIsNewPmSteps(List<PMStep> pmSteps, boolean flag) {
        List<PMStep> newSteps = new ArrayList<>();
        List<PMStep> steps = new ArrayList<>();
        for (PMStep pmStep : pmSteps) {
            if (pmStep.getId() == null) {
                newSteps.add(pmStep);
            } else {
                steps.add(pmStep);
            }
        }
        if (flag) {
            return newSteps;
        } else {
            return steps;
        }
    }

    private List<PMStep> instancePmsteps(PreventiveMaintenance pm, List<PMStep> pmSteps) {
        for (PMStep pmStep : pmSteps) {
            pmStep.setPm(pm);
            if (pmStep.getWorkTeam() != null && pmStep.getWorkTeam().getId() != null) {
                pmStep.setWorkTeam(workTeamRepository.findOne(pmStep.getWorkTeam().getId()));
            }
        }
        return pmSteps;
    }

    @Override
    @Transactional(readOnly = true)
    public List<PMStep> findByWoId(final Long id) {
        WorkOrder workOrder = workOrderRepository.findspaceByWoId(id);
        String relateWorkOrder = workOrder.getRelatedWorkOrders();
        List<Long> workOrderIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(relateWorkOrder)) {
            String[] woCodes = relateWorkOrder.split(SystemConst.STR_COMMA);
            List<WorkOrder> workOrders = workOrderRepository.findByCodes(woCodes);
            if (CollectionUtils.isNotEmpty(workOrders)) {
                for (WorkOrder wo : workOrders) {
                    workOrderIds.add(wo.getId());
                }
            }
        } else {
            workOrderIds.add(id);
        }
        PreventiveMaintenance pm = workOrder.getPreventiveMaintenance();
        if (pm == null) {
            return Collections.emptyList();
        }

        List<PMStep> pmSteps = pmStepRepository.findByPmIdAndWoIds(pm.getId(), workOrderIds);
        for (PMStep pmStep : pmSteps) {
            List<Picture> pictures = pictureRepository.findByTableNameAndPKID(PMStep.class.getSimpleName(), String.valueOf(pmStep.getId()));
            pmStep.setPictures(pictures);
        }

        return pmSteps;
    }

    /**
     * create dto from step
     *
     * @param steps
     * @param workOrder
     * @return
     */
    @Override
    public List<PMStepDTO> createDTO(List<PMStep> steps, WorkOrder workOrder) {
        if (CollectionUtils.isEmpty(steps)) {
            return Collections.emptyList();
        }
        List<PMStepDTO> dtos = new ArrayList<>();
        //sort by sort asc
        Collections.sort(steps, new Comparator<PMStep>() {
            @Override
            public int compare(PMStep o1, PMStep o2) {
                if (o1 == null || o2 == null) {
                    return 0;
                }
                Integer sort1 = o1.getSort();
                Integer sort2 = o2.getSort();
                return sort1.compareTo(sort2);
            }
        });

        Long workTeamID = workOrder.getWorkTeam() == null ? null : workOrder.getWorkTeam().getId();
        for (int i = 0, j = steps.size(); i < j; i++) {
            PMStep step = steps.get(i);
            PMStepDTO dto = new PMStepDTO();
            dto.setComment(step.getComment());
            dto.setFinished(step.isFinished());
            dto.setWorkTeam(step.getWorkTeam());
            dto.setStep(step.getStep());
            dto.setSort(step.getSort());
            dto.setId(step.getId());
            dto.setWorkTeam(step.getWorkTeam());
            dto.setPictures(step.getPictures());

            if (step.getWorkTeam().getId().equals(workTeamID)) {
                dto.setAccess(true);
            } else {
                dto.setAccess(false);
            }

            dtos.add(dto);
        }
        return dtos;
    }

    private boolean workTeamHasChanged(PMStep stepOrigin, PMStep stepTarget) {
        if (stepOrigin.getWorkTeam() == null && stepTarget.getWorkTeam() == null) {
            return false;
        } else if (stepOrigin.getWorkTeam() != null && stepTarget.getWorkTeam() != null) {
            if (stepOrigin.getWorkTeam().getId() != stepTarget.getWorkTeam().getId()) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    private Map<Long, WorkOrderProcess> convertPmStepToWorkteamAndWop(List<PMStep> pmSteps) {
        Map<Long, WorkOrderProcess> maps = new HashMap<>();
        for (PMStep pmStep : pmSteps) {
            if (pmStep.getWorkTeam() != null) {
                if (maps.get(pmStep.getWorkTeam().getId()) != null) {
                } else {
                    maps.put(pmStep.getWorkTeam().getId(), pmStep.getWorkOrderProcess());
                }
            }
        }
        return maps;
    }

    private Map<Long, PMStep> convertPmStepToMap(List<PMStep> pmSteps) {
        Map<Long, PMStep> maps = new HashMap<>();
        for (PMStep pmStep : pmSteps) {
            maps.put(pmStep.getId(), pmStep);
        }
        return maps;
    }

}
