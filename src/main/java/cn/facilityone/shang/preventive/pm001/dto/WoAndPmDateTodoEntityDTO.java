package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.workorder.WorkOrder;

import java.io.Serializable;

/**
 * Created by charles.chen on 2015/6/11.
 */
public class WoAndPmDateTodoEntityDTO implements Serializable {

    private PMDateTodo pmn;

    private WorkOrder wo;

    public WoAndPmDateTodoEntityDTO(PMDateTodo pmn, WorkOrder wo){
        this.pmn=pmn;
        this.wo=wo;
    }

    public WoAndPmDateTodoEntityDTO(PMDateTodo pmn){
        this.pmn=pmn;
    }

    public PMDateTodo getPmn() {
        return pmn;
    }

    public void setPmn(PMDateTodo pmn) {
        this.pmn = pmn;
    }

    public WorkOrder getWo() {
        return wo;
    }

    public void setWo(WorkOrder wo) {
        this.wo = wo;
    }
}
