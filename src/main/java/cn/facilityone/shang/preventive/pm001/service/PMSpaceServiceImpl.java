package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.organize.Building;
import cn.facilityone.shang.entity.organize.Floor;
import cn.facilityone.shang.entity.organize.Room;
import cn.facilityone.shang.entity.organize.Site;
import cn.facilityone.shang.entity.preventive.PMSpace;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.organize.org004.repository.BuildingRepository;
import cn.facilityone.shang.organize.org004.repository.FloorRepository;
import cn.facilityone.shang.organize.org004.repository.RoomRepository;
import cn.facilityone.shang.organize.org004.repository.SiteRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMSpaceRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by charles.chen on 2015/5/22.
 */
@Service
public class PMSpaceServiceImpl implements PMSpaceService{
    @Autowired
    private PMSpaceRepository pMSpaceRepository;

    @Autowired
    private PreventiveMaintenanceRepository preventiveMaintenanceRepository;

    @Autowired
    private SiteRepository siteRepository;

    @Autowired
    private BuildingRepository buildingRepository;

    @Autowired
    private FloorRepository floorRepository;

    @Autowired
    private RoomRepository roomRepository;
    @Override
    public PMSpace buildPMSpace(PMSpace ps) {

        PMSpace pspace =null;

        if(ps.getId()==null){
            pspace= new PMSpace();
        }else{
            pspace=pMSpaceRepository.findOne(ps.getId());
        }

        if (ps.getPm() != null && ps.getPm().getId() != null) {
            PreventiveMaintenance ptms =
                    preventiveMaintenanceRepository.findOne(ps.getPm().getId());
                pspace.setPm(ptms);
        }

        if (ps.getSite() != null && ps.getSite().getId() != null) {
            Site site = siteRepository.findOne(ps.getSite().getId());
            pspace.setSite(site);
        }else{
            pspace.setSite(null);
        }

        if (ps.getBuilding() != null && ps.getBuilding().getId() != null) {
            Building building = buildingRepository.findOne(ps.getBuilding().getId());
            pspace.setBuilding(building);
         }else{
            pspace.setBuilding(null);
        }

        if (ps.getFloor() != null && ps.getFloor().getId() != null) {
            Floor floor = floorRepository.findOne(ps.getFloor().getId());
            pspace.setFloor(floor);
        }else{
            pspace.setFloor(null);
        }

        if (ps.getRoom() != null && ps.getRoom().getId() != null) {
            Room room = roomRepository.findOne(ps.getRoom().getId());
            pspace.setRoom(room);
        }else{
            pspace.setRoom(null);
        }

        return pspace;

    }
}
