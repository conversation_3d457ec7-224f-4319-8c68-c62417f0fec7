package cn.facilityone.shang.preventive.pm001.respository;

import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.preventive.PMTool;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
public interface PMToolRepository extends XiaRepository<PMTool, Long> {

    @Modifying
    @Query("delete from #{#entityName} pt where pt.pm.id=?1")
    int deleteByPmId(Long id);


    @Query("select pt from #{#entityName} pt LEFT JOIN FETCH pt.pm where pt.pm.id in (?1)")
    List<PMTool> findByPmIds(Long[] pmId);

    @Query("select pt from #{#entityName} pt  where pt.pm.id = ?1")
    List<PMTool> findByPmId(Long pmId);
}
