package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.organize.Building;
import cn.facilityone.shang.entity.organize.Floor;
import cn.facilityone.shang.entity.organize.Room;
import cn.facilityone.shang.entity.organize.Site;

import java.io.Serializable;

public class PMSpaceDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = -572632214310260757L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Site getSite() {
        return site;
    }

    public void setSite(Site site) {
        this.site = site;
    }

    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public Floor getFloor() {
        return floor;
    }

    public void setFloor(Floor floor) {
        this.floor = floor;
    }

    public Room getRoom() {
        return room;
    }

    public void setRoom(Room room) {
        this.room = room;
    }

    private Long id;


    /** 项目 */
    private Site site;

    /** 区域 */
    private Building building;

    /** 楼层 */
    private Floor floor;

    /** 房间 */
    private Room room;
}
