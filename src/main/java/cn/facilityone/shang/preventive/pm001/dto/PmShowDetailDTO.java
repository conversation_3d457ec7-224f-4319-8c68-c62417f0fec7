package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.preventive.PMNotice;

import java.io.Serializable;
import java.util.List;

/**
 * Created by charles.chen on 2015/6/5.
 */
public class PmShowDetailDTO extends PmRequestDTO implements Serializable {
    //未及时响应
    private PmNoticeDTO unResponse;
    //未以及完成
    private PmNoticeDTO unComplete;
    //工作提醒
    private PmNoticeDTO workRemind;
    private PmNoticeDTO approvalNotice;
    //提前天数
    private List<Integer> aheadDays;

    public PmNoticeDTO getUnResponse() {
        return unResponse;
    }

    public void setUnResponse(PmNoticeDTO unResponse) {
        this.unResponse = unResponse;
    }

    public PmNoticeDTO getUnComplete() {
        return unComplete;
    }

    public void setUnComplete(PmNoticeDTO unComplete) {
        this.unComplete = unComplete;
    }

    public PmNoticeDTO getWorkRemind() {
        return workRemind;
    }

    public void setWorkRemind(PmNoticeDTO workRemind) {
        this.workRemind = workRemind;
    }

    public List<Integer> getAheadDays() {
        return aheadDays;
    }

    public void setAheadDays(List<Integer> aheadDays) {
        this.aheadDays = aheadDays;
    }

    public PmNoticeDTO getApprovalNotice() {
        return approvalNotice;
    }

    public void setApprovalNotice(PmNoticeDTO approvalNotice) {
        this.approvalNotice = approvalNotice;
    }
}
