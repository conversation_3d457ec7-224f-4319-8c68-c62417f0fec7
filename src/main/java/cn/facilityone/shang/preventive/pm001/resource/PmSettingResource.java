package cn.facilityone.shang.preventive.pm001.resource;

import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.common.service.UploadFileService;
import cn.facilityone.shang.entity.common.Attachment;
import cn.facilityone.shang.entity.common.XiaFile;
import cn.facilityone.shang.entity.preventive.PMTool;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrderLaborer;
import cn.facilityone.shang.preventive.pm001.dto.PageQueryRequestDTO;
import cn.facilityone.shang.preventive.pm001.dto.PmRequestDTO;
import cn.facilityone.shang.preventive.pm001.dto.PmShowDetailDTO;
import cn.facilityone.shang.preventive.pm001.dto.PtmsQueryRequest;
import cn.facilityone.shang.preventive.pm001.service.PMNoticeActivityService;
import cn.facilityone.shang.preventive.pm001.service.PMToolService;
import cn.facilityone.shang.preventive.pm001.service.PreventiveMaintenanceService;
import cn.facilityone.shang.preventive.pm002.service.PMDateTodoService;
import cn.facilityone.xia.core.common.Result;
import cn.facilityone.xia.core.exception.ValidationException;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.message.common.MessageType;
import org.apache.commons.lang.StringUtils;
import org.glassfish.jersey.server.mvc.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**PM 预防性维护设置
 * Created by charles.chen
 * @since 2015/5/20.
 * @version 1.0
 */
@Path("/pm001")
public class PmSettingResource {
    private static final String PREVENTTIVE_SETTING_PATH =
            "/business/preventive/pm001-pmSetting.ftl";
    @Autowired
    private PreventiveMaintenanceService preventiveMaintenanceService;
    @Autowired
    private PMDateTodoService pMDateTodoService;
    @Autowired
    private PMToolService pmToolService;
    @Autowired
    private UploadFileService uploadFileService;
    /**
     * 主页
     *
     * @return Map
     */
    @GET
    @Template(name = PREVENTTIVE_SETTING_PATH)
    public Map<String, Object> init() {
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("noticetypes", MessageType.findAll());
        return data;
    }

    /**
     * 根据多条件查询预防性设置列表-无法列搜索和列排序
     *
     * @param Page(PM)
     * @return Page
     */
    @POST
    @Path("preventives/ptms")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse findAllPatrolDetail(final PtmsQueryRequest request) {
        Pageable pageable =
                new DataTableRequest(request.getPageNumber(), request.getOffset(),
                        request.getPageSize(), new Sort(Sort.Direction.DESC, "id"), request.getDraw());
        PageQueryRequestDTO pageInfo =
                preventiveMaintenanceService.returnPageInfo(request, pageable);
        int count = pageInfo.getTotalElements().intValue();
        return new DataTableResponse(pageInfo.getPdto(), count, pageable.getPageNumber(),
                request.getDraw());
    }

    /**
     * 保存预防性维护设置
     *
     */
    @POST
    @Path("preventives/setting")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result createSetting(PmRequestDTO pmdto) {
        validate(pmdto.getPm());

        PreventiveMaintenance pm=preventiveMaintenanceService.create(pmdto);

        return new Result(XiaMesssageResource.getMessage("server.result.success.add", null, pm.getName()));

    }

    /**
     * 修改预防性维护设置
     *
     */
    @PUT
    @Path("preventives/setting")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result updateSetting(PmRequestDTO pdto) {

        validate(pdto.getPm());
        preventiveMaintenanceService.update(pdto);
        return new Result(XiaMesssageResource.getMessage("server.result.success.update", null, pdto.getPm().getName()));

    }

    /**
     * 根据ID获得设置 param:pm_id
     *
     * @return PM
     */
    @GET
    @Path("preventives/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result findPmById(@PathParam("id") Long id, @QueryParam("type") Integer type) {
        if (null != type && type == 2) {
            // 需要复制附件记录(先保存)
            List<Attachment> attachments = preventiveMaintenanceService.saveCopyFile(id);
            PmShowDetailDTO dto = preventiveMaintenanceService.findPmDtoById(id);
            dto.setAttachments(attachments);
            return new Result(dto);
        } else {
            return new Result(preventiveMaintenanceService.findPmDtoById(id));
        }
    }

    /**
     * 根据ID删除计划性维护
     * @param id
     */
    @DELETE
    @Path("preventives/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result deletePm(@PathParam("id") Long id) {
        preventiveMaintenanceService.deletePreventiveMaintenanceById(id);
        return new Result(XiaMesssageResource.getMessage("server.result.success.delete", null, ""));

    }

    /**
     * 获取计划性维护的附件
     * @param pmId
     */
    @GET
    @Path("attachments/{pmId}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result getAttachments(@PathParam("pmId") Long pmId) {
        return new  Result(uploadFileService.getAttachmentsByTableNameAndPkeyId(PreventiveMaintenance.class.getSimpleName(), String.valueOf(pmId)));
    }

    /**
     * 获取计划性维护的工具
     * @param pmid
     */
    @POST
    @Path("pmtools/pmid/{pmid}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result getPmTools(@PathParam("pmid") Long pmid, DataTableRequest request) {
        List<PMTool> pmTools = pmToolService.findPmToolsByPmId(pmid);
        return new Result(pmTools);
    }


    @GET
    @Path("fixedPeriod/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Result checkFixedPeriod(@PathParam("id") Long id) {
        return new Result(preventiveMaintenanceService.checkFixedPeriod(id));
    }


    private void validate(PreventiveMaintenance pm) {
        if (StringUtils.isBlank(pm.getName())) {
            throw ValidationException.builder().put("name", XiaMesssageResource.getMessage("MS000001", null, "")).build();
        }

        if( null==pm.getPeriod().getValue() ||pm.getPeriod().getValue().equals(0)){
            throw ValidationException.builder().put("Period",  XiaMesssageResource.getMessage("page.pm001.Period")).build();
        }


    }

}
