package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;

import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
public interface PMNoticeService {


    /**
     * 在计划性维护中生成提醒信息
     * @param PreventiveMaintenance List<PMNotice>
     * @return
     */
    void createforPm(PreventiveMaintenance pm,List<PMNotice> pmNotices);
}
