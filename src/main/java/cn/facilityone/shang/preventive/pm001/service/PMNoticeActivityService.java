package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PMNoticeActivity;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;

import java.util.List;

/**
 * Created by charles.chen on 2015/6/5.
 */
public interface PMNoticeActivityService {

    /**
     * 在计划性维护中生成TODO信息
     * @param PreventiveMaintenance List<PMDateTodo>
     * @return
     */
    void createByPmAndTodo(PreventiveMaintenance pm,List<PMDateTodo> pmDateTodoGroup);

}
