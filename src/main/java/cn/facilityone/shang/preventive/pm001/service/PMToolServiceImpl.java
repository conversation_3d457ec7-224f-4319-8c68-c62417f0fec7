package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.preventive.PMTool;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.preventive.pm001.respository.PMToolRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
@Service
public class PMToolServiceImpl implements  PMToolService {
    @Autowired
    private PreventiveMaintenanceRepository pmRepository;
    @Autowired
    private PMToolRepository pmToolRepository;

    @Override
    public void createForPm(PreventiveMaintenance pm, List<PMTool> pmTools) {
        for(PMTool pmTool : pmTools){
            pmTool.setPm(pm);
            pmToolRepository.save(pmTool);
        }
    }

    @Override
    public List<PMTool> findPmToolsByPmId(Long id) {
        if(id==null){
            return Collections.emptyList();
        }
        List<PMTool> pmTools=pmToolRepository.findByPmId(id);
        return pmTools;
    }
}
