package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.preventive.PMSpace;

import java.io.Serializable;
import java.util.List;

/**
 * Created by charles.chen on 2015/5/22.
 */
public class PmSpaceIdsDTO implements Serializable {
    private List<Long> siteIds;

    private List<Long> buildingIds;

    private List<Long> floorIds;

    private List<Long> roomIds;

    public List<Long> getSiteIds() {
        return siteIds;
    }

    public void setSiteIds(List<Long> siteIds) {
        this.siteIds = siteIds;
    }

    public List<Long> getBuildingIds() {
        return buildingIds;
    }

    public void setBuildingIds(List<Long> buildingIds) {
        this.buildingIds = buildingIds;
    }

    public List<Long> getFloorIds() {
        return floorIds;
    }

    public void setFloorIds(List<Long> floorIds) {
        this.floorIds = floorIds;
    }

    public List<Long> getRoomIds() {
        return roomIds;
    }

    public void setRoomIds(List<Long> roomIds) {
        this.roomIds = roomIds;
    }

    public PmSpaceIdsDTO buildDtoByPmSpace(List<PMSpace> pmSpaces){
        for(PMSpace pmSpace : pmSpaces){

            if(pmSpace!=null){
                if(pmSpace.getSite()!=null && pmSpace.getSite().getId()!=null){
                    this.getSiteIds().add(pmSpace.getSite().getId());
                }

                if(pmSpace.getBuilding()!=null && pmSpace.getBuilding().getId()!=null){
                    this.getBuildingIds().add(pmSpace.getBuilding().getId());
                }

                if(pmSpace.getFloor()!=null && pmSpace.getFloor().getId()!=null){
                    this.getFloorIds().add(pmSpace.getFloor().getId());
                }

                if(pmSpace.getRoom()!=null && pmSpace.getRoom().getId()!=null){
                    this.getRoomIds().add(pmSpace.getRoom().getId());
                }
            }

        }
        return this;
    }
}
