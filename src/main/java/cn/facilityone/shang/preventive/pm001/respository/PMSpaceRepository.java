package cn.facilityone.shang.preventive.pm001.respository;

import cn.facilityone.shang.entity.preventive.PMSpace;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

/**
 * Created by charles.chen on 2015/5/21.
 */
public interface PMSpaceRepository extends XiaRepository<PMSpace, Long> {

    @Modifying
    @Query("delete from #{#entityName} pms where pms.pm.id=?1")
    int deleteByPmId(Long id);

    @Modifying
    @Query("delete from #{#entityName} pms  where pms.id=?2")
    void deletedBySpaceId(Long pmId, Long pmSpaceId);
}
