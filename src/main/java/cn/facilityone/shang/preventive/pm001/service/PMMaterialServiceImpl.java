package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.inventory.Material;
import cn.facilityone.shang.entity.inventory.MaterialBatchChange;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.preventive.PMMaterial;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.inventory.respository.MaterialRepository;
import cn.facilityone.shang.organize.org001.repository.WorkTeamRepository;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.preventive.pm001.builder.PMMaterialBuilder;
import cn.facilityone.shang.preventive.pm001.respository.PMMaterialRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMStepRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import cn.facilityone.shang.stock.common.repository.InventoryManagementActivityRepository;
import cn.facilityone.shang.stock.common.repository.InventoryRepository;
import cn.facilityone.shang.stock.stock002.builder.InventoryBuilder;
import cn.facilityone.shang.stock.stock003.dto.StockDataDTO;
import cn.facilityone.shang.stock.stock003.dto.StockInDTO;
import cn.facilityone.shang.stock.stock003.service.InventoryManagementActivityService;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.security.oauth.SecurityContextHolder;
import cn.facilityone.xia.security.oauth.XiaPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by charles.chen on 2015/5/22.
 */
@Service
public class PMMaterialServiceImpl implements PMMaterialService {
    @Autowired
    private PreventiveMaintenanceRepository pmRepository;
    @Autowired
    private PMMaterialRepository pmMaterialRepository;
    @Autowired
    private MaterialRepository materialRepository;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private InventoryManagementActivityRepository activityRepository;
    @Autowired
    private WorkTeamRepository workTeamRepository;
    @Autowired
    private InventoryBuilder inventoryBuilder;
    @Autowired
    private PMMaterialBuilder pmMaterialBuilder;
    @Autowired
    private InventoryManagementActivityService inventoryManagementActivityService;

    @Override
    public void createForPm(PreventiveMaintenance pm, List<PMMaterial> pmMaterials) {
        for(PMMaterial pmMaterial : pmMaterials){
            pmMaterial.setPm(pm);
            Inventory inventory = inventoryRepository.findOne(pmMaterial.getInventory().getId());
            pmMaterial.setInventory(inventory);
            if(pmMaterial.getWorkTeam()!=null && pmMaterial.getWorkTeam().getId()!=null){
                pmMaterial.setWorkTeam(workTeamRepository.findOneHardly(pmMaterial.getWorkTeam().getId()));
            }
            pmMaterial.setAmount(pmMaterial.getAmount() == null ? 0 : pmMaterial.getAmount());
            pmMaterialRepository.save(pmMaterial);
        }

    }

    @Override
    @XiaTransactional(readOnly = false)
    public InventoryManagementActivity createActivityByPmm(List<PMMaterial> pmMaterials) {
        pmMaterialBuilder.init(pmMaterials).addInventory();
        List<Inventory> inventoryList = this.getInventoryListFromPmm(pmMaterials);
        inventoryBuilder.init(inventoryList).addWarehouse();
        StockInDTO dto = this.buildDtoForReserve(pmMaterials);
        Long projectId = pmMaterials.get(0).getPm().getProject();
        InventoryManagementActivity activity = inventoryManagementActivityService.handleStockReserveForPm(dto, projectId);
        //重新计算操作总量
        if(activity!=null){
            activity.setAmount(this.calculateAmount(activity));
        }

        return activity;
    }

    private Double calculateAmount(InventoryManagementActivity activity){
        Double amount = 0d;

        List<MaterialBatchChange> changeList = activityRepository.findMaterialBatchChangesById(activity.getId());
        for(MaterialBatchChange change : changeList){
            if(change!=null){
                amount = amount+change.getChangeNum();
            }
        }
        return amount;
    }

    private StockInDTO buildDtoForReserve(List<PMMaterial> pmMaterials){
        StockInDTO dto = null;
        if(pmMaterials!=null && pmMaterials.size()>0){
            Double totalAmount = 0d;
            Long operateUserId =
                    ((XiaPrincipal) SecurityContextHolder.getContext().getUserPrincipal())
                            .getId();
            dto = new StockInDTO();
            dto.setWarehouse(pmMaterials.get(0).getInventory().getWarehouse());
            dto.setLaborer(employeeRepository.findOneByUserId(operateUserId));
            dto.setAmount(0d);
            dto.setOperateDate(new Date());
            List<StockDataDTO> stockDatas =new ArrayList<>();
            for(PMMaterial pm : pmMaterials){
                if(pm.getInventory()!=null){
                    StockDataDTO data = new StockDataDTO();
                    data.setInventory(pm.getInventory());
                    totalAmount+= pm.getAmount() == null ? 0 : pm.getAmount();
                    data.setAmount(pm.getAmount());
                    stockDatas.add(data);
                }
            }
            dto.setAmount(totalAmount);
            dto.setStockDatas(stockDatas);
        }

        return dto;
    }

    private List<Inventory> getInventoryListFromPmm(List<PMMaterial> pmMaterials){
        List<Inventory> inventoryList = null;
        if(pmMaterials!=null && pmMaterials.size()>0){
            inventoryList = new ArrayList<>();
            for(PMMaterial pm : pmMaterials){
                if(pm.getInventory()!=null){
                    inventoryList.add(pm.getInventory());
                }
            }
        }

        return inventoryList;
    }
}
