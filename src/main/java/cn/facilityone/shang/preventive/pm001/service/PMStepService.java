package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.workorder.common.dto.PMStepDTO;

import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
public interface PMStepService {


    /**
     * 在PM中生成步骤信息
     *
     * @param PreventiveMaintenance List<PMStep>
     * @return
     */
    void createForPm(PreventiveMaintenance pm, List<PMStep> pmSteps);

    List<PMStep> createSteps(PreventiveMaintenance pm, List<PMStep> pmSteps);

    void updateSteps(PreventiveMaintenance pm, List<PMStep> pmSteps);

    List<PMStep> findByWoId(Long id);

    List<PMStepDTO> createDTO(List<PMStep> steps, WorkOrder workOrder);
}
