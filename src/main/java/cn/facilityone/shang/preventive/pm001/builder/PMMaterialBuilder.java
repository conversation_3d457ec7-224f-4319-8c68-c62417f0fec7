package cn.facilityone.shang.preventive.pm001.builder;

import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.preventive.PMMaterial;
import cn.facilityone.shang.preventive.pm001.respository.PMMaterialRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/9/1.
 */
@Service
public class PMMaterialBuilder {
    @Autowired
    private PMMaterialRepository pmMaterialRepository;

    private List<Long> ids;

    private List<PMMaterial> pmMaterialList;

    public PMMaterialBuilder init(List<PMMaterial> pmms){
        if(pmms!=null && pmms.size()>0){
            this.ids=new ArrayList<>();
            this.pmMaterialList=pmms;
            for(PMMaterial pmm : pmms){
                this.ids.add(pmm.getId());
            }
        }
        return this;
    }

    @XiaTransactional(readOnly = true)
    public PMMaterialBuilder addInventory(){
        if( this.ids!=null &&  this.ids.size()>0){
            List<Inventory> inventoryList = pmMaterialRepository.findInventorysByPmIds(this.ids.toArray(new Long[this.ids.size()]));
            Map<Long,Inventory> longInventoryMap = new HashMap<>();
            for(Inventory inventory : inventoryList){
                longInventoryMap.put(inventory.getId(),inventory);
            }
            for(PMMaterial pm : this.pmMaterialList){
                if(pm.getInventory()!=null){
                    pm.setInventory(longInventoryMap.get(pm.getInventory().getId()));
                }
            }
        }
        return this;
    }








}
