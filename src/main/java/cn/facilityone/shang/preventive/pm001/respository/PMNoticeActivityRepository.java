package cn.facilityone.shang.preventive.pm001.respository;

import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PMNotice.PMNoticeType;
import cn.facilityone.shang.entity.preventive.PMNoticeActivity;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.preventive.pm001.dto.DateAndPmNoticeDTO;
import cn.facilityone.shang.preventive.pm001.dto.PmAndPmNoticeDTO;
import cn.facilityone.shang.preventive.pm001.dto.WoAndPmNoticeDTO;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
public interface PMNoticeActivityRepository extends XiaRepository<PMNoticeActivity, Long> {
    @Modifying
    @Query("delete from #{#entityName} pma where pma.workOrder is null AND  pma.pm.id=?1")
    int deleteByPmIdAndWoIsNullAndUnNotify(Long id);

    @Modifying
    @Query("delete from #{#entityName} pma where  pma.pm.id=?1")
    int deleteByPmIdAndUnNotify(Long id);

    @Modifying
    @Query("delete from #{#entityName} pma where pma.pm.id=?1 AND pma.pmNotice.id in (?2)")
    int deleteByPmIdAndNoticeIdAndUnNotify(Long id,Long[] ids);

    @Query("select new cn.facilityone.shang.preventive.pm001.dto.WoAndPmNoticeDTO(pmn.workOrder.id as woId,pmn.pmNotice.id as pmnId) from #{#entityName} pmn where pmn.pmNotice in (?1) and pmn.workOrder is not null" )
    List<WoAndPmNoticeDTO> findWorkOrderAndPmNoticeByNotices(PMNotice[] pmNotices);

    @Deprecated
    @Query("select new cn.facilityone.shang.preventive.pm001.dto.DateAndPmNoticeDTO(pa.notifyDateTime as noticeDate,pa.pmNotice as pn) from #{#entityName} pa   where  pa.pm in (?1) and pa.pmNotice.type=?2")
    List<DateAndPmNoticeDTO> findDateAndPmNoticeByPmAndType(PreventiveMaintenance[] pms,PMNotice.PMNoticeType type);

    @Query("select pa from #{#entityName} pa left join fetch pa.pmDateTodo left join fetch pa.pmNotice  where  pa.pmDateTodo.id in (?1)")
    List<PMNoticeActivity> findByPmDateTodoIds(List<Long> pmDateTodoIds);

    @Deprecated
    @Query("select pa from #{#entityName} pa  where  pa.pm.id=?1 and pa.notifyDateTime>?2")
    List<PMNoticeActivity> findByPmId(Long pmId,Date date);

    @Query("select pa.notifyDateTime from #{#entityName} pa  where  pa.pm.id=?1 and pa.pmNotice.id=?2 and pa.isNotify=0 ORDER BY pa.notifyDateTime ASC")
    List<Date> findWoNoticeActivity(Long pmId,Long pnId);

    @Modifying
    @Query("delete from #{#entityName} pma where pma.workOrder is null AND pma.isNotify='0' AND  pma.pm.id=?1 AND pma.notifyDateTime in (?2)")
    int deleteByPmIdAndNoticeDate(Long id,Date[] date);

    @Modifying
    @Query("delete from #{#entityName} pa where pa.pmDateTodo.id in (?1) ")
    void deleteByTodoIdsIn(Long[] ids);

    @Query("select pa from #{#entityName} pa  where  pa.workOrder.id = ?1 and pa.pmNotice.type = '2' and pa.isNotify=0")
    List<PMNoticeActivity> findRemindNoticeActivityByWoId(Long woid);

    @Deprecated
    @Query("select pa from #{#entityName} pa  left join pa.pmDateTodo todo where todo.id in (?1)")
    List<PMNoticeActivity> findByTodoIdsIn(Long[] ids);
}