package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.common.Attachment;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.entity.preventive.*;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * Created by charles.chen on 2015/6/3.
 */
public class PmRequestDTO implements Serializable {

    private PreventiveMaintenance pm;

    private Set<WorkTeam> workTeams;

    private List<PMStep> pmSteps;

    private List<PMMaterial> pmMaterials;

    private List<PMTool> pmTools;

    private List<Attachment> attachments;

    private List<PMSpace> spaces;

    private List<PMNotice> pmNotices;

    public Set<WorkTeam> getWorkTeams() {
        return workTeams;
    }

    public void setWorkTeams(Set<WorkTeam> workTeams) {
        this.workTeams = workTeams;
    }

    public PreventiveMaintenance getPm() {
        return pm;
    }

    public void setPm(PreventiveMaintenance pm) {
        this.pm = pm;
    }

    public List<PMStep> getPmSteps() {
        return pmSteps;
    }

    public void setPmSteps(List<PMStep> pmSteps) {
        this.pmSteps = pmSteps;
    }

    public List<PMMaterial> getPmMaterials() {
        return pmMaterials;
    }

    public void setPmMaterials(List<PMMaterial> pmMaterials) {
        this.pmMaterials = pmMaterials;
    }

    public List<PMTool> getPmTools() {
        return pmTools;
    }

    public void setPmTools(List<PMTool> pmTools) {
        this.pmTools = pmTools;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }

    public List<PMSpace> getSpaces() {
        return spaces;
    }

    public void setSpaces(List<PMSpace> spaces) {
        this.spaces = spaces;
    }

    public List<PMNotice> getPmNotices() {
        return pmNotices;
    }

    public void setPmNotices(List<PMNotice> pmNotices) {
        this.pmNotices = pmNotices;
    }
}
