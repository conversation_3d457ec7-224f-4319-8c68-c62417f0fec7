package cn.facilityone.shang.preventive.pm001.respository;

import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.preventive.PMMaterial;
import cn.facilityone.shang.entity.preventive.PMTool;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
public interface PMMaterialRepository extends XiaRepository<PMMaterial, Long> {

    @Modifying
    @Query("delete from #{#entityName} pmm where pmm.pm.id=?1")
    int deleteByPmId(Long id);

    @Query("select pmm from #{#entityName} pmm LEFT JOIN FETCH pmm.pm where pmm.pm.id in (?1)")
    List<PMMaterial> findByPmIds(Long[] pmId);

    @Query("select pmm.inventory from #{#entityName} pmm where pmm.id in (?1)")
    List<Inventory> findInventorysByPmIds(Long[] pmId);
}
