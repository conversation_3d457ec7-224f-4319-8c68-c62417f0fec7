package cn.facilityone.shang.preventive.pm001.dto;


import cn.facilityone.shang.entity.common.Period;
import cn.facilityone.shang.entity.organize.WorkTeam;

import java.io.Serializable;
import java.util.Date;

public class PreventiveMaintenanceQueryDTO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 372746469405359987L;

    /**
     * 
     */

    private Long id;

    /** 预防性维护名称 */
    private String name;

    /** 预防性维护内容 */
    private String content;

    /** 周期 */
    private String period;

    /** 周期  枚举*/
    private Period periodEnum;

    /** 首次维护日期 */
    private Date dateFristTodo;

    /** 下一次维护日期 */
    private Date dateNextTodo;

    /** 预估工作时间 */
    private Double estimatedWorkingTime;

    /** 优先级 */
    private String priority;

    /** 关键指标 */
    private String kpi;

    /** 自动生成工单 */
    private boolean auto = true;

    /** 固定周期 */
    private boolean fixedPeriod = false;

    /** 提前生成工单的天数 */
    private int ahead;

    /** 影响 */
    private String influence;

    /** 预防性维护工作组 */
    private WorkTeam workteam;

    /** 预防性维护对象 － 设备编号 */
    private String equipmentCodes;
    /** 预防性维护对象 － 设备名称 */
    private String equipmentNames;
    /** 预防性维护对象 － 空间 */
    private String spaceNames;
    /** 预防性维护内容 － 耗材 */
    private String pMMaterials;

    private String equSysFullNames;

    public void setFixedPeriod(boolean fixedPeriod) {
        this.fixedPeriod = fixedPeriod;
    }

    public boolean isFixedPeriod() {

        return fixedPeriod;
    }

    public Period getPeriodEnum() {
        return periodEnum;
    }

    public void setPeriodEnum(Period periodEnum) {
        this.periodEnum = periodEnum;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Date getDateFristTodo() {
        return dateFristTodo;
    }

    public void setDateFristTodo(Date dateFristTodo) {
        this.dateFristTodo = dateFristTodo;
    }

    public Date getDateNextTodo() {
        return dateNextTodo;
    }

    public void setDateNextTodo(Date dateNextTodo) {
        this.dateNextTodo = dateNextTodo;
    }

    public Double getEstimatedWorkingTime() {
        return estimatedWorkingTime;
    }

    public void setEstimatedWorkingTime(Double estimatedWorkingTime) {
        this.estimatedWorkingTime = estimatedWorkingTime;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getKpi() {
        return kpi;
    }

    public void setKpi(String kpi) {
        this.kpi = kpi;
    }

    public boolean isAuto() {
        return auto;
    }

    public void setAuto(boolean auto) {
        this.auto = auto;
    }

    public int getAhead() {
        return ahead;
    }

    public void setAhead(int ahead) {
        this.ahead = ahead;
    }

    public String getInfluence() {
        return influence;
    }

    public void setInfluence(String influence) {
        this.influence = influence;
    }

    public WorkTeam getWorkteam() {
        return workteam;
    }

    public void setWorkteam(WorkTeam workteam) {
        this.workteam = workteam;
    }

    public String getEquipmentCodes() {
        return equipmentCodes;
    }

    public void setEquipmentCodes(String equipmentCodes) {
        this.equipmentCodes = equipmentCodes;
    }

    public String getEquipmentNames() {
        return equipmentNames;
    }

    public void setEquipmentNames(String equipmentNames) {
        this.equipmentNames = equipmentNames;
    }


    public String getSpaceNames() {
        return spaceNames;
    }

    public void setSpaceNames(String spaceNames) {
        this.spaceNames = spaceNames;
    }

    public String getpMMaterials() {
        return pMMaterials;
    }

    public void setpMMaterials(String pMMaterials) {
        this.pMMaterials = pMMaterials;
    }


    public String getEquSysFullNames() {
        return equSysFullNames;
    }

    public void setEquSysFullNames(String equSysFullNames) {
        this.equSysFullNames = equSysFullNames;
    }

}
