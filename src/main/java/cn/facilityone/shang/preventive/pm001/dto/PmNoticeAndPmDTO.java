package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;

import java.io.Serializable;

/**
 * Created by charles.chen on 2015/6/10.
 */
public class PmNoticeAndPmDTO implements Serializable {

    private PMNotice unResponse;

    private PMNotice unCpmolete;

    private PreventiveMaintenance pm;

    public PmNoticeAndPmDTO(PMNotice unResponse,PMNotice unCpmolete,PreventiveMaintenance pm){
        this.unResponse=unResponse;
        this.unCpmolete=unCpmolete;
        this.pm=pm;

    }

    public PMNotice getUnResponse() {
        return unResponse;
    }

    public void setUnResponse(PMNotice unResponse) {
        this.unResponse = unResponse;
    }

    public PMNotice getUnCpmolete() {
        return unCpmolete;
    }

    public void setUnCpmolete(PMNotice unCpmolete) {
        this.unCpmolete = unCpmolete;
    }

    public PreventiveMaintenance getPm() {
        return pm;
    }

    public void setPm(PreventiveMaintenance pm) {
        this.pm = pm;
    }
}
