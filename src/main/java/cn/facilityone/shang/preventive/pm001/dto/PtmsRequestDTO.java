package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.preventive.PMSpace;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;

import java.io.Serializable;
import java.util.List;

public class PtmsRequestDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = -7016809004706488723L;

    private PreventiveMaintenance ptm;

    private Long[] doc;

    private List<PMSpace> pmSpaces;

    public PreventiveMaintenance getPtm() {
        return ptm;
    }

    public void setPtm(PreventiveMaintenance ptm) {
        this.ptm = ptm;
    }

    public Long[] getDoc() {
        return doc;
    }

    public void setDoc(Long[] doc) {
        this.doc = doc;
    }

    public List<PMSpace> getPmSpaces() {
        return pmSpaces;
    }

    public void setPmSpaces(List<PMSpace> pmSpaces) {
        this.pmSpaces = pmSpaces;
    }

    //   private WorkOrderProcessDto notice;
//
//
//    public WorkOrderProcessDto getNotice() {
//        return notice;
//    }
//
//    public void setNotice(WorkOrderProcessDto notice) {
//        this.notice = notice;
//    }
}
