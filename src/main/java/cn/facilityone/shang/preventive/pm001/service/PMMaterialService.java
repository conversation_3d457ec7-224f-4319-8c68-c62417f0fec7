package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.inventory.InventoryManagementActivity;
import cn.facilityone.shang.entity.preventive.PMMaterial;
import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;

import java.util.List;

/**
 * Created by charles.chen on 2015/5/22.
 */
public interface PMMaterialService {

    /**
     * 在计划性维护中生成物料信息
     * @param PreventiveMaintenance List<PMMaterial>
     * @return
     */
    void createForPm(PreventiveMaintenance pm,List<PMMaterial> pmMaterials);

    /**
     * 根据物料进行库存预定操作
     * @param PreventiveMaintenance List<PMMaterial>
     * @return InventoryManagementActivity
     */
    InventoryManagementActivity createActivityByPmm(List<PMMaterial> pmMaterials);
}
