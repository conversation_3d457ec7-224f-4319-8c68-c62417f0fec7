package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.common.Attachment;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.entity.preventive.PMSpace;
import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrderProcess;
import cn.facilityone.shang.preventive.pm001.dto.PageQueryRequestDTO;
import cn.facilityone.shang.preventive.pm001.dto.PmRequestDTO;
import cn.facilityone.shang.preventive.pm001.dto.PmShowDetailDTO;
import cn.facilityone.shang.preventive.pm001.dto.PtmsQueryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;


public interface PreventiveMaintenanceService {
    /**
     * 根据条件获得预防性维护分页信息
     */
    Page<PreventiveMaintenance> findPreventiveMaintenance(
            final PtmsQueryRequest pagerequest, Pageable page);

    /**
     * 返回页面分页信息DTO
     */
    PageQueryRequestDTO returnPageInfo(final PtmsQueryRequest pagerequest, Pageable page);

    /**
     * 根据页面信息保存
     */
    PreventiveMaintenance create(PmRequestDTO pdto);


    /**
     * 根据页面信息修改
     */
    void update(PmRequestDTO pdto);

    /**
     * 根据Id获得记录DTO
     */
    PmShowDetailDTO findPmDtoById(Long id);

    /**
     * 根据Id删除记录 并且同时删除相关的耗材和空间，设备信息
     */
    void deletePreventiveMaintenanceById(Long id);


    /**
     * 获得唯一空间 向上取整
     * @return PMSpace
     */
    PMSpace findOneSpace(List<PMSpace> pmses);

    WorkOrderProcess createWopByPmSteps(PreventiveMaintenance pm,PMStep pmStep);

    List<WorkTeam> findWorkTeamsByPmId(Long id);

    //保存复制附件记录
    List<Attachment> saveCopyFile(Long id);

    //处理更新PPM 错误周期
    String updatePeriod();

    int getPmEqCountByEqId(Long eqId);

    void deletePmEqCountByEqId(Long eqId);

    Boolean checkFixedPeriod(Long id);
}
