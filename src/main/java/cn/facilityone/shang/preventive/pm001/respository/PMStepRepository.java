package cn.facilityone.shang.preventive.pm001.respository;

import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.entity.preventive.PMMaterial;
import cn.facilityone.shang.entity.preventive.PMSpace;
import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.entity.workorder.WorkOrderProcess;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
public interface PMStepRepository extends XiaRepository<PMStep, Long> {

    @Modifying
    @Query("delete from #{#entityName} ps where ps.pm.id=?1 and ps.workOrder is null")
    int deleteByPmId(Long id);

    @Modifying
    @Query("delete from #{#entityName} ps where ps.id in (?1)")
    int deleteByIdIn(Long ids[]);

    @Query("select ps from #{#entityName} ps LEFT JOIN FETCH ps.workTeam wt LEFT JOIN FETCH ps.workOrderProcess wop where ps.pm.id=?1 and ps.workOrder is null")
    List<PMStep> findByPmId(Long pmId);

    @Query("select ps from #{#entityName} ps LEFT JOIN FETCH ps.pm where ps.pm.id in (?1) and ps.workOrder is null")
    List<PMStep> findByPmIds(Long[] pmId);

    @Query("select ps from #{#entityName} ps LEFT JOIN FETCH ps.workTeam where ps.pm.id = ?1 and ps.workOrder is null")
    List<PMStep> findByPmIdAndWoIsNullHardly(Long id);

    List<PMStep> findByIdIn(Long ids[]);

    @Query("select wt from #{#entityName} ps LEFT JOIN  ps.workTeam wt where ps.pm.id in (?1)")
    List<WorkTeam> findWorkTeamByIds(Long[] ids);

    @Query("select wop from #{#entityName} ps LEFT JOIN  ps.workOrderProcess wop  where ps.pm.id in (?1)")
    List<WorkOrderProcess> findWorkOrderProcessByIds(Long[] ids);

    @Query("select ps from #{#entityName} ps LEFT JOIN FETCH  ps.workOrderProcess wop LEFT JOIN FETCH ps.workTeam wt where ps.pm.id = ?1 and ps.workOrder is null")
    List<PMStep> findLazyByPmIdAndWoIsNull(Long id);

    @Query("select wt from #{#entityName} ps LEFT JOIN  ps.workTeam wt  where ps.pm.id = ?1")
    List<WorkTeam> findWorkTeamByPmId(Long id);

    @Query("select ps from #{#entityName} ps where ps.pm.id = ?1 and ps.workOrder.id = ?2 ")
    List<PMStep> findByPmIdAndWoId(Long pmId, Long workOrderId);

    @Query("select ps from #{#entityName} ps LEFT JOIN FETCH ps.workTeam wt where ps.pm.id = ?1 and ps.workOrder.id in(?2) ")
    List<PMStep> findByPmIdAndWoIds(Long id, List<Long> workOrderIds);
    
    @Query("select ps from #{#entityName} ps LEFT JOIN FETCH ps.workTeam wt  LEFT JOIN FETCH ps.workOrder where ps.pm.id = ?1 and ps.workOrder.code in(?2) ")
    List<PMStep> findByPmIdAndWoCodes(Long id, List<String> workOrderCodes);

    @Query(value="select DISTINCT(pm.pm_id) FROM pm_step ps LEFT OUTER JOIN pm ON ps.pm = pm.pm_id LEFT OUTER JOIN work_team wo on ps.wt_id = wo.work_team_id" +
            " where wo.work_team_id=?1 and ps.deleted=0 and pm.deleted = 0 and ps.wo_id is null ", nativeQuery = true)
    List<PreventiveMaintenance> findByWoTeamId(Long woTeamId);

    @Query(value="select DISTINCT(ps.wo_proc_id) FROM pm_step ps LEFT OUTER JOIN pm ON ps.pm = pm.pm_id LEFT OUTER JOIN work_team wo on ps.wt_id = wo.work_team_id" +
            " where wo.work_team_id=?1 and ps.deleted=0 and pm.deleted = 0 ", nativeQuery = true)
    List<Long> findPm(Long woTeamId);

    @Query("select wt from #{#entityName} ps LEFT JOIN  ps.workTeam wt  where ps.pm.id = ?1  and ps.workOrder is null")
    List<WorkTeam> findWorkTeamByPmIdAndWorkOrder(Long id);

    @Query("select ps from #{#entityName} ps LEFT JOIN FETCH ps.workOrderProcess wop  left join fetch ps.pm where wop.id = ?1")
    List<PMStep> findPmByWopId(Long woProcessId);
}

