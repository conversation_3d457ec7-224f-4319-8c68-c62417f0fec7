package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.preventive.PMTool;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;

import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
public interface PMToolService {

    /**
     * 在PM中生成工具信息
     * @param PreventiveMaintenance List<PMTool>
     * @return
     */
    void createForPm(PreventiveMaintenance pm,List<PMTool> pmTools);

    List<PMTool> findPmToolsByPmId(Long id);
}
