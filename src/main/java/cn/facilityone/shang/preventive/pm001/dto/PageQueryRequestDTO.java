package cn.facilityone.shang.preventive.pm001.dto;


import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;

import java.util.List;

public class PageQueryRequestDTO extends DataTableRequest {



    private List<PreventiveMaintenanceQueryDTO> pdto;

    public List<PreventiveMaintenanceQueryDTO> getPdto() {
        return pdto;
    }

    public void setPdto(List<PreventiveMaintenanceQueryDTO> pdto) {
        this.pdto = pdto;
    }

    private Long totalElements;

    public Long getTotalElements() {
        return totalElements;
    }

    public void setTotalElements(Long totalElements) {
        this.totalElements = totalElements;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    private int totalPages;
}
