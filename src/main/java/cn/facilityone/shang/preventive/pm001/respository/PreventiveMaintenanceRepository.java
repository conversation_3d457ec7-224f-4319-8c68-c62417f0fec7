package cn.facilityone.shang.preventive.pm001.respository;

import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.preventive.*;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by charles.chen on 2015/5/21.
 */
public interface PreventiveMaintenanceRepository extends XiaRepository<PreventiveMaintenance, Long> {

    @Query("select eqs from #{#entityName} pm left join pm.equipments eqs left join fetch eqs.equipmentSystem sys where eqs.deleted='0' AND  pm.id=?1")
    List<Equipment> findEquipmentsByIdHardly(Long id);

    @Query("select spaces from #{#entityName} pm left join  pm.spaces spaces where  pm.id=?1")
    List<PMSpace> findPMSpacesByIdHardly(Long id);

    @Query("select steps from #{#entityName} pm left join  pm.pmSteps steps where  pm.id=?1")
    List<PMStep> findPMStepsById(Long id);

    @Query("select tools from #{#entityName} pm left join  pm.pmTools tools where  pm.id=?1")
    List<PMTool> findPMToolsByIdHardly(Long id);

    @Query("select material from #{#entityName} pm left join  pm.pmMaterials material left join fetch material.inventory inv  where  pm.id=?1 and inv.deleted = '0'")
    List<PMMaterial> findPMMaterialsByIdHardly(Long id);

    @Query("select material from #{#entityName} pm left join  pm.pmMaterials material left join fetch material.inventory inv where  pm.id=?1")
    List<PMMaterial> findPMMaterialsById(Long id);

    @Query("select pmnotice from #{#entityName} pm left join  pm.pmNotices pmnotice left join fetch pmnotice.notice  where  pm.id=?1")
    List<PMNotice> findPMNoticesById(Long id);

    /**
     * 仅限JOB
     * @return
     */
    @Query
    List<PreventiveMaintenance> findByAutoTrueAndProjectIsNotNull();

    @Query("select pm from #{#entityName} pm left join fetch pm.period left join fetch pm.estimatedWorkingTime where pm.id in (?1)")
    List<PreventiveMaintenance> findByIdIn(Long[] ids);

    @Query("select pm from #{#entityName} pm  left join fetch pm.period left join fetch pm.estimatedWorkingTime where pm.id=?1")
    PreventiveMaintenance findOneHardly(Long id);

    /**
     * 仅限Job使用
     * @return
     */
    @Query("select pm from #{#entityName} pm left join fetch pm.period where pm.project is not null")
    List<PreventiveMaintenance> findAllForJob();

    /**
     * 仅限Job使用
     * @return
     */
    @Query("select pm from #{#entityName} pm left join fetch pm.period where pm.project is not null and pm.project in (?1)")
    List<PreventiveMaintenance> findAllForJob(List<Long> projIds);

    /**
     * report
     * @param id
     * @return
     */
    @Query("select distinct pm from #{#entityName} pm " +
            " left join fetch pm.spaces " +
            " left join fetch pm.period " +
            " where pm.deleted = ?1 and pm.id = ?2")
    PreventiveMaintenance findByIdsForReport(Boolean del, Long id);

    @Query("select distinct todo from #{#entityName} pm left join pm.pmDateTodos todo left join fetch todo.workOrders where pm.id = ?1 and todo.dateTodo >= ?2 and todo.dateTodo <= ?3")
    List<PMDateTodo> getPMDateTodos(Long id, Date firstDate, Date lastDate);


    //查询周期id相同的PPM
    @Query(value = "select pm.* from pm pm where pm_id not in (select min(pm_id) as pm_id from pm where deleted=0 group by period) and deleted=0",nativeQuery = true)
    List <PreventiveMaintenance> findRepeatPeriodPPM();

}
