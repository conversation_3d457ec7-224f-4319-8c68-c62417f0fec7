package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by charles.chen on 2015/6/12.
 */
public class PmDateAheadDTO implements Serializable {

    private PreventiveMaintenance pm;

    private PMDateTodo pmDateTodo;

    private PMNotice pmNotice;

    private Date todoDate;

    private Integer todoAhead;

    private Integer totalAhead;

    private Long pmId;

    public PmDateAheadDTO(Long pmId,PMDateTodo pmDateTodo,PMNotice pmNotice,Date todoDate,Integer todoAhead,Integer totalAhead){
        this.pmId=pmId;
        this.pmDateTodo=pmDateTodo;
        this.pmNotice=pmNotice;
        this.todoDate=todoDate;
        this.todoAhead=todoAhead;
        this.totalAhead=totalAhead;
    }

    public PreventiveMaintenance getPm() {
        return pm;
    }

    public void setPm(PreventiveMaintenance pm) {
        this.pm = pm;
    }

    public Long getPmId() {
        return pmId;
    }

    public void setPmId(Long pmId) {
        this.pmId = pmId;
    }

    public PMDateTodo getPmDateTodo() {
        return pmDateTodo;
    }

    public void setPmDateTodo(PMDateTodo pmDateTodo) {
        this.pmDateTodo = pmDateTodo;
    }

    public Date getTodoDate() {
        return todoDate;
    }

    public void setTodoDate(Date todoDate) {
        this.todoDate = todoDate;
    }

    public Integer getTodoAhead() {
        return todoAhead;
    }

    public void setTodoAhead(Integer todoAhead) {
        this.todoAhead = todoAhead;
    }

    public Integer getTotalAhead() {
        return totalAhead;
    }

    public void setTotalAhead(Integer totalAhead) {
        this.totalAhead = totalAhead;
    }

    public PMNotice getPmNotice() {
        return pmNotice;
    }

    public void setPmNotice(PMNotice pmNotice) {
        this.pmNotice = pmNotice;
    }
}
