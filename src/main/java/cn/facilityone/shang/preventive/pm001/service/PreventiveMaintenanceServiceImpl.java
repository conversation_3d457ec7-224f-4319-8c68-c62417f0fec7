package cn.facilityone.shang.preventive.pm001.service;


import cn.facilityone.shang.asset.asset002.repository.EquipmentRepository;
import cn.facilityone.shang.asset.asset002.repository.TimeCountRepository;
import cn.facilityone.shang.common.component.datatable.model.DataTableColumn;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.service.DataTableService;
import cn.facilityone.shang.common.repository.AttachmentRepository;
import cn.facilityone.shang.common.repository.PeriodRepository;
import cn.facilityone.shang.common.staticmetamodel.CommonFields_;
import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.common.Attachment;
import cn.facilityone.shang.entity.common.Period;
import cn.facilityone.shang.entity.common.TimeCount;
import cn.facilityone.shang.entity.inventory.Inventory;
import cn.facilityone.shang.entity.organize.Employee;
import cn.facilityone.shang.entity.organize.WorkTeam;
import cn.facilityone.shang.entity.preventive.*;
import cn.facilityone.shang.entity.workorder.WorkOrder;
import cn.facilityone.shang.entity.workorder.WorkOrderProcess;
import cn.facilityone.shang.organize.org002.repository.EmployeeRepository;
import cn.facilityone.shang.preventive.common.util.PeriodDateUtil;
import cn.facilityone.shang.preventive.pm001.dto.*;
import cn.facilityone.shang.preventive.pm001.respository.*;
import cn.facilityone.shang.preventive.pm001.staticmetamodel.PM_;
import cn.facilityone.shang.preventive.pm002.respository.PMDateTodoRepository;
import cn.facilityone.shang.preventive.pm002.service.PMDateTodoService;
import cn.facilityone.shang.stock.stock002.builder.InventoryBuilder;
import cn.facilityone.shang.workorder.common.constant.WOProcessTemplateConstant;
import cn.facilityone.shang.workorder.wo003.repository.WorkOrderProcessRepository;
import cn.facilityone.xia.i18n.XiaMesssageResource;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import cn.facilityone.xia.persistence.project.ProjectContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.*;
import java.util.*;

/**
 * 预防性位置设置
 * 
 * <AUTHOR>
 * @version 4.0
 *  
 */
@Service
public class PreventiveMaintenanceServiceImpl  implements PreventiveMaintenanceService {

    public  final  static  String SUCCESS="Success";
    @Autowired
    private PreventiveMaintenanceRepository preventiveMaintenanceRepository;
    @Autowired
    private PMStepRepository pmStepRepository;
    @Autowired
    private PMMaterialRepository pmMaterialRepository;
    @Autowired
    private PMToolRepository pmToolRepository;
    @Autowired
    private PMSpaceRepository pMSpaceRepository;
    @Autowired
    private PMSpaceService pMSpaceService;
    @Autowired
    private EquipmentRepository equipmentRepository;
    @Autowired
    private PMNoticeActivityRepository pmNoticeActivityRepository;
    @Autowired
    private AttachmentRepository attachmentRepository;
    @Autowired
    private PMNoticeRepository pmNoticeRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private PMDateTodoService pMDateTodoService;
    @Autowired
    private WorkOrderProcessRepository workOrderProcessRepository;
    @Autowired
    private PMDateTodoRepository pmDateTodoRepository;
    @Autowired
    private TimeCountRepository timeCountRepository;
    @Autowired
    private PeriodRepository periodRepository;
    @Autowired
    private PMStepService pmStepService;
    @Autowired
    private PMToolService pmToolService;
    @Autowired
    private PMMaterialService pmMaterialService;
    @Autowired
    private PMNoticeService pmNoticeService;
    @Autowired
    private DataTableService dataTableService;
    @Autowired
    private InventoryBuilder inventoryBuilder;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    private String bpmKey;
    public String getBpmKey() {
        return bpmKey;
    }

    public void setBpmKey(String bpmKey) {
        this.bpmKey = bpmKey;
    }

    @Override
    @XiaTransactional(readOnly = true)
    public Page<PreventiveMaintenance> findPreventiveMaintenance(
            final PtmsQueryRequest pagerequest, Pageable page) {
        /**
         * 默认page
         */
        if (null == page) {
            page = new PageRequest(0, Integer.MAX_VALUE, new Sort(Direction.DESC, PM_.ID));
        }else{
            //排序
            List<DataTableColumn> tableColumn=pagerequest.getColumns();
            for(DataTableColumn column : tableColumn){
                if("equSysFullNames".equals(column.getName())){
                    column.setName("equipments.equipmentSystem.fullName");
                }
                if("spaceNames".equals(column.getName())){
                    column.setName("spaces.site.name,spaces.building.name,spaces.floor.name,spaces.room.name");
                }
                if("periodEnum.type".equals(column.getName())){
                    column.setName("period.type");
                    column.setSearchType(DataTableColumn.SEARCH_TYPE_ENUM);
                }

            }
            page =
                    new DataTableRequest(page.getPageNumber(), page.getOffset(), page.getPageSize(),
                            dataTableService.buildColumnSort(pagerequest.getColumns()),
                            pagerequest.getDraw());
        }
        Page<PreventiveMaintenance> result =
                preventiveMaintenanceRepository.findAll(new Specification<PreventiveMaintenance>() {
                    @Override
                    public Predicate toPredicate(Root<PreventiveMaintenance> root,
                            CriteriaQuery<?> query, CriteriaBuilder cb) {
                        List<Predicate> predicatesList = new ArrayList<Predicate>();
                        PtmsQueryDTO ptos = pagerequest.getPto();
                        List<DataTableColumn> dataTableColumnList=pagerequest.getColumns();
                        //搜索
                        if(dataTableColumnList!=null && dataTableColumnList.size()>0){
                            List<Predicate> lps =
                                    dataTableService.buildColumnSearch(dataTableColumnList, root, cb);
                            predicatesList.addAll(lps);
                        }
                        if (ptos != null) {
                            // 名称--模糊查找-name
                            if (!StringUtils.isBlank(ptos.getPmName())) {
                                predicatesList.add(cb.like(root.<String>get(PM_.NAME),
                                        "%" + ptos.getPmName() + "%"));
                            }
                            // 周期 -period
                            if (!StringUtils.isBlank(ptos.getPeriod())) {
                                predicatesList.add(cb.equal(root.<String>get(PM_.PERIOD),
                                        ptos.getPeriod()));
                            }

                            // 是否自动生成工单-auto
                            if (!StringUtils.isBlank(ptos.getAuto())) {
                                if (PM_.booleanTrue.equals(ptos.getAuto())) {
                                    predicatesList.add(cb.equal(root.<String>get(PM_.AUTO), true));

                                } else if (PM_.booleanFalse.equals(ptos.getAuto())) {
                                    predicatesList.add(cb.equal(root.<String>get(PM_.AUTO), false));
                                }
                            }

                            // 设备-qequipment
                            if (ptos.getEquipment() != null && ptos.getEquipment().getId() != null) {
                                Join<?, ?> equipment = root.join(PM_.EQUIPMENTS, JoinType.INNER);
                                predicatesList.add(cb.equal(equipment
                                        .<String>get(PM_.EQUIPMENTS_ID), ptos.getEquipment()
                                        .getId()));

                            }
                            // 所属分类-equSys
                            if (ptos.getEquSys() != null && ptos.getEquSys().getId() != null) {
                                From<?, ?> equipment = root.join(PM_.EQUIPMENTS, JoinType.LEFT);
                                From<?, ?> equSys =
                                        equipment.join(PM_.EQUIPMENTSYETEM, JoinType.LEFT);
                                predicatesList.add(cb.equal(equSys
                                        .<String>get(PM_.EQUIPMENTSYSETM_ID), ptos.getEquSys()
                                        .getId()));
                            }
                            if(ptos.getSiteIds()!=null && ptos.getSiteIds().length>0){
                                From<?, ?> spaceForm = root.join(PM_.SPACES, JoinType.LEFT);
                                From<?, ?> siteForm = spaceForm.join(PM_.SITE, JoinType.LEFT);
                                predicatesList.add(siteForm.get(PM_.SITE_ID).in(ptos.getSiteIds()));
                            }
                            if(ptos.getBuildingIds()!=null && ptos.getBuildingIds().length>0){
                                From<?, ?> spaceForm = root.join(PM_.SPACES, JoinType.LEFT);
                                From<?, ?> buildingForm = spaceForm.join(PM_.BUILDING, JoinType.LEFT);
                                predicatesList.add(buildingForm.get(PM_.BUILDING_ID).in(ptos.getBuildingIds()));
                            }
                            if(ptos.getFloorIds()!=null && ptos.getFloorIds().length>0){
                                From<?, ?> spaceForm = root.join(PM_.SPACES, JoinType.LEFT);
                                From<?, ?> floorForm = spaceForm.join(PM_.FLOOR, JoinType.LEFT);
                                predicatesList.add(floorForm.get(PM_.FLOOR_ID).in(ptos.getFloorIds()));
                            }
                            if(ptos.getRoomIds()!=null && ptos.getRoomIds().length>0){
                                From<?, ?> spaceForm = root.join(PM_.SPACES, JoinType.LEFT);
                                From<?, ?> roomForm = spaceForm.join(PM_.ROOM, JoinType.LEFT);
                                predicatesList.add(roomForm.get(PM_.ROOM_ID).in(ptos.getRoomIds()));
                            }



                        }
                        predicatesList.add(cb.equal(root.<String>get(PM_.DELETED), false));
                        query.distinct(true);
                        query.where(predicatesList.toArray(new Predicate[predicatesList.size()]));
                        return query.getRestriction();
                    }
                }, page);
         //lazy load
        //preventiveMaintenanceBuilder.init(result.getContent()).addPMSpace().addWorkTeam().addPMMaterial();
        return result;

    }

    @Override
    @XiaTransactional(readOnly = false)
    public PreventiveMaintenance create(PmRequestDTO pdto) {

            List<PMStep>  pmSteps=new ArrayList<>();

            PreventiveMaintenance pm = pdto.getPm();

            pm = getPreventiveBaseInfo(pm);

            List<Equipment> equipments=this.getEquipmentsByPm(pm);

            pm.setEquipments(equipments);
            pm.setProject(ProjectContext.getCurrentProject());
            pm = preventiveMaintenanceRepository.save(pm);

            //物资
            if(pdto.getPmMaterials()!=null && pdto.getPmMaterials().size()>0){
                pmMaterialService.createForPm(pm,pdto.getPmMaterials());
            }

            //工具
            if(pdto.getPmTools()!=null && pdto.getPmTools().size()>0){
                pmToolService.createForPm(pm,pdto.getPmTools());
            }

            //提醒
            if(pdto.getPmNotices()!=null && pdto.getPmNotices().size()>0){
                for (PMNotice pmNotice : pdto.getPmNotices()) {
                    pmNotice.setId(null);
                }
                pmNoticeService.createforPm(pm, pdto.getPmNotices());
            }

            // 空间
            if(pdto.getSpaces()!=null && pdto.getSpaces().size()>0){
                bindPmSpacesForPm(pm,pdto.getSpaces());
            }

            //附件
            if(pdto.getAttachments()!=null && pdto.getAttachments().size()>0){
                bindAttachmentForPm(pm, pdto.getAttachments());
            }

            // 在todo表中插入1条记录++在表中插入一条或者二条记录
            pMDateTodoService.generateTodoList(pm);

            /**
             * 维护步骤+流程需要在最后处理，和提醒同时处理业务会产生事务问题:lock timeOut
             */
           if(pdto.getPmSteps()!=null && pdto.getPmSteps().size()>0){
            pmStepService.createSteps(pm, pdto.getPmSteps());
           }

        return pm;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void update(PmRequestDTO pdto) {

        PreventiveMaintenance pm = pdto.getPm();

        List<Equipment> equipments=this.getEquipmentsByPm(pm);

        PreventiveMaintenance pmInstance = preventiveMaintenanceRepository.findOne(pm.getId());

        // 若首次维护时间修改或者周期修改
        // 另加 -- 是否是滑动周期修改
        this.checkPeriodAndFirtstTodoChange(pm, pmInstance);

        pm = getPreventiveBaseInfo(pm);

        pm.setProject(pmInstance.getProject());

        //设置相关的时间，避免在copy时被清空
        pm.setCreatedBy(pmInstance.getCreatedBy());
        pm.setCreatedDate(pmInstance.getCreatedDate());
        if(pm.getPeriod() != null && pmInstance.getPeriod() != null){
            pm.getPeriod().setCreatedBy(pmInstance.getPeriod().getCreatedBy());
            pm.getPeriod().setCreatedDate(pmInstance.getPeriod().getCreatedDate());
        }
        BeanUtils.copyProperties(pm,pmInstance);

        //先清空设备信息
        pm.setEquipments(null);
        pm.setIsImport(false);
        pm = preventiveMaintenanceRepository.save(pm);

        //清理原有关联
        this.removeOriginConnectionById(pm.getId());

        //设备
        pm.setEquipments(equipments);

        //物资-
        if(pdto.getPmMaterials()!=null && pdto.getPmMaterials().size()>0){
            pmMaterialService.createForPm(pm,pdto.getPmMaterials());
        }

        //工具-
        if(pdto.getPmTools()!=null && pdto.getPmTools().size()>0){
            pmToolService.createForPm(pm,pdto.getPmTools());
        }

        //提醒-
        this.handlePmNotice(pm,pdto.getPmNotices());

        // 空间-
        if(pdto.getSpaces()!=null && pdto.getSpaces().size()>0){
            bindPmSpacesForPm(pm,pdto.getSpaces());
        }

        //附件
        if(pdto.getAttachments()!=null && pdto.getAttachments().size()>0){
            bindAttachmentForPm(pm,pdto.getAttachments());
        }

        /**
         * 维护步骤+流程需要在最后处理，和提醒同时处理业务会产生事务问题:lock timeOut
         */
        if(pdto.getPmSteps()!=null && pdto.getPmSteps().size()>0){
            pmStepService.updateSteps(pm, pdto.getPmSteps());
        }

    }

    @Override
    @XiaTransactional(readOnly = true)
    public PmShowDetailDTO findPmDtoById(Long id) {

        PreventiveMaintenance pm = preventiveMaintenanceRepository.findOneHardly(id);
        pm=cancelLazyLoad(pm);
        PmShowDetailDTO pmDto = new PmShowDetailDTO();

        //设备
        pm=this.findEquipmentsForPm(pm);

        pmDto.setPm(pm);

        //工作组
        pmDto.setWorkTeams(new HashSet<WorkTeam>(this.findWorkTeamsByPmId(id)));

        //空间
        pmDto=this.findPmSpacesForDto(pmDto);

        //步骤
        pmDto=this.findPmStepsForDto(pmDto);

        //耗材
        pmDto=this.findPmMaterialsForDto(pmDto);

        //工具
        pmDto=this.findPmToolsForDto(pmDto);

        //提醒
        pmDto=this.findPmNoticesForDto(pmDto);

        //附件
        pmDto=this.findAttchmentForDto(pmDto);

        return pmDto;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public void deletePreventiveMaintenanceById(Long id) {
        pmNoticeActivityRepository.deleteByPmIdAndWoIsNullAndUnNotify(id);
        //删除未关联工单的计划
        //List<Long> woTodoIds=workOrderRepository.findToDoIdByPmId(id);
        pmDateTodoRepository.deleteTodoHasNoWorkOrderByPmId(id);
        preventiveMaintenanceRepository.delete(id);

    }


    // 周期调整或者首次维护时间修改之后 对todo进行处理
    // 另加 -- 调整是否是滑动周期
    public void afterChangePeriodAndFirstTodo(PreventiveMaintenance pm,Period period,Date date,Boolean fixedPeriod) {
        // 删除所有未生成的todos 再重新生成todo
        PreventiveMaintenance pmUpdate=pm;
        pMDateTodoService.deleteTodoListNoGenerateWO(pmUpdate);
        pmUpdate.setPeriod(period);
        pmUpdate.setDateFristTodo(date);
        pmUpdate.setFixedPeriod(fixedPeriod);
        pMDateTodoService.generateTodoList(pmUpdate);

    }

    @Override
    @Transactional(readOnly=true)
    public PageQueryRequestDTO returnPageInfo(PtmsQueryRequest pagerequest, Pageable page) {
        PageQueryRequestDTO pqdto = new PageQueryRequestDTO();
        Page<PreventiveMaintenance> info = findPreventiveMaintenance(pagerequest, page);

        BeanUtils.copyProperties(info, pqdto);
        List<PreventiveMaintenanceQueryDTO> ptdoList =
                new ArrayList<PreventiveMaintenanceQueryDTO>();
        List<PreventiveMaintenance> pmList = info.getContent();
        for (PreventiveMaintenance pm : pmList) {
            PreventiveMaintenanceQueryDTO ptdo = new PreventiveMaintenanceQueryDTO();
                BeanUtils.copyProperties(pm,ptdo);
                //系统分类--需要去重
                if (pm.getEquipments() != null && pm.getEquipments().size() > 0) {
                    List<String> equCodes = new LinkedList<String>();
                    List<String> equNames = new LinkedList<String>();
                    Set<String> equSysNames=new HashSet<>();
                    for (Equipment eq : pm.getEquipments()) {
                        equCodes.add(eq.getCode());
                        equNames.add(eq.getName());
                        if (eq.getEquipmentSystem() != null) {
                            eq.getEquipmentSystem().getId();
                            if(!StringUtils.isBlank(eq.getEquipmentSystem().getFullName())){
                                equSysNames.add(eq.getEquipmentSystem().getFullName());
                            }
                        }
                    }
                    ptdo.setEquipmentCodes(listToString(equCodes));
                    ptdo.setEquipmentNames(listToString(equNames));
                    // ptdo.setEquSysFullNames(listToString(equSysFullNames));
                    if(equSysNames.size()>0){
                        ptdo.setEquSysFullNames(listToString(new ArrayList<String>(equSysNames)).replace(",",";"));
                    }
                }
                //空间
                if (pm.getSpaces() != null && pm.getSpaces().size() > 0) {
                    ptdo.setSpaceNames(buildSpaceNames(pm.getSpaces()));
                }
                //周期
            if(pm.getPeriod()!=null){
                ptdo.setPeriodEnum(pm.getPeriod());
                StringBuffer stringBuffer=new StringBuffer();
                stringBuffer.append(pm.getPeriod().getValue());
                String periodValue=pm.getPeriod().getValue()+ XiaMesssageResource.getMessage(pm.getPeriod().getType().toString());
                ptdo.setPeriod(periodValue);
            }
            Long proId=ProjectContext.getCurrentProject();
            Date dateNextTodo=pmDateTodoRepository.findDateBYPmIdAndDate(ptdo.getId(),new Date(),proId);
            if(pm.getFixedPeriod()){
                if(dateNextTodo==null) {
                    Date start = DateUtil.buildDateOnFirstSecond(new Date());
                    Calendar nextCal = Calendar.getInstance();
                    long maintenanceTimeFrom = pm.getPeriod().getMaintenanceTimeFrom();
                    long maintenanceTimeTo = pm.getPeriod().getMaintenanceTimeTo();
                    int count=1;
                    do{
                         nextCal.setTime(PeriodDateUtil.buildNextTime(start, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), count));
                         count++;
                    }while (!PeriodDateUtil.monthPeriodJudge(maintenanceTimeFrom,maintenanceTimeTo,nextCal.get(Calendar.MONTH)+1));
                    String date=DateUtil.formatDateYYYYMMDD(nextCal.getTime());
                    dateNextTodo= java.sql.Date.valueOf(date);
                }
            }
            ptdo.setDateNextTodo(dateNextTodo);
            ptdoList.add(ptdo);
        }
        pqdto.setPdto(ptdoList);

        return pqdto;
    }

    @Override
    public PMSpace findOneSpace(List<PMSpace> pmses) {
        List<Long> sites=new ArrayList<>();
        List<Long> buildings=new ArrayList<>();
        List<Long> floors=new ArrayList<>();
        List<Long> rooms=new ArrayList<>();
        for(PMSpace pmSpace : pmses){
            if(pmSpace.getSite()!=null){
                sites.add(pmSpace.getSite().getId());
            }
            if(pmSpace.getBuilding()!=null){
                buildings.add(pmSpace.getBuilding().getId());
            }
            if(pmSpace.getFloor()!=null){
                floors.add(pmSpace.getFloor().getId());
            }
            if(pmSpace.getRoom()!=null){
                rooms.add(pmSpace.getRoom().getId());
            }
        }
        PMSpace pMSpace =pmses.get(0);
        if(rooms.size()<pmses.size()){
            pMSpace.setRoom(null);
        }else{
            Set<Long> longSet=new HashSet<>(rooms);
            if(longSet.size()!=1){
                pMSpace.setRoom(null);
            }
        }
        if(floors.size()<pmses.size()){
            pMSpace.setFloor(null);
        }else{
            Set<Long> longSet=new HashSet<>(floors);
            if(longSet.size()!=1){
                pMSpace.setFloor(null);
            }
        }
        if(buildings.size()<pmses.size()){
            pMSpace.setBuilding(null);
        }else{
            Set<Long> longSet=new HashSet<>(buildings);
            if(longSet.size()!=1){
                pMSpace.setBuilding(null);
            }
        }
        if(sites.size()<pmses.size()){
            pMSpace.setSite(null);
        }else{
            Set<Long> longSet=new HashSet<>(sites);
            if(longSet.size()!=1){
                pMSpace.setSite(null);
            }
        }
        return pMSpace;
    }

    @Override
    public WorkOrderProcess createWopByPmSteps(PreventiveMaintenance pm, PMStep pmStep) {

            WorkOrderProcess wop = new WorkOrderProcess();

            List<PMSpace> pmses = pm.getSpaces();
            if (pmses != null && pmses.size() > 0) {
                wop=getOneSpaceForWop(wop,pmses);
            }

            List<WorkTeam> wt = new ArrayList<>();
            wt.add(pmStep.getWorkTeam());
            wop.setWorkTeams(wt);
            wop.setAutoIssue(false);// 给个默认值 是否自动派工
            wop.setProcdefKey(WOProcessTemplateConstant.NO_BPM_KEY);
            wop.setType(WorkOrder.WorkOrderType.PPM);
            wop.setPassAndClose(pm.getArchive());//将pm里的自动存档带到工单流程中
            // 保存流程
            wop = workOrderProcessRepository.save(wop);

            // 创建流程
//            String prodefKey = super.copyBpmFromTemplate(PMProcessTemplateConstant.PM_TODO_WO_PROCESS_KEY, wop.getId().toString());
//            wop.setProcdefKey(prodefKey);

            // 更新流程
            wop = workOrderProcessRepository.save(wop);
        return wop;
    }

    @Override
    public List<WorkTeam> findWorkTeamsByPmId(Long id) {
        return pmStepRepository.findWorkTeamByPmIdAndWorkOrder(id);
    }

    @Override
    @XiaTransactional(readOnly = false)
    public List<Attachment> saveCopyFile(Long id) {
        List<Attachment> attachments=attachmentRepository.findByTableNameAndPKeyId(PreventiveMaintenance.class.getSimpleName(), id.toString());
        if (CollectionUtils.isEmpty(attachments)) {
            return null;
        }
        List<Attachment> newAttachments = new ArrayList<>();
        for(Attachment attachment : attachments){
            Attachment a = new Attachment();
            BeanUtils.copyProperties(attachment, a);
            a.setTableName(PreventiveMaintenance.class.getSimpleName());
            a.setpKeyId("0");
            a.setId(null);
            newAttachments.add(a);
        }
        return attachmentRepository.save(newAttachments);
    }

    private void handlePmNotice(PreventiveMaintenance pm,List<PMNotice> pmNotices){
        if(pmNotices!=null && pmNotices.size()>0){
            //先判断提醒是否删除 若已经删除了 则同时删除notice对应的activity
            Set<String> stringSet=new HashSet<>();
            for(PMNotice pmNotice : pmNotices){
                stringSet.add(pmNotice.getType().toString());
            }
            if(!stringSet.contains(PMNotice.PMNoticeType.RESPONSE.toString())){
                //判断原先设置中是否有未及时响应的提醒，有则删除提醒通知记录
                List<Long> responseIds=pmNoticeRepository.findIdByPmIdAndType(pm.getId(),PMNotice.PMNoticeType.RESPONSE);
                if(responseIds!=null && responseIds.size()>0){
                    if(responseIds.size()==1 && responseIds.get(0)==null){
                    }else{
                        pmNoticeActivityRepository.deleteByPmIdAndNoticeIdAndUnNotify(pm.getId(), responseIds.toArray(new Long[responseIds.size()]));
                    }
                }
                pmNoticeRepository.deleteByPmIdAndType(pm.getId(),PMNotice.PMNoticeType.RESPONSE);
            }
            if(!stringSet.contains(PMNotice.PMNoticeType.COMPLETE.toString())){
                //判断原先设置中是否有未及时完成的提醒，有则删除提醒通知记录
                List<Long> completeIds=pmNoticeRepository.findIdByPmIdAndType(pm.getId(),PMNotice.PMNoticeType.COMPLETE);
                if(completeIds!=null && completeIds.size()>0){
                    if(completeIds.size()==1 && completeIds.get(0)==null){
                    }else{
                        pmNoticeActivityRepository.deleteByPmIdAndNoticeIdAndUnNotify(pm.getId(), completeIds.toArray(new Long[completeIds.size()]));
                    }
                }
                pmNoticeRepository.deleteByPmIdAndType(pm.getId(),PMNotice.PMNoticeType.COMPLETE);

            }

            if(!stringSet.contains(PMNotice.PMNoticeType.APPROVAL.toString())){
                pmNoticeRepository.deleteByPmIdAndType(pm.getId(),PMNotice.PMNoticeType.APPROVAL);
            }
            if(!stringSet.contains(PMNotice.PMNoticeType.REMIND.toString())){
                List<Long> remindIds=pmNoticeRepository.findIdByPmIdAndType(pm.getId(),PMNotice.PMNoticeType.REMIND);
                if(remindIds!=null && remindIds.size()>0){
                    if(remindIds.size()==1 && remindIds.get(0)==null){
                    }else{
                        pmNoticeActivityRepository.deleteByPmIdAndNoticeIdAndUnNotify(pm.getId(), remindIds.toArray(new Long[remindIds.size()]));
                    }
                }
                pmNoticeRepository.deleteByPmIdAndType(pm.getId(),PMNotice.PMNoticeType.REMIND);
            }

            pmNoticeService.createforPm(pm,pmNotices);
        }else{
            this.deleteAllPmNoticeAndActivityForPm(pm);
        }
    }

    /**
     * 删除计划性维护的提醒和提醒通知
     */
    private void deleteAllPmNoticeAndActivityForPm(PreventiveMaintenance pm){
        pmNoticeActivityRepository.deleteByPmIdAndUnNotify(pm.getId());
        pmNoticeRepository.deleteByPmId(pm.getId());
    }

    private void removeOriginConnectionById(Long id){
        //步骤
        //pmStepRepository.deleteByPmId(id);
        //物资
        pmMaterialRepository.deleteByPmId(id);
        //工具
        pmToolRepository.deleteByPmId(id);
        //空间
        pMSpaceRepository.deleteByPmId(id);
        //提醒
        //pmNoticeRepository.deleteByPmId(id);
    }


    private void createWorkProcessByStepsForPm(PreventiveMaintenance pm,List<PMStep> pmSteps) {
        List<WorkOrderProcess> wops=new ArrayList<>();
        for(PMStep pmStep : pmSteps){
            if(pmStep.getWorkTeam()!=null){
                WorkOrderProcess wop=createWopByPmSteps(pm, pmStep);
                pmStep.setWorkOrderProcess(wop);
            }
        }
        pmStepRepository.saveInBatch(pmSteps);

    }

    private void bindAttachmentForPm(PreventiveMaintenance pm,List<Attachment> attachments){
        List<Attachment> files=new ArrayList<>();
        for(Attachment attachment : attachments){
            attachment=attachmentRepository.findOne(attachment.getId());
            attachment.setTableName(PreventiveMaintenance.class.getSimpleName());
            attachment.setpKeyId(pm.getId().toString());
            files.add(attachment);
        }
        if(files.size()>0){
            attachmentRepository.saveInBatch(files);
        }

    }


    private WorkOrderProcess getOneSpaceForWop(WorkOrderProcess wop,List<PMSpace> pmses){
        if (pmses != null && pmses.size() > 0) {
            PMSpace pms = new PMSpace();
            pms = findOneSpace(pmses);
            if (pms != null) {
                wop.setBuilding(pms.getBuilding());
                wop.setSite(pms.getSite());
                wop.setFloor(pms.getFloor());
                wop.setRoom(pms.getRoom());
            }
        }
        return wop;
    }

    private void checkPeriodAndFirtstTodoChange(PreventiveMaintenance pm,PreventiveMaintenance pmInstance){

        boolean hasChanged=VerifyChangesOnPm(pm,pmInstance);
        if(hasChanged){
            //保存修改后的pm
            Period period = pmInstance.getPeriod();
            period.setMaintenanceTimeFrom(pm.getPeriod().getMaintenanceTimeFrom());
            period.setMaintenanceTimeTo(pm.getPeriod().getMaintenanceTimeTo());
            period.setType(pm.getPeriod().getType());
            period.setValue(pm.getPeriod().getValue());
            period = periodRepository.save(period);

            afterChangePeriodAndFirstTodo(pmInstance,period,pm.getDateFristTodo(),pm.getFixedPeriod());
        }
    }

    private boolean VerifyChangesOnPm(PreventiveMaintenance pm,PreventiveMaintenance pmInstance){
        Period period=pm.getPeriod();
        Period periodInstance=pmInstance.getPeriod();
        Date firstTodo=pm.getDateFristTodo();
        Date firstTodoInstance=pmInstance.getDateFristTodo();
        Calendar firstTodoCal=Calendar.getInstance();
        firstTodoCal.setTime(firstTodo);
        firstTodoCal.set(Calendar.HOUR, 0);
        firstTodoCal.set(Calendar.MINUTE,0);
        firstTodoCal.set(Calendar.SECOND,0);

        Calendar firstTodoInstanceCal=Calendar.getInstance();
        firstTodoInstanceCal.setTime(firstTodoInstance);
        firstTodoInstanceCal.set(Calendar.HOUR, 0);
        firstTodoInstanceCal.set(Calendar.MINUTE,0);
        firstTodoInstanceCal.set(Calendar.SECOND,0);

        if(pmInstance.getPeriod().getMaintenanceTimeFrom() != null) {
            long pmMaintenanceTimeFrom = pm.getPeriod().getMaintenanceTimeFrom();
            long pmMaintenanceTimeTo = pm.getPeriod().getMaintenanceTimeTo();
            long pmMaintenanceTimeFromInstance = pmInstance.getPeriod().getMaintenanceTimeFrom();
            long pmMaintenanceTimeToInstance = pmInstance.getPeriod().getMaintenanceTimeTo();

            if(pmMaintenanceTimeFrom != pmMaintenanceTimeFromInstance
                    || pmMaintenanceTimeTo != pmMaintenanceTimeToInstance){
                return true;
            }
        } else {
            return true;
        }

        // 是否滑动周期修改
        if (pm.getFixedPeriod() != pmInstance.getFixedPeriod()) {
            return true;
        }

        if(firstTodoCal.getTimeInMillis()!=firstTodoInstanceCal.getTimeInMillis()){
            return true;
        }

        if(!period.getValue().equals(periodInstance.getValue())){
            return true;
        }
        if(period.getType().ordinal()!=periodInstance.getType().ordinal()){
            return true;
        }
        if(pmInstance.getIsImport()!=null && pmInstance.getIsImport()){
            return true;
        }

        return false;
    }

    private String buildSpaceName(PMSpace pspace) {
        StringBuffer sb = new StringBuffer();
        /*if (pspace.getSite() != null) {
            sb.append(pspace.getSite().getName() + "/");
        }*/
        if (pspace.getBuilding() != null) {
            sb.append(pspace.getBuilding().getName() + " / ");
        }
        if (pspace.getFloor() != null) {
            sb.append(pspace.getFloor().getName() + " / ");
        }
        if (pspace.getRoom() != null) {
            sb.append(pspace.getRoom().getName());
        }
        String result=sb.toString();
        if(result.endsWith(" / ")){
            result=result.substring(0, result.length() - 2);

        }
        return result;

    }

    private String buildSpaceNames(List<PMSpace> pspace) {
        String result = "";
        for(PMSpace space :pspace){
            if(space.getBuilding()!=null){
                result+=space.getBuilding().getName() + " / ";
            }
            if(space.getFloor()!=null){
                result+=space.getFloor().getName() + " / ";
            }
            if(space.getRoom()!=null){
                result+=space.getRoom().getName() + " / ";
            }
            if(result!=""){
                result =result.substring(0,result.length()-2);
                result+="；";
            }
        }
        if(result.endsWith("；")){
            result=result.substring(0,result.length()-2);
        }
        return result;
    }

    private String listToString(List<String> list) {
        String result = "";
        if (list != null && list.size() > 0) {
            result = list.toString().substring(1, list.toString().length() - 1);
        }
        return result;
    }


    private PreventiveMaintenance cancelLazyLoad(PreventiveMaintenance pm){
        pm.setPmMaterials(null);
        pm.setPmTools(null);
        pm.setPmSteps(null);
        pm.setPmNotices(null);
        return pm;
    }

    private PreventiveMaintenance findEquipmentsForPm(PreventiveMaintenance pm){

        List<Equipment> equipments=preventiveMaintenanceRepository.findEquipmentsByIdHardly(pm.getId());
        pm.setEquipments(equipments);
        return pm;
    }

    private PmShowDetailDTO findPmMaterialsForDto(PmShowDetailDTO pmDto){
        Long pmId=pmDto.getPm().getId();
        List<PMMaterial> pmMaterials=preventiveMaintenanceRepository.findPMMaterialsByIdHardly(pmId);
        List<Inventory> inventories = new ArrayList<>();
        if(pmMaterials != null && pmMaterials.size() > 0){
            for(PMMaterial pmMaterial : pmMaterials){
              if(pmMaterial==null){
                  return pmDto;
              }else{
                  if(pmMaterial.getInventory()!=null){
                      inventories.add(pmMaterial.getInventory());
                  }
                  pmMaterial.setPm(null);
              }
            }
        }
        if(inventories!=null && inventories.size()>0){
            inventoryBuilder.init(inventories).addMaterial().addWarehouse();
        }
        pmDto.setPmMaterials(pmMaterials);

        return pmDto;
    }

    private PmShowDetailDTO findAttchmentForDto(PmShowDetailDTO pmDto){
        Long pmId=pmDto.getPm().getId();
        List<Attachment> attachments=attachmentRepository.findByTableNameAndPKeyId(PreventiveMaintenance.class.getSimpleName(), pmId.toString());
        pmDto.setAttachments(attachments);
        return pmDto;
    }

    private PmShowDetailDTO findPmNoticesForDto(PmShowDetailDTO pmDto){
        Long pmId=pmDto.getPm().getId();
        List<Integer> aheadDays=new ArrayList<>();
        boolean hasExit=false;
        List<PMNotice> pmNotices=preventiveMaintenanceRepository.findPMNoticesById(pmId);
        if(pmNotices != null && pmNotices.size() > 0){
            for(PMNotice pmnotice : pmNotices){
                if(pmnotice==null) {
                    return pmDto;
                }else{
                    pmnotice.setPm(null);
                    if (PMNotice.PMNoticeType.RESPONSE.equals(pmnotice.getType())) {
                        PmNoticeDTO pdto = new PmNoticeDTO();
                        pdto.setId(pmnotice.getId());
                        pdto.setEmployees(findEmployeeByPmNoticeId(pmnotice.getId()));
                        pdto.setMsgType(pmnotice.getNotice().getMsgType());
                        pmDto.setUnResponse(pdto);
                    }
                    if (PMNotice.PMNoticeType.COMPLETE.equals(pmnotice.getType())) {
                        PmNoticeDTO pdto = new PmNoticeDTO();
                        pdto.setId(pmnotice.getId());
                        pdto.setEmployees(findEmployeeByPmNoticeId(pmnotice.getId()));
                        pdto.setMsgType(pmnotice.getNotice().getMsgType());
                        pmDto.setUnComplete(pdto);
                    }

                    if (PMNotice.PMNoticeType.APPROVAL.equals(pmnotice.getType())) {
                        PmNoticeDTO pdto = new PmNoticeDTO();
                        pdto.setId(pmnotice.getId());
                        pdto.setEmployees(findEmployeeByPmNoticeId(pmnotice.getId()));
                        pdto.setMsgType(pmnotice.getNotice().getMsgType());
                        pmDto.setApprovalNotice(pdto);
                    }

                    if (PMNotice.PMNoticeType.REMIND.equals(pmnotice.getType())) {
                        if (!hasExit) {
                            hasExit=true;
                            PmNoticeDTO pdto = new PmNoticeDTO();
                            pdto.setEmployees(findEmployeeByPmNoticeId(pmnotice.getId()));
                            pdto.setMsgType(pmnotice.getNotice().getMsgType());
                            pmDto.setWorkRemind(pdto);
                        }
                        aheadDays.add(pmnotice.getAhead());
                    }
                }
            }
            pmDto.setAheadDays(aheadDays);
        }
        return pmDto;
    }

    private List<Employee> findEmployeeByPmNoticeId(Long id){
        List<Employee> employees=new ArrayList<>();
        List<Long> ids=pmNoticeRepository.findEmployeeIdsById(id);
        if(ids!=null && ids.size()>0){
            if(ids.get(0)!=null){
                employees=employeeRepository.findByIdIn(new HashSet<Long>(ids));

            }
        }
        return employees;
    }

    private PmShowDetailDTO findPmToolsForDto(PmShowDetailDTO pmDto){
        Long pmId=pmDto.getPm().getId();
        List<PMTool> pmTools=preventiveMaintenanceRepository.findPMToolsByIdHardly(pmId);
        if(pmTools != null && pmTools.size() > 0){
            for(PMTool pmTool : pmTools){
                if(pmTool==null){
                  return pmDto;
                }else{
                    pmTool.setPm(null);
                }

            }

        }
        pmDto.setPmTools(pmTools);

        return pmDto;
    }

    private PmShowDetailDTO findPmStepsForDto(PmShowDetailDTO pmDto){
        Long pmId=pmDto.getPm().getId();
        List<PMStep> steps=pmStepRepository.findByPmIdAndWoIsNullHardly(pmId);
        if(steps != null && steps.size() > 0){
            for(PMStep pmStep : steps){
                if(pmStep==null){
                    return pmDto;
                }else{
                    pmStep.setPm(null);
                }
            }
        }
        pmDto.setPmSteps(steps);

        return pmDto;
    }

    private PmShowDetailDTO findPmSpacesForDto(PmShowDetailDTO pmDto){
        Long pmId=pmDto.getPm().getId();
        List<PMSpace> spaces=preventiveMaintenanceRepository.findPMSpacesByIdHardly(pmId);
        if(spaces != null && spaces.size() > 0){
            Iterator iterator=spaces.iterator();
            while (iterator.hasNext()){
                PMSpace pmSpace=(PMSpace)iterator.next();
                if(this.spaceHasDeleted(pmSpace)){
                    iterator.remove();
                }else{
                    if(pmSpace==null){
                        return pmDto;
                    }else{
                        pmSpace.setPm(null);
                    }
                }
            }
        }
        pmDto.setSpaces(spaces);

        return pmDto;
    }

    private boolean spaceHasDeleted(PMSpace pmSpace){
        boolean result=false;
        if(pmSpace==null){
            return true;
        }
        if(pmSpace.getSite()!=null && pmSpace.getSite().isDeleted()){
                result=true;
        }
        if(pmSpace.getBuilding()!=null && pmSpace.getBuilding().isDeleted()){
                result=true;
        }
        if(pmSpace.getFloor()!=null && pmSpace.getFloor().isDeleted()){
                result=true;
        }
        if(pmSpace.getRoom()!=null && pmSpace.getRoom().isDeleted()){
                result=true;
        }

        return result;
    }

    private PreventiveMaintenance getPreventiveBaseInfo(PreventiveMaintenance pm){

        //下次维护时间
        Date nextTimeate =this.buildNextDateByPm(pm);

        pm.setDateNextTodo(nextTimeate);

        // 设备---会造成持久化问题
        //pm=getEquipmentsForPm(pm);

        //预估完成时间
        pm=getTimeCountForPm(pm);

        //周期
        pm=getPeriodForPm(pm);

        return pm;
    }

    private PreventiveMaintenance getPmSpaceForPm(PreventiveMaintenance pm){
         //TODO

        return pm;
    }

    private PreventiveMaintenance getTimeCountForPm(PreventiveMaintenance pm){
        if (pm.getEstimatedWorkingTime() != null) {
            TimeCount timeCount = pm.getEstimatedWorkingTime();

            //        if(timeCount.getId()!=null) {
            //            TimeCount timeCountInstance = timeCountRepository.findOne(pm.getEstimatedWorkingTime().getId());
            //            BeanUtils.copyProperties(timeCount, timeCountInstance);
            //        }

            timeCount = timeCountRepository.save(pm.getEstimatedWorkingTime());
            pm.setEstimatedWorkingTime(timeCount);
            return pm;
        }else {
            return pm;
        }
    }

    private PreventiveMaintenance getPeriodForPm(PreventiveMaintenance pm){
        Period period=pm.getPeriod();

        if(period.getId()!=null){
            Period periodInstance=periodRepository.findOne(period.getId());
            BeanUtils.copyProperties(period, periodInstance, CommonFields_.CREATED_BY, CommonFields_.CREATED_DATE);
            periodInstance = periodRepository.save(periodInstance);
            pm.setPeriod(periodInstance);
        }else{
            period = periodRepository.save(period);
            pm.setPeriod(period);
        }

        return pm;
    }

    private List<PMSpace> createPmSpaceForPm(PreventiveMaintenance pm){
        List<PMSpace> spaceList = null;
        if (pm.getSpaces() != null && pm.getSpaces().size() > 0) {
             spaceList = new ArrayList<>();

            for (PMSpace pms : pm.getSpaces()) {
                pms=pMSpaceService.buildPMSpace(pms);
                spaceList.add(pms);
            }

            pMSpaceRepository.save(spaceList);
        }
        return spaceList;

    }

    @XiaTransactional(readOnly =false)
    private void bindPmSpacesForPm(PreventiveMaintenance pm,List<PMSpace> spaces){
                List<PMSpace> spaceList = new LinkedList<PMSpace>();
                for (PMSpace pms : spaces) {
                    pms=pMSpaceService.buildPMSpace(pms);
                    pms.setPm(pm);
                    spaceList.add(pms);
                }
                pMSpaceRepository.save(spaceList);
    }


    private List<Equipment> getEquipmentsByPm(PreventiveMaintenance pm){
        List<Equipment> equipments=null;
        if(pm.getEquipments()!=null && pm.getEquipments().size()>0){
            List<Long> eqIds=new ArrayList<>();
            for(Equipment equipment : pm.getEquipments()){
                if (equipment != null && equipment.getId() != null) {
                    eqIds.add(equipment.getId());
                }
            }
            //eqIds.size>0?
            equipments=equipmentRepository.findByIdIn(eqIds.toArray(new Long[eqIds.size()]));
        }
        return equipments;
    }

    private PreventiveMaintenance getEquipmentsForPm(PreventiveMaintenance pm){
        List<Equipment> equipments=null;
        if(pm.getEquipments()!=null && pm.getEquipments().size()>0){
            List<Long> eqIds=new ArrayList<>();
            for(Equipment equipment : pm.getEquipments()){
                if (equipment != null && equipment.getId() != null) {
                    eqIds.add(equipment.getId());
                }
            }
            //eqIds.size>0?
            equipments=equipmentRepository.findByIdIn(eqIds.toArray(new Long[eqIds.size()]));
        }
        pm.setEquipments(equipments);
        return pm;
    }

    private Date buildNextDateByPm(PreventiveMaintenance pm){

        Date start = pm.getDateFristTodo();
        start = DateUtil.buildDateOnFirstSecond(start);
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(start);
        Date now = DateUtil.buildDateOnFirstSecond(new Date());
        Calendar nextCal = Calendar.getInstance();
        long maintenanceTimeFrom = pm.getPeriod().getMaintenanceTimeFrom();
        long maintenanceTimeTo = pm.getPeriod().getMaintenanceTimeTo();
        int flag=1;
        if(now.compareTo(start)>0){

            //首次维护时间在以前
            Integer count=PeriodDateUtil.getCountForStartToToday(new Date(),pm.getDateFristTodo(), pm.getPeriod().getType(), pm.getPeriod().getValue().intValue());
            start=PeriodDateUtil.buildNextTime(start, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), (count - 1));

        }
        Date nextTimeate = PeriodDateUtil.buildNextTime(start, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), 1);
        nextCal.setTime(nextTimeate);
        //循环直到维护时间在规定月份内
        while (!PeriodDateUtil.monthPeriodJudge(maintenanceTimeFrom, maintenanceTimeTo, nextCal.get(Calendar.MONTH)+1)) {
            nextTimeate = PeriodDateUtil.buildNextTime(start, pm.getPeriod().getType(), pm.getPeriod().getValue().intValue(), flag);
            nextCal.setTime(nextTimeate);
            flag++;
        }
        return nextTimeate;
    }

    @Override
    @XiaTransactional(readOnly = false)
    public String updatePeriod() {
        List <PreventiveMaintenance> preventiveMaintenanceList = preventiveMaintenanceRepository.findRepeatPeriodPPM();
        if(CollectionUtils.isNotEmpty(preventiveMaintenanceList)){
            for(PreventiveMaintenance preventiveMaintenance:preventiveMaintenanceList){
                //旧的周期
                Period period=preventiveMaintenance.getPeriod();
                Period periodNew=new Period();
                //复制周期 并置空id 保存
                BeanUtils.copyProperties(period,periodNew);
                periodNew.setId(null);
                periodNew=periodRepository.save(periodNew);
                preventiveMaintenance.setPeriod(periodNew);
            }
         }
        return SUCCESS;
    }

    @Override
    public int getPmEqCountByEqId(Long eqId){
        String sql = "select count(*) as num from pm_eq where eq_id="+eqId;
        Map map = jdbcTemplate.queryForMap(sql);
        int result = 0;
        if(null != map){
            result = Integer.parseInt(map.get("num").toString());
        }
        return result;
    }

    @Override
    public void deletePmEqCountByEqId(Long eqId){
        String sql = "delete from pm_eq where eq_id="+eqId;
        jdbcTemplate.execute(sql);
    }

    @Override
    public Boolean checkFixedPeriod(Long id) {
        String sql = "SELECT COUNT(w.wo_id) as num FROM wo w LEFT JOIN pm pm ON w.pm_id=pm.pm_id WHERE pm.pm_id="+id+" AND w.`status`!=7";
        Map map = jdbcTemplate.queryForMap(sql);
        int num = 0;
//        String fixed = "false";
        if(null != map){
            num = Integer.parseInt(map.get("num").toString());
//            fixed = map.get("fixed").toString();
        }
        //判断是否滑动周期
//        if ("false".equals(fixed)) {
//            return Boolean.TRUE;
//        }
        //判断是否有工单未存档
        if (num>0) {
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }
    }

}
