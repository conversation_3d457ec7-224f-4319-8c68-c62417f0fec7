package cn.facilityone.shang.preventive.pm001.dto;

import java.io.Serializable;

/**
 * Created by <PERSON> on 2016/4/6.
 */
public class PmNoticeMaxAheadDTO implements Serializable {
    private Integer ahead;
    private Long pmId;
    private Long pmnId;
    public PmNoticeMaxAheadDTO(){

    }

    public PmNoticeMaxAheadDTO(Integer ahead,Long pmId,Long pmnId){
        this.ahead = ahead;
        this.pmId = pmId;
        this.pmnId = pmnId;
    }

    public Integer getAhead() {
        return ahead;
    }

    public void setAhead(Integer ahead) {
        this.ahead = ahead;
    }

    public Long getPmId() {
        return pmId;
    }

    public void setPmId(Long pmId) {
        this.pmId = pmId;
    }

    public Long getPmnId() {
        return pmnId;
    }

    public void setPmnId(Long pmnId) {
        this.pmnId = pmnId;
    }
    
    
}
