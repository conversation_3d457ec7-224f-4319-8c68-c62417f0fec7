package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.common.repository.NoticeRepository;
import cn.facilityone.shang.common.repository.NotifierRepository;
import cn.facilityone.shang.entity.common.Notice;
import cn.facilityone.shang.entity.common.Notifier;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeActivityRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import cn.facilityone.shang.preventive.pm002.respository.PMDateTodoRepository;
import cn.facilityone.xia.persistence.annotation.XiaTransactional;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Created by charles.chen on 2015/6/3.
 */
@Service
public class PMNoticeServiceImpl implements PMNoticeService {
    @Autowired
    private PMNoticeRepository pmNoticeRepository;
    @Autowired
    private PreventiveMaintenanceRepository pmRepository;
    @Autowired
    private PMNoticeActivityRepository pmNoticeActivityRepository;
    @Autowired
    private PMDateTodoRepository pmDateTodoRepository;
    @Autowired
    private NoticeRepository noticeRepository;
    @Autowired
    private NotifierRepository notifierRepository;

    @Override
    public void createforPm(PreventiveMaintenance pm, List<PMNotice> pmNotices) {
        List<PMNotice> remindNotice=new ArrayList<>();
        for(PMNotice pmNotice : pmNotices){


            if(PMNotice.PMNoticeType.RESPONSE.equals(pmNotice.getType())){
                handleUnResponseNoticeAndUnCompleteNotice(pm, pmNotice);
            }

            if(PMNotice.PMNoticeType.COMPLETE.equals(pmNotice.getType())){
                handleUnResponseNoticeAndUnCompleteNotice(pm, pmNotice);
            }

            if(PMNotice.PMNoticeType.REMIND.equals(pmNotice.getType())){
                remindNotice.add(pmNotice);
            }

            if(PMNotice.PMNoticeType.APPROVAL.equals(pmNotice.getType())){
                handleApprovalNotice(pm, pmNotice);
            }
        }
        if(remindNotice.size()>0){
            handleRemindNotice(pm, remindNotice);
        }

    }

    @XiaTransactional(readOnly = false)
    private void handleApprovalNotice(PreventiveMaintenance pm, PMNotice pmNotice) {
        if(pmNotice.getId()!=null){
            PMNotice pmnotcieInstance=pmNoticeRepository.findOneHardly(pmNotice.getId());
            if(pmnotcieInstance==null){
                Notice notice=this.createNotice(pmNotice);
                pmNotice=this.createNoticeForPmn(pmNotice,notice);
                pmNotice.setPm(pm);
                pmNoticeRepository.save(pmNotice);
            }else{
                if(pmnotcieInstance.getNotice()!=null){
                    pmnotcieInstance.getNotice().getMsgCode();
                    Notice noticeInstance=pmnotcieInstance.getNotice();
                    List<Notifier> notifiers= this.bindNotiferForNotice(pmNotice.getNotice());
                    noticeInstance.setMsgCode("0");
                    noticeInstance.setMsgType(pmNotice.getNotice().getMsgType());
                    noticeInstance = noticeRepository.save(noticeInstance);
                    notifierRepository.deleteByNoticeId(noticeInstance.getId());
                    updateNotiferForNotice(notifiers,noticeInstance);
                }
            }
        }else{
            Notice notice=this.createNotice(pmNotice);
            pmNotice=this.createNoticeForPmn(pmNotice,notice);
            pmNotice.setPm(pm);
            pmNoticeRepository.save(pmNotice);
        }
    }

    @XiaTransactional(readOnly =  false)
    private void handleUnResponseNoticeAndUnCompleteNotice(PreventiveMaintenance pm,PMNotice pmNotice){

        if(pmNotice.getId()!=null){
            PMNotice pmnotcieInstance=pmNoticeRepository.findOneHardly(pmNotice.getId());
            if(pmnotcieInstance==null){
                Notice notice=this.createNotice(pmNotice);

                pmNotice=this.createNoticeForPmn(pmNotice,notice);

                pmNotice.setPm(pm);

                pmNoticeRepository.save(pmNotice);
            }else{
                if(pmnotcieInstance.getNotice()!=null){
                    pmnotcieInstance.getNotice().getMsgCode();
                    Notice noticeInstance=pmnotcieInstance.getNotice();
                    List<Notifier> notifiers= this.bindNotiferForNotice(pmNotice.getNotice());
                    noticeInstance.setMsgCode("0");
                    noticeInstance.setMsgType(pmNotice.getNotice().getMsgType());
                    noticeInstance=noticeRepository.save(noticeInstance);
                    notifierRepository.deleteByNoticeId(noticeInstance.getId());
                    updateNotiferForNotice(notifiers,noticeInstance);

                }
            }


        }else{

            Notice notice=this.createNotice(pmNotice);

            pmNotice=this.createNoticeForPmn(pmNotice,notice);

            pmNotice.setPm(pm);

            pmNoticeRepository.save(pmNotice);
        }

    }

    /**
     * 处理计划性维护的工作提醒，前台传入提前天数和数据库中提前天数进行对比，
     */
    private void handleRemindNotice(PreventiveMaintenance pm,List<PMNotice> pmNotices){
        PMNotice pmNoticeTemplate=pmNotices.get(0);
        List<PMNotice> pmNoticeInstances=pmNoticeRepository.findByPmIdAndType(pm.getId(), PMNotice.PMNoticeType.REMIND);
        List<Integer> integerInstance=new ArrayList<>();
        List<Integer> integerInstanceMin=new ArrayList<>();
         for(PMNotice pmNotice: pmNoticeInstances){
            integerInstance.add(pmNotice.getAhead());
            integerInstanceMin.add(pmNotice.getAhead());
        }
        //Collections.copy(integerInstanceMin,integerInstance);
        List<Integer> aheadDaysAdd=new ArrayList<>();
        List<Integer> aheadDaysMin=new ArrayList<>();
        for(PMNotice pmNotice: pmNotices){
            aheadDaysAdd.add(pmNotice.getAhead());
            aheadDaysMin.add(pmNotice.getAhead());
        }
        if(integerInstance==null || integerInstance.size()==0){

            if(pmNotices!=null && pmNotices.size()>0){
                //新建工作提醒
                Notice notice=this.createNotice(pmNotices.get(0));
                for(PMNotice pmNotice: pmNotices){
                    pmNotice=createNoticeForPmn(pmNotice,notice);
                    pmNotice.setPm(pm);
                    pmNoticeRepository.save(pmNotice);
                }
            }

        }else{
            //判断notice是否有改动
            Notice noticeInstance=pmNoticeInstances.get(0).getNotice();
            Notice newNotice=pmNotices.get(0).getNotice();
            List<Notifier> notifiers= this.bindNotiferForNotice(newNotice);
            noticeInstance.setMsgType(newNotice.getMsgType());
            noticeInstance=noticeRepository.save(noticeInstance);
            //删除notice和noticer的关系
            notifierRepository.deleteByNoticeId(noticeInstance.getId());
            updateNotiferForNotice(notifiers,noticeInstance);

            //合并去重
            integerInstance.addAll(aheadDaysAdd);
            integerInstance.removeAll(integerInstanceMin);
            //添加的元素 若添加新的工作提醒 则将PM下所有todo设置为未生成工作提醒
            if(integerInstance.size()>0){
                for(Integer day : integerInstance){
                    PMNotice pmn=getPmNoticeByAhead(pmNotices,day);
                    pmn=createNoticeForPmn(pmn,noticeInstance);
                    pmn.setPm(pm);
                    pmNoticeRepository.save(pmn);
                }
                //将PM下所有todo设置为未生成工作提醒
                pmDateTodoRepository.updateTodoByRemind(pm.getId(),new Date());
            }
            //删除的元素-- 同时删除notcie的activity
            integerInstanceMin.removeAll(aheadDaysMin);
            if(integerInstanceMin.size()>0){
                List<Long> noticeIds=new ArrayList<>();
                for(Integer day : integerInstanceMin){
                    PMNotice pmn=getPmNoticeByAhead(pmNoticeInstances,day);
                    noticeIds.add(pmn.getId());
                    //删除notice和对应activity
                    pmNoticeRepository.delete(pmn);
                }
                pmNoticeActivityRepository.deleteByPmIdAndNoticeIdAndUnNotify(pm.getId(),noticeIds.toArray(new Long[noticeIds.size()]));
            }



        }

    }

    private PMNotice getPmNoticeByAhead(List<PMNotice> pmNotices,int ahead){
        PMNotice pmNotice=null;
        for(PMNotice pm: pmNotices){
            if(pm.getAhead()==ahead){
                pmNotice=pm;
                break;
            }
        }
        return pmNotice;
    }


    private PMNotice createNoticeForPmn(PMNotice pmNotice,Notice notice){

        pmNotice.setNotice(notice);

        return pmNotice;
    }

    private Notice createNotice(PMNotice pmNotice){
        Notice notice=pmNotice.getNotice();
        List<Notifier> notifiers= this.bindNotiferForNotice(notice);
        notice.setMsgCode("0");
        notice=noticeRepository.save(notice);
        updateNotiferForNotice(notifiers,notice);
        return notice;
    }

    private List<Notifier> bindNotiferForNotice(Notice notice){

        List<Notifier> notifiers=notice.getNotifiers();

        notifiers=notifierRepository.save(notifiers);

        return notifiers;
    }

    private void updateNotiferForNotice(List<Notifier> notifiers,Notice notice){

        for(Notifier notifier : notifiers){
            notifier.setNotice(notice);
        }
        notifierRepository.save(notifiers);

    }
}
