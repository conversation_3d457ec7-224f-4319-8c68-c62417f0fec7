package cn.facilityone.shang.preventive.pm001.builder;

import cn.facilityone.shang.entity.preventive.PMMaterial;
import cn.facilityone.shang.entity.preventive.PMStep;
import cn.facilityone.shang.entity.preventive.PMTool;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.preventive.pm001.respository.PMMaterialRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMStepRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMToolRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by charles.chen on 2015/6/28.
 */
@Service
public class PreventiveMaintenanceBuilder {
    @Autowired
    private PreventiveMaintenanceRepository preventiveMaintenanceRepository;
    @Autowired
    private PMMaterialRepository pmMaterialRepository;
    @Autowired
    private PMStepRepository pmStepRepository;
    @Autowired
    private PMToolRepository pmToolRepository;



    public void init(List<PreventiveMaintenance> pmList, List<Long> ids){

        if(pmList!=null && pmList.size()>0){
            for(PreventiveMaintenance pm: pmList){
                ids.add(pm.getId()) ;
            }
        }
    }
    public  void addSteps(List<PreventiveMaintenance> pmList, List<PMStep>  pmSteps){
        Map<Long,List<PMStep>>   maps=new HashMap<>();
        for(PMStep pmStep : pmSteps){
            if(maps.containsKey(pmStep.getPm().getId())){
                maps.get(pmStep.getPm().getId()).add(pmStep);
            }else{
                List<PMStep> steps=new ArrayList<>();
                steps.add(pmStep);
                maps.put(pmStep.getPm().getId(),steps);
            }
        }
        for(PreventiveMaintenance pm : pmList){
            if(maps.containsKey(pm.getId())){
                pm.setPmSteps(maps.get(pm.getId()));
            }else{
                pm.setPmSteps(null);
            }
        }
    }
    public void addTools(List<PreventiveMaintenance> pmList, List<PMTool>  tools){
        Map<Long,List<PMTool>>   maps=new HashMap<>();
        for(PMTool tool : tools){
            if(maps.containsKey(tool.getPm().getId())){
                maps.get(tool.getPm().getId()).add(tool);
            }else{
                List<PMTool> toolList=new ArrayList<>();
                toolList.add(tool);
                maps.put(tool.getPm().getId(),toolList);
            }
        }
        for(PreventiveMaintenance pm : pmList){
            if(maps.containsKey(pm.getId())){
                pm.setPmTools(maps.get(pm.getId()));
            }else{
                pm.setPmTools(null);
            }
        }
    }

    public void addMaterials(List<PreventiveMaintenance> pmList, List<PMMaterial> pmMaterials) {
        Map<Long, List<PMMaterial>> maps = new HashMap<>();
        for (PMMaterial pmm : pmMaterials) {
            if (maps.containsKey(pmm.getPm().getId())) {
                maps.get(pmm.getPm().getId()).add(pmm);
            } else {
                List<PMMaterial> pmms = new ArrayList<>();
                pmms.add(pmm);
                maps.put(pmm.getPm().getId(), pmms);
            }
        }
        for (PreventiveMaintenance pm : pmList) {
            if (maps.containsKey(pm.getId())) {
                pm.setPmMaterials(maps.get(pm.getId()));
            } else {
                pm.setPmMaterials(null);
            }
        }
    }
}