package cn.facilityone.shang.preventive.pm001.dto;

import javax.persistence.Column;
import java.io.Serializable;


public class PMMaterialDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = -5915362349841568468L;

    private Long id;

    /** 耗材名称 */
    private String name;

    /** 耗材单位 */
    private String unit;

    /** 耗材数量 */
    private Double quantity;

    /** 备注 */
    private String comment;

    /** 型号 */
    private String model;

    /**
     * 品牌
     */
    private String brand;

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
