package cn.facilityone.shang.preventive.pm001.respository;

import cn.facilityone.shang.entity.common.Notice;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.preventive.pm001.dto.PmDateAheadDTO;
import cn.facilityone.shang.preventive.pm001.dto.PmNoticeMaxAheadDTO;
import cn.facilityone.xia.persistence.repository.XiaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

/**
 * Created by charles.chen on 2015/6/3.
 */
public interface PMNoticeRepository extends XiaRepository<PMNotice, Long> {

    @Query("select notifiers.informedId from #{#entityName} pmn left join pmn.notice notice left join notice.notifiers notifiers where  pmn.id=?1")
    List<Long> findEmployeeIdsById(Long id);

    @Modifying
    @Query("delete from  #{#entityName} pmn where pmn.pm.id=?1 and pmn.type=?2")
    int deleteByPmIdAndType(Long id,PMNotice.PMNoticeType type);

    @Query("select pmn from #{#entityName} pmn LEFT JOIN FETCH  pmn.notice where  pmn.pm.id=?1 and pmn.type=?2")
    List<PMNotice> findByPmIdAndType(Long id,PMNotice.PMNoticeType type);

    @Modifying
    @Query("delete from #{#entityName} pmn where pmn.pm.id =?1")
    int deleteByPmId(Long id);

    @Query("select pmn.id from #{#entityName} pmn  where  pmn.pm.id=?1 and pmn.type=?2")
    List<Long> findIdByPmIdAndType(Long id,PMNotice.PMNoticeType type);

    @Deprecated
    @Query("select pmn.ahead from #{#entityName} pmn  where  pmn.pm.id=?1 and pmn.type=?2")
    List<Integer> findAheadDayByPmIdAndType(Long id,PMNotice.PMNoticeType type);

    @Query("select pmn from #{#entityName} pmn LEFT JOIN FETCH  pmn.pm pm left join fetch pm.period left join fetch pm.estimatedWorkingTime where pmn.pm.id in (?1) and pmn.type=?2" )
    List<PMNotice> findByPmInAndType(Set<Long> pmIds,PMNotice.PMNoticeType type);

    @Query("select pmn from #{#entityName} pmn LEFT JOIN FETCH  pmn.pm pm left join fetch pm.period left join fetch pm.estimatedWorkingTime where pmn.pm.id in (?1) and pmn.type=?2" )
    List<PMNotice> findByPmIdsInAndType(List<Long> pmIds,PMNotice.PMNoticeType type);

    @Query("select pmn from #{#entityName} pmn LEFT JOIN FETCH  pmn.pm pm left join fetch pm.period left join fetch pm.estimatedWorkingTime where pmn.type=?1" )
    List<PMNotice> findByType(PMNotice.PMNoticeType type);

    @Query("select pmn from #{#entityName} pmn  where  pmn.pm.id=?1")
    List<PMNotice> findByPmId(Long id);

    @Query("select (pmn.ahead+?3) from #{#entityName} pmn  where  pmn.pm.id=?1 and pmn.type=?2")
    List<Integer> findTotalAheadDaysByPmIdAndType(Long id,PMNotice.PMNoticeType type,Integer aheadDay);

    @Query("select  pmn.notice from #{#entityName} pmn  where pmn.id in (?1)")
    List<Notice> findNoticesInPmIdsHardly(Long[] ids);

    @Query("select  pmn from #{#entityName} pmn  where pmn.id =?1")
    PMNotice findOneHardly(Long id);
    
    @Deprecated
    @Query("select new cn.facilityone.shang.preventive.pm001.dto.PmDateAheadDTO(pm.id as pmId,pd as pmDateTodo,pn as pmNotice,pd.dateTodo as todoDate,pn.ahead as todoAhead,(pm.ahead + (select max(pmn.ahead)  from #{#entityName} pmn where pmn.pm.id=pd.pm.id group by pmn.pm )) as totalAhead) from PMDateTodo  pd inner join pd.pm pm left join pm.pmNotices pn where pn is not null and pd.genStatus=0 and pd.genRemindNotice=0")
    List<PmDateAheadDTO> findPmTodoAndAhead();

    @Query("select  pmn from #{#entityName} pmn left join fetch pmn.notice n left join fetch n.notifiers where pmn.pm.id=?1 and pmn.type = 3 ")
    PMNotice findApprovalNoticeByPmId(Long id);
}
