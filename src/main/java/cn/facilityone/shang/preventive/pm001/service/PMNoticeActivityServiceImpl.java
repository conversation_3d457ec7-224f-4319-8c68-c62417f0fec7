package cn.facilityone.shang.preventive.pm001.service;

import cn.facilityone.shang.entity.preventive.PMDateTodo;
import cn.facilityone.shang.entity.preventive.PMNotice;
import cn.facilityone.shang.entity.preventive.PMNoticeActivity;
import cn.facilityone.shang.entity.preventive.PreventiveMaintenance;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeActivityRepository;
import cn.facilityone.shang.preventive.pm001.respository.PMNoticeRepository;
import cn.facilityone.shang.preventive.pm001.respository.PreventiveMaintenanceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by charles.chen on 2015/6/5.
 */
@Service
public class PMNoticeActivityServiceImpl implements PMNoticeActivityService {
    @Autowired
    private PMNoticeActivityRepository pmNoticeActivityRepository;
    @Autowired
    private PMNoticeRepository pmNoticeRepository;
    @Autowired
    private PreventiveMaintenanceRepository pMRepository;

    @Override
    public void createByPmAndTodo(PreventiveMaintenance pm, List<PMDateTodo> pmDateTodoGroup) {

        List<PMNoticeActivity> pmNoticeActivities=new ArrayList<>();
        List<PMNotice> pmNotices=pmNoticeRepository.findByPmIdAndType(pm.getId(),PMNotice.PMNoticeType.REMIND);
        if(pmNotices!=null && pmNotices.size()>0){

           for(PMNotice pmNotice : pmNotices){
               if(pmNotice==null){
                   return;
               }
               for(PMDateTodo pmDateTodo : pmDateTodoGroup){
                   if(pmDateTodo==null){
                       return;
                   }
                   PMNoticeActivity pmNoticeActivity=new PMNoticeActivity();
                   pmNoticeActivity.setPm(pm);
                   pmNoticeActivity.setIsNotify(Boolean.FALSE);
                   pmNoticeActivity.setPmNotice(pmNotice);
                   pmNoticeActivity.setNotifyDateTime(pmDateTodo.getDateTodo());
                   pmNoticeActivities.add(pmNoticeActivity);
               }
           }
        }
        pmNoticeActivityRepository.save(pmNoticeActivities);
    }
}
