package cn.facilityone.shang.preventive.pm001.dto;

import cn.facilityone.shang.entity.organize.Employee;

import java.io.Serializable;
import java.util.List;

/**
 * Created by charles.chen on 2015/6/4.
 */
public class PmNoticeDTO implements Serializable {

    private Long id;

    private List<Employee> employees;

    private String msgType;

    public List<Employee> getEmployees() {
        return employees;
    }

    public void setEmployees(List<Employee> employees) {
        this.employees = employees;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
