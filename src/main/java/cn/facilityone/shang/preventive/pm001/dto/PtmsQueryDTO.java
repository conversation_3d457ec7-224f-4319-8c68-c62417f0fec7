package cn.facilityone.shang.preventive.pm001.dto;


import cn.facilityone.shang.entity.asset.Equipment;
import cn.facilityone.shang.entity.asset.EquipmentSystem;
import cn.facilityone.shang.entity.organize.Building;
import cn.facilityone.shang.entity.organize.Floor;
import cn.facilityone.shang.entity.organize.Room;
import cn.facilityone.shang.entity.organize.Site;

import java.io.Serializable;

public class PtmsQueryDTO implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 2375390932384629666L;
    private String pmName;
    private String period;
    private String auto;
    private Site site;
    private Building building;
    private Floor floor;
    private Room room;
    private Equipment equipment;
    private EquipmentSystem equSys;
    private Long[] siteIds;
    private Long[] buildingIds;
    private Long[] floorIds;
    private Long[] roomIds;
    public String getPmName() {
        return pmName;
    }

    public void setPmName(String pmName) {
        this.pmName = pmName;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }


    public String getAuto() {
        return auto;
    }

    public void setAuto(String auto) {
        this.auto = auto;
    }

    public Site getSite() {
        return site;
    }

    public void setSite(Site site) {
        this.site = site;
    }

    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public Floor getFloor() {
        return floor;
    }

    public void setFloor(Floor floor) {
        this.floor = floor;
    }

    public Room getRoom() {
        return room;
    }

    public void setRoom(Room room) {
        this.room = room;
    }

    public Equipment getEquipment() {
        return equipment;
    }

    public void setEquipment(Equipment equipment) {
        this.equipment = equipment;
    }

    public EquipmentSystem getEquSys() {
        return equSys;
    }

    public void setEquSys(EquipmentSystem equSys) {
        this.equSys = equSys;
    }

    public Long[] getRoomIds() {
        return roomIds;
    }

    public void setRoomIds(Long[] roomIds) {
        this.roomIds = roomIds;
    }

    public Long[] getFloorIds() {
        return floorIds;
    }

    public void setFloorIds(Long[] floorIds) {
        this.floorIds = floorIds;
    }

    public Long[] getBuildingIds() {
        return buildingIds;
    }

    public void setBuildingIds(Long[] buildingIds) {
        this.buildingIds = buildingIds;
    }

    public Long[] getSiteIds() {
        return siteIds;
    }

    public void setSiteIds(Long[] siteIds) {
        this.siteIds = siteIds;
    }
}
