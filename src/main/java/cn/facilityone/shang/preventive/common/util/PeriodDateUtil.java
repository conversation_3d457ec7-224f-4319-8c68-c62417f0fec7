package cn.facilityone.shang.preventive.common.util;


import cn.facilityone.shang.common.util.DateUtil;
import cn.facilityone.shang.entity.common.Period;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigInteger;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.concurrent.TimeUnit;

public class PeriodDateUtil {

  public static final String PERIOD_DAY = "每天一次";
  public static final String PERIOD_WEEK = "每周一次";
  public static final String PERIOD_MONTH = "每月一次";
  public static final String PERIOD_SEASON = "每季一次";
  public static final String PERIOD_YEAR = "每年一次";
  
  /**
   * 根据当前时间推算下一次循环日期<br>
   * 对于开始日期为1月31日的一月一次的循环的下一次为2月28日（闰年29日）
   * @param start 开始时间
   * @param period  循环周期
   * @param time 第几次，0当前，1下一次，n之后第N次
   * @return
   */
  public static Date buildNextTime(Date start, Period.PeriodType period,Integer periodNum, int time) {
      Calendar tododate = Calendar.getInstance();
      tododate.setTime(start);
      if(periodNum==null){
          periodNum =1;
      }
      time = periodNum * time;

      // 一小时一次
      if (period.equals(Period.PeriodType.HOUR)) {

          tododate.add(Calendar.HOUR, time);

      }
      // 一天一次
      else if (period.equals(Period.PeriodType.DAY)) {
          
          tododate.add(Calendar.DAY_OF_YEAR, time);
  
          // 一月一次
      } else if (period.equals(Period.PeriodType.MONTH)) {
          tododate.add(Calendar.MONTH, time);
  
          // 一季度一次
      } else if (period.equals(Period.PeriodType.SEASON)) {
          tododate.add(Calendar.MONTH, time * 3);
  
          // 一周一次
      } else if (period.equals(Period.PeriodType.WEEK)) {
          tododate.add(Calendar.WEEK_OF_YEAR, time);
  
          // 一年一次
      } else if (period.equals(Period.PeriodType.YEAR)) {
          tododate.add(Calendar.YEAR, time);
      }
  
      return tododate.getTime();
  }


    public static Date buildCompleteWorkTime(Date start, TimeUnit period,Integer periodNum, int time) {
        Calendar tododate = Calendar.getInstance();
        tododate.setTime(start);
        if(periodNum==null){
            periodNum =1;
        }
        time = periodNum * time;


        if (period.equals(period.MINUTES)) {

            tododate.add(Calendar.MINUTE, time);

            // 一月一次
        }
        // 一小时一次
        else if (period.equals(period.HOURS)) {

            tododate.add(Calendar.HOUR, time);

            // 一月一次
        }
        // 一天一次
        else if (period.equals(period.DAYS)) {

            tododate.add(Calendar.DAY_OF_YEAR, time);


        }

        return tododate.getTime();
    }


  
  /**
   * 推算now日期是否可以执行的日期，或满足周期<br>
   * 如果1月29(或者30，31)作为一月一次的开始循环日，则下一次的循环日为2月28，<br>
   * @param now 
   * @param start 任务开始时间
   * @param period 周期
   * @return
   */
  public static boolean isTimeToto(Date now,Date start, Period.PeriodType period,Integer periodNum){
    boolean result  = false;
    now = DateUtil.buildDateOnFirstSecond(now);
    start = DateUtil.buildDateOnFirstSecond(start);
    Calendar startdate = Calendar.getInstance();
    startdate.setTime(start);
    
    Calendar nowdate = Calendar.getInstance();
    nowdate.setTime(now);
    
    if(periodNum==null || periodNum.intValue() ==0){
        periodNum = 1;
    }
    //小时
      if (period.equals(Period.PeriodType.HOUR)) {
          int sday = startdate.get(Calendar.HOUR);
          int nday = nowdate.get(Calendar.HOUR);
          if(((nday-sday)%periodNum.intValue()) == 0){
              result = true;
          }
          // 一天一次
      }else if (period.equals(Period.PeriodType.DAY)) {
        int sday = startdate.get(Calendar.DAY_OF_YEAR);
        int nday = nowdate.get(Calendar.DAY_OF_YEAR);
        if(((nday-sday)%periodNum.intValue()) == 0){
            result = true;
        }
        // 一月一次
    } else if (period.equals(Period.PeriodType.MONTH)) {
        
        int sday = startdate.get(Calendar.MONTH);
        int nday = nowdate.get(Calendar.MONTH);
        if(((nday-sday)%periodNum.intValue()) == 0){
            if (nowdate.get(Calendar.DAY_OF_MONTH) == startdate.get(Calendar.DAY_OF_MONTH)) {
                result = true;
              }
              if(!result){
                result = DateUtil.isLastDayEqualOfMonthPeriod(nowdate,startdate);
              }
        }
        // 一季度一次
    } else if (period.equals(Period.PeriodType.SEASON)) {
        int nowMonth = nowdate.get(Calendar.MONTH) % (3*periodNum.intValue());
        int firstMonth = startdate.get(Calendar.MONTH) % (3*periodNum.intValue());
        if (nowMonth == firstMonth) {
          if (nowdate.get(Calendar.DAY_OF_MONTH) == startdate.get(Calendar.DAY_OF_MONTH)) {
            result = true;
          }
          if(!result){
            result = DateUtil.isLastDayEqualOfMonthPeriod(nowdate,startdate);
          }
        }
        
        // 一周一次
    } else if (period.equals(Period.PeriodType.WEEK)) {
        
        int sday = startdate.get(Calendar.WEEK_OF_YEAR);
        int nday = nowdate.get(Calendar.WEEK_OF_YEAR);
        if(((nday-sday)%periodNum.intValue()) == 0){
            if (nowdate.get(Calendar.DAY_OF_WEEK) == startdate.get(Calendar.DAY_OF_WEEK)) {
                result = true;
            }
        }
        // 一年一次
    } else if (period.equals(Period.PeriodType.YEAR)) {
        int sday = startdate.get(Calendar.YEAR);
        int nday = nowdate.get(Calendar.YEAR);
        if(((nday-sday)%periodNum.intValue()) == 0){
            if (nowdate.get(Calendar.DAY_OF_YEAR) == startdate.get(Calendar.DAY_OF_YEAR)) {
                result = true;
              }
        }
    }
    return result;
  }

    /**
     * 将周期全部转化为小时
     */

    public static BigInteger convetToHours(Period period){
        BigInteger resultValue=null;
        Long value=period.getValue();
        Period.PeriodType type=period.getType();

        return resultValue;
    }

    public static Integer getCountForStartToToday(Date now,Date start, Period.PeriodType period,Integer periodNum){
        int count=0;
        Calendar calNow=Calendar.getInstance();
        calNow.setTime(now);

        Calendar calStart=Calendar.getInstance();
        calStart.setTime(start);

        while(calStart.compareTo(calNow)<0){
            count++;
            if (period.equals(Period.PeriodType.DAY)) {
                calStart.add(Calendar.DAY_OF_YEAR,periodNum);
                // 一月一次
            } else if (period.equals(Period.PeriodType.MONTH)) {
                calStart.add(Calendar.MONTH,periodNum);

                // 一季度一次
            } else if (period.equals(Period.PeriodType.SEASON)) {

                calStart.add(Calendar.MONTH,(3*periodNum));
                // 一周一次
            } else if (period.equals(Period.PeriodType.WEEK)) {
                calStart.add(Calendar.WEEK_OF_YEAR,periodNum);

                // 一年一次
            } else if (period.equals(Period.PeriodType.YEAR)) {
                calStart.add(Calendar.YEAR,periodNum);
            }
        }
        return count;
    }

    /**
     * 当月第一天
     * @return
     */
    public static Date getFirstDay(Date date) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        return calendar.getTime();

    }

    /**
     * 当月最后一天
     * @return
     */
    public static Date getLastDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 0);

        return calendar.getTime();

    }

    /**
     * 判断月份是否在月份周期内
     * @param
     * @param end
     * @param now
     * @return
     */
    public static Boolean monthPeriodJudge(long start,long end,long now) {
        if((start == end && start == now)
                || ((start < end && (start <= now && now <= end))
                || (start > end && ((start <= now && now < 13) || (0<now && now <= end ))))){

            return true;
        }
        return false;
    }

}
