package cn.facilityone.shang.auth.auth002.view;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON> on 2018/5/10.
 */
public class ProjectAuthSaveRequest {

    @NotNull
    private Long projectId;

    private Integer userTotalNumber;

    private String endDate;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Integer getUserTotalNumber() {
        return userTotalNumber;
    }

    public void setUserTotalNumber(Integer userTotalNumber) {
        this.userTotalNumber = userTotalNumber;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
}
