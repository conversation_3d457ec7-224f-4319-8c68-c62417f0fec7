package cn.facilityone.shang.auth.auth003.service;

import cn.facilityone.shang.auth.auth003.dto.WechatSettingDTO;
import cn.facilityone.shang.auth.auth003.view.IdView;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/6/6
 */
public interface WechatSettingService {
    DataTableResponse getWechatSettingTable(DataTableRequest request);

    void saveWechatSettingInfo(WechatSettingDTO request);

    void deletedWechatSettingInfo(Long id);

    WechatSettingDTO getWeChatSettingInfo(Long id);

    Boolean checkDefaultSet(IdView view);
}
