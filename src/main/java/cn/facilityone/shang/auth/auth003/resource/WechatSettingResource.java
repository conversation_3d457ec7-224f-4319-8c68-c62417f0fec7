package cn.facilityone.shang.auth.auth003.resource;

import cn.facilityone.shang.auth.auth003.dto.WechatSettingDTO;
import cn.facilityone.shang.auth.auth003.view.IdView;
import cn.facilityone.shang.common.component.datatable.model.DataTableRequest;
import cn.facilityone.shang.common.component.datatable.model.DataTableResponse;
import cn.facilityone.shang.auth.auth003.service.WechatSettingService;
import cn.facilityone.xia.core.common.Result;
import org.glassfish.jersey.server.mvc.Template;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * 微信公共号设置
 * <AUTHOR>
 * @version 1.0
 * @date 2018/6/5
 */
@Path("/auth003")
public class WechatSettingResource {

    private final Logger logger = LoggerFactory.getLogger(WechatSettingResource.class);

    private static final String TEMPLATE_PATH = "/business/auth/auth003-wechatSetting.ftl";

    @Autowired
    private WechatSettingService wechatSettingService;

    @GET
    @Template(name = TEMPLATE_PATH)
    public Map<String, Object> init() {
        return new HashMap<String, Object>(0);
    }

    /**
     * 获取列表
     *
     * @param request
     * @return
     */
    @POST
    @Path("wechatSetting/table")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public DataTableResponse getWechatSettingTable(DataTableRequest request) {
        return wechatSettingService.getWechatSettingTable(request);
    }

    /**
     * 保存微信设置
     * @param request
     * @return
     */
    @POST
    @Path("wechatSetting/save")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result save(WechatSettingDTO request) {
        wechatSettingService.saveWechatSettingInfo(request);
        return new Result();
    }

    /**
     * 删除微信设置
     * @param id
     * @return
     */
    @GET
    @Path("wechatSetting/deleted/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Result deleted(@PathParam("id") Long id) {
        wechatSettingService.deletedWechatSettingInfo(id);
        return new Result();
    }

    /**
     * 获得微信设置信息
     * @param id
     * @return
     */
    @POST
    @Path("wechatSetting/info/{id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public WechatSettingDTO getWeChatSettingInfo(@PathParam("id") Long id) {
        return wechatSettingService.getWeChatSettingInfo(id);
    }

    /**
     * 查看是否设置默认公众号
     * @return
     */
    @POST
    @Path("wechatSetting/checkDefaultSet")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Boolean checkDefaultSet(IdView view) {
        return wechatSettingService.checkDefaultSet(view);
    }

}
